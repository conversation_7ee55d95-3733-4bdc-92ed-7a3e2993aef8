{"name": "nette/schema", "description": "📐 Nette Schema: validating data structures against a given Schema.", "keywords": ["nette", "config"], "homepage": "https://nette.org", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "require": {"php": "8.1 - 8.4", "nette/utils": "^4.0"}, "require-dev": {"nette/tester": "^2.5.2", "tracy/tracy": "^2.8", "phpstan/phpstan-nette": "^1.0"}, "autoload": {"classmap": ["src/"]}, "minimum-stability": "dev", "scripts": {"phpstan": "phpstan analyse", "tester": "tester tests -s"}, "extra": {"branch-alias": {"dev-master": "1.3-dev"}}}