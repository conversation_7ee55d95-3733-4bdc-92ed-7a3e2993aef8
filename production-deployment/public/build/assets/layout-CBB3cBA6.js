import{j as e,t as n}from"./app-J5EqS6dS.js";import{B as r,a as i}from"./smartphone-GGiwNneF.js";import{S as c}from"./app-layout-ox1kAwY6.js";function h({title:t,description:s}){return e.jsxs("header",{children:[e.jsx("h3",{className:"mb-0.5 text-base font-medium",children:t}),s&&e.jsx("p",{className:"text-sm text-muted-foreground",children:s})]})}function o({title:t,description:s}){return e.jsxs("div",{className:"mb-8 space-y-0.5",children:[e.jsx("h2",{className:"text-xl font-semibold tracking-tight",children:t}),s&&e.jsx("p",{className:"text-sm text-muted-foreground",children:s})]})}const x=[{title:"Profile",href:"/settings/profile",icon:null},{title:"Password",href:"/settings/password",icon:null},{title:"Two-Factor Auth",href:"/settings/two-factor",icon:null},{title:"Appearance",href:"/settings/appearance",icon:null}];function u({children:t}){if(typeof window>"u")return null;const s=window.location.pathname;return e.jsxs("div",{className:"px-4 py-6",children:[e.jsx(o,{title:"Settings",description:"Manage your profile and account settings"}),e.jsxs("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-y-0 lg:space-x-12",children:[e.jsx("aside",{className:"w-full max-w-xl lg:w-48",children:e.jsx("nav",{className:"flex flex-col space-y-1 space-x-0",children:x.map((a,l)=>e.jsx(r,{size:"sm",variant:"ghost",asChild:!0,className:i("w-full justify-start",{"bg-muted":s===a.href}),children:e.jsx(n,{href:a.href,prefetch:!0,children:a.title})},`${a.href}-${l}`))})}),e.jsx(c,{className:"my-6 md:hidden"}),e.jsx("div",{className:"flex-1 md:max-w-2xl",children:e.jsx("section",{className:"max-w-xl space-y-12",children:t})})]})]})}export{h as H,u as S};
