import{j as e,Q as j,t as a}from"./app-J5EqS6dS.js";import{C as l,a as r,b as m,c as d,d as u}from"./card-9XCADs-4.js";import{B as n}from"./badge-BucYuCBs.js";import{B as i,S as c}from"./smartphone-GGiwNneF.js";import{A as f}from"./app-layout-ox1kAwY6.js";import{A as p}from"./arrow-left-D4U9AVF9.js";import{E as x}from"./external-link-A4n9PP4e.js";import{S as N}from"./square-pen-Bepbg6wc.js";import{B as g}from"./building-Dgyml3QN.js";import{M as v}from"./map-pin-BdPUntxP.js";import{G as w}from"./globe-zfFlVOSX.js";import{C as h}from"./calendar-B-u_QN2Q.js";/* empty css            */import"./ImpersonationBanner-CYn5eDk6.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./crown-UDSxMtlm.js";import"./shield-D9nQfigG.js";import"./user-DCnDRzMf.js";import"./search-DBK6jUoc.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./eye-D-fsmYB2.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./triangle-alert-BW76NKO9.js";function Y({brand:s}){var o;return e.jsxs(f,{children:[e.jsx(j,{title:`${s.name} - Brands - Admin`}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(a,{href:"/admin/brands",children:e.jsxs(i,{variant:"outline",size:"sm",children:[e.jsx(p,{className:"w-4 h-4 mr-2"}),"Back to Brands"]})}),e.jsxs("div",{className:"flex items-center gap-3",children:[s.logo_url&&e.jsx("img",{src:s.logo_url,alt:s.name,className:"w-8 h-8 object-contain"}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:s.name}),e.jsx("p",{className:"text-muted-foreground",children:"Brand details and associated models"})]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(a,{href:route("brands.show",s.slug||s.id),children:e.jsxs(i,{variant:"outline",children:[e.jsx(x,{className:"h-4 w-4 mr-2"}),"View Public Page"]})}),e.jsx(a,{href:`/admin/brands/${s.id}/edit`,children:e.jsxs(i,{children:[e.jsx(N,{className:"h-4 w-4 mr-2"}),"Edit Brand"]})})]})]}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[e.jsxs(l,{children:[e.jsx(r,{children:e.jsxs(m,{className:"flex items-center gap-2",children:[e.jsx(g,{className:"h-5 w-5"}),"Brand Information"]})}),e.jsxs(d,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Brand Name"}),e.jsx("p",{className:"text-lg font-medium",children:s.name})]}),s.logo_url&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Logo"}),e.jsx("div",{className:"mt-2",children:e.jsx("img",{src:s.logo_url,alt:s.name,className:"w-16 h-16 object-contain border rounded-lg p-2"})})]}),s.country&&e.jsxs("div",{children:[e.jsxs("label",{className:"text-sm font-medium text-muted-foreground flex items-center gap-1",children:[e.jsx(v,{className:"h-3 w-3"}),"Country"]}),e.jsx("p",{className:"text-sm",children:s.country})]}),s.website&&e.jsxs("div",{children:[e.jsxs("label",{className:"text-sm font-medium text-muted-foreground flex items-center gap-1",children:[e.jsx(w,{className:"h-3 w-3"}),"Website"]}),e.jsxs("a",{href:s.website,target:"_blank",rel:"noopener noreferrer",className:"text-sm text-blue-600 hover:underline flex items-center gap-1",children:[s.website," ",e.jsx(x,{className:"h-3 w-3"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Status"}),e.jsx("div",{className:"mt-1",children:e.jsx(n,{variant:s.is_active?"default":"secondary",children:s.is_active?"Active":"Inactive"})})]})]})]}),e.jsxs(l,{children:[e.jsx(r,{children:e.jsx(m,{children:"Statistics"})}),e.jsxs(d,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(c,{className:"h-5 w-5 text-muted-foreground"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Models"}),e.jsx("p",{className:"text-2xl font-bold",children:((o=s.models)==null?void 0:o.length)||0})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(h,{className:"h-5 w-5 text-muted-foreground"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Created"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:new Date(s.created_at).toLocaleDateString()})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(h,{className:"h-5 w-5 text-muted-foreground"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Last Updated"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:new Date(s.updated_at).toLocaleDateString()})]})]})]})]})]}),s.models&&s.models.length>0&&e.jsxs(l,{children:[e.jsxs(r,{children:[e.jsxs(m,{className:"flex items-center gap-2",children:[e.jsx(c,{className:"h-5 w-5"}),"Associated Models"]}),e.jsxs(u,{children:["Mobile models from this brand (",s.models.length," models)"]})]}),e.jsx(d,{children:e.jsx("div",{className:"space-y-4",children:s.models.map(t=>e.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h3",{className:"font-medium",children:t.name}),t.release_year&&e.jsx(n,{variant:"outline",className:"text-xs",children:t.release_year})]}),t.model_number&&e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Model #: ",t.model_number]}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:["Created: ",new Date(t.created_at).toLocaleDateString()]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(n,{variant:t.is_active?"default":"secondary",children:t.is_active?"Active":"Inactive"}),e.jsx(a,{href:`/admin/models/${t.id}`,children:e.jsx(i,{variant:"outline",size:"sm",children:"View Model"})})]})]},t.id))})})]}),(!s.models||s.models.length===0)&&e.jsxs(l,{children:[e.jsx(r,{children:e.jsxs(m,{className:"flex items-center gap-2",children:[e.jsx(c,{className:"h-5 w-5"}),"Associated Models"]})}),e.jsx(d,{children:e.jsxs("div",{className:"text-center py-8",children:[e.jsx(c,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No models found"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:"This brand doesn't have any models yet."}),e.jsx(a,{href:"/admin/models/create",children:e.jsx(i,{variant:"outline",children:"Add Model"})})]})})]})]})})]})}export{Y as default};
