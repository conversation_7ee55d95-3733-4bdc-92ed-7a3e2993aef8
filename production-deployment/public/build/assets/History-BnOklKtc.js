import{r as k,j as e,Q as v,t as o}from"./app-J5EqS6dS.js";import{B as d}from"./smartphone-GGiwNneF.js";import{C as s,c as r,a as h,b as g,d as p}from"./card-9XCADs-4.js";import{B as n}from"./badge-BucYuCBs.js";import{A as C,D as P,C as _}from"./app-layout-ox1kAwY6.js";import{F as j}from"./ImpersonationBanner-CYn5eDk6.js";import{C as u}from"./circle-check-big-DOFoatRy.js";import{C as y}from"./clock-Brl7_5s7.js";import{F as D}from"./filter-DKJvAZFg.js";import{D as S}from"./download-fvx_BKiV.js";import{C as T}from"./calendar-B-u_QN2Q.js";import{E as B}from"./external-link-A4n9PP4e.js";import{C as F}from"./circle-alert-C6UwDlxH.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./users-RYmOyic9.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./search-DBK6jUoc.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./eye-D-fsmYB2.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";function me({paymentHistory:c,stats:a,user:E}){const[l,f]=k.useState("all"),b=t=>{switch(t){case"completed":case"processed":return e.jsx(u,{className:"w-4 h-4 text-green-500"});case"pending":return e.jsx(y,{className:"w-4 h-4 text-yellow-500"});case"failed":case"rejected":return e.jsx(_,{className:"w-4 h-4 text-red-500"});default:return e.jsx(F,{className:"w-4 h-4 text-gray-500"})}},N=t=>{switch(t){case"completed":case"processed":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"pending":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"failed":case"rejected":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},x=t=>{switch(t){case"Paddle":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"ShurjoPay":return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";case"Coinbase Commerce":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"Offline Payment":return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},w=t=>new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),i=c.filter(t=>l==="all"?!0:t.status===l);return e.jsxs(C,{children:[e.jsx(v,{title:"Payment History"}),e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Payment History"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 mt-2",children:"View all your payment transactions and subscription history"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsx(s,{children:e.jsx(r,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Total Transactions"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:a.total_transactions})]}),e.jsx(j,{className:"w-8 h-8 text-blue-500"})]})})}),e.jsx(s,{children:e.jsx(r,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Total Paid"}),e.jsxs("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:["$",a.total_paid.toFixed(2)]})]}),e.jsx(P,{className:"w-8 h-8 text-green-500"})]})})}),e.jsx(s,{children:e.jsx(r,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Completed"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:a.completed_transactions})]}),e.jsx(u,{className:"w-8 h-8 text-green-500"})]})})}),e.jsx(s,{children:e.jsx(r,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Pending"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:a.pending_transactions})]}),e.jsx(y,{className:"w-8 h-8 text-yellow-500"})]})})})]}),e.jsxs(s,{className:"mb-8",children:[e.jsxs(h,{children:[e.jsx(g,{children:"Payment Methods"}),e.jsx(p,{children:"Breakdown by payment gateway"})]}),e.jsx(r,{children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:Object.entries(a.by_gateway).map(([t,m])=>e.jsxs("div",{className:"p-4 border rounded-lg",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"font-medium capitalize",children:t.replace("_"," ")}),e.jsx(n,{className:x(t.replace("_"," ")),children:m.count})]}),e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["$",m.total_paid.toFixed(2)," total"]})]},t))})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(D,{className:"w-4 h-4 text-gray-500"}),e.jsxs("select",{value:l,onChange:t=>f(t.target.value),className:"border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-white",children:[e.jsx("option",{value:"all",children:"All Transactions"}),e.jsx("option",{value:"completed",children:"Completed"}),e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"failed",children:"Failed"}),e.jsx("option",{value:"processed",children:"Processed"}),e.jsx("option",{value:"rejected",children:"Rejected"})]})]}),e.jsxs(d,{variant:"outline",size:"sm",children:[e.jsx(S,{className:"w-4 h-4 mr-2"}),"Export"]})]}),e.jsxs(s,{children:[e.jsxs(h,{children:[e.jsx(g,{children:"Transaction History"}),e.jsxs(p,{children:[i.length," of ",c.length," transactions"]})]}),e.jsx(r,{children:i.length>0?e.jsx("div",{className:"space-y-4",children:i.map(t=>e.jsx("div",{className:"p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-start space-x-3 flex-1",children:[b(t.status),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t.formatted_amount}),e.jsx(n,{className:N(t.status),children:t.status}),e.jsx(n,{className:x(t.gateway),children:t.gateway})]}),e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:["Transaction ID: ",t.transaction_id]}),e.jsxs("div",{className:"flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400",children:[e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(T,{className:"w-3 h-3"}),w(t.created_at)]}),t.subscription&&e.jsxs("span",{children:["Subscription: ",t.subscription.plan_name]})]})]})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(d,{asChild:!0,variant:"outline",size:"sm",children:e.jsxs(o,{href:route("payment-history.show",[t.type,t.id]),children:[e.jsx(B,{className:"w-3 h-3 mr-1"}),"View"]})})})]})},`${t.type}-${t.id}`))}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(j,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No transactions found"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:l==="all"?"You haven't made any payments yet.":`No ${l} transactions found.`}),e.jsx(d,{asChild:!0,children:e.jsx(o,{href:route("subscription.plans"),children:"View Plans"})})]})})]})]})]})}export{me as default};
