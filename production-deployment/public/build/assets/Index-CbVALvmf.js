import{j as e,Q as w,t as d,S as o}from"./app-J5EqS6dS.js";import{C as r,c as l,a as h,b as p,d as C}from"./card-9XCADs-4.js";import{B as i}from"./smartphone-GGiwNneF.js";import{B as S}from"./badge-BucYuCBs.js";import{S as P,a as R,b as B,c as F,d as A}from"./select-CIhY0l9J.js";import{A as D,C as f}from"./app-layout-ox1kAwY6.js";import{P as j,F as u}from"./ImpersonationBanner-CYn5eDk6.js";import{C as N}from"./clock-Brl7_5s7.js";import{C as n}from"./circle-check-big-DOFoatRy.js";import{C as T}from"./calendar-B-u_QN2Q.js";import{F as _}from"./file-text-Dx6bYLtE.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./search-DBK6jUoc.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./eye-D-fsmYB2.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";const $=[{title:"Payment Requests",href:"/payment-requests"}],L=t=>{switch(t){case"pending":return e.jsx(N,{className:"w-5 h-5 text-yellow-500"});case"approved":return e.jsx(n,{className:"w-5 h-5 text-green-500"});case"rejected":return e.jsx(f,{className:"w-5 h-5 text-red-500"});case"processed":return e.jsx(n,{className:"w-5 h-5 text-blue-500"});default:return e.jsx(_,{className:"w-5 h-5 text-gray-500"})}},M=t=>{switch(t){case"pending":return"bg-yellow-100 text-yellow-800";case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";case"processed":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},I=t=>{switch(t){case"bank_transfer":return"Bank Transfer";case"mobile_money":return"Mobile Money";case"cash_deposit":return"Cash Deposit";case"other":return"Other";default:return t}};function je({payment_requests:t,stats:c,filters:g,statuses:y}){const b=(s,a)=>{const m=new URLSearchParams(window.location.search);a==="all"||a===""?m.delete(s):m.set(s,a),o.get(route("payment-requests.index"),Object.fromEntries(m))},x=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),v=(s,a)=>`${a} ${parseFloat(s).toFixed(2)}`;return e.jsxs(D,{breadcrumbs:$,children:[e.jsx(w,{title:"Payment Requests"}),e.jsxs("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Payment Requests"}),e.jsx("p",{className:"text-gray-600",children:"Submit and track your offline payment requests"})]}),e.jsx(d,{href:route("payment-requests.create"),children:e.jsxs(i,{children:[e.jsx(j,{className:"w-4 h-4 mr-2"}),"New Payment Request"]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsx(r,{children:e.jsx(l,{className:"p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(u,{className:"w-8 h-8 text-blue-500"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Requests"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:c.total})]})]})})}),e.jsx(r,{children:e.jsx(l,{className:"p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(N,{className:"w-8 h-8 text-yellow-500"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Pending"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:c.pending})]})]})})}),e.jsx(r,{children:e.jsx(l,{className:"p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(n,{className:"w-8 h-8 text-green-500"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Approved"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:c.approved})]})]})})}),e.jsx(r,{children:e.jsx(l,{className:"p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(f,{className:"w-8 h-8 text-red-500"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Rejected"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:c.rejected})]})]})})})]}),e.jsxs(r,{children:[e.jsx(h,{children:e.jsx(p,{children:"Filters"})}),e.jsx(l,{children:e.jsx("div",{className:"flex gap-4",children:e.jsxs("div",{className:"flex-1",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),e.jsxs(P,{value:g.status,onValueChange:s=>b("status",s),children:[e.jsx(R,{children:e.jsx(B,{placeholder:"Select status"})}),e.jsx(F,{children:Object.entries(y).map(([s,a])=>e.jsx(A,{value:s,children:a},s))})]})]})})})]}),e.jsxs(r,{children:[e.jsxs(h,{children:[e.jsx(p,{children:"Your Payment Requests"}),e.jsx(C,{children:t.meta.total>0?`Showing ${t.meta.from} to ${t.meta.to} of ${t.meta.total} requests`:"No payment requests found"})]}),e.jsx(l,{children:t.data.length>0?e.jsx("div",{className:"space-y-4",children:t.data.map(s=>e.jsx("div",{className:"p-4 border rounded-lg transition-colors hover:bg-gray-50",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-start space-x-3 flex-1",children:[L(s.status),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsxs("h3",{className:"text-sm font-medium text-gray-900",children:[v(s.amount,s.currency)," - ",s.subscription_plan]}),e.jsx(S,{className:`text-xs ${M(s.status)}`,children:s.status})]}),e.jsxs("p",{className:"text-sm text-gray-600 mb-2",children:["Payment Method: ",I(s.payment_method)]}),e.jsxs("div",{className:"flex items-center text-xs text-gray-500 space-x-4",children:[e.jsxs("span",{className:"flex items-center",children:[e.jsx(T,{className:"w-3 h-3 mr-1"}),"Requested: ",x(s.requested_at)]}),s.approved_at&&e.jsxs("span",{className:"flex items-center",children:[e.jsx(n,{className:"w-3 h-3 mr-1"}),s.status==="approved"?"Approved":"Processed",": ",x(s.approved_at)]}),s.approvedBy&&e.jsxs("span",{children:["By: ",s.approvedBy.name]})]}),s.notes&&e.jsxs("p",{className:"text-xs text-gray-600 mt-2",children:["Notes: ",s.notes]}),s.admin_notes&&e.jsxs("p",{className:"text-xs text-blue-600 mt-2",children:["Admin Notes: ",s.admin_notes]})]})]}),e.jsx(d,{href:route("payment-requests.show",s.id),children:e.jsx(i,{variant:"outline",size:"sm",children:"View Details"})})]})},s.id))}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx(u,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No payment requests"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"You haven't submitted any payment requests yet."}),e.jsx(d,{href:route("payment-requests.create"),children:e.jsxs(i,{children:[e.jsx(j,{className:"w-4 h-4 mr-2"}),"Submit Your First Request"]})})]})})]}),t.meta.last_page>1&&e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"flex space-x-1",children:t.links.map((s,a)=>e.jsx(i,{variant:s.active?"default":"outline",size:"sm",disabled:!s.url,onClick:()=>s.url&&o.get(s.url),dangerouslySetInnerHTML:{__html:s.label}},a))})})]})]})}export{je as default};
