import{j as s,Q as i,t}from"./app-J5EqS6dS.js";import{B as r}from"./smartphone-GGiwNneF.js";import{C as l,a as c,b as m,d as n,c as o}from"./card-9XCADs-4.js";import{A as x}from"./app-layout-ox1kAwY6.js";import{C as e}from"./circle-check-big-DOFoatRy.js";import{F as d}from"./ImpersonationBanner-CYn5eDk6.js";import{A as p}from"./arrow-right-CCfGNWZ9.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./users-RYmOyic9.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./search-DBK6jUoc.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./badge-BucYuCBs.js";import"./eye-D-fsmYB2.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";function L({transaction_id:a}){return s.jsxs(x,{children:[s.jsx(i,{title:"Payment Successful"}),s.jsx("div",{className:"py-12",children:s.jsxs("div",{className:"max-w-2xl mx-auto sm:px-6 lg:px-8",children:[s.jsxs("div",{className:"text-center mb-8",children:[s.jsx("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4",children:s.jsx(e,{className:"w-8 h-8 text-green-600"})}),s.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Payment Successful!"}),s.jsx("p",{className:"text-gray-600",children:"Your subscription has been activated successfully"})]}),s.jsxs(l,{className:"mb-8",children:[s.jsxs(c,{className:"text-center",children:[s.jsxs(m,{className:"flex items-center justify-center gap-2",children:[s.jsx(d,{className:"w-5 h-5"}),"Subscription Activated"]}),s.jsx(n,{children:"You now have access to all premium features"})]}),s.jsxs(o,{className:"space-y-4",children:[a&&s.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"font-medium",children:"Transaction ID:"}),s.jsx("span",{className:"ml-2 font-mono text-gray-600",children:a})]})}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("h3",{className:"font-semibold",children:"What's included in your subscription:"}),s.jsxs("ul",{className:"space-y-2",children:[s.jsxs("li",{className:"flex items-center gap-2",children:[s.jsx(e,{className:"w-4 h-4 text-green-600"}),s.jsx("span",{children:"Unlimited searches"})]}),s.jsxs("li",{className:"flex items-center gap-2",children:[s.jsx(e,{className:"w-4 h-4 text-green-600"}),s.jsx("span",{children:"High-resolution images"})]}),s.jsxs("li",{className:"flex items-center gap-2",children:[s.jsx(e,{className:"w-4 h-4 text-green-600"}),s.jsx("span",{children:"Detailed specifications"})]}),s.jsxs("li",{className:"flex items-center gap-2",children:[s.jsx(e,{className:"w-4 h-4 text-green-600"}),s.jsx("span",{children:"Priority support"})]}),s.jsxs("li",{className:"flex items-center gap-2",children:[s.jsx(e,{className:"w-4 h-4 text-green-600"}),s.jsx("span",{children:"Advanced filters"})]})]})]}),s.jsxs("div",{className:"pt-4 space-y-3",children:[s.jsx(t,{href:route("dashboard"),children:s.jsxs(r,{className:"w-full",size:"lg",children:["Start Exploring",s.jsx(p,{className:"w-4 h-4 ml-2"})]})}),s.jsx(t,{href:route("subscription.dashboard"),children:s.jsx(r,{variant:"outline",className:"w-full",children:"View Subscription Details"})})]})]})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"You will receive a confirmation email shortly with your receipt and subscription details."}),s.jsxs("p",{className:"text-sm text-gray-500",children:["Need help? ",s.jsx("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"Contact Support"})]})]})]})})]})}export{L as default};
