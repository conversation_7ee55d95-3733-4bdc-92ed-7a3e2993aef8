import{r as i,j as e,Q as n}from"./app-J5EqS6dS.js";import{B as d,S as h}from"./smartphone-GGiwNneF.js";import{C as s,c as t,a as j,b,d as g}from"./card-9XCADs-4.js";import{A as u}from"./app-layout-ox1kAwY6.js";import{U as y}from"./unified-search-interface-CjLSucUK.js";import{T as N}from"./trending-up-BtixJGWw.js";import{P as f}from"./package-CoyvngX8.js";import{T as S}from"./tag-C9-9psxB.js";/* empty css            */import"./ImpersonationBanner-CYn5eDk6.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./badge-BucYuCBs.js";import"./crown-UDSxMtlm.js";import"./shield-D9nQfigG.js";import"./user-DCnDRzMf.js";import"./search-DBK6jUoc.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./eye-D-fsmYB2.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./input-Bo8dOn9p.js";import"./select-CIhY0l9J.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./category-utils-DblfPn34.js";import"./map-pin-BdPUntxP.js";import"./hard-drive-BTn_ba7c.js";import"./circle-ButWjt_D.js";import"./building-Dgyml3QN.js";function le({filters:l}){const[m,r]=i.useState(""),[o,c]=i.useState(!1),p=[{label:"iPhone Display",type:"part"},{label:"Samsung Battery",type:"part"},{label:"Camera Module",type:"category"},{label:"Charging IC",type:"category"},{label:"OnePlus",type:"brand"},{label:"Xiaomi",type:"brand"}];return e.jsxs(u,{children:[e.jsx(n,{title:"Search Parts"}),e.jsx("div",{className:"py-12",children:e.jsxs("div",{className:"max-w-6xl mx-auto sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Find Mobile Parts"}),e.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Search through our comprehensive database of mobile device parts and components"})]}),e.jsx(s,{className:"mb-8",children:e.jsx(t,{className:"p-6",children:e.jsx(y,{searchQuery:m,setSearchQuery:r,isAuthenticated:!0,isLoading:o,setIsLoading:c,showFilters:!0,showSuggestions:!0,size:"lg",filters:l})})}),e.jsxs(s,{className:"mb-8",children:[e.jsxs(j,{children:[e.jsxs(b,{className:"flex items-center gap-2",children:[e.jsx(N,{className:"w-5 h-5"}),"Popular Searches"]}),e.jsx(g,{children:"Quick access to commonly searched items"})]}),e.jsx(t,{children:e.jsx("div",{className:"flex flex-wrap gap-2",children:p.map((a,x)=>e.jsx(d,{variant:"outline",size:"sm",onClick:()=>{r(a.label),setSearchType(a.type)},className:"text-sm",children:a.label},x))})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsx(s,{children:e.jsxs(t,{className:"p-6 text-center",children:[e.jsx(f,{className:"w-12 h-12 mx-auto mb-4 text-green-600"}),e.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"10,000+"}),e.jsx("p",{className:"text-gray-600",children:"Parts Available"})]})}),e.jsx(s,{children:e.jsxs(t,{className:"p-6 text-center",children:[e.jsx(h,{className:"w-12 h-12 mx-auto mb-4 text-blue-600"}),e.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"500+"}),e.jsx("p",{className:"text-gray-600",children:"Mobile Models"})]})}),e.jsx(s,{children:e.jsxs(t,{className:"p-6 text-center",children:[e.jsx(S,{className:"w-12 h-12 mx-auto mb-4 text-purple-600"}),e.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"50+"}),e.jsx("p",{className:"text-gray-600",children:"Categories"})]})})]})]})})]})}export{le as default};
