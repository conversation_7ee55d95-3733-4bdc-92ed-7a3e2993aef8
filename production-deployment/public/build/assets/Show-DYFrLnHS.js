import{r as u,j as e,Q as ae,t as te,S as x}from"./app-J5EqS6dS.js";import{C as r,a as i,b as l,c,d as g}from"./card-9XCADs-4.js";import{B as S}from"./badge-BucYuCBs.js";import{B as t}from"./smartphone-GGiwNneF.js";import{I as _}from"./input-Bo8dOn9p.js";import{L as d}from"./label-BlOrdc-X.js";import{S as k,a as L,b as P,c as A,d as o}from"./select-CIhY0l9J.js";import{T as ne,a as re,b,c as y}from"./tabs-DZAL-HvD.js";import{X as I,A as T,F as B,D as ie,a3 as le,K as ce,b as de,c as oe,d as me,e as xe,f as pe,H as he}from"./ImpersonationBanner-CYn5eDk6.js";import{C as q}from"./checkbox-CsTWa9ph.js";import{A as je,D as ue}from"./app-layout-ox1kAwY6.js";import{I as ge}from"./ImpersonationSecurityCheck-Bzbl0nMh.js";import{A as fe}from"./arrow-left-D4U9AVF9.js";import{S as ve}from"./square-pen-Bepbg6wc.js";import{S as Ne}from"./save-DfhL0V-C.js";import{U as we}from"./user-DCnDRzMf.js";import{S as H}from"./search-DBK6jUoc.js";import{M as _e}from"./mail-CDon-vZy.js";import{C as be}from"./calendar-B-u_QN2Q.js";import{C as U}from"./circle-check-big-DOFoatRy.js";import{B as M}from"./ban-Dctu8q_b.js";import{E as ye}from"./eye-off-BGSyeByl.js";import{E as R}from"./eye-D-fsmYB2.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./index-BzZWUWqx.js";import"./crown-UDSxMtlm.js";import"./shield-D9nQfigG.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./textarea-BDEiXlPH.js";import"./clock-Brl7_5s7.js";const Se=({status:s})=>{const p={active:"bg-green-100 text-green-800",pending:"bg-yellow-100 text-yellow-800",suspended:"bg-red-100 text-red-800",banned:"bg-gray-100 text-gray-800"};return e.jsx(S,{className:p[s],children:s.charAt(0).toUpperCase()+s.slice(1)})},Ce=({status:s})=>{const p={approved:"bg-green-100 text-green-800",pending:"bg-yellow-100 text-yellow-800",rejected:"bg-red-100 text-red-800"};return e.jsx(S,{className:p[s],children:s.charAt(0).toUpperCase()+s.slice(1)})};function js({user:s}){const[p,C]=u.useState(!1),[V,f]=u.useState(!1),[z,D]=u.useState(!1),[v,Y]=u.useState(!1),[n,h]=u.useState({name:s.name,email:s.email,subscription_plan:s.subscription_plan,status:s.status,approval_status:s.approval_status}),[j,F]=u.useState({password:"",password_confirmation:"",generate_password:!0,send_notification:!0}),O=()=>{C(!0)},Q=()=>{x.put(`/admin/users/${s.id}`,n,{onSuccess:()=>{C(!1)}})},K=()=>{h({name:s.name,email:s.email,subscription_plan:s.subscription_plan,status:s.status,approval_status:s.approval_status}),C(!1)},G=()=>{x.post(`/admin/users/${s.id}/approve`,{},{preserveScroll:!0})},J=()=>{const a=prompt("Please provide a reason for suspension:"),m=prompt("Suspension expires at (YYYY-MM-DD HH:MM, leave empty for permanent):");a&&x.post(`/admin/users/${s.id}/suspend`,{reason:a,expires_at:m||null},{preserveScroll:!0})},X=()=>{x.post(`/admin/users/${s.id}/unsuspend`,{},{preserveScroll:!0})},W=()=>{f(!0)},Z=(a,m)=>{x.post(`/admin/impersonate/${s.id}`,{reason:a,duration:m},{onSuccess:()=>{f(!1)},onError:()=>{f(!1)}})},ee=()=>{confirm(`Are you sure you want to delete user "${s.name}"? This action cannot be undone.`)&&x.delete(`/admin/users/${s.id}`)},se=()=>{x.post(`/admin/users/${s.id}/change-password`,j,{onSuccess:()=>{D(!1),F({password:"",password_confirmation:"",generate_password:!0,send_notification:!0})}})},N=(a,m)=>{F(w=>({...w,[a]:m}))};return e.jsxs(je,{children:[e.jsx(ae,{title:`User: ${s.name}`}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(te,{href:"/admin/users",children:e.jsxs(t,{variant:"outline",size:"sm",children:[e.jsx(fe,{className:"h-4 w-4 mr-2"}),"Back to Users"]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:s.name}),e.jsxs("p",{className:"text-muted-foreground mt-1",children:["User ID: ",s.id," • ",s.email]})]})]}),e.jsx("div",{className:"flex items-center gap-2",children:p?e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(t,{onClick:Q,children:[e.jsx(Ne,{className:"h-4 w-4 mr-2"}),"Save"]}),e.jsxs(t,{onClick:K,variant:"outline",children:[e.jsx(I,{className:"h-4 w-4 mr-2"}),"Cancel"]})]}):e.jsxs(t,{onClick:O,variant:"outline",children:[e.jsx(ve,{className:"h-4 w-4 mr-2"}),"Edit User"]})})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[e.jsxs(r,{children:[e.jsxs(i,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(l,{className:"text-sm font-medium",children:"Account Status"}),e.jsx(we,{className:"h-4 w-4 text-blue-600"})]}),e.jsx(c,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Se,{status:s.status}),e.jsx(Ce,{status:s.approval_status})]})})]}),e.jsxs(r,{children:[e.jsxs(i,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(l,{className:"text-sm font-medium",children:"Searches"}),e.jsx(H,{className:"h-4 w-4 text-green-600"})]}),e.jsxs(c,{children:[e.jsx("div",{className:"text-2xl font-bold text-foreground",children:s.searches_count}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:["Current: ",s.search_count]})]})]}),e.jsxs(r,{children:[e.jsxs(i,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(l,{className:"text-sm font-medium",children:"Logins"}),e.jsx(T,{className:"h-4 w-4 text-purple-600"})]}),e.jsxs(c,{children:[e.jsx("div",{className:"text-2xl font-bold text-foreground",children:s.login_count}),e.jsx("p",{className:"text-xs text-muted-foreground",children:s.last_login_at?`Last: ${new Date(s.last_login_at).toLocaleDateString()}`:"Never"})]})]}),e.jsxs(r,{children:[e.jsxs(i,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(l,{className:"text-sm font-medium",children:"Subscription"}),e.jsx(B,{className:"h-4 w-4 text-orange-600"})]}),e.jsxs(c,{children:[e.jsx("div",{className:"text-lg font-bold text-foreground capitalize",children:s.subscription_plan}),s.active_subscription&&e.jsxs("p",{className:"text-xs text-muted-foreground",children:["Until: ",new Date(s.active_subscription.current_period_end).toLocaleDateString()]})]})]})]}),e.jsxs("div",{className:"grid gap-6 lg:grid-cols-3",children:[e.jsxs("div",{className:"lg:col-span-1",children:[e.jsxs(r,{children:[e.jsx(i,{children:e.jsx(l,{children:"User Information"})}),e.jsx(c,{className:"space-y-4",children:p?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(d,{htmlFor:"name",children:"Name"}),e.jsx(_,{id:"name",value:n.name,onChange:a=>h({...n,name:a.target.value})})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"email",children:"Email"}),e.jsx(_,{id:"email",type:"email",value:n.email,onChange:a=>h({...n,email:a.target.value})})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"subscription_plan",children:"Subscription Plan"}),e.jsxs(k,{value:n.subscription_plan,onValueChange:a=>h({...n,subscription_plan:a}),children:[e.jsx(L,{children:e.jsx(P,{})}),e.jsxs(A,{children:[e.jsx(o,{value:"free",children:"Free"}),e.jsx(o,{value:"premium",children:"Premium"})]})]})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"status",children:"Status"}),e.jsxs(k,{value:n.status,onValueChange:a=>h({...n,status:a}),children:[e.jsx(L,{children:e.jsx(P,{})}),e.jsxs(A,{children:[e.jsx(o,{value:"active",children:"Active"}),e.jsx(o,{value:"pending",children:"Pending"}),e.jsx(o,{value:"suspended",children:"Suspended"}),e.jsx(o,{value:"banned",children:"Banned"})]})]})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"approval_status",children:"Approval Status"}),e.jsxs(k,{value:n.approval_status,onValueChange:a=>h({...n,approval_status:a}),children:[e.jsx(L,{children:e.jsx(P,{})}),e.jsxs(A,{children:[e.jsx(o,{value:"approved",children:"Approved"}),e.jsx(o,{value:"pending",children:"Pending"}),e.jsx(o,{value:"rejected",children:"Rejected"})]})]})]})]}):e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(_e,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"text-sm",children:s.email})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(be,{className:"h-4 w-4 text-muted-foreground"}),e.jsxs("span",{className:"text-sm",children:["Joined ",new Date(s.created_at).toLocaleDateString()]})]}),s.approved_at&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(U,{className:"h-4 w-4 text-green-600"}),e.jsxs("span",{className:"text-sm",children:["Approved ",new Date(s.approved_at).toLocaleDateString(),s.approved_by&&` by ${s.approved_by.name}`]})]}),s.suspended_at&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(M,{className:"h-4 w-4 text-red-600"}),e.jsxs("span",{className:"text-sm",children:["Suspended ",new Date(s.suspended_at).toLocaleDateString(),s.suspended_by&&` by ${s.suspended_by.name}`]})]}),s.suspension_reason&&e.jsxs("div",{className:"ml-6 text-sm text-muted-foreground",children:["Reason: ",s.suspension_reason]}),s.suspension_expires_at&&e.jsxs("div",{className:"ml-6 text-sm text-muted-foreground",children:["Expires: ",new Date(s.suspension_expires_at).toLocaleDateString()]})]})]})})]}),e.jsxs(r,{className:"mt-6",children:[e.jsx(i,{children:e.jsx(l,{children:"Quick Actions"})}),e.jsxs(c,{className:"space-y-2",children:[s.approval_status==="pending"&&e.jsxs(t,{onClick:G,className:"w-full",variant:"default",children:[e.jsx(U,{className:"h-4 w-4 mr-2"}),"Approve User"]}),s.status==="active"&&e.jsxs(t,{onClick:J,className:"w-full",variant:"destructive",children:[e.jsx(M,{className:"h-4 w-4 mr-2"}),"Suspend User"]}),s.status==="suspended"&&e.jsxs(t,{onClick:X,className:"w-full",variant:"default",children:[e.jsx(U,{className:"h-4 w-4 mr-2"}),"Unsuspend User"]}),e.jsxs(ie,{open:z,onOpenChange:D,children:[e.jsx(le,{asChild:!0,children:e.jsxs(t,{className:"w-full",variant:"outline",children:[e.jsx(ce,{className:"h-4 w-4 mr-2"}),"Change Password"]})}),e.jsxs(de,{className:"sm:max-w-md",children:[e.jsxs(oe,{children:[e.jsx(me,{children:"Change Password"}),e.jsxs(xe,{children:["Change the password for ",s.name]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(q,{id:"generate_password",checked:j.generate_password,onCheckedChange:a=>N("generate_password",a)}),e.jsx(d,{htmlFor:"generate_password",children:"Generate random password"})]}),!j.generate_password&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(d,{htmlFor:"password",children:"New Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(_,{id:"password",type:v?"text":"password",value:j.password,onChange:a=>N("password",a.target.value),placeholder:"Enter new password",className:"pr-10"}),e.jsx(t,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>Y(!v),children:v?e.jsx(ye,{className:"h-4 w-4"}):e.jsx(R,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"password_confirmation",children:"Confirm Password"}),e.jsx(_,{id:"password_confirmation",type:v?"text":"password",value:j.password_confirmation,onChange:a=>N("password_confirmation",a.target.value),placeholder:"Confirm new password"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(q,{id:"send_notification",checked:j.send_notification,onCheckedChange:a=>N("send_notification",a)}),e.jsx(d,{htmlFor:"send_notification",children:"Send email notification to user"})]})]}),e.jsxs(pe,{children:[e.jsx(t,{type:"button",variant:"outline",onClick:()=>D(!1),children:"Cancel"}),e.jsx(t,{onClick:se,children:"Change Password"})]})]})]}),e.jsxs(t,{onClick:W,className:"w-full",variant:"outline",children:[e.jsx(R,{className:"h-4 w-4 mr-2"}),"Login as User"]}),e.jsxs(t,{onClick:ee,className:"w-full",variant:"destructive",children:[e.jsx(I,{className:"h-4 w-4 mr-2"}),"Delete User"]})]})]})]}),e.jsx("div",{className:"lg:col-span-2",children:e.jsxs(ne,{defaultValue:"activity",className:"space-y-4",children:[e.jsxs(re,{className:"grid w-full grid-cols-4",children:[e.jsx(b,{value:"activity",children:"Activity"}),e.jsx(b,{value:"payments",children:"Payments"}),e.jsx(b,{value:"searches",children:"Searches"}),e.jsx(b,{value:"favorites",children:"Favorites"})]}),e.jsx(y,{value:"activity",children:e.jsxs(r,{children:[e.jsxs(i,{children:[e.jsx(l,{children:"Recent Activity"}),e.jsxs(g,{children:["Latest ",s.activity_logs.length," activities"]})]}),e.jsx(c,{children:e.jsx("div",{className:"space-y-4",children:s.activity_logs.length>0?s.activity_logs.map(a=>e.jsxs("div",{className:"flex items-start gap-3 p-3 border rounded-lg",children:[e.jsx(T,{className:"h-4 w-4 mt-1 text-blue-600"}),e.jsxs("div",{className:"flex-1 space-y-1",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm font-medium",children:a.activity_type.replace("_"," ").toUpperCase()}),e.jsx("span",{className:"text-xs text-muted-foreground",children:new Date(a.created_at).toLocaleString()})]}),e.jsx("p",{className:"text-sm text-muted-foreground",children:a.description}),a.performed_by&&e.jsxs("p",{className:"text-xs text-muted-foreground",children:["by ",a.performed_by.name]})]})]},a.id)):e.jsx("p",{className:"text-center text-muted-foreground py-8",children:"No activity logs found"})})})]})}),e.jsxs(y,{value:"payments",children:[e.jsxs(r,{children:[e.jsxs(i,{children:[e.jsx(l,{children:"Payment Requests"}),e.jsxs(g,{children:[s.payment_requests.length," payment requests"]})]}),e.jsx(c,{children:e.jsx("div",{className:"space-y-4",children:s.payment_requests.length>0?s.payment_requests.map(a=>e.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ue,{className:"h-4 w-4 text-green-600"}),e.jsxs("div",{children:[e.jsxs("p",{className:"text-sm font-medium",children:[a.currency," ",a.amount]}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:[a.payment_method," • ",new Date(a.requested_at).toLocaleDateString()]})]})]}),e.jsx(S,{className:a.status==="approved"?"bg-green-100 text-green-800":a.status==="pending"?"bg-yellow-100 text-yellow-800":a.status==="rejected"?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800",children:a.status})]},a.id)):e.jsx("p",{className:"text-center text-muted-foreground py-8",children:"No payment requests found"})})})]}),e.jsxs(r,{className:"mt-4",children:[e.jsxs(i,{children:[e.jsx(l,{children:"Subscription History"}),e.jsxs(g,{children:[s.subscriptions.length," subscriptions"]})]}),e.jsx(c,{children:e.jsx("div",{className:"space-y-4",children:s.subscriptions.length>0?s.subscriptions.map(a=>e.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(B,{className:"h-4 w-4 text-purple-600"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium capitalize",children:a.plan_name}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:["Started: ",new Date(a.created_at).toLocaleDateString(),a.current_period_end&&` • Ends: ${new Date(a.current_period_end).toLocaleDateString()}`]})]})]}),e.jsx(S,{className:a.status==="active"?"bg-green-100 text-green-800":a.status==="canceled"?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800",children:a.status})]},a.id)):e.jsx("p",{className:"text-center text-muted-foreground py-8",children:"No subscription history found"})})})]})]}),e.jsx(y,{value:"searches",children:e.jsxs(r,{children:[e.jsxs(i,{children:[e.jsx(l,{children:"Recent Searches"}),e.jsxs(g,{children:["Latest ",s.searches.length," searches"]})]}),e.jsx(c,{children:e.jsx("div",{className:"space-y-4",children:s.searches.length>0?s.searches.map(a=>e.jsx("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(H,{className:"h-4 w-4 text-blue-600"}),e.jsxs("div",{children:[e.jsxs("p",{className:"text-sm font-medium",children:['"',a.search_query,'"']}),e.jsx("p",{className:"text-xs text-muted-foreground",children:new Date(a.created_at).toLocaleString()})]})]})},a.id)):e.jsx("p",{className:"text-center text-muted-foreground py-8",children:"No searches found"})})})]})}),e.jsx(y,{value:"favorites",children:e.jsxs(r,{children:[e.jsxs(i,{children:[e.jsx(l,{children:"Favorite Parts"}),e.jsxs(g,{children:[s.favorites.length," favorite parts"]})]}),e.jsx(c,{children:e.jsx("div",{className:"space-y-4",children:s.favorites.length>0?s.favorites.map(a=>{var m,w,E,$;return e.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(he,{className:"h-4 w-4 text-red-600"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:((m=a.favoritable)==null?void 0:m.name)||"Unknown Item"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:(w=a.favoritable)!=null&&w.part_number?`Part #: ${a.favoritable.part_number}`:(E=a.favoritable)!=null&&E.model_number?`Model #: ${a.favoritable.model_number}`:`Type: ${(($=a.favoritable_type)==null?void 0:$.split("\\").pop())||"Unknown"}`})]})]}),e.jsx("span",{className:"text-xs text-muted-foreground",children:new Date(a.created_at).toLocaleDateString()})]},a.id)}):e.jsx("p",{className:"text-center text-muted-foreground py-8",children:"No favorites found"})})})]})})]})})]})]})}),e.jsx(ge,{user:s,isOpen:V,onClose:()=>f(!1),onConfirm:Z})]})}export{js as default};
