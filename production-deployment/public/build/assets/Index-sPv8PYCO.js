import{r as p,j as e,Q as Ce,t as x,S as v}from"./app-J5EqS6dS.js";import{C as k,c as A,a as Se,b as be,d as _e}from"./card-9XCADs-4.js";import{B as w}from"./badge-BucYuCBs.js";import{B as t,S as ae}from"./smartphone-GGiwNneF.js";import{I as ke}from"./input-Bo8dOn9p.js";import{L as P}from"./label-BlOrdc-X.js";import{S as L,a as M,b as R,c as U,d as N}from"./select-CIhY0l9J.js";import{C as B}from"./checkbox-CsTWa9ph.js";import{A as Ae,G as Be}from"./app-layout-ox1kAwY6.js";import{u as Ee}from"./use-delete-confirmation-CFAJok5Z.js";import{U as Ie,P as le,X as G,a as ze,t as f}from"./ImpersonationBanner-CYn5eDk6.js";import{F as De}from"./file-text-Dx6bYLtE.js";import{D as re}from"./download-fvx_BKiV.js";import{S as ie}from"./search-DBK6jUoc.js";import{F as Fe}from"./filter-DKJvAZFg.js";import{A as W,a as O,b as ce}from"./arrow-up-DSYswbzJ.js";import{T as Te}from"./table-gSl3ppmW.js";import{L as $e}from"./list-CNjrM85i.js";import{E as S}from"./external-link-A4n9PP4e.js";import{E as Q}from"./eye-D-fsmYB2.js";import{S as Y}from"./square-pen-Bepbg6wc.js";import{T as H}from"./trash-2-B3ZEh4hl.js";import{C as Ve}from"./chevron-left-C6ZNA5qQ.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";function bs({brands:a,filters:ne,queryParams:y}){const{showDeleteConfirmation:oe}=Ee(),[n,K]=p.useState(y.search||""),[o,X]=p.useState(y.country||"all"),[d,J]=p.useState(y.status||"all"),[i,E]=p.useState(y.sort_by||"name"),[m,Z]=p.useState(y.sort_order||"asc"),[I,de]=p.useState(!1),[c,me]=p.useState(y.view||"table"),[h,b]=p.useState([]),[he,z]=p.useState(!1),C=p.useRef(null),_=n||o!=="all"||d!=="all",D=s=>{const l={page:s,...n&&{search:n},...o!=="all"&&{country:o},...d!=="all"&&{status:d},...i!=="name"&&{sort_by:i},...m!=="asc"&&{sort_order:m},...c!=="table"&&{view:c}};v.get("/admin/brands",l,{preserveState:!0,preserveScroll:!0})},q=()=>{const s={...n&&{search:n},...o!=="all"&&{country:o},...d!=="all"&&{status:d},...i!=="name"&&{sort_by:i},...m!=="asc"&&{sort_order:m},...c!=="table"&&{view:c}};v.get("/admin/brands",s,{preserveState:!0,preserveScroll:!1})},ee=()=>{K(""),X("all"),J("all"),E("name"),Z("asc"),v.get("/admin/brands",{view:c!=="table"?c:void 0},{preserveState:!0,preserveScroll:!1})},F=s=>{const l=i===s&&m==="asc"?"desc":"asc";E(s),Z(l);const r={...n&&{search:n},...o!=="all"&&{country:o},...d!=="all"&&{status:d},sort_by:s,sort_order:l,...c!=="table"&&{view:c}};v.get("/admin/brands",r,{preserveState:!0,preserveScroll:!0})},T=s=>{me(s);const l={...n&&{search:n},...o!=="all"&&{country:o},...d!=="all"&&{status:d},...i!=="name"&&{sort_by:i},...m!=="asc"&&{sort_order:m},...s!=="table"&&{view:s}};v.get("/admin/brands",l,{preserveState:!0,preserveScroll:!0})},$=s=>{oe({title:`Delete "${s.name}"?`,description:"This action cannot be undone. All associated models will also be affected.",onConfirm:()=>{v.delete(`/admin/brands/${s.id}`,{onSuccess:()=>{f.success(`Brand "${s.name}" has been deleted successfully.`)},onError:l=>{const r=l.message||"Failed to delete brand. It may have associated models.";f.error(r)}})},onCancel:()=>{f.info("Delete cancelled")}})},xe=()=>{const s=new URLSearchParams;n&&s.append("search",n),o!=="all"&&s.append("country",o),d!=="all"&&s.append("status",d),i!=="name"&&s.append("sort_by",i),m!=="asc"&&s.append("sort_order",m),window.location.href=`/admin/brands/export?${s.toString()}`,f.success("Export started. Your download will begin shortly.")},ue=()=>{if(h.length===0){f.error("Please select brands to export");return}const s=new URLSearchParams;h.forEach(l=>s.append("ids[]",l.toString())),window.location.href=`/admin/brands/export?${s.toString()}`,f.success("Export started. Your download will begin shortly.")},pe=()=>{window.location.href="/admin/brands/template/download",f.success("Template download started.")},je=()=>{var s;(s=C.current)==null||s.click()},fe=s=>{var r;const l=(r=s.target.files)==null?void 0:r[0];if(l){z(!0);const g=new FormData;g.append("file",l),g.append("duplicate_action","skip"),v.post("/admin/bulk-import/brands",g,{onSuccess:u=>{var se,te;z(!1);const j=(te=(se=u.props)==null?void 0:se.flash)==null?void 0:te.import_errors;j&&Array.isArray(j)&&j.length>0&&(console.warn("Import completed with some issues:",j),f.warning(`Import completed but ${j.length} rows had issues. Check console for details.`)),C.current&&(C.current.value=""),v.reload({only:["brands"]})},onError:u=>{z(!1);let j="Import failed. Please check your file format.";u.file?j=Array.isArray(u.file)?u.file[0]:u.file:u.message&&(j=u.message),f.error(j),C.current&&(C.current.value="")}})}},ge=s=>{if(s){const l=a.data.map(r=>r.id);b(l)}else b([])},V=(s,l)=>{b(l?r=>[...r,s]:r=>r.filter(g=>g!==s))},ve=a.data.length>0&&h.length===a.data.length,Ne=h.length>0&&h.length<a.data.length,we=()=>e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b",children:[e.jsx("th",{className:"text-left p-3 w-12",children:e.jsx(B,{checked:ve,onCheckedChange:ge,"aria-label":"Select all brands",className:Ne?"data-[state=checked]:bg-primary data-[state=checked]:border-primary":""})}),e.jsx("th",{className:"text-left p-3",children:e.jsxs(t,{variant:"ghost",onClick:()=>F("name"),className:"h-auto p-0 font-semibold hover:bg-transparent",children:["Brand",i==="name"&&(m==="asc"?e.jsx(W,{className:"ml-1 h-4 w-4"}):e.jsx(O,{className:"ml-1 h-4 w-4"})),i!=="name"&&e.jsx(ce,{className:"ml-1 h-4 w-4 opacity-50"})]})}),e.jsx("th",{className:"text-left p-3",children:e.jsxs(t,{variant:"ghost",onClick:()=>F("country"),className:"h-auto p-0 font-semibold hover:bg-transparent",children:["Country",i==="country"&&(m==="asc"?e.jsx(W,{className:"ml-1 h-4 w-4"}):e.jsx(O,{className:"ml-1 h-4 w-4"})),i!=="country"&&e.jsx(ce,{className:"ml-1 h-4 w-4 opacity-50"})]})}),e.jsx("th",{className:"text-left p-3",children:"Website"}),e.jsx("th",{className:"text-left p-3",children:"Status"}),e.jsx("th",{className:"text-left p-3",children:"Models"}),e.jsx("th",{className:"text-right p-3",children:"Actions"})]})}),e.jsx("tbody",{children:a.data.map(s=>e.jsxs("tr",{className:"border-b hover:bg-muted/50",children:[e.jsx("td",{className:"p-3",children:e.jsx(B,{checked:h.includes(s.id),onCheckedChange:l=>V(s.id,l),"aria-label":`Select ${s.name}`})}),e.jsx("td",{className:"p-3",children:e.jsxs("div",{className:"flex items-center gap-3",children:[s.logo_url&&e.jsx("img",{src:s.logo_url,alt:s.name,className:"w-6 h-6 object-contain"}),e.jsx("div",{children:e.jsx("div",{className:"font-medium",children:s.name})})]})}),e.jsx("td",{className:"p-3",children:e.jsx("span",{className:"text-sm",children:s.country||"-"})}),e.jsx("td",{className:"p-3",children:s.website?e.jsxs("a",{href:s.website,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-blue-600 hover:text-blue-800 text-sm",children:["Website ",e.jsx(S,{className:"h-3 w-3"})]}):e.jsx("span",{className:"text-sm text-muted-foreground",children:"-"})}),e.jsx("td",{className:"p-3",children:e.jsx(w,{variant:s.is_active?"default":"secondary",children:s.is_active?"Active":"Inactive"})}),e.jsx("td",{className:"p-3",children:e.jsxs(w,{variant:"outline",children:[s.models_count||0," models"]})}),e.jsx("td",{className:"p-3",children:e.jsxs("div",{className:"flex items-center gap-1 justify-end",children:[e.jsx(x,{href:route("brands.show",s.slug||s.id),children:e.jsx(t,{variant:"outline",size:"sm",title:"View Public Page",children:e.jsx(S,{className:"h-3 w-3"})})}),e.jsx(x,{href:`/admin/brands/${s.id}`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Admin View",children:e.jsx(Q,{className:"h-3 w-3"})})}),e.jsx(x,{href:`/admin/brands/${s.id}/edit`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Edit",children:e.jsx(Y,{className:"h-3 w-3"})})}),e.jsx(t,{variant:"outline",size:"sm",className:"text-destructive hover:text-destructive",onClick:()=>$(s),title:"Delete Brand",children:e.jsx(H,{className:"h-3 w-3"})})]})})]},s.id))})]})}),ye=({brand:s})=>e.jsx(k,{className:"h-full",children:e.jsx(A,{className:"p-4",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"space-y-1 flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[s.logo_url&&e.jsx("img",{src:s.logo_url,alt:s.name,className:"w-6 h-6 object-contain flex-shrink-0"}),e.jsx("h3",{className:"font-medium truncate",children:s.name})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(w,{variant:s.is_active?"default":"secondary",children:s.is_active?"Active":"Inactive"}),e.jsxs(w,{variant:"outline",children:[s.models_count||0," models"]})]})]}),e.jsx(B,{checked:h.includes(s.id),onCheckedChange:l=>V(s.id,l),"aria-label":`Select ${s.name}`,className:"mt-1"})]}),(s.country||s.website)&&e.jsxs("div",{className:"space-y-1 text-sm text-muted-foreground",children:[s.country&&e.jsxs("p",{children:["Country: ",s.country]}),s.website&&e.jsxs("a",{href:s.website,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-blue-600 hover:text-blue-800",children:["Website ",e.jsx(S,{className:"h-3 w-3"})]})]}),e.jsxs("div",{className:"flex items-center gap-1 pt-2",children:[e.jsx(x,{href:route("brands.show",s.slug||s.id),children:e.jsx(t,{variant:"outline",size:"sm",title:"View Public Page",children:e.jsx(S,{className:"h-3 w-3"})})}),e.jsx(x,{href:`/admin/brands/${s.id}`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Admin View",children:e.jsx(Q,{className:"h-3 w-3"})})}),e.jsx(x,{href:`/admin/brands/${s.id}/edit`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Edit",children:e.jsx(Y,{className:"h-3 w-3"})})}),e.jsx(t,{variant:"outline",size:"sm",className:"text-destructive hover:text-destructive",onClick:()=>$(s),title:"Delete Brand",children:e.jsx(H,{className:"h-3 w-3"})})]})]})})});return e.jsxs(Ae,{children:[e.jsx(Ce,{title:"Brands - Admin"}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Brands"}),e.jsx("p",{className:"text-muted-foreground",children:"Manage mobile device brands"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(t,{variant:"outline",className:"rounded-full border-border bg-transparent hover:bg-accent hover:text-accent-foreground px-6 py-2 h-auto transition-colors",onClick:pe,title:"Download CSV Template",children:[e.jsx(De,{className:"h-4 w-4 mr-2"}),"Template"]}),e.jsxs(t,{variant:"outline",className:"rounded-full border-border bg-transparent hover:bg-accent hover:text-accent-foreground px-6 py-2 h-auto transition-colors disabled:opacity-50",onClick:je,disabled:he,title:"Import Brands from CSV",children:[e.jsx(Ie,{className:"h-4 w-4 mr-2"}),"Import"]}),e.jsxs(t,{variant:"outline",className:"rounded-full border-border bg-transparent hover:bg-accent hover:text-accent-foreground px-6 py-2 h-auto transition-colors",onClick:xe,title:"Export All Brands to CSV",children:[e.jsx(re,{className:"h-4 w-4 mr-2"}),"Export All"]})]}),e.jsx(x,{href:"/admin/brands/create",children:e.jsxs(t,{children:[e.jsx(le,{className:"h-4 w-4 mr-2"}),"Add Brand"]})})]})]}),e.jsx("input",{ref:C,type:"file",accept:".csv",onChange:fe,className:"hidden"}),e.jsx(k,{children:e.jsx(A,{className:"pt-6",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(ie,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),e.jsx(ke,{placeholder:"Search brands, countries, or websites...",value:n,onChange:s=>K(s.target.value),className:"pl-10",onKeyDown:s=>{s.key==="Enter"&&q()}})]}),e.jsxs(t,{onClick:q,children:[e.jsx(ie,{className:"h-4 w-4 mr-2"}),"Search"]}),e.jsxs(t,{variant:"outline",onClick:()=>de(!I),className:I?"bg-muted":"",children:[e.jsx(Fe,{className:"h-4 w-4 mr-2"}),"Filters",_&&e.jsx(w,{variant:"secondary",className:"ml-2 h-5 w-5 p-0 flex items-center justify-center text-xs",children:"!"})]}),_&&e.jsxs(t,{variant:"ghost",onClick:ee,children:[e.jsx(G,{className:"h-4 w-4 mr-2"}),"Clear"]})]}),I&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(P,{htmlFor:"country-filter",children:"Country"}),e.jsxs(L,{value:o,onValueChange:X,children:[e.jsx(M,{id:"country-filter",children:e.jsx(R,{placeholder:"All countries"})}),e.jsxs(U,{children:[e.jsx(N,{value:"all",children:"All countries"}),ne.countries.map(s=>e.jsx(N,{value:s,children:s},s))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(P,{htmlFor:"status-filter",children:"Status"}),e.jsxs(L,{value:d,onValueChange:J,children:[e.jsx(M,{id:"status-filter",children:e.jsx(R,{placeholder:"All statuses"})}),e.jsxs(U,{children:[e.jsx(N,{value:"all",children:"All statuses"}),e.jsx(N,{value:"active",children:"Active"}),e.jsx(N,{value:"inactive",children:"Inactive"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(P,{htmlFor:"sort-filter",children:"Sort By"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(L,{value:i,onValueChange:E,children:[e.jsx(M,{id:"sort-filter",className:"flex-1",children:e.jsx(R,{})}),e.jsxs(U,{children:[e.jsx(N,{value:"name",children:"Name"}),e.jsx(N,{value:"country",children:"Country"}),e.jsx(N,{value:"created_at",children:"Created Date"})]})]}),e.jsx(t,{variant:"outline",size:"sm",onClick:()=>F(i),className:"px-3",children:m==="asc"?e.jsx(W,{className:"h-4 w-4"}):e.jsx(O,{className:"h-4 w-4"})})]})]})]})]})})}),h.length>0&&e.jsx(k,{className:"border-blue-200 bg-blue-50",children:e.jsx(A,{className:"py-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs("span",{className:"text-sm font-medium",children:[h.length," brand",h.length!==1?"s":""," selected"]})}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(t,{variant:"outline",className:"rounded-full border-primary bg-transparent text-primary hover:bg-primary hover:text-primary-foreground px-4 py-2 h-auto transition-colors",onClick:ue,children:[e.jsx(re,{className:"h-4 w-4 mr-2"}),"Export Selected"]}),e.jsxs(t,{variant:"ghost",className:"rounded-full text-muted-foreground hover:bg-accent hover:text-accent-foreground px-4 py-2 h-auto transition-colors",onClick:()=>b([]),children:[e.jsx(G,{className:"h-4 w-4 mr-2"}),"Clear Selection"]})]})]})})}),e.jsxs(k,{children:[e.jsx(Se,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(be,{className:"flex items-center gap-2",children:[e.jsx(ae,{className:"h-5 w-5"}),"All Brands"]}),e.jsxs(_e,{children:[a.total," brands total",a.data.length>0&&e.jsxs("span",{className:"ml-2",children:["(showing ",a.from,"-",a.to,")"]})]})]}),e.jsxs("div",{className:"flex items-center gap-1 border rounded-lg p-1",children:[e.jsx(t,{variant:c==="table"?"default":"ghost",size:"sm",onClick:()=>T("table"),className:"h-8 px-3",children:e.jsx(Te,{className:"h-4 w-4"})}),e.jsx(t,{variant:c==="list"?"default":"ghost",size:"sm",onClick:()=>T("list"),className:"h-8 px-3",children:e.jsx($e,{className:"h-4 w-4"})}),e.jsx(t,{variant:c==="grid"?"default":"ghost",size:"sm",onClick:()=>T("grid"),className:"h-8 px-3",children:e.jsx(Be,{className:"h-4 w-4"})})]})]})}),e.jsxs(A,{children:[a.data.length>0?e.jsx(e.Fragment,{children:c==="table"?e.jsx(we,{}):c==="grid"?e.jsx("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:a.data.map(s=>e.jsx(ye,{brand:s},s.id))}):e.jsx("div",{className:"space-y-4",children:a.data.map(s=>e.jsxs("div",{className:"border rounded-lg p-4 space-y-3",children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[s.logo_url&&e.jsx("img",{src:s.logo_url,alt:s.name,className:"w-6 h-6 object-contain"}),e.jsx("h3",{className:"font-medium",children:s.name})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(w,{variant:s.is_active?"default":"secondary",children:s.is_active?"Active":"Inactive"}),e.jsxs(w,{variant:"outline",children:[s.models_count||0," models"]})]})]}),e.jsx(B,{checked:h.includes(s.id),onCheckedChange:l=>V(s.id,l),"aria-label":`Select ${s.name}`,className:"mt-1"})]}),(s.country||s.website)&&e.jsxs("div",{className:"space-y-1 text-sm text-muted-foreground",children:[s.country&&e.jsxs("p",{children:["Country: ",s.country]}),s.website&&e.jsxs("a",{href:s.website,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-blue-600 hover:text-blue-800",children:["Website ",e.jsx(S,{className:"h-3 w-3"})]})]}),e.jsxs("div",{className:"flex items-center gap-2 pt-2",children:[e.jsx(x,{href:route("brands.show",s.slug||s.id),children:e.jsx(t,{variant:"outline",size:"sm",title:"View Public Page",children:e.jsx(S,{className:"h-4 w-4"})})}),e.jsx(x,{href:`/admin/brands/${s.id}`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Admin View",children:e.jsx(Q,{className:"h-4 w-4"})})}),e.jsx(x,{href:`/admin/brands/${s.id}/edit`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Edit",children:e.jsx(Y,{className:"h-4 w-4"})})}),e.jsx(t,{variant:"outline",size:"sm",className:"text-destructive hover:text-destructive",onClick:()=>$(s),title:"Delete",children:e.jsx(H,{className:"h-4 w-4"})})]})]},s.id))})}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(ae,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No brands found"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:_?"No brands match your current filters. Try adjusting your search criteria.":"Get started by adding your first brand."}),_?e.jsxs(t,{variant:"outline",onClick:ee,children:[e.jsx(G,{className:"h-4 w-4 mr-2"}),"Clear Filters"]}):e.jsx(x,{href:"/admin/brands/create",children:e.jsxs(t,{children:[e.jsx(le,{className:"h-4 w-4 mr-2"}),"Add Brand"]})})]}),a.data.length>0&&a.last_page>1&&e.jsxs("div",{className:"flex items-center justify-between pt-4 border-t",children:[e.jsxs("div",{className:"text-sm text-muted-foreground",children:["Showing ",a.from," to ",a.to," of ",a.total," brands"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(t,{variant:"outline",size:"sm",onClick:()=>D(a.current_page-1),disabled:a.current_page<=1,children:[e.jsx(Ve,{className:"h-4 w-4 mr-1"}),"Previous"]}),e.jsx("div",{className:"flex items-center gap-1",children:Array.from({length:Math.min(5,a.last_page)},(s,l)=>{let r;if(a.last_page<=5)r=l+1;else{const g=Math.max(1,a.current_page-2),u=Math.min(a.last_page,g+4);if(r=g+l,r>u)return null}return r>a.last_page||r<1?null:e.jsx(t,{variant:a.current_page===r?"default":"outline",size:"sm",onClick:()=>D(r),className:"w-8 h-8 p-0",children:r},r)})}),e.jsxs(t,{variant:"outline",size:"sm",onClick:()=>D(a.current_page+1),disabled:a.current_page>=a.last_page,children:["Next",e.jsx(ze,{className:"h-4 w-4 ml-1"})]})]})]})]})]})]})})]})}export{bs as default};
