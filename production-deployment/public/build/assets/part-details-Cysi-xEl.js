import{J as R,r as m,j as e,Q as J,t as x,S as W}from"./app-J5EqS6dS.js";import{c as X,B as a,S as b}from"./smartphone-GGiwNneF.js";import{C as g,a as i,b as o,c as n,d as Z}from"./card-9XCADs-4.js";import{B as h}from"./badge-BucYuCBs.js";import{L as G}from"./label-BlOrdc-X.js";import{H as N,I as K,D as Y,b as ee,X as se,a as re}from"./ImpersonationBanner-CYn5eDk6.js";import{A as ae}from"./app-layout-ox1kAwY6.js";import{A as $}from"./Watermark-BujLnmGI.js";import{C as M}from"./CompatibleModelsProtection-BfRsG4tU.js";import{A as y}from"./arrow-left-D4U9AVF9.js";import{S as le}from"./search-DBK6jUoc.js";import{P as u}from"./package-CoyvngX8.js";import{B as T}from"./building-Dgyml3QN.js";import{H as te}from"./hash-Bk6gEERd.js";import{F as de}from"./file-text-Dx6bYLtE.js";import{S as ie}from"./users-RYmOyic9.js";import{T as oe}from"./table-gSl3ppmW.js";import{L as ne}from"./list-CNjrM85i.js";import{C as ce}from"./chevron-left-C6ZNA5qQ.js";import{C as B}from"./circle-check-big-DOFoatRy.js";import{C as L}from"./circle-alert-C6UwDlxH.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./index-BzZWUWqx.js";import"./crown-UDSxMtlm.js";import"./shield-D9nQfigG.js";import"./user-DCnDRzMf.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./eye-D-fsmYB2.js";import"./lock-Tx_yfI4R.js";import"./mail-CDon-vZy.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const me=[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]],P=X("Share2",me);function Je({part:s}){var A,z;const{auth:F}=R().props,[d,V]=m.useState(!1),[j,p]=m.useState(!1),[c,v]=m.useState("table"),[l,f]=m.useState(null),[D,w]=m.useState(!1),C=!!F.user,S=()=>{navigator.share?navigator.share({title:s.name,text:`Check out this mobile part: ${s.name}`,url:window.location.href}):navigator.clipboard.writeText(window.location.href)},I=()=>{p(!0),W.post(route("dashboard.add-favorite"),{type:"part",id:s.id},{onSuccess:()=>{V(!0),p(!1)},onError:r=>{p(!1)}})},H=r=>{f(r),w(!0)},_=()=>{w(!1),f(null)},E=()=>{l!==null&&s.images&&l>0&&f(l-1)},O=()=>{l!==null&&s.images&&l<s.images.length-1&&f(l+1)},q=()=>e.jsxs(M,{className:"relative",children:[e.jsx("div",{className:"border border-gray-400 dark:border-gray-600 rounded-lg overflow-hidden bg-white dark:bg-gray-900",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full border-collapse",children:[e.jsx("thead",{className:"bg-gradient-to-r from-blue-100/80 to-indigo-100/80 dark:from-blue-900/40 dark:to-indigo-900/40",children:e.jsxs("tr",{children:[e.jsx("th",{className:"text-left p-3 font-semibold text-blue-900 dark:text-blue-100 border-r border-gray-400 dark:border-gray-600 first:rounded-tl-lg last:rounded-tr-lg",children:"Brand"}),e.jsx("th",{className:"text-left p-3 font-semibold text-blue-900 dark:text-blue-100 border-r border-gray-400 dark:border-gray-600",children:"Model"}),e.jsx("th",{className:"text-left p-3 font-semibold text-blue-900 dark:text-blue-100 border-r border-gray-400 dark:border-gray-600 hidden sm:table-cell",children:"Model Number"}),e.jsx("th",{className:"text-left p-3 font-semibold text-blue-900 dark:text-blue-100 border-r border-gray-400 dark:border-gray-600 hidden lg:table-cell",children:"Notes"}),e.jsx("th",{className:"text-left p-3 font-semibold text-blue-900 dark:text-blue-100 last:rounded-tr-lg",children:"Status"})]})}),e.jsx("tbody",{children:s.models.map((r,t)=>e.jsxs("tr",{className:`border-b border-gray-400 dark:border-gray-600 hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors ${t%2===0?"bg-white dark:bg-gray-900":"bg-gray-50/50 dark:bg-gray-800/50"} ${t===s.models.length-1?"last:border-b-0":""}`,children:[e.jsx("td",{className:"p-3 border-r border-gray-400 dark:border-gray-600",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-100 dark:bg-blue-900/50 rounded flex items-center justify-center flex-shrink-0",children:e.jsx(b,{className:"w-4 h-4 text-blue-600 dark:text-blue-400"})}),e.jsx("span",{className:"font-semibold text-gray-900 dark:text-gray-100 text-sm",children:r.brand.name})]})}),e.jsx("td",{className:"p-3 border-r border-gray-400 dark:border-gray-600",children:e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-800 dark:text-gray-200 text-sm",children:r.name}),r.model_number&&e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 font-mono sm:hidden",children:r.model_number}),r.pivot.compatibility_notes&&e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 lg:hidden truncate mt-1",children:r.pivot.compatibility_notes})]})}),e.jsx("td",{className:"p-3 border-r border-gray-400 dark:border-gray-600 hidden sm:table-cell",children:e.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-300 font-mono",children:r.model_number||"-"})}),e.jsx("td",{className:"p-3 border-r border-gray-400 dark:border-gray-600 hidden lg:table-cell",children:e.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-300",children:r.pivot.compatibility_notes||"-"})}),e.jsx("td",{className:"p-3",children:r.pivot.is_verified?e.jsxs(h,{variant:"default",className:"bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800",children:[e.jsx(B,{className:"w-3 h-3 mr-1"}),"Verified"]}):e.jsxs(h,{variant:"outline",className:"bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800",children:[e.jsx(L,{className:"w-3 h-3 mr-1"}),"Unverified"]})})]},r.id))})]})})}),e.jsx($,{})]}),Q=()=>e.jsxs(M,{className:"relative",children:[e.jsx("div",{className:"space-y-3",children:s.models.map(r=>e.jsxs("div",{className:"flex items-center justify-between p-4 border border-blue-200/50 dark:border-blue-800/50 rounded-lg hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors bg-white/60 dark:bg-gray-800/30",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-blue-100 dark:bg-blue-900/50 rounded-lg flex items-center justify-center",children:e.jsx(b,{className:"w-5 h-5 text-blue-600 dark:text-blue-400"})}),e.jsxs("div",{children:[e.jsxs("div",{className:"font-medium text-gray-900 dark:text-gray-100",children:[r.brand.name," ",r.name]}),e.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-300",children:[r.model_number&&`Model: ${r.model_number}`,r.release_year&&` • ${r.release_year}`]}),r.pivot.compatibility_notes&&e.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:["Note: ",r.pivot.compatibility_notes]})]})]}),e.jsx("div",{className:"flex items-center gap-2",children:r.pivot.is_verified?e.jsxs(h,{variant:"default",className:"bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800",children:[e.jsx(B,{className:"w-3 h-3 mr-1"}),"Verified"]}):e.jsxs(h,{variant:"outline",className:"bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800",children:[e.jsx(L,{className:"w-3 h-3 mr-1"}),"Unverified"]})})]},r.id))}),e.jsx($,{})]});return e.jsxs(ae,{children:[e.jsx(J,{title:s.name}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-3 rounded-xl p-3 sm:p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-3 sm:space-y-4",children:[e.jsxs("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-xl border border-blue-200/50 dark:border-blue-800/50 p-4 mb-4",children:[e.jsx("div",{className:"mb-3 flex flex-col sm:flex-row gap-2 sm:gap-3",children:C?e.jsx(x,{href:route("search.index"),children:e.jsxs(a,{variant:"ghost",size:"sm",className:"text-muted-foreground hover:text-foreground",children:[e.jsx(y,{className:"w-4 h-4 mr-2"}),"Back to Search"]})}):e.jsxs(e.Fragment,{children:[e.jsx(x,{href:route("home"),children:e.jsxs(a,{variant:"ghost",size:"sm",className:"text-blue-600 hover:text-blue-700",children:[e.jsx(y,{className:"w-4 h-4 mr-2"}),"Back to Home"]})}),e.jsx(x,{href:route("home"),children:e.jsxs(a,{variant:"outline",size:"sm",className:"bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 hover:border-blue-300 dark:bg-blue-950/20 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-950/30",children:[e.jsx(le,{className:"w-4 h-4 mr-2"}),"Search Again"]})})]})}),e.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-3 mb-2",children:[e.jsx("h1",{className:"text-xl sm:text-2xl font-bold tracking-tight text-gray-900 dark:text-gray-100 truncate",children:s.name}),e.jsx("div",{className:"flex items-center gap-2 flex-shrink-0",children:s.part_number&&e.jsxs(h,{variant:"outline",className:"font-mono text-xs",children:["#",s.part_number]})})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-x-6 gap-y-2 text-sm text-muted-foreground mb-3",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(u,{className:"w-3 h-3"}),e.jsx("span",{className:"font-medium",children:s.category.name})]}),s.manufacturer&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(T,{className:"w-3 h-3"}),e.jsx("span",{className:"font-medium",children:s.manufacturer})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(b,{className:"w-3 h-3"}),e.jsxs("span",{className:"font-medium",children:[((A=s.models)==null?void 0:A.length)||0," models"]})]})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2 flex-shrink-0",children:[C?e.jsxs(a,{size:"sm",className:"bg-green-600 hover:bg-green-700 text-white",onClick:I,disabled:d||j,children:[e.jsx(N,{className:`w-4 h-4 mr-2 ${d?"fill-current":""}`}),d?"Saved":j?"Saving...":"Save"]}):e.jsx(x,{href:route("register"),children:e.jsxs(a,{size:"sm",className:"bg-blue-600 hover:bg-blue-700 text-white",children:[e.jsx(N,{className:"w-4 h-4 mr-2"}),"Sign up to Save"]})}),e.jsxs(a,{variant:"outline",size:"sm",onClick:S,children:[e.jsx(P,{className:"w-4 h-4 mr-2"}),"Share"]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-3 lg:gap-4",children:[e.jsxs("div",{className:"lg:col-span-3 space-y-3 lg:space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 xl:grid-cols-2 gap-3 lg:gap-4",children:[e.jsxs(g,{className:"border-blue-200/50 dark:border-blue-800/50",children:[e.jsx(i,{className:"bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3",children:e.jsxs(o,{className:"flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg",children:[e.jsx(u,{className:"w-4 h-4"}),"Basic Information"]})}),e.jsxs(n,{className:"p-0",children:[e.jsx("div",{className:"border border-blue-200/50 dark:border-blue-800/50 rounded-lg overflow-hidden bg-white/50 dark:bg-gray-900/50 mx-4 mb-4",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsx("table",{className:"w-full",children:e.jsxs("tbody",{children:[e.jsxs("tr",{className:"border-b border-blue-200/30 dark:border-blue-800/30 hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors bg-white/60 dark:bg-gray-800/30",children:[e.jsx("td",{className:"p-3 w-1/3",children:e.jsxs("span",{className:"font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2",children:[e.jsx(u,{className:"w-3 h-3 text-blue-600 dark:text-blue-400"}),"Category"]})}),e.jsx("td",{className:"p-3",children:e.jsx("span",{className:"text-gray-800 dark:text-gray-200 font-medium",children:s.category.name})})]}),s.part_number&&e.jsxs("tr",{className:"border-b border-blue-200/30 dark:border-blue-800/30 hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors bg-blue-50/30 dark:bg-blue-950/10",children:[e.jsx("td",{className:"p-3",children:e.jsxs("span",{className:"font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2",children:[e.jsx(te,{className:"w-3 h-3 text-green-600 dark:text-green-400"}),"Part Number"]})}),e.jsx("td",{className:"p-3",children:e.jsx("span",{className:"text-gray-800 dark:text-gray-200 font-medium font-mono",children:s.part_number})})]}),s.manufacturer&&e.jsxs("tr",{className:"hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors bg-white/60 dark:bg-gray-800/30",children:[e.jsx("td",{className:"p-3",children:e.jsxs("span",{className:"font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2",children:[e.jsx(T,{className:"w-3 h-3 text-purple-600 dark:text-purple-400"}),"Manufacturer"]})}),e.jsx("td",{className:"p-3",children:e.jsx("span",{className:"text-gray-800 dark:text-gray-200 font-medium",children:s.manufacturer})})]})]})})})}),s.description&&e.jsxs("div",{className:"mx-4 mb-4 p-3 bg-gradient-to-br from-gray-50 to-slate-50 dark:from-gray-900/50 dark:to-slate-900/50 rounded-lg border border-gray-200 dark:border-gray-700",children:[e.jsxs(G,{className:"text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-1 mb-2",children:[e.jsx(de,{className:"w-3 h-3"}),"Description"]}),e.jsx("p",{className:"text-sm leading-relaxed whitespace-pre-wrap text-gray-800 dark:text-gray-200",children:s.description})]})]})]}),s.specifications&&Object.keys(s.specifications).length>0&&e.jsxs(g,{className:"border-blue-200/50 dark:border-blue-800/50",children:[e.jsx(i,{className:"bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3",children:e.jsxs(o,{className:"flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg",children:[e.jsx(ie,{className:"w-4 h-4"}),"Specifications"]})}),e.jsx(n,{className:"p-0",children:e.jsx("div",{className:"border border-blue-200/50 dark:border-blue-800/50 rounded-lg overflow-hidden bg-white/50 dark:bg-gray-900/50 mx-4 mb-4",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-gradient-to-r from-blue-100/80 to-indigo-100/80 dark:from-blue-900/40 dark:to-indigo-900/40",children:e.jsxs("tr",{children:[e.jsx("th",{className:"text-left p-3 font-semibold text-blue-900 dark:text-blue-100 w-1/2",children:"Specification"}),e.jsx("th",{className:"text-left p-3 font-semibold text-blue-900 dark:text-blue-100",children:"Value"})]})}),e.jsx("tbody",{children:Object.entries(s.specifications).map(([r,t],k)=>e.jsxs("tr",{className:`border-b border-blue-200/30 dark:border-blue-800/30 hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors ${k%2===0?"bg-white/60 dark:bg-gray-800/30":"bg-blue-50/30 dark:bg-blue-950/10"}`,children:[e.jsx("td",{className:"p-3",children:e.jsx("span",{className:"font-semibold text-gray-900 dark:text-gray-100 capitalize",children:r.replace(/([A-Z])/g," $1").replace(/^./,U=>U.toUpperCase())})}),e.jsx("td",{className:"p-3",children:e.jsx("span",{className:"text-gray-800 dark:text-gray-200 font-medium",children:t})})]},r))})]})})})})]})]}),s.models&&s.models.length>0&&e.jsxs(g,{className:"border-blue-200/50 dark:border-blue-800/50",children:[e.jsx(i,{className:"bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs(o,{className:"flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg",children:[e.jsx(b,{className:"w-4 h-4"}),"Compatible Models (",s.models.length,")"]}),e.jsx(Z,{className:"mt-1 text-blue-700/70 dark:text-blue-300/70 text-sm",children:"Mobile device models that are compatible with this part"})]}),e.jsxs("div",{className:"flex items-center gap-1 bg-white/60 dark:bg-gray-800/60 border border-blue-200 dark:border-blue-700 rounded-lg p-1",children:[e.jsxs(a,{variant:c==="table"?"default":"ghost",size:"sm",onClick:()=>v("table"),className:`h-7 px-2 ${c==="table"?"bg-blue-600 hover:bg-blue-700 text-white shadow-sm":"hover:bg-blue-100 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300"}`,title:"Table View",children:[e.jsx(oe,{className:"h-3 w-3"}),e.jsx("span",{className:"ml-1 hidden sm:inline text-xs",children:"Table"})]}),e.jsxs(a,{variant:c==="list"?"default":"ghost",size:"sm",onClick:()=>v("list"),className:`h-7 px-2 ${c==="list"?"bg-blue-600 hover:bg-blue-700 text-white shadow-sm":"hover:bg-blue-100 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300"}`,title:"List View",children:[e.jsx(ne,{className:"h-3 w-3"}),e.jsx("span",{className:"ml-1 hidden sm:inline text-xs",children:"List"})]})]})]})}),e.jsx(n,{className:"p-4",children:c==="table"?e.jsx(q,{}):e.jsx(Q,{})})]})]}),e.jsxs("div",{className:"space-y-4",children:[s.images&&s.images.length>0&&e.jsxs(g,{className:"border-blue-200/50 dark:border-blue-800/50",children:[e.jsx(i,{className:"bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3",children:e.jsxs(o,{className:"flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg",children:[e.jsx(K,{className:"w-4 h-4"}),"Images (",s.images.length,")"]})}),e.jsx(n,{className:"p-3",children:e.jsx("div",{className:"grid grid-cols-3 gap-2",children:s.images.map((r,t)=>e.jsx("div",{className:"aspect-square w-full max-w-[80px] mx-auto border border-blue-200/50 dark:border-blue-800/50 rounded-lg overflow-hidden group cursor-pointer hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300",onClick:()=>H(t),children:e.jsx("img",{src:r,alt:`${s.name} - Image ${t+1}`,className:"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300",onError:k=>{k.currentTarget.src="/placeholder-image.svg"}})},t))})})]}),e.jsxs(g,{className:"border-blue-200/50 dark:border-blue-800/50",children:[e.jsx(i,{className:"bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3",children:e.jsxs(o,{className:"flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg",children:[e.jsx(u,{className:"w-4 h-4"}),"Quick Info"]})}),e.jsx(n,{className:"p-3 pb-0",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"bg-white/60 dark:bg-gray-800/60 rounded-lg p-3 border border-blue-200/50 dark:border-blue-700",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx(u,{className:"w-4 h-4 text-blue-600 dark:text-blue-400"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Category"})]}),e.jsx("p",{className:"font-semibold text-gray-900 dark:text-gray-100",children:s.category.name}),s.category.description&&e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:s.category.description})]}),e.jsxs("div",{className:"bg-white/60 dark:bg-gray-800/60 rounded-lg p-3 border border-blue-200/50 dark:border-blue-700",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx(b,{className:"w-4 h-4 text-green-600 dark:text-green-400"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Compatibility"})]}),e.jsxs("p",{className:"font-semibold text-gray-900 dark:text-gray-100",children:[s.models.length," ",s.models.length===1?"Model":"Models"]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Compatible devices"})]})]})}),e.jsx(i,{className:"bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3 border-t border-blue-200/50 dark:border-blue-800/50 mt-3",children:e.jsxs(o,{className:"flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg",children:[e.jsx(N,{className:"w-4 h-4"}),"Actions"]})}),e.jsx(n,{className:"space-y-2 p-3",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs(a,{className:"w-full bg-green-600 hover:bg-green-700 text-white border-0 justify-start text-sm",onClick:I,disabled:d||j,children:[e.jsx(N,{className:`w-3 h-3 mr-2 ${d?"fill-current":""}`}),d?"Added to Favorites":j?"Adding...":"Add to Favorites"]}),e.jsxs(a,{className:"w-full bg-blue-600 hover:bg-blue-700 text-white border-0 justify-start text-sm",onClick:S,children:[e.jsx(P,{className:"w-3 h-3 mr-2"}),"Share Part"]}),e.jsx(x,{href:route("search.index"),children:e.jsxs(a,{className:"w-full justify-start text-sm",variant:"outline",children:[e.jsx(y,{className:"w-3 h-3 mr-2"}),"Back to Search"]})})]})})]})]})]})]})}),e.jsx(Y,{open:D,onOpenChange:_,children:e.jsx(ee,{className:"max-w-4xl max-h-[90vh] p-0 overflow-hidden",children:e.jsxs("div",{className:"relative bg-black",children:[e.jsx(a,{variant:"ghost",size:"sm",className:"absolute top-2 right-2 z-10 text-white hover:bg-white/20 rounded-full w-8 h-8 p-0",onClick:_,children:e.jsx(se,{className:"w-4 h-4"})}),s.images&&s.images.length>1&&e.jsxs(e.Fragment,{children:[e.jsx(a,{variant:"ghost",size:"sm",className:"absolute left-2 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20 rounded-full w-10 h-10 p-0 disabled:opacity-50",onClick:E,disabled:l===0,children:e.jsx(ce,{className:"w-6 h-6"})}),e.jsx(a,{variant:"ghost",size:"sm",className:"absolute right-2 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20 rounded-full w-10 h-10 p-0 disabled:opacity-50",onClick:O,disabled:l===(((z=s.images)==null?void 0:z.length)||0)-1,children:e.jsx(re,{className:"w-6 h-6"})})]}),l!==null&&s.images&&e.jsx("div",{className:"flex items-center justify-center min-h-[60vh] max-h-[80vh]",children:e.jsx("img",{src:s.images[l],alt:`${s.name} - Image ${l+1}`,className:"max-w-full max-h-full object-contain",onError:r=>{r.currentTarget.src="/placeholder-image.svg"}})}),s.images&&s.images.length>1&&l!==null&&e.jsxs("div",{className:"absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm",children:[l+1," / ",s.images.length]})]})})})]})}export{Je as default};
