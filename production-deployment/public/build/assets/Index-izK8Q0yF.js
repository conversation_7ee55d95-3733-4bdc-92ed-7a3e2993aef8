import{r as d,x as L,j as e,Q as K,S as v}from"./app-J5EqS6dS.js";import{U,P as Q,D as $,b as J,c as Y,d as Z,e as ee,t as l}from"./ImpersonationBanner-CYn5eDk6.js";import{A as se,G as te}from"./app-layout-ox1kAwY6.js";import{c as ae,B as a}from"./smartphone-GGiwNneF.js";import{I as N}from"./input-Bo8dOn9p.js";import{C as S,c as C}from"./card-9XCADs-4.js";import{L as w}from"./label-BlOrdc-X.js";import{T as re}from"./textarea-BDEiXlPH.js";import{S as le,a as ie,b as ne,c as oe,d as b}from"./select-CIhY0l9J.js";import{g as ce,v as de,r as A}from"./checkout-helpers-CMrRJez4.js";import{S as me}from"./search-DBK6jUoc.js";import{F as he}from"./filter-DKJvAZFg.js";import{L as pe}from"./list-CNjrM85i.js";import{E as z}from"./eye-D-fsmYB2.js";import{T as R}from"./trash-2-B3ZEh4hl.js";/* empty css            */import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./badge-BucYuCBs.js";import"./crown-UDSxMtlm.js";import"./shield-D9nQfigG.js";import"./user-DCnDRzMf.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xe=[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]],O=ae("PenLine",xe);function Ze({media:s,filters:p}){console.log("MediaIndex component received media data:",s),console.log("First media item:",s.data[0]);const[o,m]=d.useState("grid"),[c,r]=d.useState(null),[P,x]=d.useState(!1),[fe,ge]=d.useState(!1),[k,D]=d.useState(!1),u=d.useRef(null),{data:_,setData:E,get:q}=L({search:p.search||"",type:p.type||"all"}),{data:y,setData:f,put:X,processing:F}=L({title:"",alt_text:"",description:""}),H=t=>{t.preventDefault(),q("/admin/media",{preserveState:!0,preserveScroll:!0})},T=()=>{var t;(t=u.current)==null||t.click()},V=async t=>{const n=t.target.files;if(!n||n.length===0)return;D(!0);const g=new FormData;Array.from(n).forEach(i=>{g.append("files[]",i)});const I=async i=>fetch("/admin/media",{method:"POST",body:g,headers:{"X-CSRF-TOKEN":i,"X-Requested-With":"XMLHttpRequest"}});try{let i=ce();if(!de(i)){console.warn("Admin Media: Invalid CSRF token detected, attempting refresh...");try{i=await A()}catch(h){console.error("Admin Media: Failed to refresh CSRF token:",h),l.error("Security token error. Please refresh the page and try again.");return}}let j=await I(i);if(j.status===419){console.warn("Admin Media: CSRF token mismatch (419), attempting token refresh and retry...");try{const h=await A();j=await I(h)}catch(h){console.error("Admin Media: Failed to refresh CSRF token on retry:",h),l.error("Security token error. Please refresh the page and try again.");return}}const M=await j.json();j.ok?(l.success(M.message),v.reload()):l.error(M.message||"Upload failed")}catch(i){console.error("Admin Media: Upload error:",i),l.error("Upload failed")}finally{D(!1),u.current&&(u.current.value="")}},W=t=>{r(t),f({title:t.title||"",alt_text:t.alt_text||"",description:t.description||""}),x(!0)},B=t=>{t.preventDefault(),c&&X(`/admin/media/${c.id}`,{onSuccess:()=>{l.success("Media updated successfully"),x(!1),r(null)},onError:()=>{l.error("Failed to update media")}})},G=async t=>{var n;if(confirm("Are you sure you want to delete this media file?"))try{(await fetch(`/admin/media/${t.id}`,{method:"DELETE",headers:{"X-CSRF-TOKEN":((n=document.querySelector('meta[name="csrf-token"]'))==null?void 0:n.getAttribute("content"))||""}})).ok?(l.success("Media deleted successfully"),v.reload()):l.error("Failed to delete media")}catch{l.error("Failed to delete media")}};return e.jsxs(se,{children:[e.jsx(K,{title:"Media Library - Admin"}),e.jsxs("div",{className:"container mx-auto px-4 py-6 space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-semibold text-gray-900",children:"Media Library"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Manage your uploaded files and images"})]}),e.jsxs(a,{onClick:T,disabled:k,children:[e.jsx(U,{className:"w-4 h-4 mr-2"}),k?"Uploading...":"Upload Files"]})]}),e.jsx(S,{children:e.jsx(C,{className:"p-4",children:e.jsxs("form",{onSubmit:H,className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(me,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),e.jsx(N,{placeholder:"Search media files...",value:_.search,onChange:t=>E("search",t.target.value),className:"pl-10"})]})}),e.jsxs(le,{value:_.type,onValueChange:t=>E("type",t),children:[e.jsx(ie,{className:"w-48",children:e.jsx(ne,{placeholder:"All file types"})}),e.jsxs(oe,{children:[e.jsx(b,{value:"all",children:"All file types"}),e.jsx(b,{value:"images",children:"Images"}),e.jsx(b,{value:"application",children:"Documents"})]})]}),e.jsxs(a,{type:"submit",children:[e.jsx(he,{className:"w-4 h-4 mr-2"}),"Filter"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(a,{type:"button",variant:o==="grid"?"default":"outline",size:"sm",onClick:()=>m("grid"),children:e.jsx(te,{className:"w-4 h-4"})}),e.jsx(a,{type:"button",variant:o==="list"?"default":"outline",size:"sm",onClick:()=>m("list"),children:e.jsx(pe,{className:"w-4 h-4"})})]})]})})}),s.data.length>0?e.jsx("div",{className:o==="grid"?"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6":"space-y-4",children:s.data.map(t=>e.jsx(ue,{media:t,viewMode:o,onEdit:W,onDelete:G},t.id))}):e.jsx(S,{children:e.jsxs(C,{className:"p-8 text-center",children:[e.jsx(U,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No media files found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Upload your first files to get started"}),e.jsxs(a,{onClick:T,children:[e.jsx(Q,{className:"w-4 h-4 mr-2"}),"Upload Files"]})]})}),s.last_page>1&&e.jsx("div",{className:"flex items-center justify-center gap-2",children:s.links.map((t,n)=>e.jsx(a,{variant:t.active?"default":"outline",size:"sm",disabled:!t.url,onClick:()=>t.url&&v.get(t.url),dangerouslySetInnerHTML:{__html:t.label}},n))})]}),e.jsx("input",{ref:u,type:"file",multiple:!0,accept:"image/*,.pdf,.txt",onChange:V,className:"hidden"}),e.jsx($,{open:P,onOpenChange:x,children:e.jsxs(J,{className:"sm:max-w-[500px]",children:[e.jsxs(Y,{children:[e.jsx(Z,{children:"Edit Media Details"}),e.jsx(ee,{children:"Update the title, alt text, and description for this media file."})]}),c&&e.jsxs("form",{onSubmit:B,className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(w,{htmlFor:"title",children:"Title"}),e.jsx(N,{id:"title",value:y.title,onChange:t=>f("title",t.target.value),placeholder:"Enter a title for this media"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(w,{htmlFor:"alt_text",children:"Alt Text"}),e.jsx(N,{id:"alt_text",value:y.alt_text,onChange:t=>f("alt_text",t.target.value),placeholder:"Describe this image for accessibility"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(w,{htmlFor:"description",children:"Description"}),e.jsx(re,{id:"description",value:y.description,onChange:t=>f("description",t.target.value),placeholder:"Add a description for this media",rows:3})]}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx(a,{type:"button",variant:"outline",onClick:()=>x(!1),children:"Cancel"}),e.jsx(a,{type:"submit",disabled:F,children:F?"Saving...":"Save Changes"})]})]})]})})]})}function ue({media:s,viewMode:p,onEdit:o,onDelete:m}){const c=r=>r.startsWith("image/");return p==="grid"?e.jsxs("div",{className:"relative group",children:[e.jsx("div",{className:"aspect-square bg-gray-100 rounded-lg overflow-hidden border border-gray-200",children:c(s.mime_type)?e.jsx("img",{src:s.url||"/placeholder.svg",alt:s.alt_text||s.original_filename,className:"w-full h-full object-cover",onError:r=>{console.error("Failed to load image:",s.url,"Error:",r),console.error("Image element:",r.currentTarget),r.currentTarget.style.display="none"},onLoad:()=>{console.log("Image loaded successfully:",s.url)},onLoadStart:()=>{console.log("Image load started:",s.url)}}):e.jsx("div",{className:"w-full h-full flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl mb-2",children:"📄"}),e.jsx("div",{className:"text-xs text-gray-500 uppercase",children:s.mime_type.split("/")[1]})]})})}),e.jsxs("div",{className:"absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[e.jsx(a,{size:"sm",variant:"secondary",className:"h-6 w-6 p-0",onClick:()=>o(s),children:e.jsx(O,{className:"w-3 h-3"})}),e.jsx(a,{size:"sm",variant:"secondary",className:"h-6 w-6 p-0",onClick:()=>window.open(s.url,"_blank"),children:e.jsx(z,{className:"w-3 h-3"})}),e.jsx(a,{size:"sm",variant:"secondary",className:"h-6 w-6 p-0",onClick:()=>m(s),children:e.jsx(R,{className:"w-3 h-3"})})]}),e.jsxs("div",{className:"absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white p-2 opacity-0 group-hover:opacity-100 transition-opacity",children:[e.jsx("p",{className:"text-xs font-medium truncate",title:s.original_filename,children:s.original_filename}),e.jsxs("div",{className:"flex justify-between items-center text-xs text-gray-300",children:[e.jsx("span",{children:s.formatted_size}),s.width&&s.height&&e.jsxs("span",{children:[s.width," × ",s.height]})]})]})]}):e.jsx(S,{children:e.jsx(C,{className:"p-4",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0",children:c(s.mime_type)?e.jsx("img",{src:s.url||"/placeholder.svg",alt:s.alt_text||s.original_filename,className:"w-full h-full object-cover",onError:r=>{console.error("Failed to load image:",s.url,"Error:",r),console.error("Image element:",r.currentTarget),r.currentTarget.style.display="none"},onLoad:()=>{console.log("Image loaded successfully:",s.url)},onLoadStart:()=>{console.log("Image load started:",s.url)}}):e.jsx("div",{className:"w-full h-full flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-lg",children:"📄"}),e.jsx("div",{className:"text-xs text-gray-500 uppercase",children:s.mime_type.split("/")[1]})]})})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"font-medium truncate",children:s.original_filename}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-500 mt-1",children:[e.jsx("span",{children:s.formatted_size}),s.width&&s.height&&e.jsxs("span",{children:[s.width," × ",s.height]}),e.jsxs("span",{children:["Uploaded by ",s.uploader.name]})]}),s.title&&e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:s.title})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(a,{size:"sm",variant:"outline",onClick:()=>o(s),children:e.jsx(O,{className:"w-4 h-4"})}),e.jsx(a,{size:"sm",variant:"outline",onClick:()=>window.open(s.url,"_blank"),children:e.jsx(z,{className:"w-4 h-4"})}),e.jsx(a,{size:"sm",variant:"outline",onClick:()=>m(s),children:e.jsx(R,{className:"w-4 h-4"})})]})]})})})}export{Ze as default};
