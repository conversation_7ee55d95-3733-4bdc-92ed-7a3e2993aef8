import{J,r as g,j as e,Q as W,t as P,S as u}from"./app-J5EqS6dS.js";import{B as l}from"./smartphone-GGiwNneF.js";import{C as f,a as I,b as K,c as v}from"./card-9XCADs-4.js";import{B as c}from"./badge-BucYuCBs.js";import{S as N,a as p,b as w,c as b,d as n}from"./select-CIhY0l9J.js";import{A as O,G as X}from"./app-layout-ox1kAwY6.js";import{a as Z,H as E,t as q}from"./ImpersonationBanner-CYn5eDk6.js";import{g as M,b as T}from"./category-utils-DblfPn34.js";import{A as H}from"./Watermark-BujLnmGI.js";import{P as y}from"./CompatibleModelsProtection-BfRsG4tU.js";import{L as ee}from"./list-CNjrM85i.js";import{S as se}from"./sliders-horizontal-UvqkUz7X.js";import{C as ae}from"./chevron-left-C6ZNA5qQ.js";import{S as Y}from"./search-DBK6jUoc.js";import{E as D}from"./eye-D-fsmYB2.js";import{P as te}from"./package-CoyvngX8.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";import"./map-pin-BdPUntxP.js";import"./hard-drive-BTn_ba7c.js";import"./circle-ButWjt_D.js";function Ye({results:i,filters:m,applied_filters:x,query:F,remaining_searches:z}){var R,B;const{auth:k}=J().props,[h,A]=g.useState("grid"),[L,G]=g.useState(!1),[S,o]=g.useState(new Set),[C,$]=g.useState(()=>{if(!k.user||!i.data)return new Set;const s=i.data.filter(r=>r.is_favorited).map(r=>r.id);return new Set(s)}),_=s=>{const r=new URLSearchParams(window.location.search);r.set("page",s.toString()),u.get(window.location.pathname+"?"+r.toString())},j=(s,r)=>{const a=new URLSearchParams(window.location.search);r&&r!=="all"?a.set(s,r):a.delete(s),a.delete("page"),u.get(window.location.pathname+"?"+a.toString())},V=s=>{if(!k.user){q.error("Please log in to manage favorites",{description:"You need to be logged in to save favorites."});return}if(S.has(s))return;const r=C.has(s);o(a=>new Set(a).add(s)),r?u.delete(route("dashboard.remove-favorite"),{data:{type:"part",id:s}},{onSuccess:()=>{$(a=>{const t=new Set(a);return t.delete(s),t}),o(a=>{const t=new Set(a);return t.delete(s),t})},onError:a=>{o(t=>{const d=new Set(t);return d.delete(s),d})}}):u.post(route("dashboard.add-favorite"),{type:"part",id:s},{onSuccess:()=>{$(a=>new Set(a).add(s)),o(a=>{const t=new Set(a);return t.delete(s),t})},onError:a=>{o(t=>{const d=new Set(t);return d.delete(s),d})}})},Q=({part:s})=>{const r=S.has(s.id),a=C.has(s.id);return e.jsxs("div",{className:"relative",children:[e.jsx(f,{className:"hover:shadow-lg transition-shadow",children:e.jsxs(v,{className:"p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-semibold text-lg text-gray-900 mb-1",children:s.name}),s.part_number&&e.jsxs("p",{className:"text-sm text-gray-600 mb-1",children:["Part #: ",s.part_number]}),s.manufacturer&&e.jsxs("p",{className:"text-sm text-gray-600 mb-2",children:["by ",s.manufacturer]})]}),e.jsx(l,{variant:"ghost",size:"sm",onClick:()=>V(s.id),disabled:r,className:`${a?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-500"} transition-colors`,children:e.jsx(E,{className:`w-4 h-4 ${a?"fill-current":""} ${r?"animate-pulse":""}`})})]}),e.jsxs("div",{className:"mb-3",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[(()=>{const t=M(s.category.name);return e.jsx(t,{className:"w-4 h-4 text-gray-600"})})(),e.jsx(c,{variant:"outline",className:`${T(s.category.name,"badge")} font-medium border-2`,children:s.category.name})]}),s.description&&e.jsx("p",{className:"text-sm text-gray-600 line-clamp-2",children:s.description})]}),s.models.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"text-xs text-gray-500 mb-1",children:"Compatible with:"}),e.jsxs("div",{className:"flex flex-wrap gap-1",children:[s.models.slice(0,3).map(t=>e.jsx(y,{children:e.jsxs(c,{variant:"secondary",className:"text-xs",children:[t.brand.name," ",t.name]})},t.id)),s.models.length>3&&e.jsx(y,{children:e.jsxs(c,{variant:"secondary",className:"text-xs",children:["+",s.models.length-3," more"]})})]})]}),e.jsx("div",{className:"flex justify-between items-center",children:e.jsx(P,{href:route("parts.show",s.slug||s.id),children:e.jsxs(l,{size:"sm",children:[e.jsx(D,{className:"w-4 h-4 mr-2"}),"View Details"]})})})]})}),e.jsx(H,{})]})},U=({part:s})=>{const r=S.has(s.id),a=C.has(s.id);return e.jsxs("div",{className:"relative",children:[e.jsx(f,{children:e.jsx(v,{className:"p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center",children:e.jsx(te,{className:"w-8 h-8 text-gray-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-semibold text-lg text-gray-900 mb-1",children:s.name}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-600 mb-2",children:[s.part_number&&e.jsxs("span",{children:["Part #: ",s.part_number]}),s.manufacturer&&e.jsxs("span",{children:["by ",s.manufacturer]}),e.jsxs("div",{className:"flex items-center gap-1",children:[(()=>{const t=M(s.category.name);return e.jsx(t,{className:"w-3 h-3 text-gray-500"})})(),e.jsx(c,{variant:"outline",className:`${T(s.category.name,"badge")} text-xs font-medium border`,children:s.category.name})]})]}),s.description&&e.jsx("p",{className:"text-sm text-gray-600 mb-2 line-clamp-1",children:s.description}),s.models.length>0&&e.jsxs("div",{className:"flex flex-wrap gap-1",children:[s.models.slice(0,5).map(t=>e.jsx(y,{children:e.jsxs(c,{variant:"secondary",className:"text-xs",children:[t.brand.name," ",t.name]})},t.id)),s.models.length>5&&e.jsx(y,{children:e.jsxs(c,{variant:"secondary",className:"text-xs",children:["+",s.models.length-5," more"]})})]})]})]})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(l,{variant:"ghost",size:"sm",onClick:()=>V(s.id),disabled:r,className:`${a?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-500"} transition-colors`,children:e.jsx(E,{className:`w-4 h-4 ${a?"fill-current":""} ${r?"animate-pulse":""}`})}),e.jsx(P,{href:route("parts.show",s.slug||s.id),children:e.jsxs(l,{size:"sm",children:[e.jsx(D,{className:"w-4 h-4 mr-2"}),"View Details"]})})]})]})})}),e.jsx(H,{})]})};return e.jsxs(O,{children:[e.jsx(W,{title:`Search Results: ${F}`}),e.jsx("div",{className:"py-12",children:e.jsxs("div",{className:"max-w-7xl mx-auto sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Search Results"}),e.jsxs("p",{className:"text-gray-600",children:[i.total,' results for "',F,'"',z!==-1&&e.jsxs("span",{className:"ml-2",children:["• ",z," searches remaining today"]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(l,{variant:h==="grid"?"default":"outline",size:"sm",onClick:()=>A("grid"),children:e.jsx(X,{className:"w-4 h-4"})}),e.jsx(l,{variant:h==="list"?"default":"outline",size:"sm",onClick:()=>A("list"),children:e.jsx(ee,{className:"w-4 h-4"})}),e.jsxs(l,{variant:"outline",size:"sm",onClick:()=>G(!L),children:[e.jsx(se,{className:"w-4 h-4 mr-2"}),"Filters"]})]})]}),L&&e.jsxs(f,{className:"mb-6",children:[e.jsx(I,{children:e.jsx(K,{children:"Filters"})}),e.jsx(v,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs(N,{value:x.category_id||"all",onValueChange:s=>j("category_id",s),children:[e.jsx(p,{children:e.jsx(w,{placeholder:"Category"})}),e.jsxs(b,{children:[e.jsx(n,{value:"all",children:"All Categories"}),m.categories.map(s=>e.jsx(n,{value:s.id.toString(),children:s.name},s.id))]})]}),e.jsxs(N,{value:x.brand_id||"all",onValueChange:s=>j("brand_id",s),children:[e.jsx(p,{children:e.jsx(w,{placeholder:"Brand"})}),e.jsxs(b,{children:[e.jsx(n,{value:"all",children:"All Brands"}),m.brands.map(s=>e.jsx(n,{value:s.id.toString(),children:s.name},s.id))]})]}),e.jsxs(N,{value:x.manufacturer||"all",onValueChange:s=>j("manufacturer",s),children:[e.jsx(p,{children:e.jsx(w,{placeholder:"Manufacturer"})}),e.jsxs(b,{children:[e.jsx(n,{value:"all",children:"All Manufacturers"}),(R=m.manufacturers)==null?void 0:R.filter(s=>s&&s.trim()!=="").map(s=>e.jsx(n,{value:s,children:s},s))]})]}),e.jsxs(N,{value:x.release_year||"all",onValueChange:s=>j("release_year",s),children:[e.jsx(p,{children:e.jsx(w,{placeholder:"Year"})}),e.jsxs(b,{children:[e.jsx(n,{value:"all",children:"All Years"}),(B=m.release_years)==null?void 0:B.filter(s=>s&&s.toString().trim()!=="").map(s=>e.jsx(n,{value:s.toString(),children:s},s))]})]})]})})]}),i.data.length>0?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:h==="grid"?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8":"space-y-4 mb-8",children:i.data.map(s=>h==="grid"?e.jsx(Q,{part:s},s.id):e.jsx(U,{part:s},s.id))}),i.last_page>1&&e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm text-gray-600",children:["Showing ",i.from," to ",i.to," of ",i.total," results"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(l,{variant:"outline",size:"sm",onClick:()=>_(i.current_page-1),disabled:i.current_page===1,children:[e.jsx(ae,{className:"w-4 h-4"}),"Previous"]}),Array.from({length:Math.min(5,i.last_page)},(s,r)=>{const a=r+1;return e.jsx(l,{variant:a===i.current_page?"default":"outline",size:"sm",onClick:()=>_(a),children:a},a)}),e.jsxs(l,{variant:"outline",size:"sm",onClick:()=>_(i.current_page+1),disabled:i.current_page===i.last_page,children:["Next",e.jsx(Z,{className:"w-4 h-4"})]})]})]})]}):e.jsx(f,{children:e.jsxs(v,{className:"text-center py-12",children:[e.jsx(Y,{className:"w-16 h-16 mx-auto mb-4 text-gray-300"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No results found"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Try adjusting your search terms or filters"}),e.jsx(P,{href:route("search.index"),children:e.jsxs(l,{children:[e.jsx(Y,{className:"w-4 h-4 mr-2"}),"New Search"]})})]})})]})})]})}export{Ye as default};
