import{x as p,j as e,Q as h}from"./app-J5EqS6dS.js";import{I as m}from"./input-error-SDo-ayIc.js";import{T as d}from"./text-link-BJiDbWz5.js";import{B as g}from"./smartphone-GGiwNneF.js";import{C as f}from"./checkbox-CsTWa9ph.js";import{I as n}from"./input-Bo8dOn9p.js";import{L as o}from"./label-BlOrdc-X.js";import{A as j}from"./auth-layout-Do0a8FOS.js";import{M as v}from"./mail-CDon-vZy.js";import{L as y}from"./lock-Tx_yfI4R.js";import{L as N}from"./loader-circle-B1NtNhL1.js";import{A as w}from"./arrow-right-CCfGNWZ9.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-Ba8m9N9L.js";import"./index-BzZWUWqx.js";import"./index-CJpBU2i9.js";import"./check-C7SdgHPn.js";import"./database-s9JOA0jY.js";import"./shield-D9nQfigG.js";import"./zap-BcmHRR4K.js";function K({status:i,canResetPassword:c}){const{data:s,setData:t,post:x,processing:a,errors:l,reset:u}=p({email:"",password:"",remember:!1}),b=r=>{r.preventDefault(),x(route("login"),{onFinish:()=>u("password")})};return e.jsxs(j,{title:"Welcome Back",description:"Sign in to access your mobile parts database account",children:[e.jsx(h,{title:"Log in"}),i&&e.jsx("div",{className:"mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg",children:e.jsx("div",{className:"text-center text-sm font-medium text-green-700 dark:text-green-400",children:i})}),e.jsxs("form",{className:"space-y-6",onSubmit:b,children:[e.jsxs("div",{className:"space-y-5",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"email",className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Email Address"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(v,{className:"h-5 w-5 text-gray-400"})}),e.jsx(n,{id:"email",type:"email",required:!0,autoFocus:!0,tabIndex:1,autoComplete:"email",value:s.email,onChange:r=>t("email",r.target.value),placeholder:"Enter your email address",className:"pl-10 h-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:focus:border-blue-400",disabled:a})]}),e.jsx(m,{message:l.email})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(o,{htmlFor:"password",className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Password"}),c&&e.jsx(d,{href:route("password.request"),className:"text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300",tabIndex:5,children:"Forgot password?"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(y,{className:"h-5 w-5 text-gray-400"})}),e.jsx(n,{id:"password",type:"password",required:!0,tabIndex:2,autoComplete:"current-password",value:s.password,onChange:r=>t("password",r.target.value),placeholder:"Enter your password",className:"pl-10 h-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:focus:border-blue-400",disabled:a})]}),e.jsx(m,{message:l.password})]}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(f,{id:"remember",name:"remember",checked:s.remember,onClick:()=>t("remember",!s.remember),tabIndex:3,className:"border-gray-300 text-blue-600 focus:ring-blue-500"}),e.jsx(o,{htmlFor:"remember",className:"text-sm text-gray-700 dark:text-gray-300",children:"Remember me"})]})}),e.jsx(g,{type:"submit",className:"w-full h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]",tabIndex:4,disabled:a,children:a?e.jsxs(e.Fragment,{children:[e.jsx(N,{className:"h-5 w-5 animate-spin mr-2"}),"Signing In..."]}):e.jsxs(e.Fragment,{children:["Sign In",e.jsx(w,{className:"ml-2 h-5 w-5"})]})})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("div",{className:"w-full border-t border-gray-300 dark:border-gray-600"})}),e.jsx("div",{className:"relative flex justify-center text-sm",children:e.jsx("span",{className:"px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400",children:"Don't have an account?"})})]}),e.jsx("div",{className:"text-center",children:e.jsx(d,{href:route("register"),tabIndex:6,className:"text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium",children:"Create your account"})})]})]})}export{K as default};
