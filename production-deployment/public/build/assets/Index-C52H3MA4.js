import{r as R,j as e,Q as A,t as _,S as i}from"./app-J5EqS6dS.js";import{C as l,c,a as u,b as g,d as I}from"./card-9XCADs-4.js";import{B as n}from"./smartphone-GGiwNneF.js";import{B as N}from"./badge-BucYuCBs.js";import{S as f,a as y,b,c as v,d}from"./select-CIhY0l9J.js";import{A as T,B as M,M as E,C as F,c as $}from"./app-layout-ox1kAwY6.js";import{C as D}from"./check-C7SdgHPn.js";import{B as x}from"./ImpersonationBanner-CYn5eDk6.js";import{C as L}from"./circle-ButWjt_D.js";import{C as w}from"./circle-check-big-DOFoatRy.js";import{E as U}from"./eye-D-fsmYB2.js";import{T as V,I as z}from"./triangle-alert-BW76NKO9.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./search-DBK6jUoc.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";const O=[{title:"Notifications",href:"/notifications"}],H=a=>{switch(a){case"info":return e.jsx(z,{className:"w-5 h-5 text-blue-500"});case"warning":return e.jsx(V,{className:"w-5 h-5 text-yellow-500"});case"success":return e.jsx($,{className:"w-5 h-5 text-green-500"});case"error":return e.jsx(F,{className:"w-5 h-5 text-red-500"});case"announcement":return e.jsx(E,{className:"w-5 h-5 text-purple-500"});default:return e.jsx(x,{className:"w-5 h-5 text-gray-500"})}},Q=a=>{switch(a){case"info":return"bg-blue-100 text-blue-800";case"warning":return"bg-yellow-100 text-yellow-800";case"success":return"bg-green-100 text-green-800";case"error":return"bg-red-100 text-red-800";case"announcement":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}};function be({notifications:a,stats:r,filters:o,notification_types:C}){const[S,h]=R.useState(!1),j=(s,t)=>{const m=new URLSearchParams(window.location.search);t==="all"||t===""?m.delete(s):m.set(s,t),i.get(route("notifications.index"),Object.fromEntries(m))},k=async s=>{try{await i.post(route("notifications.mark-read",s))}catch(t){console.error("Failed to mark notification as read:",t)}},B=async()=>{if(r.unread!==0){h(!0);try{await i.post(route("notifications.mark-all-read"))}catch(s){console.error("Failed to mark all notifications as read:",s)}finally{h(!1)}}},p=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return e.jsxs(T,{breadcrumbs:O,children:[e.jsx(A,{title:"Notifications"}),e.jsxs("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Notifications"}),e.jsx("p",{className:"text-gray-600",children:"Stay updated with important messages and announcements"})]}),r.unread>0&&e.jsxs(n,{onClick:B,disabled:S,variant:"outline",children:[e.jsx(D,{className:"w-4 h-4 mr-2"}),"Mark All Read"]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsx(l,{children:e.jsx(c,{className:"p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(x,{className:"w-8 h-8 text-blue-500"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:r.total})]})]})})}),e.jsx(l,{children:e.jsx(c,{className:"p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(L,{className:"w-8 h-8 text-orange-500"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Unread"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:r.unread})]})]})})}),e.jsx(l,{children:e.jsx(c,{className:"p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(w,{className:"w-8 h-8 text-green-500"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Read"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:r.read})]})]})})}),e.jsx(l,{children:e.jsx(c,{className:"p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(x,{className:"w-8 h-8 text-purple-500"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Recent (7 days)"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:r.recent})]})]})})})]}),e.jsxs(l,{children:[e.jsx(u,{children:e.jsx(g,{children:"Filters"})}),e.jsx(c,{children:e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Type"}),e.jsxs(f,{value:o.type,onValueChange:s=>j("type",s),children:[e.jsx(y,{children:e.jsx(b,{placeholder:"Select type"})}),e.jsx(v,{children:Object.entries(C).map(([s,t])=>e.jsx(d,{value:s,children:t},s))})]})]}),e.jsxs("div",{className:"flex-1",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),e.jsxs(f,{value:o.status,onValueChange:s=>j("status",s),children:[e.jsx(y,{children:e.jsx(b,{placeholder:"Select status"})}),e.jsxs(v,{children:[e.jsx(d,{value:"all",children:"All Status"}),e.jsx(d,{value:"unread",children:"Unread"}),e.jsx(d,{value:"read",children:"Read"})]})]})]})]})})]}),e.jsxs(l,{children:[e.jsxs(u,{children:[e.jsx(g,{children:"Your Notifications"}),e.jsx(I,{children:a.meta.total>0?`Showing ${a.meta.from} to ${a.meta.to} of ${a.meta.total} notifications`:"No notifications found"})]}),e.jsx(c,{children:a.data.length>0?e.jsx("div",{className:"space-y-4",children:a.data.map(s=>e.jsx("div",{className:`p-4 border rounded-lg transition-colors hover:bg-gray-50 ${s.read_at?"bg-white border-gray-200":"bg-blue-50 border-blue-200"}`,children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-start space-x-3 flex-1",children:[H(s.type),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-900 truncate",children:s.title}),e.jsx(N,{className:`text-xs ${Q(s.type)}`,children:s.type}),!s.read_at&&e.jsx(N,{variant:"secondary",className:"text-xs",children:"New"})]}),e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:s.message}),e.jsxs("div",{className:"flex items-center text-xs text-gray-500 space-x-4",children:[e.jsx("span",{children:p(s.created_at)}),s.sentBy&&e.jsxs("span",{children:["From: ",s.sentBy.name]}),s.read_at&&e.jsxs("span",{className:"flex items-center",children:[e.jsx(U,{className:"w-3 h-3 mr-1"}),"Read ",p(s.read_at)]})]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2 ml-4",children:[e.jsx(_,{href:route("notifications.show",s.id),children:e.jsx(n,{variant:"outline",size:"sm",children:"View"})}),!s.read_at&&e.jsx(n,{variant:"ghost",size:"sm",onClick:()=>k(s.id),children:e.jsx(w,{className:"w-4 h-4"})})]})]})},s.id))}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx(M,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No notifications"}),e.jsx("p",{className:"text-gray-600",children:"You don't have any notifications yet."})]})})]}),a.meta.last_page>1&&e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"flex space-x-1",children:a.links.map((s,t)=>e.jsx(n,{variant:s.active?"default":"outline",size:"sm",disabled:!s.url,onClick:()=>s.url&&i.get(s.url),dangerouslySetInnerHTML:{__html:s.label}},t))})})]})]})}export{be as default};
