function l(){var s,a,o;const e=(s=document.querySelector('meta[name="csrf-token"]'))==null?void 0:s.getAttribute("content");if(e)return e;const r=(a=document.querySelector('input[name="_token"]'))==null?void 0:a.getAttribute("value");if(r)return r;const t=(o=window.Laravel)==null?void 0:o.csrfToken;return t||""}function h(e){return e.length>=40}function w(e){return{"Content-Type":"application/json",Accept:"application/json","X-CSRF-TOKEN":e,"X-Requested-With":"XMLHttpRequest"}}async function p(e){const r=e.headers.get("content-type");if(!r||!r.includes("application/json"))throw await e.text(),e.status===419?new Error("CSRF token mismatch"):e.status===401?new Error("Authentication required. Please log in and try again."):e.status===403?new Error("Access denied. Please check your permissions."):e.status===422?new Error("Invalid data provided. Please check your input and try again."):e.status===503?new Error("Service temporarily unavailable. Please try again later or use another payment method."):e.status>=500?new Error("Server error. Please try again later."):new Error(`Request failed (${e.status}). Please try again.`);const t=await e.json();if(!e.ok)throw t.development_mode?(console.warn("Development Mode Error:",{error:t.error,help:t.help,documentation:t.documentation,current_price_id:t.current_price_id,plan_name:t.plan_name}),t.current_price_id&&t.plan_name?new Error(`Development Mode: Placeholder price IDs detected for "${t.plan_name}" plan.

Help: ${t.help||"Configure real Paddle price IDs in your dashboard."}`):new Error(`Development Mode: ${t.error}

Help: ${t.help||"Configure real Paddle credentials for testing."}`)):t.error&&t.error.includes("Coinbase Commerce is not configured")?new Error("Cryptocurrency payments are not configured. Please contact support or use another payment method."):e.status===419||t.error&&t.error.toLowerCase().includes("csrf")?new Error("CSRF token mismatch"):new Error(t.error||t.message||`Request failed with status ${e.status}`);return t}async function m(e,r,t,s){const a=l();if(!a)throw new Error("CSRF token not found. Please refresh the page and try again.");if(!h(a))throw new Error("Invalid CSRF token. Please refresh the page and try again.");const o=new AbortController,i=setTimeout(()=>{o.abort()},3e4);try{const n=await fetch(e,{method:"POST",headers:w(a),body:JSON.stringify({plan_id:r,pricing_plan_id:r,billing_cycle:t}),signal:o.signal});if(clearTimeout(i),!n.ok)throw new Error(`HTTP ${n.status}: ${n.statusText}`);const c=await p(n);if(!c.checkout_url&&!c.hosted_url)throw new Error("No checkout URL received from server");return c}catch(n){throw clearTimeout(i),n.name==="AbortError"?new Error("Request timed out. Please check your connection and try again."):n}}async function u(){try{const e=await fetch("/csrf-token",{method:"GET",headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"}});if(e.ok){const r=await e.json();if(r.csrf_token){const t=document.querySelector('meta[name="csrf-token"]');return t&&t.setAttribute("content",r.csrf_token),r.csrf_token}}}catch(e){console.error("Failed to refresh CSRF token:",e)}throw new Error("Failed to refresh CSRF token")}async function k(){try{const e=l();return e?(await fetch("/csrf-token",{method:"GET",headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":e}})).ok:!1}catch{return!1}}async function y(){try{const e=l(),r=await fetch("/csrf-fix",{method:"POST",headers:{Accept:"application/json","Content-Type":"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":e}});if(r.ok){const t=await r.json();if(t.success&&t.new_token){const s=document.querySelector('meta[name="csrf-token"]');return s&&s.setAttribute("content",t.new_token),t.new_token}}}catch(e){console.error("Failed to fix CSRF issues:",e)}throw new Error("Failed to fix CSRF issues")}async function g(e,r,t,s,a=1){let o=null;for(let i=0;i<=a;i++)try{return await m(e,r,t,s)}catch(n){o=n instanceof Error?n:new Error("Unknown error");const c=o.message.includes("CSRF token mismatch")||o.message.includes("Session expired")||o.message.includes("419");if(i<a&&c)try{if(await k())await u();else try{await y()}catch(d){console.warn("CSRF fix failed, trying simple refresh:",d),await u()}continue}catch(f){console.error("Failed to refresh CSRF token:",f);break}break}throw o||new Error("Checkout request failed")}function E(e,r){console.error(`${e} payment error:`,r)}export{g as c,l as g,E as l,u as r,h as v};
