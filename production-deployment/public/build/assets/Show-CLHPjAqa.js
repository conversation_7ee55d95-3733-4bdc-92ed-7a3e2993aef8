import{j as e,Q as u,t as r}from"./app-J5EqS6dS.js";import{C as c,c as d,a as j,b as p,d as f}from"./card-9XCADs-4.js";import{B as a}from"./smartphone-GGiwNneF.js";import{B as g}from"./badge-BucYuCBs.js";import{A as b,D as N,C as w}from"./app-layout-ox1kAwY6.js";import{A as n}from"./arrow-left-D4U9AVF9.js";import{F as y}from"./ImpersonationBanner-CYn5eDk6.js";import{C as v}from"./calendar-B-u_QN2Q.js";import{U as C}from"./user-DCnDRzMf.js";import{F as i}from"./file-text-Dx6bYLtE.js";import{T as P}from"./triangle-alert-BW76NKO9.js";import{D}from"./download-fvx_BKiV.js";import{C as m}from"./circle-check-big-DOFoatRy.js";import{C as B}from"./clock-Brl7_5s7.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./users-RYmOyic9.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./search-DBK6jUoc.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./eye-D-fsmYB2.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";const R=[{title:"Payment Requests",href:"/payment-requests"},{title:"Request Details",href:"#"}],A=s=>{switch(s){case"pending":return e.jsx(B,{className:"w-8 h-8 text-yellow-500"});case"approved":return e.jsx(m,{className:"w-8 h-8 text-green-500"});case"rejected":return e.jsx(w,{className:"w-8 h-8 text-red-500"});case"processed":return e.jsx(m,{className:"w-8 h-8 text-blue-500"});default:return e.jsx(i,{className:"w-8 h-8 text-gray-500"})}},k=s=>{switch(s){case"pending":return"bg-yellow-100 text-yellow-800";case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";case"processed":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},S=s=>{switch(s){case"pending":return"Your payment request is being reviewed by our team.";case"approved":return"Your payment has been approved and your subscription is being activated.";case"rejected":return"Your payment request has been rejected. Please check the admin notes below.";case"processed":return"Your payment has been processed and your subscription is now active.";default:return"Payment request status unknown."}},Y=s=>{switch(s){case"bank_transfer":return"Bank Transfer";case"mobile_money":return"Mobile Money";case"cash_deposit":return"Cash Deposit";case"other":return"Other";default:return s}};function de({payment_request:s,can_download_proof:o}){const l=t=>new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),x=(t,h)=>`${h} ${parseFloat(t).toFixed(2)}`;return e.jsxs(b,{breadcrumbs:R,children:[e.jsx(u,{title:`Payment Request #${s.id}`}),e.jsxs("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(r,{href:route("payment-requests.index"),children:e.jsxs(a,{variant:"outline",size:"sm",children:[e.jsx(n,{className:"w-4 h-4 mr-2"}),"Back to Payment Requests"]})}),e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900",children:["Payment Request #",s.id]}),e.jsx("p",{className:"text-gray-600",children:"View payment request details and status"})]})]})}),e.jsx(c,{className:`border-2 ${s.status==="approved"?"border-green-200 bg-green-50":s.status==="rejected"?"border-red-200 bg-red-50":s.status==="processed"?"border-blue-200 bg-blue-50":"border-yellow-200 bg-yellow-50"}`,children:e.jsx(d,{className:"p-6",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[A(s.status),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:s.status.charAt(0).toUpperCase()+s.status.slice(1)}),e.jsx(g,{className:`${k(s.status)}`,children:s.status})]}),e.jsx("p",{className:"text-gray-700",children:S(s.status)})]})]})})})}),e.jsxs(c,{className:"max-w-4xl",children:[e.jsxs(j,{children:[e.jsxs(p,{className:"flex items-center",children:[e.jsx(y,{className:"w-5 h-5 mr-2"}),"Payment Details"]}),e.jsx(f,{children:"Information about your payment request"})]}),e.jsxs(d,{children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsxs("h4",{className:"text-sm font-medium text-gray-900 mb-3 flex items-center",children:[e.jsx(N,{className:"w-4 h-4 mr-2"}),"Payment Information"]}),e.jsxs("div",{className:"bg-white border rounded-lg p-4 space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Amount:"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:x(s.amount,s.currency)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Payment Method:"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:Y(s.payment_method)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Subscription Plan:"}),e.jsx("span",{className:"text-sm font-medium text-gray-900 capitalize",children:s.subscription_plan})]})]})]}),e.jsxs("div",{children:[e.jsxs("h4",{className:"text-sm font-medium text-gray-900 mb-3 flex items-center",children:[e.jsx(v,{className:"w-4 h-4 mr-2"}),"Timeline"]}),e.jsxs("div",{className:"bg-white border rounded-lg p-4 space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Requested:"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:l(s.requested_at)})]}),s.approved_at&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:s.status==="approved"?"Approved:":s.status==="rejected"?"Rejected:":"Processed:"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:l(s.approved_at)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Request ID:"}),e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["#",s.id]})]})]})]})]}),s.approvedBy&&e.jsxs("div",{className:"mt-6",children:[e.jsxs("h4",{className:"text-sm font-medium text-gray-900 mb-3 flex items-center",children:[e.jsx(C,{className:"w-4 h-4 mr-2"}),"Reviewed By"]}),e.jsxs("div",{className:"bg-white border rounded-lg p-4",children:[e.jsx("p",{className:"font-medium text-gray-900",children:s.approvedBy.name}),e.jsx("p",{className:"text-sm text-gray-600",children:s.approvedBy.email})]})]}),s.notes&&e.jsxs("div",{className:"mt-6",children:[e.jsxs("h4",{className:"text-sm font-medium text-gray-900 mb-3 flex items-center",children:[e.jsx(i,{className:"w-4 h-4 mr-2"}),"Your Notes"]}),e.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:e.jsx("p",{className:"text-sm text-gray-700 whitespace-pre-wrap",children:s.notes})})]}),s.admin_notes&&e.jsxs("div",{className:"mt-6",children:[e.jsxs("h4",{className:"text-sm font-medium text-gray-900 mb-3 flex items-center",children:[e.jsx(P,{className:"w-4 h-4 mr-2"}),"Admin Notes"]}),e.jsx("div",{className:`rounded-lg p-4 ${s.status==="rejected"?"bg-red-50 border border-red-200":"bg-blue-50 border border-blue-200"}`,children:e.jsx("p",{className:`text-sm whitespace-pre-wrap ${s.status==="rejected"?"text-red-700":"text-blue-700"}`,children:s.admin_notes})})]}),s.proof_of_payment&&e.jsxs("div",{className:"mt-6",children:[e.jsxs("h4",{className:"text-sm font-medium text-gray-900 mb-3 flex items-center",children:[e.jsx(i,{className:"w-4 h-4 mr-2"}),"Proof of Payment"]}),e.jsx("div",{className:"bg-white border rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Payment Receipt"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Uploaded with your request"})]}),o&&e.jsx(r,{href:route("payment-requests.download-proof",s.id),children:e.jsxs(a,{variant:"outline",size:"sm",children:[e.jsx(D,{className:"w-4 h-4 mr-2"}),"Download"]})})]})})]}),e.jsx("div",{className:"mt-6 pt-6 border-t",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(r,{href:route("payment-requests.index"),children:e.jsxs(a,{variant:"outline",children:[e.jsx(n,{className:"w-4 h-4 mr-2"}),"Back to All Requests"]})}),s.status==="pending"&&e.jsx("div",{className:"text-sm text-gray-600",children:"Your request is being reviewed. You'll be notified once it's processed."})]})})]})]})]})]})}export{de as default};
