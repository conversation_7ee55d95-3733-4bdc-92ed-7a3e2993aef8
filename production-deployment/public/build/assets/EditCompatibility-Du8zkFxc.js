import{r as m,x as K,j as e,Q as W,t as Z,S as v}from"./app-J5EqS6dS.js";import{B as d}from"./smartphone-GGiwNneF.js";import{C as p,c as u,a as $,b as B,d as ee}from"./card-9XCADs-4.js";import{I as C}from"./input-Bo8dOn9p.js";import{L as b}from"./label-BlOrdc-X.js";import{S as se,a as te,b as ie,c as ae,d as F}from"./select-CIhY0l9J.js";import{S as L}from"./switch-yFNfZ5X-.js";import{T as le}from"./textarea-BDEiXlPH.js";import{B as re}from"./badge-BucYuCBs.js";import{C as D}from"./checkbox-CsTWa9ph.js";import{A as ce}from"./app-layout-ox1kAwY6.js";import{u as ne}from"./use-delete-confirmation-CFAJok5Z.js";import{P as oe,X as P,t as i}from"./ImpersonationBanner-CYn5eDk6.js";import{A as de}from"./arrow-left-D4U9AVF9.js";import{F as me}from"./filter-DKJvAZFg.js";import{S as he}from"./search-DBK6jUoc.js";import{T as xe}from"./trash-2-B3ZEh4hl.js";import{S as M}from"./square-pen-Bepbg6wc.js";import{C as pe}from"./check-C7SdgHPn.js";import{S as ue}from"./save-DfhL0V-C.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./eye-D-fsmYB2.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";function ss({part:n,allModels:a,showVerificationStatus:w=!0}){const[h,R]=m.useState(""),[N,V]=m.useState("all"),[y,O]=m.useState(!1),[f,_]=m.useState(null),[l,j]=m.useState([]),{showDeleteConfirmation:k}=ne(),{data:g,setData:x,put:Q,processing:q,reset:A}=K({compatibility_notes:"",is_verified:!1,display_type:"",display_size:"",location:""}),H=m.useMemo(()=>Array.from(new Map(a.map(s=>[s.brand.id,s.brand])).values()),[a]),o=m.useMemo(()=>a.filter(s=>{const t=h===""||s.name.toLowerCase().includes(h.toLowerCase())||s.brand.name.toLowerCase().includes(h.toLowerCase())||s.model_number&&s.model_number.toLowerCase().includes(h.toLowerCase()),r=N==="all"||s.brand.id.toString()===N,c=!y||s.is_compatible;return t&&r&&c}),[a,h,N,y]),I=s=>{_(s.id),x({compatibility_notes:s.compatibility_notes||"",is_verified:s.is_verified,display_type:s.display_type||"",display_size:s.display_size||"",location:s.location||""})},U=()=>{_(null),A()},X=s=>{Q(`/admin/parts/${n.id}/compatibility/${s}`,{onSuccess:()=>{i.success("Compatibility updated successfully."),_(null),A()},onError:()=>{i.error("Failed to update compatibility. Please try again.")}})},Y=s=>{s.is_compatible?k({title:`Remove compatibility with ${s.brand.name} ${s.name}?`,description:"This will remove the compatibility relationship between this part and the selected model. This action cannot be undone.",onConfirm:()=>{v.delete(`/admin/parts/${n.id}/compatibility/${s.id}`,{onSuccess:()=>{i.success("Model compatibility removed successfully.")},onError:()=>{i.error("Failed to remove compatibility. Please try again.")}})},onCancel:()=>{i.info("Removal cancelled")}}):v.post(`/admin/parts/${n.id}/compatibility`,{model_id:s.id.toString(),compatibility_notes:"",is_verified:!1},{onSuccess:()=>{i.success("Model compatibility added successfully.")},onError:()=>{i.error("Failed to add compatibility. Please try again.")}})},T=s=>{if(l.length===0){i.error("Please select models first.");return}if(s){const t=l.filter(r=>{const c=a.find(S=>S.id===r);return c&&!c.is_compatible});if(t.length===0){i.error("All selected models are already compatible.");return}v.post(`/admin/parts/${n.id}/compatibility/bulk`,{model_ids:t,compatibility_notes:"",is_verified:!1},{onSuccess:()=>{i.success(`${t.length} model compatibilities added successfully.`),j([])},onError:()=>{i.error("Failed to add bulk compatibility. Please try again.")}})}else{const t=l.filter(r=>{const c=a.find(S=>S.id===r);return c&&c.is_compatible});if(t.length===0){i.error("None of the selected models are currently compatible.");return}k({title:`Remove compatibility for ${t.length} models?`,description:"This will remove the compatibility relationships for all selected models. This action cannot be undone.",onConfirm:()=>{let r=0;t.forEach(c=>{v.delete(`/admin/parts/${n.id}/compatibility/${c}`,{onSuccess:()=>{r++,r===t.length&&(i.success(`${t.length} model compatibilities removed successfully.`),j([]))},onError:()=>{i.error("Some compatibilities failed to remove. Please try again.")}})})},onCancel:()=>{i.info("Bulk removal cancelled")}})}},G=s=>{j(t=>t.includes(s)?t.filter(r=>r!==s):[...t,s])},E=()=>{l.length===o.length?j([]):j(o.map(s=>s.id))},z=a.filter(s=>s.is_compatible).length,J=a.filter(s=>s.is_compatible&&s.is_verified).length;return e.jsxs(ce,{children:[e.jsx(W,{title:`Edit Compatibility - ${n.name} - Parts - Admin`}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(Z,{href:`/admin/parts/${n.id}/compatibility`,children:e.jsxs(d,{variant:"outline",size:"sm",children:[e.jsx(de,{className:"w-4 h-4 mr-2"}),"Back to Compatibility"]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Edit Compatibility"}),e.jsxs("p",{className:"text-muted-foreground",children:['Manage compatibility for "',n.name,'" in table view']})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsx(p,{children:e.jsxs(u,{className:"p-4",children:[e.jsx("div",{className:"text-2xl font-bold",children:a.length}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Total Models"})]})}),e.jsx(p,{children:e.jsxs(u,{className:"p-4",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:z}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Compatible"})]})}),e.jsx(p,{children:e.jsxs(u,{className:"p-4",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:J}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Verified"})]})}),e.jsx(p,{children:e.jsxs(u,{className:"p-4",children:[e.jsx("div",{className:"text-2xl font-bold text-orange-600",children:a.length-z}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Not Compatible"})]})})]}),e.jsxs(p,{children:[e.jsx($,{children:e.jsxs(B,{className:"flex items-center gap-2",children:[e.jsx(me,{className:"w-5 h-5"}),"Filters & Actions"]})}),e.jsx(u,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(b,{htmlFor:"search",children:"Search Models"}),e.jsxs("div",{className:"relative",children:[e.jsx(he,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),e.jsx(C,{id:"search",placeholder:"Search models...",value:h,onChange:s=>R(s.target.value),className:"pl-10"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(b,{htmlFor:"brand-filter",children:"Filter by Brand"}),e.jsxs(se,{value:N,onValueChange:V,children:[e.jsx(te,{children:e.jsx(ie,{placeholder:"All brands"})}),e.jsxs(ae,{children:[e.jsx(F,{value:"all",children:"All brands"}),H.map(s=>e.jsx(F,{value:s.id.toString(),children:e.jsxs("div",{className:"flex items-center gap-2",children:[s.logo_url&&e.jsx("img",{src:s.logo_url,alt:s.name,className:"w-4 h-4 object-contain"}),s.name]})},s.id))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(b,{children:"Show Compatible Only"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(L,{id:"compatible-only",checked:y,onCheckedChange:O}),e.jsx(b,{htmlFor:"compatible-only",className:"text-sm",children:y?"Compatible only":"All models"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(b,{children:"Bulk Actions"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(d,{variant:"outline",size:"sm",onClick:()=>T(!0),disabled:l.length===0,children:[e.jsx(oe,{className:"w-4 h-4 mr-1"}),"Add"]}),e.jsxs(d,{variant:"outline",size:"sm",onClick:()=>T(!1),disabled:l.length===0,className:"text-destructive hover:text-destructive",children:[e.jsx(xe,{className:"w-4 h-4 mr-1"}),"Remove"]})]})]})]})})]}),e.jsxs(p,{children:[e.jsxs($,{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(B,{className:"flex items-center gap-2",children:[e.jsx(M,{className:"w-5 h-5"}),"Models (",o.length,")"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("span",{className:"text-sm text-muted-foreground",children:[l.length," selected"]}),e.jsx(d,{variant:"outline",size:"sm",onClick:E,children:l.length===o.length?"Deselect All":"Select All"})]})]}),e.jsx(ee,{children:"Click on compatibility status to toggle, or use the edit button to modify details"})]}),e.jsx(u,{children:e.jsxs("div",{className:"overflow-x-auto",children:[e.jsxs("table",{className:"w-full border-collapse",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b",children:[e.jsx("th",{className:"text-left p-3 font-medium",children:e.jsx(D,{checked:l.length===o.length&&o.length>0,onCheckedChange:E})}),e.jsx("th",{className:"text-left p-3 font-medium",children:"Brand"}),e.jsx("th",{className:"text-left p-3 font-medium",children:"Model"}),e.jsx("th",{className:"text-left p-3 font-medium",children:"Model Number"}),e.jsx("th",{className:"text-left p-3 font-medium",children:"Release Year"}),e.jsx("th",{className:"text-left p-3 font-medium",children:"Compatible"}),w&&e.jsx("th",{className:"text-left p-3 font-medium",children:"Verified"}),e.jsx("th",{className:"text-left p-3 font-medium",children:"Display Type"}),e.jsx("th",{className:"text-left p-3 font-medium",children:"Display Size"}),e.jsx("th",{className:"text-left p-3 font-medium",children:"Location"}),e.jsx("th",{className:"text-left p-3 font-medium",children:"Notes"}),e.jsx("th",{className:"text-left p-3 font-medium",children:"Actions"})]})}),e.jsx("tbody",{children:o.map(s=>e.jsxs("tr",{className:"border-b hover:bg-muted/50",children:[e.jsx("td",{className:"p-3",children:e.jsx(D,{checked:l.includes(s.id),onCheckedChange:()=>G(s.id)})}),e.jsx("td",{className:"p-3",children:e.jsxs("div",{className:"flex items-center gap-2",children:[s.brand.logo_url&&e.jsx("img",{src:s.brand.logo_url,alt:s.brand.name,className:"w-6 h-6 object-contain"}),e.jsx("span",{className:"font-medium",children:s.brand.name})]})}),e.jsx("td",{className:"p-3",children:e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:s.name}),s.model_number&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s.model_number}),s.release_year&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s.release_year})]})}),e.jsx("td",{className:"p-3",children:e.jsx("span",{className:"text-sm",children:s.model_number||"-"})}),e.jsx("td",{className:"p-3",children:e.jsx("span",{className:"text-sm",children:s.release_year||"-"})}),e.jsx("td",{className:"p-3",children:e.jsx(d,{variant:"ghost",size:"sm",onClick:()=>Y(s),className:s.is_compatible?"text-green-600 hover:text-green-700":"text-gray-400 hover:text-gray-600",children:s.is_compatible?e.jsx(pe,{className:"w-4 h-4"}):e.jsx(P,{className:"w-4 h-4"})})}),w&&e.jsx("td",{className:"p-3",children:s.is_compatible&&e.jsx(re,{variant:s.is_verified?"default":"secondary",children:s.is_verified?"Verified":"Unverified"})}),e.jsx("td",{className:"p-3",children:f===s.id?e.jsx(C,{value:g.display_type,onChange:t=>x("display_type",t.target.value),placeholder:"Display type...",className:"text-sm"}):e.jsx("span",{className:"text-sm text-muted-foreground",children:s.display_type||(s.is_compatible,"-")})}),e.jsx("td",{className:"p-3",children:f===s.id?e.jsx(C,{value:g.display_size,onChange:t=>x("display_size",t.target.value),placeholder:"Display size...",className:"text-sm"}):e.jsx("span",{className:"text-sm text-muted-foreground",children:s.display_size||(s.is_compatible,"-")})}),e.jsx("td",{className:"p-3",children:f===s.id?e.jsx(C,{value:g.location,onChange:t=>x("location",t.target.value),placeholder:"Location...",className:"text-sm"}):e.jsx("span",{className:"text-sm text-muted-foreground",children:s.location||(s.is_compatible,"-")})}),e.jsx("td",{className:"p-3 max-w-xs",children:f===s.id?e.jsx(le,{value:g.compatibility_notes,onChange:t=>x("compatibility_notes",t.target.value),placeholder:"Compatibility notes...",rows:2,className:"text-sm"}):e.jsx("span",{className:"text-sm text-muted-foreground",children:s.compatibility_notes||(s.is_compatible?"No notes":"-")})}),e.jsx("td",{className:"p-3",children:e.jsx("div",{className:"flex items-center gap-1",children:s.is_compatible&&e.jsx(e.Fragment,{children:f===s.id?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center gap-1 mr-2",children:[e.jsx(L,{checked:g.is_verified,onCheckedChange:t=>x("is_verified",t)}),e.jsx("span",{className:"text-xs",children:"Verified"})]}),e.jsx(d,{variant:"outline",size:"sm",onClick:()=>X(s.id),disabled:q,children:e.jsx(ue,{className:"w-3 h-3"})}),e.jsx(d,{variant:"ghost",size:"sm",onClick:U,children:e.jsx(P,{className:"w-3 h-3"})})]}):e.jsx(d,{variant:"outline",size:"sm",onClick:()=>I(s),children:e.jsx(M,{className:"w-3 h-3"})})})})})]},s.id))})]}),o.length===0&&e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-muted-foreground",children:"No models found matching your filters."})})]})})]})]})})]})}export{ss as default};
