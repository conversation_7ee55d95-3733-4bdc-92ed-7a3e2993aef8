import{r as a}from"./app-J5EqS6dS.js";import{u as E}from"./smartphone-GGiwNneF.js";import{b as A}from"./index-D86BnqlV.js";function T(n,e){return a.useReducer((r,t)=>e[r][t]??r,n)}var P=n=>{const{present:e,children:r}=n,t=R(e),i=typeof r=="function"?r({present:t.isPresent}):a.Children.only(r),c=E(t.ref,v(i));return typeof r=="function"||t.isPresent?a.cloneElement(i,{ref:c}):null};P.displayName="Presence";function R(n){const[e,r]=a.useState(),t=a.useRef(null),i=a.useRef(n),c=a.useRef("none"),p=n?"mounted":"unmounted",[N,s]=T(p,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return a.useEffect(()=>{const o=l(t.current);c.current=N==="mounted"?o:"none"},[N]),A(()=>{const o=t.current,m=i.current;if(m!==n){const f=c.current,u=l(o);n?s("MOUNT"):u==="none"||(o==null?void 0:o.display)==="none"?s("UNMOUNT"):s(m&&f!==u?"ANIMATION_OUT":"UNMOUNT"),i.current=n}},[n,s]),A(()=>{if(e){let o;const m=e.ownerDocument.defaultView??window,d=u=>{const g=l(t.current).includes(u.animationName);if(u.target===e&&g&&(s("ANIMATION_END"),!i.current)){const O=e.style.animationFillMode;e.style.animationFillMode="forwards",o=m.setTimeout(()=>{e.style.animationFillMode==="forwards"&&(e.style.animationFillMode=O)})}},f=u=>{u.target===e&&(c.current=l(t.current))};return e.addEventListener("animationstart",f),e.addEventListener("animationcancel",d),e.addEventListener("animationend",d),()=>{m.clearTimeout(o),e.removeEventListener("animationstart",f),e.removeEventListener("animationcancel",d),e.removeEventListener("animationend",d)}}else s("ANIMATION_END")},[e,s]),{isPresent:["mounted","unmountSuspended"].includes(N),ref:a.useCallback(o=>{t.current=o?getComputedStyle(o):null,r(o)},[])}}function l(n){return(n==null?void 0:n.animationName)||"none"}function v(n){var t,i;let e=(t=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:t.get,r=e&&"isReactWarning"in e&&e.isReactWarning;return r?n.ref:(e=(i=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:i.get,r=e&&"isReactWarning"in e&&e.isReactWarning,r?n.props.ref:n.props.ref||n.ref)}export{P};
