import{r as v,x as O,j as e,Q as ee,S as j}from"./app-J5EqS6dS.js";import{C as t,a,b as i,c as r,d as m}from"./card-9XCADs-4.js";import{B as n}from"./smartphone-GGiwNneF.js";import{I as Y}from"./input-Bo8dOn9p.js";import{L as o}from"./label-BlOrdc-X.js";import{S as se}from"./switch-yFNfZ5X-.js";import{T as te,a as ae,b as u,c as f}from"./tabs-DZAL-HvD.js";import{B as d}from"./badge-BucYuCBs.js";import{K as w,A as y,g as x,h}from"./ImpersonationBanner-CYn5eDk6.js";import{A as ie,C as R}from"./app-layout-ox1kAwY6.js";import{S}from"./shield-D9nQfigG.js";import{S as G}from"./users-RYmOyic9.js";import{T as M}from"./triangle-alert-BW76NKO9.js";import{M as A}from"./mail-CDon-vZy.js";import{C as T}from"./circle-check-big-DOFoatRy.js";import{S as re}from"./send-CDQ6ON0R.js";import{C as le}from"./clock-Brl7_5s7.js";import{T as ce}from"./trash-2-B3ZEh4hl.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./index-Ba8m9N9L.js";import"./index-BzZWUWqx.js";import"./crown-UDSxMtlm.js";import"./user-DCnDRzMf.js";import"./search-DBK6jUoc.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./eye-D-fsmYB2.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./globe-zfFlVOSX.js";function Me({user_2fa_status:l,global_settings:c,recent_verifications:p,active_sessions:g}){const[C,D]=v.useState(!1),[q,N]=v.useState(!1),[K,F]=v.useState(!1),{data:k,setData:L,post:P,processing:_}=O({confirmation:""}),{data:E,setData:Q,post:W,processing:V,reset:B}=O({otp_code:""}),H=()=>{j.post("/admin/two-factor/enable")},U=s=>{s.preventDefault(),P("/admin/two-factor/disable",{onSuccess:()=>{L("confirmation","")}})},X=()=>{D(!0),j.post("/admin/two-factor/send-test",{},{onSuccess:()=>{N(!0)},onFinish:()=>D(!1)})},z=s=>{s.preventDefault(),W("/admin/two-factor/verify-test",{onSuccess:()=>{N(!1),B()}})},J=()=>{F(!0),j.post("/admin/two-factor/toggle-global",{enabled:!c.enabled},{onFinish:()=>F(!1)})},Z=()=>{confirm("Are you sure you want to clear all verification sessions? You will need to re-verify for protected actions.")&&j.post("/admin/two-factor/clear-sessions")},I=s=>s.replace(/_/g," ").replace(/\b\w/g,b=>b.toUpperCase()),$=s=>s.two_factor_enabled?s.is_locked_out?e.jsxs(d,{variant:"destructive",children:[e.jsx(R,{className:"h-3 w-3 mr-1"}),"Locked Out"]}):e.jsxs(d,{variant:"default",className:"bg-green-100 text-green-800",children:[e.jsx(T,{className:"h-3 w-3 mr-1"}),"Active"]}):e.jsx(d,{variant:"secondary",children:"Disabled"});return e.jsxs(ie,{children:[e.jsx(ee,{title:"Two-Factor Authentication"}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:"Two-Factor Authentication"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Secure your admin actions with email-based verification codes"})]})}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-4",children:[e.jsxs(t,{children:[e.jsxs(a,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(i,{className:"text-sm font-medium",children:"Your 2FA Status"}),e.jsx(S,{className:"h-4 w-4 text-blue-600"})]}),e.jsxs(r,{children:[e.jsx("div",{className:"text-2xl font-bold",children:l.two_factor_enabled?"Enabled":"Disabled"}),e.jsx("div",{className:"mt-2",children:$(l)})]})]}),e.jsxs(t,{children:[e.jsxs(a,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(i,{className:"text-sm font-medium",children:"Global Setting"}),e.jsx(G,{className:"h-4 w-4 text-purple-600"})]}),e.jsxs(r,{children:[e.jsx("div",{className:"text-2xl font-bold",children:c.enabled?"Required":"Optional"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"For all admin actions"})]})]}),e.jsxs(t,{children:[e.jsxs(a,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(i,{className:"text-sm font-medium",children:"Active Sessions"}),e.jsx(w,{className:"h-4 w-4 text-green-600"})]}),e.jsxs(r,{children:[e.jsx("div",{className:"text-2xl font-bold",children:g.length}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Verified actions"})]})]}),e.jsxs(t,{children:[e.jsxs(a,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(i,{className:"text-sm font-medium",children:"Recent Attempts"}),e.jsx(y,{className:"h-4 w-4 text-orange-600"})]}),e.jsxs(r,{children:[e.jsx("div",{className:"text-2xl font-bold",children:p.length}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Last 10 verifications"})]})]})]}),l.is_locked_out&&e.jsxs(x,{variant:"destructive",children:[e.jsx(M,{className:"h-4 w-4"}),e.jsxs(h,{children:["Your account is temporarily locked due to too many failed verification attempts. You can try again in ",l.lockout_remaining_minutes," minutes."]})]}),e.jsxs(te,{defaultValue:"settings",className:"space-y-4",children:[e.jsxs(ae,{className:"grid w-full grid-cols-4",children:[e.jsx(u,{value:"settings",children:"Settings"}),e.jsx(u,{value:"test",children:"Test"}),e.jsx(u,{value:"sessions",children:"Sessions"}),e.jsx(u,{value:"activity",children:"Activity"})]}),e.jsx(f,{value:"settings",className:"space-y-4",children:e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs(t,{children:[e.jsxs(a,{children:[e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(S,{className:"h-5 w-5"}),"Your 2FA Settings"]}),e.jsx(m,{children:"Enable or disable two-factor authentication for your account"})]}),e.jsx(r,{className:"space-y-4",children:l.two_factor_enabled?e.jsxs("div",{className:"space-y-4",children:[e.jsxs(x,{className:"border-green-200 bg-green-50",children:[e.jsx(T,{className:"h-4 w-4 text-green-600"}),e.jsx(h,{className:"text-green-800",children:"Two-factor authentication is enabled for your account."})]}),e.jsxs("form",{onSubmit:U,className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"confirmation",children:'Type "DISABLE_2FA" to confirm disabling'}),e.jsx(Y,{id:"confirmation",value:k.confirmation,onChange:s=>L("confirmation",s.target.value),placeholder:"DISABLE_2FA"})]}),e.jsx(n,{type:"submit",variant:"destructive",disabled:k.confirmation!=="DISABLE_2FA"||_,className:"w-full",children:_?"Disabling...":"Disable Two-Factor Authentication"})]})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs(x,{children:[e.jsx(A,{className:"h-4 w-4"}),e.jsx(h,{children:"When enabled, you'll receive verification codes via email for sensitive admin actions."})]}),e.jsxs(n,{onClick:H,className:"w-full",children:[e.jsx(S,{className:"h-4 w-4 mr-2"}),"Enable Two-Factor Authentication"]})]})})]}),e.jsxs(t,{children:[e.jsxs(a,{children:[e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(G,{className:"h-5 w-5"}),"Global Settings"]}),e.jsx(m,{children:"System-wide two-factor authentication settings"})]}),e.jsxs(r,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(o,{htmlFor:"global-2fa",children:"Require 2FA for Admin Actions"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"When enabled, all admins must use 2FA for sensitive actions"})]}),e.jsx(se,{id:"global-2fa",checked:c.enabled,onCheckedChange:J,disabled:K})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 pt-4 border-t",children:[e.jsxs("div",{children:[e.jsx(o,{className:"text-sm font-medium",children:"Code Expiry"}),e.jsxs("p",{className:"text-sm text-muted-foreground",children:[c.otp_expiry_minutes," minutes"]})]}),e.jsxs("div",{children:[e.jsx(o,{className:"text-sm font-medium",children:"Max Attempts"}),e.jsxs("p",{className:"text-sm text-muted-foreground",children:[c.max_attempts," attempts"]})]}),e.jsxs("div",{children:[e.jsx(o,{className:"text-sm font-medium",children:"Lockout Duration"}),e.jsxs("p",{className:"text-sm text-muted-foreground",children:[c.lockout_duration_minutes," minutes"]})]})]})]})]})]})}),e.jsx(f,{value:"test",className:"space-y-4",children:e.jsxs(t,{children:[e.jsxs(a,{children:[e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(A,{className:"h-5 w-5"}),"Test Email Verification"]}),e.jsx(m,{children:"Send a test verification code to ensure your email setup is working"})]}),e.jsx(r,{children:l.two_factor_enabled?e.jsx("div",{className:"space-y-4",children:q?e.jsxs("form",{onSubmit:z,className:"space-y-4",children:[e.jsxs(x,{className:"border-blue-200 bg-blue-50",children:[e.jsx(A,{className:"h-4 w-4 text-blue-600"}),e.jsx(h,{className:"text-blue-800",children:"Test verification code sent! Check your email and enter the 6-digit code below."})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"test_otp",children:"Verification Code"}),e.jsx(Y,{id:"test_otp",value:E.otp_code,onChange:s=>Q("otp_code",s.target.value),placeholder:"000000",maxLength:6,className:"text-center text-lg tracking-widest"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(n,{type:"submit",disabled:E.otp_code.length!==6||V,className:"flex-1",children:V?"Verifying...":"Verify Code"}),e.jsx(n,{type:"button",variant:"outline",onClick:()=>{N(!1),B()},children:"Cancel"})]})]}):e.jsxs(n,{onClick:X,disabled:C||l.is_locked_out,className:"w-full",children:[e.jsx(re,{className:"h-4 w-4 mr-2"}),C?"Sending...":"Send Test Verification Code"]})}):e.jsxs(x,{children:[e.jsx(M,{className:"h-4 w-4"}),e.jsx(h,{children:"You must enable two-factor authentication before you can test it."})]})})]})}),e.jsx(f,{value:"sessions",className:"space-y-4",children:e.jsxs(t,{children:[e.jsxs(a,{children:[e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(w,{className:"h-5 w-5"}),"Active Verification Sessions"]}),e.jsx(m,{children:"Actions you've recently verified and don't need to re-verify"})]}),e.jsx(r,{children:g.length>0?e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"space-y-3",children:g.map((s,b)=>e.jsxs("div",{className:"flex justify-between items-center p-3 border rounded-lg",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:I(s.action)}),e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Verified: ",new Date(s.verified_at).toLocaleString()]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs(d,{variant:"outline",className:"border-green-500 text-green-700",children:[e.jsx(le,{className:"h-3 w-3 mr-1"}),"Active"]}),e.jsxs("p",{className:"text-xs text-muted-foreground mt-1",children:["Expires: ",new Date(s.expires_at).toLocaleString()]})]})]},b))}),e.jsxs(n,{onClick:Z,variant:"outline",className:"w-full",children:[e.jsx(ce,{className:"h-4 w-4 mr-2"}),"Clear All Sessions"]})]}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(w,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-foreground mb-2",children:"No Active Sessions"}),e.jsx("p",{className:"text-muted-foreground",children:"You haven't verified any admin actions recently."})]})})]})}),e.jsx(f,{value:"activity",className:"space-y-4",children:e.jsxs(t,{children:[e.jsxs(a,{children:[e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-5 w-5"}),"Recent Verification Activity"]}),e.jsx(m,{children:"Your recent verification attempts and their outcomes"})]}),e.jsx(r,{children:p.length>0?e.jsx("div",{className:"space-y-3",children:p.map(s=>e.jsxs("div",{className:"flex justify-between items-center p-3 border rounded-lg",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:I(s.action)}),e.jsxs("p",{className:"text-sm text-muted-foreground",children:[new Date(s.created_at).toLocaleString()," • ",s.ip_address]})]}),e.jsx("div",{children:s.success?e.jsxs(d,{variant:"default",className:"bg-green-100 text-green-800",children:[e.jsx(T,{className:"h-3 w-3 mr-1"}),"Success"]}):e.jsxs(d,{variant:"destructive",children:[e.jsx(R,{className:"h-3 w-3 mr-1"}),"Failed"]})})]},s.id))}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(y,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-foreground mb-2",children:"No Recent Activity"}),e.jsx("p",{className:"text-muted-foreground",children:"No verification attempts recorded yet."})]})})]})})]})]})})]})}export{Me as default};
