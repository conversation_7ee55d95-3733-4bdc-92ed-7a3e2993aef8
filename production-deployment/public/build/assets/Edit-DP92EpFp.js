import{x as I,j as e,Q as U,t as _}from"./app-J5EqS6dS.js";import{C as m,a as p,b as x,d as u,c as h}from"./card-9XCADs-4.js";import{B as j}from"./smartphone-GGiwNneF.js";import{I as o}from"./input-Bo8dOn9p.js";import{L as d}from"./label-BlOrdc-X.js";import{S as g,a as f,b as v,c as N,d as n}from"./select-CIhY0l9J.js";import{B as c}from"./badge-BucYuCBs.js";import{A as B,D as A}from"./app-layout-ox1kAwY6.js";import{F as L,t as w}from"./ImpersonationBanner-CYn5eDk6.js";import{A as V}from"./arrow-left-D4U9AVF9.js";import{E as T}from"./eye-D-fsmYB2.js";import{U as k}from"./user-DCnDRzMf.js";import{C as G}from"./calendar-B-u_QN2Q.js";import{S as Q}from"./users-RYmOyic9.js";import{S as z}from"./save-DfhL0V-C.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./search-DBK6jUoc.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";function Ne({subscription:r,pricingPlans:b}){var S,C;const{data:a,setData:i,put:P,processing:y,errors:t}=I({pricing_plan_id:((C=(S=r.pricingPlan)==null?void 0:S.id)==null?void 0:C.toString())||"",status:r.status,current_period_start:r.current_period_start?r.current_period_start.split("T")[0]:"",current_period_end:r.current_period_end?r.current_period_end.split("T")[0]:"",payment_gateway:r.payment_gateway,paddle_subscription_id:r.paddle_subscription_id||"",shurjopay_subscription_id:r.shurjopay_subscription_id||"",coinbase_commerce_subscription_id:r.coinbase_commerce_subscription_id||""}),D=s=>{s.preventDefault(),P(route("admin.subscriptions.update",r.id),{onSuccess:()=>{w.success("Subscription updated successfully.")},onError:()=>{w.error("Failed to update subscription. Please check the form and try again.")}})},l=b.find(s=>s.id.toString()===a.pricing_plan_id),F=s=>{switch(s){case"active":return e.jsx(c,{className:"bg-green-500 hover:bg-green-600",children:"Active"});case"cancelled":return e.jsx(c,{variant:"destructive",children:"Cancelled"});case"expired":return e.jsx(c,{variant:"outline",className:"border-yellow-500 text-yellow-700",children:"Expired"});case"pending":return e.jsx(c,{variant:"secondary",children:"Pending"});default:return e.jsx(c,{variant:"outline",children:s})}},E=s=>{switch(s){case"premium":return e.jsx(c,{className:"bg-amber-500 hover:bg-amber-600",children:"Premium"});default:return e.jsx(c,{variant:"outline",children:"Free"})}};return e.jsxs(B,{children:[e.jsx(U,{title:`Edit Subscription: ${r.user.name}`}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(_,{href:route("admin.subscriptions.index"),children:e.jsxs(j,{variant:"outline",size:"sm",children:[e.jsx(V,{className:"h-4 w-4 mr-2"}),"Back to Subscriptions"]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:"Edit Subscription"}),e.jsxs("p",{className:"text-muted-foreground mt-1",children:["Subscription ID: ",r.id," • ",r.user.name]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(_,{href:route("admin.subscriptions.show",r.id),children:e.jsxs(j,{variant:"outline",children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"View Details"]})}),e.jsx(L,{className:"h-8 w-8 text-muted-foreground"})]})]}),e.jsxs(m,{children:[e.jsxs(p,{children:[e.jsxs(x,{className:"flex items-center gap-2",children:[e.jsx(k,{className:"h-5 w-5"}),"Current Subscription Status"]}),e.jsxs(u,{children:["Current subscription information for ",r.user.name]})]}),e.jsx(h,{children:e.jsxs("div",{className:"grid gap-4 md:grid-cols-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"User"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:r.user.name}),e.jsx("p",{className:"text-xs text-muted-foreground",children:r.user.email})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Current Plan"}),e.jsx("div",{className:"flex items-center gap-2 mt-1",children:E(r.plan_name)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Status"}),e.jsx("div",{className:"flex items-center gap-2 mt-1",children:F(r.status)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Created"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:new Date(r.created_at).toLocaleDateString()})]})]})})]}),e.jsxs("form",{onSubmit:D,className:"space-y-6",children:[e.jsxs("div",{className:"grid gap-6 lg:grid-cols-2",children:[e.jsxs(m,{children:[e.jsxs(p,{children:[e.jsxs(x,{className:"flex items-center gap-2",children:[e.jsx(A,{className:"h-5 w-5"}),"Plan & Status"]}),e.jsx(u,{children:"Update the pricing plan and subscription status"})]}),e.jsxs(h,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(d,{htmlFor:"pricing_plan_id",children:"Pricing Plan *"}),e.jsxs(g,{value:a.pricing_plan_id,onValueChange:s=>i("pricing_plan_id",s),children:[e.jsx(f,{className:t.pricing_plan_id?"border-red-500":"",children:e.jsx(v,{placeholder:"Select a pricing plan"})}),e.jsx(N,{children:b.map(s=>e.jsxs(n,{value:s.id.toString(),children:[s.display_name," - ",s.formatted_price]},s.id))})]}),t.pricing_plan_id&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:t.pricing_plan_id}),l&&e.jsxs("div",{className:"mt-2 p-3 bg-muted rounded-md",children:[e.jsx("p",{className:"text-sm font-medium",children:l.display_name}),e.jsx("p",{className:"text-xs text-muted-foreground",children:l.description}),e.jsxs("p",{className:"text-xs text-muted-foreground mt-1",children:["Search Limit: ",l.search_limit===-1?"Unlimited":l.search_limit]})]})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"status",children:"Status *"}),e.jsxs(g,{value:a.status,onValueChange:s=>i("status",s),children:[e.jsx(f,{className:t.status?"border-red-500":"",children:e.jsx(v,{placeholder:"Select status"})}),e.jsxs(N,{children:[e.jsx(n,{value:"active",children:"Active"}),e.jsx(n,{value:"cancelled",children:"Cancelled"}),e.jsx(n,{value:"expired",children:"Expired"})]})]}),t.status&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:t.status})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"payment_gateway",children:"Payment Gateway"}),e.jsxs(g,{value:a.payment_gateway,onValueChange:s=>i("payment_gateway",s),children:[e.jsx(f,{children:e.jsx(v,{placeholder:"Select payment gateway"})}),e.jsxs(N,{children:[e.jsx(n,{value:"offline",children:"Offline Payment"}),e.jsx(n,{value:"paddle",children:"Paddle"}),e.jsx(n,{value:"shurjopay",children:"ShurjoPay"}),e.jsx(n,{value:"coinbase_commerce",children:"Coinbase Commerce"})]})]})]})]})]}),e.jsxs(m,{children:[e.jsxs(p,{children:[e.jsxs(x,{className:"flex items-center gap-2",children:[e.jsx(G,{className:"h-5 w-5"}),"Date Configuration"]}),e.jsx(u,{children:"Update the subscription period dates"})]}),e.jsxs(h,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(d,{htmlFor:"current_period_start",children:"Period Start"}),e.jsx(o,{id:"current_period_start",type:"date",value:a.current_period_start,onChange:s=>i("current_period_start",s.target.value),className:t.current_period_start?"border-red-500":""}),t.current_period_start&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:t.current_period_start})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"current_period_end",children:"Period End"}),e.jsx(o,{id:"current_period_end",type:"date",value:a.current_period_end,onChange:s=>i("current_period_end",s.target.value),className:t.current_period_end?"border-red-500":""}),t.current_period_end&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:t.current_period_end})]})]})]})]}),e.jsxs(m,{children:[e.jsxs(p,{children:[e.jsxs(x,{className:"flex items-center gap-2",children:[e.jsx(Q,{className:"h-5 w-5"}),"Payment Gateway IDs"]}),e.jsx(u,{children:"Update external subscription IDs from payment gateways"})]}),e.jsx(h,{className:"space-y-4",children:e.jsxs("div",{className:"grid gap-4 md:grid-cols-3",children:[e.jsxs("div",{children:[e.jsx(d,{htmlFor:"paddle_subscription_id",children:"Paddle Subscription ID"}),e.jsx(o,{id:"paddle_subscription_id",value:a.paddle_subscription_id,onChange:s=>i("paddle_subscription_id",s.target.value),placeholder:"sub_01234567890"})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"shurjopay_subscription_id",children:"ShurjoPay Subscription ID"}),e.jsx(o,{id:"shurjopay_subscription_id",value:a.shurjopay_subscription_id,onChange:s=>i("shurjopay_subscription_id",s.target.value),placeholder:"sp_01234567890"})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"coinbase_commerce_subscription_id",children:"Coinbase Commerce ID"}),e.jsx(o,{id:"coinbase_commerce_subscription_id",value:a.coinbase_commerce_subscription_id,onChange:s=>i("coinbase_commerce_subscription_id",s.target.value),placeholder:"cb_01234567890"})]})]})})]}),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx(_,{href:route("admin.subscriptions.show",r.id),children:e.jsx(j,{type:"button",variant:"outline",children:"Cancel"})}),e.jsxs(j,{type:"submit",disabled:y,children:[e.jsx(z,{className:"h-4 w-4 mr-2"}),y?"Updating...":"Update Subscription"]})]})]})]})})]})}export{Ne as default};
