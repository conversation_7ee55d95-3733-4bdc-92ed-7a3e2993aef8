import{x as h,j as e,Q as u,t as c}from"./app-J5EqS6dS.js";import{B as n}from"./smartphone-GGiwNneF.js";import{C as j,a as g,b as f,d as v,c as b}from"./card-9XCADs-4.js";import{I as l}from"./input-Bo8dOn9p.js";import{L as o}from"./label-BlOrdc-X.js";import{S as N}from"./switch-yFNfZ5X-.js";import{A as y}from"./app-layout-ox1kAwY6.js";import{t as m}from"./ImpersonationBanner-CYn5eDk6.js";import{A as w}from"./arrow-left-D4U9AVF9.js";import{B as _}from"./building-Dgyml3QN.js";import{S as C}from"./save-DfhL0V-C.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./index-Ba8m9N9L.js";import"./index-BzZWUWqx.js";import"./users-RYmOyic9.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./search-DBK6jUoc.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./badge-BucYuCBs.js";import"./eye-D-fsmYB2.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";function te({brand:a}){const{data:t,setData:i,put:p,processing:d,errors:r}=h({name:a.name,logo_url:a.logo_url||"",country:a.country||"",website:a.website||"",is_active:a.is_active}),x=s=>{s.preventDefault(),p(`/admin/brands/${a.id}`,{onSuccess:()=>{m.success(`Brand "${t.name}" has been updated successfully.`)},onError:()=>{m.error("Failed to update brand. Please check the form and try again.")}})};return e.jsxs(y,{children:[e.jsx(u,{title:`Edit ${a.name} - Brands - Admin`}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(c,{href:"/admin/brands",children:e.jsxs(n,{variant:"outline",size:"sm",children:[e.jsx(w,{className:"w-4 h-4 mr-2"}),"Back to Brands"]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Edit Brand"}),e.jsx("p",{className:"text-muted-foreground",children:"Update the brand information"})]})]}),e.jsxs(j,{children:[e.jsxs(g,{children:[e.jsxs(f,{className:"flex items-center gap-2",children:[e.jsx(_,{className:"h-5 w-5"}),"Brand Details"]}),e.jsx(v,{children:"Update the information for this brand"})]}),e.jsx(b,{children:e.jsxs("form",{onSubmit:x,className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"name",children:"Brand Name *"}),e.jsx(l,{id:"name",type:"text",value:t.name,onChange:s=>i("name",s.target.value),placeholder:"e.g., Apple, Samsung, Xiaomi",className:r.name?"border-red-500":""}),r.name&&e.jsx("p",{className:"text-sm text-red-600",children:r.name})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"logo_url",children:"Logo URL"}),e.jsx(l,{id:"logo_url",type:"url",value:t.logo_url,onChange:s=>i("logo_url",s.target.value),placeholder:"https://example.com/logo.png",className:r.logo_url?"border-red-500":""}),r.logo_url&&e.jsx("p",{className:"text-sm text-red-600",children:r.logo_url}),t.logo_url&&e.jsxs("div",{className:"mt-2",children:[e.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:"Logo Preview:"}),e.jsx("img",{src:t.logo_url,alt:"Brand logo preview",className:"w-16 h-16 object-contain border rounded-lg p-2",onError:s=>{s.currentTarget.style.display="none"}})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"country",children:"Country"}),e.jsx(l,{id:"country",type:"text",value:t.country,onChange:s=>i("country",s.target.value),placeholder:"e.g., United States, South Korea, China",className:r.country?"border-red-500":""}),r.country&&e.jsx("p",{className:"text-sm text-red-600",children:r.country})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"website",children:"Website"}),e.jsx(l,{id:"website",type:"url",value:t.website,onChange:s=>i("website",s.target.value),placeholder:"https://www.example.com",className:r.website?"border-red-500":""}),r.website&&e.jsx("p",{className:"text-sm text-red-600",children:r.website})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(N,{id:"is_active",checked:t.is_active,onCheckedChange:s=>i("is_active",s)}),e.jsx(o,{htmlFor:"is_active",children:"Active"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Active brands are available for selection when creating models"})]}),e.jsxs("div",{className:"flex justify-end gap-4 pt-6 border-t",children:[e.jsx(c,{href:"/admin/brands",children:e.jsx(n,{variant:"outline",type:"button",children:"Cancel"})}),e.jsxs(n,{type:"submit",disabled:d,children:[e.jsx(C,{className:"w-4 h-4 mr-2"}),d?"Updating...":"Update Brand"]})]})]})})]})]})})]})}export{te as default};
