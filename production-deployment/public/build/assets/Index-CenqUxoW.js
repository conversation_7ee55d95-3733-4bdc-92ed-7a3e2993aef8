import{j as e,Q as C,t as l}from"./app-J5EqS6dS.js";import{B as n}from"./smartphone-GGiwNneF.js";import{C as a,c as o,a as u,b as h,d as p}from"./card-9XCADs-4.js";import{B as t}from"./badge-BucYuCBs.js";import{A as S,C as P}from"./app-layout-ox1kAwY6.js";import{S as k}from"./shield-D9nQfigG.js";import{G as b}from"./globe-zfFlVOSX.js";import{Z as m}from"./zap-BcmHRR4K.js";import{C as i}from"./circle-check-big-DOFoatRy.js";import{F as j}from"./ImpersonationBanner-CYn5eDk6.js";import{T as _}from"./trending-up-BtixJGWw.js";import{S as G}from"./star-D0YOm-Sd.js";import{S as c}from"./users-RYmOyic9.js";import{E as y}from"./external-link-A4n9PP4e.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./search-DBK6jUoc.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./eye-D-fsmYB2.js";import"./triangle-alert-BW76NKO9.js";import"./crown-UDSxMtlm.js";const A=[{id:"paddle",name:"Paddle",description:"Complete payment solution with global tax compliance",status:"configured",logo:"/images/paddle-logo.png",features:["Subscription Billing","Global Tax Compliance","Fraud Protection","Analytics"],supported_currencies:["USD","EUR","GBP","CAD","AUD","JPY","CHF","SEK","NOK","DKK"],configuration_url:"/admin/payment-gateways/paddle/configure",documentation_url:"https://developer.paddle.com/getting-started"},{id:"shurjopay",name:"ShurjoPay",description:"Leading payment gateway for Bangladesh with local banking support",status:"configured",logo:"/images/shurjopay-logo.png",features:["Mobile Banking","Internet Banking","Card Payments","Local Support"],supported_currencies:["BDT","USD","EUR","GBP"],configuration_url:"/admin/payment-gateways/shurjopay/configure",documentation_url:"https://shurjopay.com.bd/developers"},{id:"coinbase",name:"Coinbase Commerce",description:"Next-generation crypto payments with Onchain Payment Protocol - automatic USDC settlement, hundreds of currencies, instant confirmation",status:"configured",logo:"/images/coinbase-logo.png",features:["Onchain Payment Protocol","Auto USDC Settlement","Hundreds of Currencies","Instant Confirmation","Base & Polygon Networks","Volatility Protection","Enterprise Security","Real-time Updates"],supported_currencies:["BTC","ETH","LTC","BCH","USDC","DAI","+100s more"],configuration_url:"/admin/payment-gateways/coinbase/configure",documentation_url:"https://docs.cdp.coinbase.com/commerce/introduction/welcome"}];function oe({paymentGateways:w}){const f=w||A,x=f.filter(r=>r.status==="configured"||r.status==="active"),g=f.filter(r=>r.status==="not_configured"||r.status==="inactive"),N=r=>{switch(r){case"active":case"configured":return e.jsxs(t,{className:"bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border-green-200 hover:from-green-100 hover:to-emerald-100 dark:from-green-950 dark:to-emerald-950 dark:text-green-300 dark:border-green-800 shadow-sm",children:[e.jsx(i,{className:"w-3 h-3 mr-1"}),"Active"]});case"inactive":case"not_configured":return e.jsxs(t,{variant:"outline",className:"text-muted-foreground border-border bg-muted/30 hover:bg-muted/50 transition-colors",children:[e.jsx(P,{className:"w-3 h-3 mr-1"}),"Not Configured"]});default:return e.jsx(t,{variant:"outline",className:"bg-gray-50 dark:bg-gray-900",children:"Unknown"})}},v=r=>{const s="w-12 h-12 rounded-xl flex items-center justify-center font-bold text-sm shadow-lg transition-all duration-200 hover:scale-105";switch(r){case"paddle":return e.jsx("div",{className:`${s} bg-gradient-to-br from-primary to-primary/80 text-primary-foreground ring-2 ring-primary/20`,children:"P"});case"shurjopay":return e.jsx("div",{className:`${s} bg-gradient-to-br from-green-600 to-green-700 text-white dark:from-green-500 dark:to-green-600 ring-2 ring-green-200 dark:ring-green-800`,children:"SP"});case"coinbase":return e.jsxs("div",{className:`${s} bg-gradient-to-br from-orange-500 to-orange-600 text-white dark:from-orange-400 dark:to-orange-500 relative overflow-hidden ring-2 ring-orange-200 dark:ring-orange-800`,children:[e.jsx("span",{className:"relative z-10 font-bold text-lg",children:"₿"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-orange-300/30 to-orange-800/30"})]});default:return e.jsx("div",{className:`${s} bg-gradient-to-br from-muted to-muted/80 text-muted-foreground ring-2 ring-border`,children:e.jsx(j,{className:"w-6 h-6"})})}};return e.jsxs(S,{children:[e.jsx(C,{title:"Payment Gateways"}),e.jsxs("div",{className:"flex h-full flex-1 flex-col space-y-8 p-6",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:"Payment Gateways"}),e.jsx("p",{className:"text-muted-foreground max-w-2xl",children:"Manage and configure payment processing integrations to accept payments from customers worldwide"})]}),e.jsxs("div",{className:"flex items-center gap-3 flex-wrap",children:[e.jsxs(t,{variant:"outline",className:"text-sm px-4 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 border-blue-200 dark:from-blue-950 dark:to-indigo-950 dark:text-blue-300 dark:border-blue-800",children:[e.jsx(k,{className:"w-4 h-4 mr-2"}),"Enterprise Security"]}),e.jsxs(t,{variant:"outline",className:"text-sm px-4 py-2 bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border-green-200 dark:from-green-950 dark:to-emerald-950 dark:text-green-300 dark:border-green-800",children:[e.jsx(b,{className:"w-4 h-4 mr-2"}),"Global Coverage"]}),e.jsxs(t,{variant:"outline",className:"text-sm px-4 py-2 bg-gradient-to-r from-purple-50 to-violet-50 text-purple-700 border-purple-200 dark:from-purple-950 dark:to-violet-950 dark:text-purple-300 dark:border-purple-800",children:[e.jsx(m,{className:"w-4 h-4 mr-2"}),"Real-time Processing"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:[e.jsx(a,{className:"border-l-4 border-l-green-500 dark:border-l-green-400 bg-gradient-to-br from-green-50/50 to-emerald-50/30 dark:from-green-950/20 dark:to-emerald-950/10 hover:shadow-lg transition-all duration-200",children:e.jsx(o,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-semibold text-muted-foreground uppercase tracking-wide",children:"Active Gateways"}),e.jsx("p",{className:"text-3xl font-bold text-foreground mt-2",children:x.length}),e.jsxs("p",{className:"text-xs text-muted-foreground mt-2 flex items-center",children:[e.jsx(i,{className:"w-3 h-3 mr-1 text-green-600"}),"Ready to process payments"]})]}),e.jsx("div",{className:"p-4 bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/20 rounded-xl shadow-sm",children:e.jsx(i,{className:"w-6 h-6 text-green-600 dark:text-green-400"})})]})})}),e.jsx(a,{className:"border-l-4 border-l-primary bg-gradient-to-br from-blue-50/50 to-indigo-50/30 dark:from-blue-950/20 dark:to-indigo-950/10 hover:shadow-lg transition-all duration-200",children:e.jsx(o,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-semibold text-muted-foreground uppercase tracking-wide",children:"Available"}),e.jsx("p",{className:"text-3xl font-bold text-foreground mt-2",children:g.length}),e.jsxs("p",{className:"text-xs text-muted-foreground mt-2 flex items-center",children:[e.jsx(m,{className:"w-3 h-3 mr-1 text-primary"}),"Ready to configure"]})]}),e.jsx("div",{className:"p-4 bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl shadow-sm",children:e.jsx(m,{className:"w-6 h-6 text-primary"})})]})})}),e.jsx(a,{className:"border-l-4 border-l-purple-500 dark:border-l-purple-400 bg-gradient-to-br from-purple-50/50 to-violet-50/30 dark:from-purple-950/20 dark:to-violet-950/10 hover:shadow-lg transition-all duration-200",children:e.jsx(o,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-semibold text-muted-foreground uppercase tracking-wide",children:"Total Options"}),e.jsx("p",{className:"text-3xl font-bold text-foreground mt-2",children:f.length}),e.jsxs("p",{className:"text-xs text-muted-foreground mt-2 flex items-center",children:[e.jsx(j,{className:"w-3 h-3 mr-1 text-purple-600"}),"Payment solutions"]})]}),e.jsx("div",{className:"p-4 bg-gradient-to-br from-purple-100 to-violet-100 dark:from-purple-900/30 dark:to-violet-900/20 rounded-xl shadow-sm",children:e.jsx(j,{className:"w-6 h-6 text-purple-600 dark:text-purple-400"})})]})})}),e.jsx(a,{className:"border-l-4 border-l-orange-500 dark:border-l-orange-400 bg-gradient-to-br from-orange-50/50 to-amber-50/30 dark:from-orange-950/20 dark:to-amber-950/10 hover:shadow-lg transition-all duration-200",children:e.jsx(o,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-semibold text-muted-foreground uppercase tracking-wide",children:"Coverage"}),e.jsx("p",{className:"text-3xl font-bold text-foreground mt-2",children:"190+"}),e.jsxs("p",{className:"text-xs text-muted-foreground mt-2 flex items-center",children:[e.jsx(b,{className:"w-3 h-3 mr-1 text-orange-600"}),"Countries supported"]})]}),e.jsx("div",{className:"p-4 bg-gradient-to-br from-orange-100 to-amber-100 dark:from-orange-900/30 dark:to-amber-900/20 rounded-xl shadow-sm",children:e.jsx(_,{className:"w-6 h-6 text-orange-600 dark:text-orange-400"})})]})})})]})]}),x.length>0&&e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("h2",{className:"text-2xl font-semibold text-foreground",children:"Active Payment Gateways"}),e.jsxs(t,{className:"bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800",children:[x.length," Active"]})]})}),e.jsx("div",{className:"grid gap-8 sm:grid-cols-1 md:grid-cols-2 xl:grid-cols-3",children:x.map(r=>e.jsxs(a,{className:`relative hover:shadow-xl transition-all duration-300 group border-0 shadow-lg ${r.id==="coinbase"?"ring-2 ring-orange-200 dark:ring-orange-800 bg-gradient-to-br from-orange-50/80 to-amber-50/40 dark:from-orange-950/30 dark:to-amber-950/20":r.id==="shurjopay"?"ring-2 ring-green-200 dark:ring-green-800 bg-gradient-to-br from-green-50/80 to-emerald-50/40 dark:from-green-950/30 dark:to-emerald-950/20":"ring-2 ring-primary/20 bg-gradient-to-br from-primary/5 to-primary/10 dark:from-primary/10 dark:to-primary/5"} hover:ring-4 ${r.id==="coinbase"?"hover:ring-orange-300 dark:hover:ring-orange-700":r.id==="shurjopay"?"hover:ring-green-300 dark:hover:ring-green-700":"hover:ring-primary/30"}`,children:[r.id==="coinbase"&&e.jsx("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2 z-10",children:e.jsxs(t,{className:"bg-gradient-to-r from-orange-600 to-orange-500 text-white border-0 shadow-lg dark:from-orange-500 dark:to-orange-400 px-3 py-1",children:[e.jsx(m,{className:"w-3 h-3 mr-1"}),"Onchain Protocol"]})}),e.jsx(u,{className:"pb-4",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[v(r.id),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx(h,{className:`text-xl font-bold transition-colors ${r.id==="coinbase"?"text-foreground group-hover:text-orange-600 dark:group-hover:text-orange-400":r.id==="shurjopay"?"text-foreground group-hover:text-green-600 dark:group-hover:text-green-400":"text-foreground group-hover:text-primary"}`,children:r.name}),e.jsx(p,{className:"text-muted-foreground mt-2 line-clamp-2 text-sm leading-relaxed",children:r.description})]})]}),N(r.status)]})}),e.jsxs(o,{className:"space-y-8 pt-2",children:[e.jsxs("div",{children:[e.jsxs("h4",{className:"text-sm font-bold text-foreground mb-4 flex items-center",children:[e.jsx(G,{className:"w-4 h-4 mr-2 text-yellow-500 dark:text-yellow-400"}),"Key Features"]}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[r.features.slice(0,4).map((s,d)=>e.jsx(t,{variant:"outline",className:`text-xs px-3 py-1 font-medium transition-all hover:scale-105 ${r.id==="coinbase"?"bg-gradient-to-r from-orange-50 to-amber-50 text-orange-700 border-orange-200 hover:from-orange-100 hover:to-amber-100 dark:from-orange-950/30 dark:to-amber-950/20 dark:text-orange-300 dark:border-orange-800":r.id==="shurjopay"?"bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border-green-200 hover:from-green-100 hover:to-emerald-100 dark:from-green-950/30 dark:to-emerald-950/20 dark:text-green-300 dark:border-green-800":"bg-gradient-to-r from-primary/10 to-primary/5 text-primary border-primary/20 hover:from-primary/20 hover:to-primary/10"}`,children:s},d)),r.features.length>4&&e.jsxs(t,{variant:"outline",className:"text-xs bg-gradient-to-r from-muted to-muted/80 text-muted-foreground border-border px-3 py-1",children:["+",r.features.length-4," more"]})]})]}),e.jsxs("div",{children:[e.jsxs("h4",{className:"text-sm font-bold text-foreground mb-4 flex items-center",children:[e.jsx(b,{className:"w-4 h-4 mr-2 text-green-500 dark:text-green-400"}),r.id==="coinbase"?"Cryptocurrencies":"Currencies",e.jsx("span",{className:"ml-2 text-xs bg-muted text-muted-foreground px-2 py-1 rounded-full",children:r.supported_currencies.length})]}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[r.supported_currencies.slice(0,6).map((s,d)=>e.jsx(t,{className:`text-xs px-3 py-1 font-medium transition-all hover:scale-105 ${r.id==="coinbase"?"bg-gradient-to-r from-orange-100 to-amber-100 text-orange-800 border-orange-300 hover:from-orange-200 hover:to-amber-200 dark:from-orange-900/40 dark:to-amber-900/30 dark:text-orange-200 dark:border-orange-700":"bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-300 hover:from-green-200 hover:to-emerald-200 dark:from-green-900/40 dark:to-emerald-900/30 dark:text-green-200 dark:border-green-700"}`,children:s},d)),r.supported_currencies.length>6&&e.jsxs(t,{className:"text-xs bg-gradient-to-r from-muted to-muted/80 text-muted-foreground border-border px-3 py-1",children:["+",r.supported_currencies.length-6," more"]})]})]}),e.jsxs("div",{className:"flex items-center gap-3 pt-6 border-t border-border/50",children:[r.id==="paddle"?e.jsx(l,{href:route("admin.payment-gateways.paddle.configure"),className:"flex-1",children:e.jsxs(n,{className:"w-full bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80 text-white shadow-lg hover:shadow-xl transition-all duration-200",size:"sm",children:[e.jsx(c,{className:"w-4 h-4 mr-2"}),"Configure Gateway"]})}):r.id==="shurjopay"?e.jsx(l,{href:route("admin.payment-gateways.shurjopay.configure"),className:"flex-1",children:e.jsxs(n,{className:"w-full bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white shadow-lg hover:shadow-xl transition-all duration-200",size:"sm",children:[e.jsx(c,{className:"w-4 h-4 mr-2"}),"Configure Gateway"]})}):r.id==="coinbase"?e.jsx(l,{href:route("admin.payment-gateways.coinbase.configure"),className:"flex-1",children:e.jsxs(n,{className:"w-full bg-gradient-to-r from-orange-600 to-orange-500 hover:from-orange-700 hover:to-orange-600 text-white shadow-lg hover:shadow-xl transition-all duration-200",size:"sm",children:[e.jsx(c,{className:"w-4 h-4 mr-2"}),"Configure Crypto Gateway"]})}):e.jsxs(n,{className:"flex-1 bg-gradient-to-r from-muted to-muted/80 text-muted-foreground",variant:"outline",size:"sm",disabled:!0,children:[e.jsx(c,{className:"w-4 h-4 mr-2"}),"Coming Soon"]}),r.documentation_url&&e.jsx(l,{href:r.documentation_url,target:"_blank",children:e.jsx(n,{variant:"outline",size:"sm",title:"View Documentation",className:"border-2 hover:bg-accent/50 transition-all duration-200",children:e.jsx(y,{className:"w-4 h-4"})})})]})]})]},r.id))})]}),g.length>0&&e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("h2",{className:"text-2xl font-semibold text-foreground",children:"Available Payment Gateways"}),e.jsxs(t,{variant:"outline",className:"text-muted-foreground border-border",children:[g.length," Available"]})]})}),e.jsx("div",{className:"grid gap-6 sm:grid-cols-1 md:grid-cols-2 xl:grid-cols-3",children:g.map(r=>e.jsxs(a,{className:"relative border-2 border-dashed border-border hover:border-primary/50 transition-all duration-300 bg-muted/30 hover:bg-background group",children:[e.jsx(u,{children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[v(r.id),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx(h,{className:"text-lg font-semibold text-muted-foreground group-hover:text-primary transition-colors",children:r.name}),e.jsx(p,{className:"text-muted-foreground/80 mt-1 line-clamp-2",children:r.description})]})]}),N(r.status)]})}),e.jsxs(o,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium text-muted-foreground mb-2",children:"Key Features"}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[r.features.slice(0,3).map((s,d)=>e.jsx(t,{variant:"outline",className:"text-xs text-muted-foreground border-border",children:s},d)),r.features.length>3&&e.jsxs(t,{variant:"outline",className:"text-xs text-muted-foreground/60",children:["+",r.features.length-3," more"]})]})]}),e.jsxs("div",{className:"flex items-center gap-2 pt-4",children:[e.jsxs(n,{className:"flex-1",variant:"outline",size:"sm",disabled:!0,children:[e.jsx(c,{className:"w-4 h-4 mr-2"}),"Coming Soon"]}),r.documentation_url&&e.jsx(l,{href:r.documentation_url,target:"_blank",children:e.jsx(n,{variant:"outline",size:"sm",title:"View Documentation",children:e.jsx(y,{className:"w-4 h-4"})})})]})]})]},r.id))})]}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[e.jsxs(a,{className:"border-l-4 border-l-primary",children:[e.jsxs(u,{children:[e.jsxs(h,{className:"text-foreground flex items-center",children:[e.jsx(k,{className:"w-5 h-5 mr-2 text-primary"}),"Security & Compliance"]}),e.jsx(p,{className:"text-muted-foreground",children:"Enterprise-grade security for all payment processing"})]}),e.jsx(o,{className:"text-foreground",children:e.jsxs("ul",{className:"space-y-3 text-sm",children:[e.jsxs("li",{className:"flex items-start",children:[e.jsx(i,{className:"w-4 h-4 mr-2 mt-0.5 text-green-600 dark:text-green-400 flex-shrink-0"}),"PCI DSS Level 1 compliant payment processing"]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx(i,{className:"w-4 h-4 mr-2 mt-0.5 text-green-600 dark:text-green-400 flex-shrink-0"}),"End-to-end encryption for all transactions"]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx(i,{className:"w-4 h-4 mr-2 mt-0.5 text-green-600 dark:text-green-400 flex-shrink-0"}),"Advanced fraud detection and prevention"]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx(i,{className:"w-4 h-4 mr-2 mt-0.5 text-green-600 dark:text-green-400 flex-shrink-0"}),"Real-time transaction monitoring"]})]})})]}),e.jsxs(a,{className:"border-l-4 border-l-green-500 dark:border-l-green-400",children:[e.jsxs(u,{children:[e.jsxs(h,{className:"text-foreground flex items-center",children:[e.jsx(m,{className:"w-5 h-5 mr-2 text-green-600 dark:text-green-400"}),"Integration Guide"]}),e.jsx(p,{className:"text-muted-foreground",children:"Quick setup guide for payment gateway configuration"})]}),e.jsx(o,{className:"text-foreground",children:e.jsxs("ul",{className:"space-y-3 text-sm",children:[e.jsxs("li",{className:"flex items-start",children:[e.jsx("div",{className:"w-5 h-5 rounded-full bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 text-xs flex items-center justify-center mr-2 mt-0.5 font-semibold flex-shrink-0",children:"1"}),"Choose your preferred payment gateway"]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("div",{className:"w-5 h-5 rounded-full bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 text-xs flex items-center justify-center mr-2 mt-0.5 font-semibold flex-shrink-0",children:"2"}),"Obtain API credentials from the provider"]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("div",{className:"w-5 h-5 rounded-full bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 text-xs flex items-center justify-center mr-2 mt-0.5 font-semibold flex-shrink-0",children:"3"}),"Configure gateway settings and test in sandbox"]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("div",{className:"w-5 h-5 rounded-full bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 text-xs flex items-center justify-center mr-2 mt-0.5 font-semibold flex-shrink-0",children:"4"}),"Enable live mode and start accepting payments"]})]})})]})]})]})]})}export{oe as default};
