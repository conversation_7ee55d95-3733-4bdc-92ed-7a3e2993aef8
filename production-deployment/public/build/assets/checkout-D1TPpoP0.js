import{r as p,j as e,t as H,Q as W}from"./app-J5EqS6dS.js";import{B as b,S as V}from"./smartphone-GGiwNneF.js";import{C as u,c as g,a as N,b as v,d as _}from"./card-9XCADs-4.js";import{A as Z}from"./app-layout-ox1kAwY6.js";import{B as P}from"./badge-BucYuCBs.js";import{u as J,P as K}from"./PaddleContext-CGPdt1Ri.js";import{F as S,t as l,g as R,h as U}from"./ImpersonationBanner-CYn5eDk6.js";import{c as F,l as O}from"./checkout-helpers-CMrRJez4.js";import{L as B}from"./loader-circle-B1NtNhL1.js";import{C as G}from"./circle-alert-C6UwDlxH.js";import{C}from"./check-C7SdgHPn.js";import{T as Y}from"./triangle-alert-BW76NKO9.js";import{B as X}from"./building-Dgyml3QN.js";import{G as A}from"./globe-zfFlVOSX.js";import{B as T}from"./bitcoin-CeyOvZEq.js";import{Z as z}from"./zap-BcmHRR4K.js";import{S as D}from"./shield-D9nQfigG.js";import{E}from"./external-link-A4n9PP4e.js";import{F as L}from"./file-text-Dx6bYLtE.js";import{C as I}from"./clock-Brl7_5s7.js";import{A as ee}from"./arrow-left-D4U9AVF9.js";import{S as q}from"./star-D0YOm-Sd.js";import{C as M}from"./circle-check-big-DOFoatRy.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./users-RYmOyic9.js";import"./lock-Tx_yfI4R.js";import"./search-DBK6jUoc.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./eye-D-fsmYB2.js";import"./crown-UDSxMtlm.js";function se({plan:s,billingCycle:n,onError:a,disabled:f=!1}){const[o,x]=p.useState(!1),{isConfigured:m,isLoaded:j,config:c,error:y}=J(),t=async()=>{if(!j){const i="Paddle is still loading, please wait...";l.error(i),a==null||a(i);return}const h=(c==null?void 0:c.development_mode)||(c==null?void 0:c.mock_mode);if(!m&&!h){const i=y||"Paddle is not available";l.error(i),a==null||a(i);return}if(!s.supports_online_payment){const i="Online payments are not available for this plan";l.error(i),a==null||a(i);return}if(!s.supports_monthly&&n==="month"){const i="This plan does not support monthly billing";l.error(i),a==null||a(i);return}if(!s.supports_yearly&&n==="year"){const i="This plan does not support yearly billing";l.error(i),a==null||a(i);return}x(!0);try{const i=await F("/paddle/checkout",s.id,n,"Paddle");i.development_mode&&l.info("Development Mode: Redirecting to mock checkout page",{duration:3e3}),window.location.href=i.checkout_url}catch(i){const k=i instanceof Error?i.message:"Failed to start checkout";O("Paddle",i),k.includes("Development Mode:")?l.error("Development Mode: Please configure real Paddle sandbox credentials for testing.",{duration:8e3}):(l.error(k),a==null||a(k))}finally{x(!1)}},d=()=>{if(n==="year"){const h=s.price/12;return e.jsxs("div",{children:[e.jsxs("span",{className:"text-3xl font-bold",children:["$",s.price]}),e.jsx("span",{className:"text-gray-600 ml-1",children:"/year"}),e.jsxs("div",{className:"text-sm text-gray-500",children:["$",h.toFixed(2),"/month billed annually"]})]})}return e.jsxs("div",{children:[e.jsxs("span",{className:"text-3xl font-bold",children:["$",s.price]}),e.jsx("span",{className:"text-gray-600 ml-1",children:"/month"})]})},w=()=>{if(n==="year"){const h=s.price*12,i=s.price,k=h-i,Q=Math.round(k/h*100);return e.jsxs(P,{variant:"secondary",className:"bg-green-100 text-green-800",children:["Save $",k," (",Q,"%)"]})}return null};if(!j)return e.jsx(u,{className:"w-full max-w-md",children:e.jsxs(g,{className:"flex items-center justify-center p-6",children:[e.jsx(B,{className:"h-6 w-6 animate-spin"}),e.jsx("span",{className:"ml-2",children:"Loading payment options..."})]})});const r=(c==null?void 0:c.development_mode)||(c==null?void 0:c.mock_mode);return(y||!m)&&!r?e.jsx(u,{className:"w-full max-w-md border-red-200",children:e.jsxs(g,{className:"flex items-center justify-center p-6 text-red-600",children:[e.jsx(G,{className:"h-6 w-6"}),e.jsx("span",{className:"ml-2",children:"Payment system unavailable"})]})}):e.jsxs(u,{className:"w-full max-w-md",children:[e.jsxs(N,{className:"text-center",children:[e.jsxs(v,{className:"flex items-center justify-center gap-2",children:[e.jsx(S,{className:"h-5 w-5"}),s.display_name]}),e.jsx(_,{children:s.description}),e.jsxs("div",{className:"mt-4",children:[d(),w()]})]}),e.jsxs(g,{className:"space-y-4",children:[e.jsx("div",{className:"space-y-2",children:s.features.map((h,i)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(C,{className:"h-4 w-4 text-green-600"}),e.jsx("span",{className:"text-sm",children:h})]},i))}),e.jsx(b,{onClick:t,disabled:f||o||!s.supports_online_payment,className:"w-full",size:"lg",children:o?e.jsxs(e.Fragment,{children:[e.jsx(B,{className:"h-4 w-4 animate-spin mr-2"}),"Processing..."]}):e.jsxs(e.Fragment,{children:[e.jsx(S,{className:"h-4 w-4 mr-2"}),"Subscribe Now"]})}),!s.supports_online_payment&&e.jsx("p",{className:"text-sm text-gray-500 text-center",children:"Online payments not available for this plan"})]})]})}function te({plan:s,billingCycle:n,onError:a,disabled:f=!1}){const[o,x]=p.useState(!1),m=async()=>{if(!s.supports_online_payment){const t="Online payments are not available for this plan";l.error(t),a==null||a(t);return}if(!s.supports_monthly&&n==="month"){const t="This plan does not support monthly billing";l.error(t),a==null||a(t);return}if(!s.supports_yearly&&n==="year"){const t="This plan does not support yearly billing";l.error(t),a==null||a(t);return}x(!0);try{const t=await F("/shurjopay/checkout",s.id,n,"ShurjoPay");window.location.href=t.checkout_url}catch(t){const d=t instanceof Error?t.message:"An unexpected error occurred";O("ShurjoPay",t),l.error(d),a==null||a(d)}finally{x(!1)}},j=()=>{const t=s.price,d=n==="year"?t*12:t;return s.currency==="USD"||s.currency,n==="year"?e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-3xl font-bold text-gray-900",children:["৳",(d*85).toLocaleString()," "]}),e.jsxs("div",{className:"text-sm text-gray-500",children:["৳",(t*85).toLocaleString(),"/month, billed annually"]})]}):e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-3xl font-bold text-gray-900",children:["৳",(t*85).toLocaleString()]}),e.jsx("div",{className:"text-sm text-gray-500",children:"per month"})]})},c=()=>{if(n==="year"){const t=s.price*12,d=s.price*12,w=t-d;if(w>0)return e.jsxs(P,{className:"bg-green-100 text-green-800 border-green-200 mt-2",children:["Save ৳",(w*85).toLocaleString()," per year"]})}return null},y=()=>e.jsxs("div",{className:"space-y-3",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Supported Payment Methods:"}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[e.jsx(V,{className:"h-4 w-4 text-green-600"}),"Mobile Banking"]}),e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[e.jsx(X,{className:"h-4 w-4 text-blue-600"}),"Internet Banking"]}),e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[e.jsx(S,{className:"h-4 w-4 text-purple-600"}),"Card Payments"]}),e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[e.jsx(A,{className:"h-4 w-4 text-orange-600"}),"Local Banks"]})]})]});return s.supports_online_payment?e.jsxs(u,{className:"w-full max-w-md border-green-200",children:[e.jsxs(N,{className:"text-center bg-gradient-to-br from-green-50 to-green-100",children:[e.jsxs("div",{className:"flex items-center justify-center gap-2 mb-2",children:[e.jsx("div",{className:"w-8 h-8 rounded-lg bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center",children:e.jsx("span",{className:"text-white font-semibold text-sm",children:"SP"})}),e.jsx(P,{className:"bg-green-100 text-green-800 border-green-200",children:"ShurjoPay"})]}),e.jsx(v,{className:"text-green-900",children:s.display_name}),e.jsx(_,{className:"text-green-700",children:s.description}),e.jsxs("div",{className:"mt-4",children:[j(),c()]})]}),e.jsxs(g,{className:"space-y-6 pt-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"Features Included:"}),s.features.map((t,d)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(C,{className:"h-4 w-4 text-green-600 flex-shrink-0"}),e.jsx("span",{className:"text-sm text-gray-700",children:t})]},d))]}),y(),e.jsxs("div",{className:"pt-4 border-t border-gray-100",children:[e.jsx(b,{onClick:m,disabled:f||o||!s.supports_online_payment,className:"w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-md",size:"lg",children:o?e.jsxs(e.Fragment,{children:[e.jsx(B,{className:"h-4 w-4 animate-spin mr-2"}),"Redirecting to ShurjoPay..."]}):e.jsxs(e.Fragment,{children:[e.jsx(S,{className:"h-4 w-4 mr-2"}),"Pay with ShurjoPay"]})}),e.jsxs("div",{className:"mt-3 text-center",children:[e.jsx("p",{className:"text-xs text-gray-500",children:"🔒 Secure payment powered by ShurjoPay"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Leading payment gateway in Bangladesh"})]})]}),!s.supports_online_payment&&e.jsxs(R,{children:[e.jsx(Y,{className:"h-4 w-4"}),e.jsx(U,{className:"text-sm",children:"Online payments not available for this plan"})]})]})]}):e.jsxs(R,{variant:"destructive",children:[e.jsx(Y,{className:"h-4 w-4"}),e.jsx(U,{children:"Online payments are not available for this plan. Please contact support for assistance."})]})}function ae({plan:s,billingCycle:n,onError:a,disabled:f=!1}){const[o,x]=p.useState(!1),m=(t,d)=>{},j=async()=>{if(!s.crypto_payment_enabled){const t="Crypto payments are not available for this plan";m("Checkout failed: Crypto payments not enabled",{plan_id:s.id}),l.error(t),a==null||a(t);return}if(!s.has_coinbase_commerce_integration){const t="Coinbase Commerce integration not configured for this plan";m("Checkout failed: No Coinbase Commerce integration",{plan_id:s.id}),l.error(t),a==null||a(t);return}if(!s.supports_monthly&&n==="month"){const t="This plan does not support monthly billing";m("Checkout failed: Monthly billing not supported",{plan_id:s.id}),l.error(t),a==null||a(t);return}if(!s.supports_yearly&&n==="year"){const t="This plan does not support yearly billing";m("Checkout failed: Yearly billing not supported",{plan_id:s.id}),l.error(t),a==null||a(t);return}m("Starting Coinbase Commerce checkout",{plan_id:s.id,plan_name:s.name}),x(!0);try{const t=await F(route("coinbase-commerce.charge"),s.id,n,"Coinbase Commerce");if(m("Checkout request successful",{charge_id:t.charge_id,transaction_id:t.transaction_id}),t.hosted_url)m("Redirecting to Coinbase Commerce",{hosted_url:t.hosted_url}),window.location.href=t.hosted_url;else throw new Error("No hosted checkout URL received from Coinbase Commerce")}catch(t){const d=t instanceof Error?t.message:"An unexpected error occurred";O("Coinbase Commerce",t),l.error(d),a==null||a(d)}finally{x(!1)}},c=()=>[{name:"Bitcoin",symbol:"BTC",icon:"₿"},{name:"Ethereum",symbol:"ETH",icon:"Ξ"},{name:"USD Coin",symbol:"USDC",icon:"$"},{name:"Dai",symbol:"DAI",icon:"◈"},{name:"Litecoin",symbol:"LTC",icon:"Ł"},{name:"Bitcoin Cash",symbol:"BCH",icon:"₿"}],y=()=>[{icon:e.jsx(D,{className:"h-4 w-4 text-orange-600"}),title:"Auto USDC Settlement",description:"Avoid volatility with guaranteed settlement"},{icon:e.jsx(A,{className:"h-4 w-4 text-blue-600"}),title:"Hundreds of Currencies",description:"Accept payments in any supported crypto"},{icon:e.jsx(z,{className:"h-4 w-4 text-green-600"}),title:"Instant Confirmation",description:"Low-cost transactions on Base & Polygon"}];return e.jsxs(u,{className:"w-full max-w-md mx-auto border-2 border-orange-200 dark:border-orange-800 bg-gradient-to-br from-orange-50/30 to-amber-50/20 dark:from-orange-950/20 dark:to-amber-950/10 shadow-lg hover:shadow-xl transition-all duration-200",children:[e.jsxs(N,{className:"text-center pb-4",children:[e.jsx("div",{className:"flex items-center justify-center mb-3",children:e.jsx("div",{className:"w-12 h-12 rounded-xl bg-gradient-to-br from-orange-500 to-orange-600 flex items-center justify-center shadow-lg ring-2 ring-orange-200 dark:ring-orange-800",children:e.jsx(T,{className:"w-6 h-6 text-white"})})}),e.jsx(v,{className:"text-xl font-bold bg-gradient-to-r from-orange-600 to-amber-600 bg-clip-text text-transparent dark:from-orange-400 dark:to-amber-400",children:"Coinbase Commerce"}),e.jsx(_,{className:"text-sm text-muted-foreground",children:"Pay with cryptocurrency using Onchain Payment Protocol"})]}),e.jsxs(g,{className:"space-y-4",children:[e.jsxs("div",{className:"bg-white/50 dark:bg-gray-900/30 rounded-lg p-3 border border-orange-100 dark:border-orange-900",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Plan:"}),e.jsx("span",{className:"text-sm font-semibold text-orange-700 dark:text-orange-300",children:s.display_name})]}),e.jsxs("div",{className:"flex justify-between items-center mt-1",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Billing:"}),e.jsx("span",{className:"text-sm font-semibold text-orange-700 dark:text-orange-300",children:n==="year"?"Yearly":"Monthly"})]}),e.jsxs("div",{className:"flex justify-between items-center mt-1",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Amount:"}),e.jsx("span",{className:"text-lg font-bold text-orange-700 dark:text-orange-300",children:s.formatted_price})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("h4",{className:"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center",children:[e.jsx(A,{className:"h-4 w-4 mr-2 text-orange-600"}),"Supported Cryptocurrencies"]}),e.jsx("div",{className:"grid grid-cols-3 gap-2",children:c().map(t=>e.jsxs("div",{className:"flex items-center space-x-1 text-xs",children:[e.jsx("span",{className:"text-orange-600 font-mono",children:t.icon}),e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:t.symbol})]},t.symbol))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("h4",{className:"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center",children:[e.jsx(z,{className:"h-4 w-4 mr-2 text-orange-600"}),"Onchain Protocol Features"]}),e.jsx("div",{className:"space-y-2",children:y().map((t,d)=>e.jsxs("div",{className:"flex items-start space-x-2",children:[t.icon,e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-xs font-medium text-gray-700 dark:text-gray-300",children:t.title}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:t.description})]})]},d))})]}),e.jsxs("div",{className:"pt-4 border-t border-orange-100 dark:border-orange-900",children:[e.jsx(b,{onClick:j,disabled:f||o||!s.crypto_payment_enabled,className:"w-full bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 text-white shadow-md",size:"lg",children:o?e.jsxs(e.Fragment,{children:[e.jsx(B,{className:"h-4 w-4 animate-spin mr-2"}),"Creating Charge..."]}):e.jsxs(e.Fragment,{children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"Pay with Crypto"]})}),e.jsxs("div",{className:"mt-3 text-center",children:[e.jsxs("p",{className:"text-xs text-gray-500 flex items-center justify-center",children:[e.jsx(D,{className:"h-3 w-3 mr-1"}),"Secure crypto payments powered by Coinbase Commerce"]}),e.jsxs("p",{className:"text-xs text-gray-500 mt-1 flex items-center justify-center",children:[e.jsx(E,{className:"h-3 w-3 mr-1"}),"Onchain Payment Protocol with auto USDC settlement"]})]})]})]})]})}function re({plan:s,billingCycle:n,onBillingCycleChange:a}){const f=()=>s.has_online_payment_enabled?s.has_shurjopay_integration?"shurjopay":s.has_coinbase_commerce_integration&&s.crypto_payment_enabled?"coinbase":"paddle":"offline",[o,x]=p.useState(f()),m=r=>{console.log("Paddle payment successful:",r),window.location.href=route("subscription.success",{transaction_id:r,gateway:"paddle"})},j=r=>{console.error("Paddle payment error:",r),r.includes("Development Mode:")||(l.error(r),r.includes("failed")||r.includes("declined")?window.location.href=route("subscription.cancelled",{reason:r,gateway:"paddle"}):x("offline"))},c=r=>{console.log("ShurjoPay payment successful:",r),window.location.href=route("subscription.success",{transaction_id:r,gateway:"shurjopay"})},y=r=>{console.error("ShurjoPay payment error:",r),l.error(r),r.includes("failed")||r.includes("declined")?window.location.href=route("subscription.cancelled",{reason:r,gateway:"shurjopay"}):x("offline")},t=r=>{console.log("Coinbase Commerce payment successful:",r),window.location.href=route("subscription.success",{transaction_id:r,gateway:"coinbase_commerce"})},d=r=>{console.error("Coinbase Commerce payment error:",r),l.error(r),r.includes("failed")||r.includes("declined")||r.includes("expired")?window.location.href=route("subscription.cancelled",{reason:r,gateway:"coinbase_commerce"}):x("offline")},w=()=>{const r=[];return s.has_online_payment_enabled&&s.has_paddle_integration&&r.push({id:"paddle",name:"Pay with Card",icon:S,badge:{text:"Instant",color:"blue"},description:"Credit & Debit Cards"}),s.has_online_payment_enabled&&s.has_shurjopay_integration&&r.push({id:"shurjopay",name:"ShurjoPay",icon:V,badge:{text:"Local",color:"green"},description:"Mobile & Internet Banking"}),s.crypto_payment_enabled&&s.has_coinbase_commerce_integration&&r.push({id:"coinbase",name:"Pay with Crypto",icon:T,badge:{text:"Onchain",color:"orange"},description:"Bitcoin, Ethereum & more"}),s.has_offline_payment_enabled&&r.push({id:"offline",name:"Offline Payment",icon:L,badge:{text:"Manual",color:"gray"},description:"Bank Transfer & Manual Review"}),r};return e.jsxs("div",{className:"space-y-8",children:[s.supports_monthly&&s.supports_yearly&&e.jsx("div",{className:"flex justify-center",children:e.jsxs("div",{className:"inline-flex items-center bg-muted/50 rounded-xl p-1.5 border border-border/50",children:[e.jsx(b,{variant:n==="month"?"default":"ghost",size:"sm",onClick:()=>a("month"),className:"rounded-lg px-6 py-2 font-medium transition-all duration-200",children:"Monthly"}),e.jsxs(b,{variant:n==="year"?"default":"ghost",size:"sm",onClick:()=>a("year"),className:"rounded-lg px-6 py-2 font-medium transition-all duration-200",children:["Yearly",e.jsx(P,{variant:"secondary",className:"ml-2 bg-green-500/10 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800",children:"Save 20%"})]})]})}),s.has_any_payment_method?e.jsxs("div",{className:"grid lg:grid-cols-4 gap-6 lg:gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsxs("div",{className:"lg:sticky lg:top-6",children:[e.jsx("h4",{className:"text-sm font-semibold text-muted-foreground uppercase tracking-wider mb-4 lg:block hidden",children:"Payment Options"}),e.jsx("div",{className:"space-y-3 lg:space-y-3 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 gap-3 lg:gap-0",children:w().map(r=>{const $=r.icon,h=o===r.id;return e.jsx("button",{onClick:()=>x(r.id),className:`w-full p-4 lg:p-4 sm:p-3 rounded-xl border-2 transition-all duration-200 text-left ${h?"border-primary bg-primary/5 shadow-lg ring-1 ring-primary/20":"border-border/50 hover:border-primary/50 hover:bg-muted/30 hover:shadow-md"}`,children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:`w-10 h-10 rounded-lg flex items-center justify-center ${h?"bg-primary/10":"bg-muted/50"}`,children:e.jsx($,{className:`w-5 h-5 ${h?"text-primary":"text-muted-foreground"}`})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h3",{className:`font-semibold text-sm ${h?"text-foreground":"text-foreground/80"}`,children:r.name}),e.jsx(P,{variant:r.badge.color==="gray"?"outline":"secondary",className:`text-xs ${r.badge.color==="blue"?"bg-blue-500/10 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-800":r.badge.color==="green"?"bg-green-500/10 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800":r.badge.color==="orange"?"bg-orange-500/10 text-orange-700 dark:text-orange-400 border-orange-200 dark:border-orange-800":"border-muted-foreground/30"}`,children:r.badge.text})]}),e.jsx("p",{className:"text-xs text-muted-foreground",children:r.description})]})]})},r.id)})})]})}),e.jsx("div",{className:"lg:col-span-3",children:e.jsxs("div",{className:"bg-gradient-to-br from-muted/20 to-muted/5 rounded-xl p-6 lg:p-8 min-h-[400px]",children:[o==="paddle"&&s.has_online_payment_enabled&&s.has_paddle_integration&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center space-y-2 mb-6",children:[e.jsx("h3",{className:"text-xl font-bold text-foreground",children:"Pay with Credit Card"}),e.jsx("p",{className:"text-muted-foreground",children:"Secure payment powered by Paddle"})]}),e.jsx("div",{className:"flex justify-center mb-6",children:e.jsx(se,{plan:s,billingCycle:n,onSuccess:m,onError:j})}),e.jsx("div",{className:"bg-gradient-to-r from-green-50 to-green-100/50 dark:from-green-950/20 dark:to-green-900/10 border border-green-200 dark:border-green-800 rounded-xl p-4 sm:p-6",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"w-10 h-10 bg-green-500/10 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx(C,{className:"h-5 w-5 text-green-600 dark:text-green-400"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-green-900 dark:text-green-100 mb-1",children:"Instant Activation"}),e.jsx("p",{className:"text-sm text-green-700 dark:text-green-300",children:"Your subscription will be activated immediately after payment"})]})]})})]}),o==="shurjopay"&&s.has_online_payment_enabled&&s.has_shurjopay_integration&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center space-y-2 mb-6",children:[e.jsx("h3",{className:"text-xl font-bold text-foreground",children:"Pay with ShurjoPay"}),e.jsx("p",{className:"text-muted-foreground",children:"Secure payment with local banking support"})]}),e.jsx("div",{className:"flex justify-center mb-6",children:e.jsx(te,{plan:s,billingCycle:n,onSuccess:c,onError:y})}),e.jsx("div",{className:"bg-gradient-to-r from-green-50 to-green-100/50 dark:from-green-950/20 dark:to-green-900/10 border border-green-200 dark:border-green-800 rounded-xl p-4 sm:p-6",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"w-10 h-10 bg-green-500/10 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx(C,{className:"h-5 w-5 text-green-600 dark:text-green-400"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-green-900 dark:text-green-100 mb-1",children:"Local Payment Methods"}),e.jsx("p",{className:"text-sm text-green-700 dark:text-green-300",children:"Pay using mobile banking, internet banking, or local cards"})]})]})})]}),o==="coinbase"&&s.crypto_payment_enabled&&s.has_coinbase_commerce_integration&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center space-y-2 mb-6",children:[e.jsx("h3",{className:"text-xl font-bold text-foreground",children:"Pay with Cryptocurrency"}),e.jsx("p",{className:"text-muted-foreground",children:"Secure crypto payments with Onchain Payment Protocol"})]}),e.jsx("div",{className:"flex justify-center mb-6",children:e.jsx(ae,{plan:s,billingCycle:n,onSuccess:t,onError:d})}),e.jsx("div",{className:"bg-gradient-to-r from-orange-50 to-orange-100/50 dark:from-orange-950/20 dark:to-orange-900/10 border border-orange-200 dark:border-orange-800 rounded-xl p-4 sm:p-6",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"w-10 h-10 bg-orange-500/10 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx(C,{className:"h-5 w-5 text-orange-600 dark:text-orange-400"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-orange-900 dark:text-orange-100 mb-1",children:"Onchain Payment Protocol"}),e.jsx("p",{className:"text-sm text-orange-700 dark:text-orange-300",children:"Auto USDC settlement, hundreds of currencies, instant confirmation"})]})]})})]}),o==="offline"&&s.has_offline_payment_enabled&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center space-y-2 mb-6",children:[e.jsx("h3",{className:"text-xl font-bold text-foreground",children:"Offline Payment"}),e.jsx("p",{className:"text-muted-foreground",children:"Submit payment proof for manual approval"})]}),e.jsxs(u,{className:"border-0 shadow-lg bg-gradient-to-br from-card to-card/50",children:[e.jsxs(N,{className:"pb-4",children:[e.jsxs(v,{className:"flex items-center gap-3 text-lg",children:[e.jsx("div",{className:"w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center",children:e.jsx(L,{className:"h-5 w-5 text-primary"})}),"Payment Request"]}),e.jsx(_,{className:"text-base",children:"Submit your payment details and proof for manual verification"})]}),e.jsxs(g,{className:"space-y-6",children:[e.jsxs("div",{className:"bg-muted/30 rounded-xl p-4 space-y-3",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-muted-foreground",children:"Plan:"}),e.jsx("span",{className:"font-semibold text-foreground",children:s.display_name})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-muted-foreground",children:"Billing:"}),e.jsx("span",{className:"font-semibold text-foreground",children:n==="year"?"Yearly":"Monthly"})]}),e.jsxs("div",{className:"flex justify-between items-center pt-2 border-t border-border/50",children:[e.jsx("span",{className:"text-muted-foreground",children:"Amount:"}),e.jsxs("span",{className:"font-bold text-xl text-primary",children:["$",s.price," ",s.currency]})]})]}),e.jsx(H,{href:route("payment-requests.create"),children:e.jsxs(b,{className:"w-full h-12 text-base font-semibold",size:"lg",children:[e.jsx(L,{className:"h-5 w-5 mr-2"}),"Submit Payment Request"]})})]})]}),e.jsx("div",{className:"bg-gradient-to-r from-blue-50 to-blue-100/50 dark:from-blue-950/20 dark:to-blue-900/10 border border-blue-200 dark:border-blue-800 rounded-xl p-4 sm:p-6",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"w-10 h-10 bg-blue-500/10 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx(I,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-blue-900 dark:text-blue-100 mb-1",children:"Manual Review"}),e.jsx("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:"Your payment will be reviewed and activated within 24-48 hours"})]})]})})]})]})})]}):e.jsx("div",{className:"text-center py-12",children:e.jsx(u,{className:"border-0 shadow-lg bg-gradient-to-br from-yellow-50 to-yellow-100/50 dark:from-yellow-950/20 dark:to-yellow-900/10 max-w-md mx-auto",children:e.jsxs(g,{className:"p-8",children:[e.jsxs("div",{className:"flex items-center justify-center gap-3 mb-6",children:[e.jsx("div",{className:"w-12 h-12 bg-yellow-500/10 rounded-full flex items-center justify-center",children:e.jsx(G,{className:"h-6 w-6 text-yellow-600 dark:text-yellow-400"})}),e.jsx("h3",{className:"text-xl font-bold text-yellow-900 dark:text-yellow-100",children:"No Payment Methods Available"})]}),e.jsx("p",{className:"text-yellow-700 dark:text-yellow-300 mb-6 leading-relaxed",children:"This plan currently has no payment methods enabled. Please contact support for assistance."}),e.jsx(b,{variant:"outline",className:"border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-700 dark:text-yellow-300 dark:hover:bg-yellow-900/20",asChild:!0,children:e.jsx("a",{href:"mailto:<EMAIL>",children:"Contact Support"})})]})})})]})}function ne({plan:s}){const[n,a]=p.useState(!1),[f,o]=p.useState(""),[x,m]=p.useState(!1),[j,c]=p.useState({}),[y,t]=p.useState(!1);return null}function ze({plan:s}){const[n,a]=p.useState("month");return p.useEffect(()=>{},[]),e.jsxs(Z,{children:[e.jsx(W,{title:`Checkout - ${s.display_name}`}),e.jsxs(K,{children:[e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-background via-background to-muted/20",children:e.jsx("div",{className:"py-8 sm:py-12",children:e.jsxs("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"mb-8 sm:mb-12",children:[e.jsx(H,{href:route("subscription.plans"),children:e.jsxs(b,{variant:"outline",size:"sm",className:"mb-6 hover:bg-primary/5 transition-colors duration-200 border-border/50",children:[e.jsx(ee,{className:"w-4 h-4 mr-2"}),"Back to Plans"]})}),e.jsxs("div",{className:"text-center space-y-4",children:[e.jsxs("div",{className:"inline-flex items-center gap-2 px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium mb-4",children:[e.jsx(q,{className:"w-4 h-4"}),"Premium Subscription"]}),e.jsxs("h1",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent mb-3",children:["Subscribe to ",s.display_name]}),e.jsx("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Choose your preferred payment method and get instant access to premium features"})]})]}),e.jsxs("div",{className:"grid lg:grid-cols-3 gap-6 sm:gap-8 mb-8 sm:mb-12",children:[e.jsx("div",{className:"lg:col-span-2",children:e.jsxs(u,{className:"border-0 shadow-xl bg-gradient-to-br from-card to-card/50 backdrop-blur-sm",children:[e.jsx(N,{className:"pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(v,{className:"text-xl sm:text-2xl font-bold text-foreground",children:s.display_name}),e.jsx(_,{className:"text-base text-muted-foreground mt-2",children:s.description})]}),s.is_popular&&e.jsx("div",{className:"bg-primary text-primary-foreground px-3 py-1 rounded-full text-xs font-semibold",children:"Most Popular"})]})}),e.jsxs(g,{className:"space-y-6",children:[e.jsxs("div",{className:"bg-muted/30 rounded-xl p-4 sm:p-6",children:[e.jsxs("div",{className:"flex items-baseline gap-2 mb-2",children:[e.jsxs("span",{className:"text-3xl sm:text-4xl font-bold text-foreground",children:["$",s.price]}),e.jsxs("span",{className:"text-muted-foreground",children:["/",s.interval]})]}),e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Billed ",s.interval,"ly • Cancel anytime"]})]}),e.jsxs("div",{className:"grid sm:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-muted/20 rounded-lg",children:[e.jsx("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center",children:e.jsx(M,{className:"w-4 h-4 text-primary"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-sm",children:"Search Limit"}),e.jsx("div",{className:"text-xs text-muted-foreground",children:s.search_limit===-1?"Unlimited":`${s.search_limit} per day`})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-muted/20 rounded-lg",children:[e.jsx("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center",children:e.jsx(D,{className:"w-4 h-4 text-primary"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-sm",children:"Support"}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Priority Email"})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-muted/20 rounded-lg",children:[e.jsx("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center",children:e.jsx(M,{className:"w-4 h-4 text-primary"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-sm",children:"High-res Images"}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Included"})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-muted/20 rounded-lg",children:[e.jsx("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center",children:e.jsx(I,{className:"w-4 h-4 text-primary"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-sm",children:"Activation"}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Instant"})]})]})]})]})]})}),e.jsx("div",{className:"lg:col-span-1",children:e.jsxs(u,{className:"border-0 shadow-lg bg-gradient-to-br from-primary/5 to-primary/10 h-full",children:[e.jsx(N,{children:e.jsxs(v,{className:"text-lg font-semibold flex items-center gap-2",children:[e.jsx(q,{className:"w-5 h-5 text-primary"}),"Features Included"]})}),e.jsx(g,{children:e.jsx("ul",{className:"space-y-3",children:s.features.map((f,o)=>e.jsxs("li",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:e.jsx(M,{className:"w-3 h-3 text-primary"})}),e.jsx("span",{className:"text-sm text-foreground leading-relaxed",children:f})]},o))})})]})})]}),e.jsx("div",{className:"max-w-6xl mx-auto",children:e.jsxs(u,{className:"border-0 shadow-xl bg-gradient-to-br from-card to-card/50 backdrop-blur-sm",children:[e.jsxs(N,{className:"text-center pb-6",children:[e.jsx(v,{className:"text-2xl font-bold text-foreground",children:"Choose Payment Method"}),e.jsx(_,{className:"text-base text-muted-foreground",children:"Select your preferred payment option to complete your subscription"})]}),e.jsx(g,{children:e.jsx(re,{plan:s,billingCycle:n,onBillingCycleChange:a})})]})}),e.jsx("div",{className:"mt-8 sm:mt-12 max-w-6xl mx-auto",children:e.jsxs("div",{className:"grid sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6",children:[e.jsx(u,{className:"border-0 bg-gradient-to-br from-green-50 to-green-100/50 dark:from-green-950/20 dark:to-green-900/10",children:e.jsx(g,{className:"p-4 sm:p-6",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-green-500/10 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx(D,{className:"w-5 h-5 text-green-600 dark:text-green-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-green-900 dark:text-green-100 mb-1",children:"Secure & Encrypted"}),e.jsx("p",{className:"text-sm text-green-700 dark:text-green-300",children:"Your payment information is protected with industry-standard security measures"})]})]})})}),e.jsx(u,{className:"border-0 bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-950/20 dark:to-blue-900/10",children:e.jsx(g,{className:"p-4 sm:p-6",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-blue-500/10 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx(M,{className:"w-5 h-5 text-blue-600 dark:text-blue-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-blue-900 dark:text-blue-100 mb-1",children:"Instant Activation"}),e.jsx("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:"Your subscription will be activated immediately after payment"})]})]})})}),e.jsx(u,{className:"border-0 bg-gradient-to-br from-purple-50 to-purple-100/50 dark:from-purple-950/20 dark:to-purple-900/10 sm:col-span-2 lg:col-span-1",children:e.jsx(g,{className:"p-4 sm:p-6",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-purple-500/10 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx(I,{className:"w-5 h-5 text-purple-600 dark:text-purple-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-purple-900 dark:text-purple-100 mb-1",children:"24/7 Support"}),e.jsx("p",{className:"text-sm text-purple-700 dark:text-purple-300 mb-2",children:"Need help? We're here for you"}),e.jsx("a",{href:"mailto:<EMAIL>",className:"text-sm font-medium text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 transition-colors",children:"Contact Support →"})]})]})})})]})})]})})}),e.jsx(ne,{plan:s})]})]})}export{ze as default};
