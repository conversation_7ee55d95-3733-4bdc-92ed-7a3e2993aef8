import{j as e,Q as T,t as a,S as x}from"./app-J5EqS6dS.js";import{C as i,a as n,b as c,c as r,d as S}from"./card-9XCADs-4.js";import{B as l}from"./badge-BucYuCBs.js";import{c as N,B as d}from"./smartphone-GGiwNneF.js";import{A as k,D as h}from"./app-layout-ox1kAwY6.js";import{u as A}from"./use-delete-confirmation-CFAJok5Z.js";import{P as f,k as B,l as L,o as M,w as m}from"./ImpersonationBanner-CYn5eDk6.js";import{C as p}from"./circle-check-big-DOFoatRy.js";import{U as z}from"./users-RYmOyic9.js";import{S as g}from"./star-D0YOm-Sd.js";import{S as E}from"./square-pen-Bepbg6wc.js";import{E as I}from"./eye-D-fsmYB2.js";import{C as $}from"./copy-BXzJyUa4.js";import{T as F}from"./trash-2-B3ZEh4hl.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./search-DBK6jUoc.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R=[["rect",{width:"20",height:"12",x:"2",y:"6",rx:"6",ry:"6",key:"f2vt7d"}],["circle",{cx:"8",cy:"12",r:"2",key:"1nvbw3"}]],U=N("ToggleLeft",R);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Q=[["rect",{width:"20",height:"12",x:"2",y:"6",rx:"6",ry:"6",key:"f2vt7d"}],["circle",{cx:"16",cy:"12",r:"2",key:"4ma0v8"}]],q=N("ToggleRight",Q);function ve({pricingPlans:t}){var u;const{showDeleteConfirmation:v}=A(),w=s=>{if(s.subscriptions_count>0){alert("Cannot delete pricing plan with active subscriptions.");return}v({title:"Delete Pricing Plan",description:`Are you sure you want to delete "${s.display_name}"? This action cannot be undone.`,onConfirm:()=>{x.delete(route("admin.pricing-plans.destroy",s.id))}})},y=s=>{x.post(route("admin.pricing-plans.toggle-active",s.id))},b=s=>{x.post(route("admin.pricing-plans.duplicate",s.id))},C=s=>s.is_active?s.is_default?e.jsx(l,{variant:"default",children:"Default"}):s.is_popular?e.jsx(l,{variant:"destructive",children:"Popular"}):e.jsx(l,{variant:"outline",children:"Active"}):e.jsx(l,{variant:"secondary",children:"Inactive"}),_=s=>s===-1?"Unlimited":`${s} per day`;return e.jsxs(k,{children:[e.jsx(T,{title:"Pricing Plans Management"}),e.jsxs("div",{className:"flex h-full flex-1 flex-col p-4 space-y-6",children:[e.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Pricing Plans"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Manage subscription pricing plans and features"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(a,{href:route("admin.dashboard"),children:e.jsxs(d,{variant:"outline",size:"sm",children:[e.jsx(h,{className:"mr-2 h-4 w-4"}),"Dashboard"]})}),e.jsx(a,{href:route("admin.pricing-plans.create"),children:e.jsxs(d,{size:"sm",children:[e.jsx(f,{className:"mr-2 h-4 w-4"}),"Create Plan"]})})]})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-4",children:[e.jsxs(i,{children:[e.jsxs(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(c,{className:"text-sm font-medium",children:"Total Plans"}),e.jsx(h,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsx(r,{children:e.jsx("div",{className:"text-2xl font-bold",children:t.length})})]}),e.jsxs(i,{children:[e.jsxs(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(c,{className:"text-sm font-medium",children:"Active Plans"}),e.jsx(p,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsx(r,{children:e.jsx("div",{className:"text-2xl font-bold",children:t.filter(s=>s.is_active).length})})]}),e.jsxs(i,{children:[e.jsxs(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(c,{className:"text-sm font-medium",children:"Total Subscriptions"}),e.jsx(z,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsx(r,{children:e.jsx("div",{className:"text-2xl font-bold",children:t.reduce((s,o)=>s+o.subscriptions_count,0)})})]}),e.jsxs(i,{children:[e.jsxs(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(c,{className:"text-sm font-medium",children:"Popular Plan"}),e.jsx(g,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsx(r,{children:e.jsx("div",{className:"text-2xl font-bold",children:((u=t.find(s=>s.is_popular))==null?void 0:u.display_name)||"None"})})]})]}),e.jsx("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:t.map(s=>{var o,j;return e.jsxs(i,{className:`relative ${s.is_popular?"ring-2 ring-primary":""}`,children:[s.is_popular&&e.jsx("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:e.jsxs(l,{variant:"destructive",className:"px-3 py-1",children:[e.jsx(g,{className:"mr-1 h-3 w-3"}),"Popular"]})}),e.jsxs(n,{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(c,{className:"text-xl",children:s.display_name}),C(s)]}),e.jsx(S,{children:s.description}),e.jsx("div",{className:"text-3xl font-bold",children:s.formatted_price})]}),e.jsxs(r,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium mb-2",children:"Features:"}),e.jsxs("ul",{className:"text-sm text-muted-foreground space-y-1",children:[(o=s.features)==null?void 0:o.slice(0,3).map((D,P)=>e.jsxs("li",{className:"flex items-center",children:[e.jsx(p,{className:"mr-2 h-3 w-3 text-green-500"}),D]},P)),((j=s.features)==null?void 0:j.length)>3&&e.jsxs("li",{className:"text-xs text-muted-foreground",children:["+",s.features.length-3," more features"]})]})]}),e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{children:"Search Limit:"}),e.jsx("span",{className:"font-medium",children:_(s.search_limit)})]}),e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{children:"Subscriptions:"}),e.jsx("span",{className:"font-medium",children:s.subscriptions_count})]}),e.jsxs("div",{className:"flex items-center gap-2 pt-4",children:[e.jsx(a,{href:route("admin.pricing-plans.edit",s.id),children:e.jsxs(d,{size:"sm",variant:"outline",children:[e.jsx(E,{className:"mr-1 h-3 w-3"}),"Edit"]})}),e.jsxs(B,{children:[e.jsx(L,{asChild:!0,children:e.jsx(d,{size:"sm",variant:"outline",children:"Actions"})}),e.jsxs(M,{children:[e.jsx(m,{asChild:!0,children:e.jsxs(a,{href:route("admin.pricing-plans.show",s.id),children:[e.jsx(I,{className:"mr-2 h-4 w-4"}),"View Details"]})}),e.jsxs(m,{onClick:()=>b(s),children:[e.jsx($,{className:"mr-2 h-4 w-4"}),"Duplicate"]}),e.jsx(m,{onClick:()=>y(s),children:s.is_active?e.jsxs(e.Fragment,{children:[e.jsx(U,{className:"mr-2 h-4 w-4"}),"Deactivate"]}):e.jsxs(e.Fragment,{children:[e.jsx(q,{className:"mr-2 h-4 w-4"}),"Activate"]})}),s.subscriptions_count===0&&e.jsxs(m,{onClick:()=>w(s),className:"text-destructive",children:[e.jsx(F,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})]})]})]},s.id)})}),t.length===0&&e.jsx(i,{children:e.jsxs(r,{className:"flex flex-col items-center justify-center py-12",children:[e.jsx(h,{className:"h-12 w-12 text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No pricing plans found"}),e.jsx("p",{className:"text-muted-foreground text-center mb-4",children:"Get started by creating your first pricing plan."}),e.jsx(a,{href:route("admin.pricing-plans.create"),children:e.jsxs(d,{children:[e.jsx(f,{className:"mr-2 h-4 w-4"}),"Create Pricing Plan"]})})]})})]})]})}export{ve as default};
