import{r as d,U as _,j as a,S as Ht,x as pr,Q as gr,t as un}from"./app-J5EqS6dS.js";import{C as pt,a as gt,b as vt,d as mt,c as bt}from"./card-9XCADs-4.js";import{c as En,a as vr,B as he}from"./smartphone-GGiwNneF.js";import{I as Me}from"./input-Bo8dOn9p.js";import{L as H}from"./label-BlOrdc-X.js";import{S as ze,a as Fe,b as Be,c as $e,d as xe}from"./select-CIhY0l9J.js";import{S as fn}from"./switch-yFNfZ5X-.js";import{T as mr,a as br,b as Lt,c as Pt}from"./tabs-DZAL-HvD.js";import{t as _e,M as zt,P as Vt}from"./ImpersonationBanner-CYn5eDk6.js";import{A as xr}from"./app-layout-ox1kAwY6.js";import{r as Ye}from"./index-CJpBU2i9.js";import{B as et}from"./badge-BucYuCBs.js";import{E as yr}from"./eye-off-BGSyeByl.js";import{E as wr}from"./external-link-A4n9PP4e.js";import{T as Cr}from"./trash-2-B3ZEh4hl.js";import{A as Sr}from"./arrow-left-D4U9AVF9.js";import{S as jr}from"./users-RYmOyic9.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./index-BzZWUWqx.js";import"./crown-UDSxMtlm.js";import"./shield-D9nQfigG.js";import"./user-DCnDRzMf.js";import"./search-DBK6jUoc.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./eye-D-fsmYB2.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dr=[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]],In=En("GripVertical",Dr);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nr=[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]],Rr=En("Pencil",Nr);function On(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=On(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}function Ft(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=On(e))&&(r&&(r+=" "),r+=t);return r}function Er(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return d.useMemo(()=>r=>{t.forEach(s=>s(r))},t)}const Nt=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";function Ve(e){const t=Object.prototype.toString.call(e);return t==="[object Window]"||t==="[object global]"}function Qt(e){return"nodeType"in e}function Q(e){var t,n;return e?Ve(e)?e:Qt(e)&&(t=(n=e.ownerDocument)==null?void 0:n.defaultView)!=null?t:window:window}function Zt(e){const{Document:t}=Q(e);return e instanceof t}function at(e){return Ve(e)?!1:e instanceof Q(e).HTMLElement}function An(e){return e instanceof Q(e).SVGElement}function Ke(e){return e?Ve(e)?e.document:Qt(e)?Zt(e)?e:at(e)||An(e)?e.ownerDocument:document:document:document}const pe=Nt?d.useLayoutEffect:d.useEffect;function Rt(e){const t=d.useRef(e);return pe(()=>{t.current=e}),d.useCallback(function(){for(var n=arguments.length,r=new Array(n),s=0;s<n;s++)r[s]=arguments[s];return t.current==null?void 0:t.current(...r)},[])}function Ir(){const e=d.useRef(null),t=d.useCallback((r,s)=>{e.current=setInterval(r,s)},[]),n=d.useCallback(()=>{e.current!==null&&(clearInterval(e.current),e.current=null)},[]);return[t,n]}function st(e,t){t===void 0&&(t=[e]);const n=d.useRef(e);return pe(()=>{n.current!==e&&(n.current=e)},t),n}function lt(e,t){const n=d.useRef();return d.useMemo(()=>{const r=e(n.current);return n.current=r,r},[...t])}function xt(e){const t=Rt(e),n=d.useRef(null),r=d.useCallback(s=>{s!==n.current&&(t==null||t(s,n.current)),n.current=s},[]);return[n,r]}function yt(e){const t=d.useRef();return d.useEffect(()=>{t.current=e},[e]),t.current}let Bt={};function ct(e,t){return d.useMemo(()=>{if(t)return t;const n=Bt[e]==null?0:Bt[e]+1;return Bt[e]=n,e+"-"+n},[e,t])}function Mn(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),s=1;s<n;s++)r[s-1]=arguments[s];return r.reduce((i,o)=>{const l=Object.entries(o);for(const[c,p]of l){const u=i[c];u!=null&&(i[c]=u+e*p)}return i},{...t})}}const He=Mn(1),wt=Mn(-1);function Or(e){return"clientX"in e&&"clientY"in e}function Et(e){if(!e)return!1;const{KeyboardEvent:t}=Q(e.target);return t&&e instanceof t}function Ar(e){if(!e)return!1;const{TouchEvent:t}=Q(e.target);return t&&e instanceof t}function Ct(e){if(Ar(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return Or(e)?{x:e.clientX,y:e.clientY}:null}const ye=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[ye.Translate.toString(e),ye.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),hn="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function Mr(e){return e.matches(hn)?e:e.querySelector(hn)}const Tr={display:"none"};function _r(e){let{id:t,value:n}=e;return _.createElement("div",{id:t,style:Tr},n)}function kr(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;const s={position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"};return _.createElement("div",{id:t,style:s,role:"status","aria-live":r,"aria-atomic":!0},n)}function Lr(){const[e,t]=d.useState("");return{announce:d.useCallback(r=>{r!=null&&t(r)},[]),announcement:e}}const Tn=d.createContext(null);function Pr(e){const t=d.useContext(Tn);d.useEffect(()=>{if(!t)throw new Error("useDndMonitor must be used within a children of <DndContext>");return t(e)},[e,t])}function zr(){const[e]=d.useState(()=>new Set),t=d.useCallback(r=>(e.add(r),()=>e.delete(r)),[e]);return[d.useCallback(r=>{let{type:s,event:i}=r;e.forEach(o=>{var l;return(l=o[s])==null?void 0:l.call(o,i)})},[e]),t]}const Fr={draggable:`
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  `},Br={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function $r(e){let{announcements:t=Br,container:n,hiddenTextDescribedById:r,screenReaderInstructions:s=Fr}=e;const{announce:i,announcement:o}=Lr(),l=ct("DndLiveRegion"),[c,p]=d.useState(!1);if(d.useEffect(()=>{p(!0)},[]),Pr(d.useMemo(()=>({onDragStart(f){let{active:m}=f;i(t.onDragStart({active:m}))},onDragMove(f){let{active:m,over:v}=f;t.onDragMove&&i(t.onDragMove({active:m,over:v}))},onDragOver(f){let{active:m,over:v}=f;i(t.onDragOver({active:m,over:v}))},onDragEnd(f){let{active:m,over:v}=f;i(t.onDragEnd({active:m,over:v}))},onDragCancel(f){let{active:m,over:v}=f;i(t.onDragCancel({active:m,over:v}))}}),[i,t])),!c)return null;const u=_.createElement(_.Fragment,null,_.createElement(_r,{id:r,value:s.draggable}),_.createElement(kr,{id:l,announcement:o}));return n?Ye.createPortal(u,n):u}var B;(function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"})(B||(B={}));function St(){}function Wr(e,t){return d.useMemo(()=>({sensor:e,options:t??{}}),[e,t])}function Xr(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return d.useMemo(()=>[...t].filter(r=>r!=null),[...t])}const ge=Object.freeze({x:0,y:0});function Ur(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function Yr(e,t){const n=Ct(e);if(!n)return"0 0";const r={x:(n.x-t.left)/t.width*100,y:(n.y-t.top)/t.height*100};return r.x+"% "+r.y+"%"}function Hr(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function Vr(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function Kr(e,t){if(!e||e.length===0)return null;const[n]=e;return n[t]}function pn(e,t,n){return t===void 0&&(t=e.left),n===void 0&&(n=e.top),{x:t+e.width*.5,y:n+e.height*.5}}const Gr=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const s=pn(t,t.left,t.top),i=[];for(const o of r){const{id:l}=o,c=n.get(l);if(c){const p=Ur(pn(c),s);i.push({id:l,data:{droppableContainer:o,value:p}})}}return i.sort(Hr)};function qr(e,t){const n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),s=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height),o=s-r,l=i-n;if(r<s&&n<i){const c=t.width*t.height,p=e.width*e.height,u=o*l,f=u/(c+p-u);return Number(f.toFixed(4))}return 0}const Jr=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const s=[];for(const i of r){const{id:o}=i,l=n.get(o);if(l){const c=qr(l,t);c>0&&s.push({id:o,data:{droppableContainer:i,value:c}})}}return s.sort(Vr)};function Qr(e,t,n){return{...e,scaleX:t&&n?t.width/n.width:1,scaleY:t&&n?t.height/n.height:1}}function _n(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:ge}function Zr(e){return function(n){for(var r=arguments.length,s=new Array(r>1?r-1:0),i=1;i<r;i++)s[i-1]=arguments[i];return s.reduce((o,l)=>({...o,top:o.top+e*l.y,bottom:o.bottom+e*l.y,left:o.left+e*l.x,right:o.right+e*l.x}),{...n})}}const es=Zr(1);function kn(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}else if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}function ts(e,t,n){const r=kn(t);if(!r)return e;const{scaleX:s,scaleY:i,x:o,y:l}=r,c=e.left-o-(1-s)*parseFloat(n),p=e.top-l-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),u=s?e.width/s:e.width,f=i?e.height/i:e.height;return{width:u,height:f,top:p,right:c+u,bottom:p+f,left:c}}const ns={ignoreTransform:!1};function Ge(e,t){t===void 0&&(t=ns);let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:p,transformOrigin:u}=Q(e).getComputedStyle(e);p&&(n=ts(n,p,u))}const{top:r,left:s,width:i,height:o,bottom:l,right:c}=n;return{top:r,left:s,width:i,height:o,bottom:l,right:c}}function gn(e){return Ge(e,{ignoreTransform:!0})}function rs(e){const t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}function ss(e,t){return t===void 0&&(t=Q(e).getComputedStyle(e)),t.position==="fixed"}function is(e,t){t===void 0&&(t=Q(e).getComputedStyle(e));const n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(s=>{const i=t[s];return typeof i=="string"?n.test(i):!1})}function en(e,t){const n=[];function r(s){if(t!=null&&n.length>=t||!s)return n;if(Zt(s)&&s.scrollingElement!=null&&!n.includes(s.scrollingElement))return n.push(s.scrollingElement),n;if(!at(s)||An(s)||n.includes(s))return n;const i=Q(e).getComputedStyle(s);return s!==e&&is(s,i)&&n.push(s),ss(s,i)?n:r(s.parentNode)}return e?r(e):n}function Ln(e){const[t]=en(e,1);return t??null}function $t(e){return!Nt||!e?null:Ve(e)?e:Qt(e)?Zt(e)||e===Ke(e).scrollingElement?window:at(e)?e:null:null}function Pn(e){return Ve(e)?e.scrollX:e.scrollLeft}function zn(e){return Ve(e)?e.scrollY:e.scrollTop}function Kt(e){return{x:Pn(e),y:zn(e)}}var X;(function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"})(X||(X={}));function Fn(e){return!Nt||!e?!1:e===document.scrollingElement}function Bn(e){const t={x:0,y:0},n=Fn(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},s=e.scrollTop<=t.y,i=e.scrollLeft<=t.x,o=e.scrollTop>=r.y,l=e.scrollLeft>=r.x;return{isTop:s,isLeft:i,isBottom:o,isRight:l,maxScroll:r,minScroll:t}}const os={x:.2,y:.2};function as(e,t,n,r,s){let{top:i,left:o,right:l,bottom:c}=n;r===void 0&&(r=10),s===void 0&&(s=os);const{isTop:p,isBottom:u,isLeft:f,isRight:m}=Bn(e),v={x:0,y:0},C={x:0,y:0},g={height:t.height*s.y,width:t.width*s.x};return!p&&i<=t.top+g.height?(v.y=X.Backward,C.y=r*Math.abs((t.top+g.height-i)/g.height)):!u&&c>=t.bottom-g.height&&(v.y=X.Forward,C.y=r*Math.abs((t.bottom-g.height-c)/g.height)),!m&&l>=t.right-g.width?(v.x=X.Forward,C.x=r*Math.abs((t.right-g.width-l)/g.width)):!f&&o<=t.left+g.width&&(v.x=X.Backward,C.x=r*Math.abs((t.left+g.width-o)/g.width)),{direction:v,speed:C}}function ls(e){if(e===document.scrollingElement){const{innerWidth:i,innerHeight:o}=window;return{top:0,left:0,right:i,bottom:o,width:i,height:o}}const{top:t,left:n,right:r,bottom:s}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:s,width:e.clientWidth,height:e.clientHeight}}function $n(e){return e.reduce((t,n)=>He(t,Kt(n)),ge)}function cs(e){return e.reduce((t,n)=>t+Pn(n),0)}function ds(e){return e.reduce((t,n)=>t+zn(n),0)}function Wn(e,t){if(t===void 0&&(t=Ge),!e)return;const{top:n,left:r,bottom:s,right:i}=t(e);Ln(e)&&(s<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}const us=[["x",["left","right"],cs],["y",["top","bottom"],ds]];class tn{constructor(t,n){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const r=en(n),s=$n(r);this.rect={...t},this.width=t.width,this.height=t.height;for(const[i,o,l]of us)for(const c of o)Object.defineProperty(this,c,{get:()=>{const p=l(r),u=s[i]-p;return this.rect[c]+u},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class tt{constructor(t){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(n=>{var r;return(r=this.target)==null?void 0:r.removeEventListener(...n)})},this.target=t}add(t,n,r){var s;(s=this.target)==null||s.addEventListener(t,n,r),this.listeners.push([t,n,r])}}function fs(e){const{EventTarget:t}=Q(e);return e instanceof t?e:Ke(e)}function Wt(e,t){const n=Math.abs(e.x),r=Math.abs(e.y);return typeof t=="number"?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t?r>t.y:!1}var ce;(function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"})(ce||(ce={}));function vn(e){e.preventDefault()}function hs(e){e.stopPropagation()}var P;(function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"})(P||(P={}));const Xn={start:[P.Space,P.Enter],cancel:[P.Esc],end:[P.Space,P.Enter,P.Tab]},ps=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case P.Right:return{...n,x:n.x+25};case P.Left:return{...n,x:n.x-25};case P.Down:return{...n,y:n.y+25};case P.Up:return{...n,y:n.y-25}}};class Un{constructor(t){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=t;const{event:{target:n}}=t;this.props=t,this.listeners=new tt(Ke(n)),this.windowListeners=new tt(Q(n)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(ce.Resize,this.handleCancel),this.windowListeners.add(ce.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(ce.Keydown,this.handleKeyDown))}handleStart(){const{activeNode:t,onStart:n}=this.props,r=t.node.current;r&&Wn(r),n(ge)}handleKeyDown(t){if(Et(t)){const{active:n,context:r,options:s}=this.props,{keyboardCodes:i=Xn,coordinateGetter:o=ps,scrollBehavior:l="smooth"}=s,{code:c}=t;if(i.end.includes(c)){this.handleEnd(t);return}if(i.cancel.includes(c)){this.handleCancel(t);return}const{collisionRect:p}=r.current,u=p?{x:p.left,y:p.top}:ge;this.referenceCoordinates||(this.referenceCoordinates=u);const f=o(t,{active:n,context:r.current,currentCoordinates:u});if(f){const m=wt(f,u),v={x:0,y:0},{scrollableAncestors:C}=r.current;for(const g of C){const b=t.code,{isTop:D,isRight:y,isLeft:S,isBottom:N,maxScroll:j,minScroll:E}=Bn(g),R=ls(g),h={x:Math.min(b===P.Right?R.right-R.width/2:R.right,Math.max(b===P.Right?R.left:R.left+R.width/2,f.x)),y:Math.min(b===P.Down?R.bottom-R.height/2:R.bottom,Math.max(b===P.Down?R.top:R.top+R.height/2,f.y))},O=b===P.Right&&!y||b===P.Left&&!S,k=b===P.Down&&!N||b===P.Up&&!D;if(O&&h.x!==f.x){const I=g.scrollLeft+m.x,L=b===P.Right&&I<=j.x||b===P.Left&&I>=E.x;if(L&&!m.y){g.scrollTo({left:I,behavior:l});return}L?v.x=g.scrollLeft-I:v.x=b===P.Right?g.scrollLeft-j.x:g.scrollLeft-E.x,v.x&&g.scrollBy({left:-v.x,behavior:l});break}else if(k&&h.y!==f.y){const I=g.scrollTop+m.y,L=b===P.Down&&I<=j.y||b===P.Up&&I>=E.y;if(L&&!m.x){g.scrollTo({top:I,behavior:l});return}L?v.y=g.scrollTop-I:v.y=b===P.Down?g.scrollTop-j.y:g.scrollTop-E.y,v.y&&g.scrollBy({top:-v.y,behavior:l});break}}this.handleMove(t,He(wt(f,this.referenceCoordinates),v))}}}handleMove(t,n){const{onMove:r}=this.props;t.preventDefault(),r(n)}handleEnd(t){const{onEnd:n}=this.props;t.preventDefault(),this.detach(),n()}handleCancel(t){const{onCancel:n}=this.props;t.preventDefault(),this.detach(),n()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}Un.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=Xn,onActivation:s}=t,{active:i}=n;const{code:o}=e.nativeEvent;if(r.start.includes(o)){const l=i.activatorNode.current;return l&&e.target!==l?!1:(e.preventDefault(),s==null||s({event:e.nativeEvent}),!0)}return!1}}];function mn(e){return!!(e&&"distance"in e)}function bn(e){return!!(e&&"delay"in e)}class nn{constructor(t,n,r){var s;r===void 0&&(r=fs(t.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=t,this.events=n;const{event:i}=t,{target:o}=i;this.props=t,this.events=n,this.document=Ke(o),this.documentListeners=new tt(this.document),this.listeners=new tt(r),this.windowListeners=new tt(Q(o)),this.initialCoordinates=(s=Ct(i))!=null?s:ge,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:t,props:{options:{activationConstraint:n,bypassActivationConstraint:r}}}=this;if(this.listeners.add(t.move.name,this.handleMove,{passive:!1}),this.listeners.add(t.end.name,this.handleEnd),t.cancel&&this.listeners.add(t.cancel.name,this.handleCancel),this.windowListeners.add(ce.Resize,this.handleCancel),this.windowListeners.add(ce.DragStart,vn),this.windowListeners.add(ce.VisibilityChange,this.handleCancel),this.windowListeners.add(ce.ContextMenu,vn),this.documentListeners.add(ce.Keydown,this.handleKeydown),n){if(r!=null&&r({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(bn(n)){this.timeoutId=setTimeout(this.handleStart,n.delay),this.handlePending(n);return}if(mn(n)){this.handlePending(n);return}}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),this.timeoutId!==null&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(t,n){const{active:r,onPending:s}=this.props;s(r,t,this.initialCoordinates,n)}handleStart(){const{initialCoordinates:t}=this,{onStart:n}=this.props;t&&(this.activated=!0,this.documentListeners.add(ce.Click,hs,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(ce.SelectionChange,this.removeTextSelection),n(t))}handleMove(t){var n;const{activated:r,initialCoordinates:s,props:i}=this,{onMove:o,options:{activationConstraint:l}}=i;if(!s)return;const c=(n=Ct(t))!=null?n:ge,p=wt(s,c);if(!r&&l){if(mn(l)){if(l.tolerance!=null&&Wt(p,l.tolerance))return this.handleCancel();if(Wt(p,l.distance))return this.handleStart()}if(bn(l)&&Wt(p,l.tolerance))return this.handleCancel();this.handlePending(l,p);return}t.cancelable&&t.preventDefault(),o(c)}handleEnd(){const{onAbort:t,onEnd:n}=this.props;this.detach(),this.activated||t(this.props.active),n()}handleCancel(){const{onAbort:t,onCancel:n}=this.props;this.detach(),this.activated||t(this.props.active),n()}handleKeydown(t){t.code===P.Esc&&this.handleCancel()}removeTextSelection(){var t;(t=this.document.getSelection())==null||t.removeAllRanges()}}const gs={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class rn extends nn{constructor(t){const{event:n}=t,r=Ke(n.target);super(t,gs,r)}}rn.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!n.isPrimary||n.button!==0?!1:(r==null||r({event:n}),!0)}}];const vs={move:{name:"mousemove"},end:{name:"mouseup"}};var Gt;(function(e){e[e.RightClick=2]="RightClick"})(Gt||(Gt={}));class ms extends nn{constructor(t){super(t,vs,Ke(t.event.target))}}ms.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button===Gt.RightClick?!1:(r==null||r({event:n}),!0)}}];const Xt={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class bs extends nn{constructor(t){super(t,Xt)}static setup(){return window.addEventListener(Xt.move.name,t,{capture:!1,passive:!1}),function(){window.removeEventListener(Xt.move.name,t)};function t(){}}}bs.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;const{touches:s}=n;return s.length>1?!1:(r==null||r({event:n}),!0)}}];var nt;(function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"})(nt||(nt={}));var jt;(function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"})(jt||(jt={}));function xs(e){let{acceleration:t,activator:n=nt.Pointer,canScroll:r,draggingRect:s,enabled:i,interval:o=5,order:l=jt.TreeOrder,pointerCoordinates:c,scrollableAncestors:p,scrollableAncestorRects:u,delta:f,threshold:m}=e;const v=ws({delta:f,disabled:!i}),[C,g]=Ir(),b=d.useRef({x:0,y:0}),D=d.useRef({x:0,y:0}),y=d.useMemo(()=>{switch(n){case nt.Pointer:return c?{top:c.y,bottom:c.y,left:c.x,right:c.x}:null;case nt.DraggableRect:return s}},[n,s,c]),S=d.useRef(null),N=d.useCallback(()=>{const E=S.current;if(!E)return;const R=b.current.x*D.current.x,h=b.current.y*D.current.y;E.scrollBy(R,h)},[]),j=d.useMemo(()=>l===jt.TreeOrder?[...p].reverse():p,[l,p]);d.useEffect(()=>{if(!i||!p.length||!y){g();return}for(const E of j){if((r==null?void 0:r(E))===!1)continue;const R=p.indexOf(E),h=u[R];if(!h)continue;const{direction:O,speed:k}=as(E,h,y,t,m);for(const I of["x","y"])v[I][O[I]]||(k[I]=0,O[I]=0);if(k.x>0||k.y>0){g(),S.current=E,C(N,o),b.current=k,D.current=O;return}}b.current={x:0,y:0},D.current={x:0,y:0},g()},[t,N,r,g,i,o,JSON.stringify(y),JSON.stringify(v),C,p,j,u,JSON.stringify(m)])}const ys={x:{[X.Backward]:!1,[X.Forward]:!1},y:{[X.Backward]:!1,[X.Forward]:!1}};function ws(e){let{delta:t,disabled:n}=e;const r=yt(t);return lt(s=>{if(n||!r||!s)return ys;const i={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[X.Backward]:s.x[X.Backward]||i.x===-1,[X.Forward]:s.x[X.Forward]||i.x===1},y:{[X.Backward]:s.y[X.Backward]||i.y===-1,[X.Forward]:s.y[X.Forward]||i.y===1}}},[n,t,r])}function Cs(e,t){const n=t!=null?e.get(t):void 0,r=n?n.node.current:null;return lt(s=>{var i;return t==null?null:(i=r??s)!=null?i:null},[r,t])}function Ss(e,t){return d.useMemo(()=>e.reduce((n,r)=>{const{sensor:s}=r,i=s.activators.map(o=>({eventName:o.eventName,handler:t(o.handler,r)}));return[...n,...i]},[]),[e,t])}var it;(function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"})(it||(it={}));var qt;(function(e){e.Optimized="optimized"})(qt||(qt={}));const xn=new Map;function js(e,t){let{dragging:n,dependencies:r,config:s}=t;const[i,o]=d.useState(null),{frequency:l,measure:c,strategy:p}=s,u=d.useRef(e),f=b(),m=st(f),v=d.useCallback(function(D){D===void 0&&(D=[]),!m.current&&o(y=>y===null?D:y.concat(D.filter(S=>!y.includes(S))))},[m]),C=d.useRef(null),g=lt(D=>{if(f&&!n)return xn;if(!D||D===xn||u.current!==e||i!=null){const y=new Map;for(let S of e){if(!S)continue;if(i&&i.length>0&&!i.includes(S.id)&&S.rect.current){y.set(S.id,S.rect.current);continue}const N=S.node.current,j=N?new tn(c(N),N):null;S.rect.current=j,j&&y.set(S.id,j)}return y}return D},[e,i,n,f,c]);return d.useEffect(()=>{u.current=e},[e]),d.useEffect(()=>{f||v()},[n,f]),d.useEffect(()=>{i&&i.length>0&&o(null)},[JSON.stringify(i)]),d.useEffect(()=>{f||typeof l!="number"||C.current!==null||(C.current=setTimeout(()=>{v(),C.current=null},l))},[l,f,v,...r]),{droppableRects:g,measureDroppableContainers:v,measuringScheduled:i!=null};function b(){switch(p){case it.Always:return!1;case it.BeforeDragging:return n;default:return!n}}}function sn(e,t){return lt(n=>e?n||(typeof t=="function"?t(e):e):null,[t,e])}function Ds(e,t){return sn(e,t)}function Ns(e){let{callback:t,disabled:n}=e;const r=Rt(t),s=d.useMemo(()=>{if(n||typeof window>"u"||typeof window.MutationObserver>"u")return;const{MutationObserver:i}=window;return new i(r)},[r,n]);return d.useEffect(()=>()=>s==null?void 0:s.disconnect(),[s]),s}function It(e){let{callback:t,disabled:n}=e;const r=Rt(t),s=d.useMemo(()=>{if(n||typeof window>"u"||typeof window.ResizeObserver>"u")return;const{ResizeObserver:i}=window;return new i(r)},[n]);return d.useEffect(()=>()=>s==null?void 0:s.disconnect(),[s]),s}function Rs(e){return new tn(Ge(e),e)}function yn(e,t,n){t===void 0&&(t=Rs);const[r,s]=d.useState(null);function i(){s(c=>{if(!e)return null;if(e.isConnected===!1){var p;return(p=c??n)!=null?p:null}const u=t(e);return JSON.stringify(c)===JSON.stringify(u)?c:u})}const o=Ns({callback(c){if(e)for(const p of c){const{type:u,target:f}=p;if(u==="childList"&&f instanceof HTMLElement&&f.contains(e)){i();break}}}}),l=It({callback:i});return pe(()=>{i(),e?(l==null||l.observe(e),o==null||o.observe(document.body,{childList:!0,subtree:!0})):(l==null||l.disconnect(),o==null||o.disconnect())},[e]),r}function Es(e){const t=sn(e);return _n(e,t)}const wn=[];function Is(e){const t=d.useRef(e),n=lt(r=>e?r&&r!==wn&&e&&t.current&&e.parentNode===t.current.parentNode?r:en(e):wn,[e]);return d.useEffect(()=>{t.current=e},[e]),n}function Os(e){const[t,n]=d.useState(null),r=d.useRef(e),s=d.useCallback(i=>{const o=$t(i.target);o&&n(l=>l?(l.set(o,Kt(o)),new Map(l)):null)},[]);return d.useEffect(()=>{const i=r.current;if(e!==i){o(i);const l=e.map(c=>{const p=$t(c);return p?(p.addEventListener("scroll",s,{passive:!0}),[p,Kt(p)]):null}).filter(c=>c!=null);n(l.length?new Map(l):null),r.current=e}return()=>{o(e),o(i)};function o(l){l.forEach(c=>{const p=$t(c);p==null||p.removeEventListener("scroll",s)})}},[s,e]),d.useMemo(()=>e.length?t?Array.from(t.values()).reduce((i,o)=>He(i,o),ge):$n(e):ge,[e,t])}function Cn(e,t){t===void 0&&(t=[]);const n=d.useRef(null);return d.useEffect(()=>{n.current=null},t),d.useEffect(()=>{const r=e!==ge;r&&!n.current&&(n.current=e),!r&&n.current&&(n.current=null)},[e]),n.current?wt(e,n.current):ge}function As(e){d.useEffect(()=>{if(!Nt)return;const t=e.map(n=>{let{sensor:r}=n;return r.setup==null?void 0:r.setup()});return()=>{for(const n of t)n==null||n()}},e.map(t=>{let{sensor:n}=t;return n}))}function Ms(e,t){return d.useMemo(()=>e.reduce((n,r)=>{let{eventName:s,handler:i}=r;return n[s]=o=>{i(o,t)},n},{}),[e,t])}function Yn(e){return d.useMemo(()=>e?rs(e):null,[e])}const Sn=[];function Ts(e,t){t===void 0&&(t=Ge);const[n]=e,r=Yn(n?Q(n):null),[s,i]=d.useState(Sn);function o(){i(()=>e.length?e.map(c=>Fn(c)?r:new tn(t(c),c)):Sn)}const l=It({callback:o});return pe(()=>{l==null||l.disconnect(),o(),e.forEach(c=>l==null?void 0:l.observe(c))},[e]),s}function Hn(e){if(!e)return null;if(e.children.length>1)return e;const t=e.children[0];return at(t)?t:e}function _s(e){let{measure:t}=e;const[n,r]=d.useState(null),s=d.useCallback(p=>{for(const{target:u}of p)if(at(u)){r(f=>{const m=t(u);return f?{...f,width:m.width,height:m.height}:m});break}},[t]),i=It({callback:s}),o=d.useCallback(p=>{const u=Hn(p);i==null||i.disconnect(),u&&(i==null||i.observe(u)),r(u?t(u):null)},[t,i]),[l,c]=xt(o);return d.useMemo(()=>({nodeRef:l,rect:n,setRef:c}),[n,l,c])}const ks=[{sensor:rn,options:{}},{sensor:Un,options:{}}],Ls={current:{}},ht={draggable:{measure:gn},droppable:{measure:gn,strategy:it.WhileDragging,frequency:qt.Optimized},dragOverlay:{measure:Ge}};class rt extends Map{get(t){var n;return t!=null&&(n=super.get(t))!=null?n:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(t=>{let{disabled:n}=t;return!n})}getNodeFor(t){var n,r;return(n=(r=this.get(t))==null?void 0:r.node.current)!=null?n:void 0}}const Ps={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new rt,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:St},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:ht,measureDroppableContainers:St,windowRect:null,measuringScheduled:!1},Vn={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:St,draggableNodes:new Map,over:null,measureDroppableContainers:St},dt=d.createContext(Vn),Kn=d.createContext(Ps);function zs(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new rt}}}function Fs(e,t){switch(t.type){case B.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case B.DragMove:return e.draggable.active==null?e:{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case B.DragEnd:case B.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case B.RegisterDroppable:{const{element:n}=t,{id:r}=n,s=new rt(e.droppable.containers);return s.set(r,n),{...e,droppable:{...e.droppable,containers:s}}}case B.SetDroppableDisabled:{const{id:n,key:r,disabled:s}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;const o=new rt(e.droppable.containers);return o.set(n,{...i,disabled:s}),{...e,droppable:{...e.droppable,containers:o}}}case B.UnregisterDroppable:{const{id:n,key:r}=t,s=e.droppable.containers.get(n);if(!s||r!==s.key)return e;const i=new rt(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function Bs(e){let{disabled:t}=e;const{active:n,activatorEvent:r,draggableNodes:s}=d.useContext(dt),i=yt(r),o=yt(n==null?void 0:n.id);return d.useEffect(()=>{if(!t&&!r&&i&&o!=null){if(!Et(i)||document.activeElement===i.target)return;const l=s.get(o);if(!l)return;const{activatorNode:c,node:p}=l;if(!c.current&&!p.current)return;requestAnimationFrame(()=>{for(const u of[c.current,p.current]){if(!u)continue;const f=Mr(u);if(f){f.focus();break}}})}},[r,t,s,o,i]),null}function Gn(e,t){let{transform:n,...r}=t;return e!=null&&e.length?e.reduce((s,i)=>i({transform:s,...r}),n):n}function $s(e){return d.useMemo(()=>({draggable:{...ht.draggable,...e==null?void 0:e.draggable},droppable:{...ht.droppable,...e==null?void 0:e.droppable},dragOverlay:{...ht.dragOverlay,...e==null?void 0:e.dragOverlay}}),[e==null?void 0:e.draggable,e==null?void 0:e.droppable,e==null?void 0:e.dragOverlay])}function Ws(e){let{activeNode:t,measure:n,initialRect:r,config:s=!0}=e;const i=d.useRef(!1),{x:o,y:l}=typeof s=="boolean"?{x:s,y:s}:s;pe(()=>{if(!o&&!l||!t){i.current=!1;return}if(i.current||!r)return;const p=t==null?void 0:t.node.current;if(!p||p.isConnected===!1)return;const u=n(p),f=_n(u,r);if(o||(f.x=0),l||(f.y=0),i.current=!0,Math.abs(f.x)>0||Math.abs(f.y)>0){const m=Ln(p);m&&m.scrollBy({top:f.y,left:f.x})}},[t,o,l,r,n])}const Ot=d.createContext({...ge,scaleX:1,scaleY:1});var Te;(function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"})(Te||(Te={}));const Xs=d.memo(function(t){var n,r,s,i;let{id:o,accessibility:l,autoScroll:c=!0,children:p,sensors:u=ks,collisionDetection:f=Jr,measuring:m,modifiers:v,...C}=t;const g=d.useReducer(Fs,void 0,zs),[b,D]=g,[y,S]=zr(),[N,j]=d.useState(Te.Uninitialized),E=N===Te.Initialized,{draggable:{active:R,nodes:h,translate:O},droppable:{containers:k}}=b,I=R!=null?h.get(R):null,L=d.useRef({initial:null,translated:null}),G=d.useMemo(()=>{var K;return R!=null?{id:R,data:(K=I==null?void 0:I.data)!=null?K:Ls,rect:L}:null},[R,I]),Z=d.useRef(null),[de,ue]=d.useState(null),[A,z]=d.useState(null),$=st(C,Object.values(C)),Ce=ct("DndDescribedBy",o),Ne=d.useMemo(()=>k.getEnabled(),[k]),V=$s(m),{droppableRects:fe,measureDroppableContainers:Se,measuringScheduled:ke}=js(Ne,{dragging:E,dependencies:[O.x,O.y],config:V.droppable}),ee=Cs(h,R),Le=d.useMemo(()=>A?Ct(A):null,[A]),ie=hr(),w=Ds(ee,V.draggable.measure);Ws({activeNode:R!=null?h.get(R):null,config:ie.layoutShiftCompensation,initialRect:w,measure:V.draggable.measure});const x=yn(ee,V.draggable.measure,w),M=yn(ee?ee.parentElement:null),T=d.useRef({activatorEvent:null,active:null,activeNode:ee,collisionRect:null,collisions:null,droppableRects:fe,draggableNodes:h,draggingNode:null,draggingNodeRect:null,droppableContainers:k,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),W=k.getNodeFor((n=T.current.over)==null?void 0:n.id),F=_s({measure:V.dragOverlay.measure}),te=(r=F.nodeRef.current)!=null?r:ee,U=E?(s=F.rect)!=null?s:x:null,ve=!!(F.nodeRef.current&&F.rect),Pe=Es(ve?null:x),je=Yn(te?Q(te):null),oe=Is(E?W??ee:null),q=Ts(oe),Re=Gn(v,{transform:{x:O.x-Pe.x,y:O.y-Pe.y,scaleX:1,scaleY:1},activatorEvent:A,active:G,activeNodeRect:x,containerNodeRect:M,draggingNodeRect:U,over:T.current.over,overlayNodeRect:F.rect,scrollableAncestors:oe,scrollableAncestorRects:q,windowRect:je}),J=Le?He(Le,O):null,ae=Os(oe),Mt=Cn(ae),Tt=Cn(ae,[x]),We=He(Re,Mt),Xe=U?es(U,Re):null,qe=G&&Xe?f({active:G,collisionRect:Xe,droppableRects:fe,droppableContainers:Ne,pointerCoordinates:J}):null,an=Kr(qe,"id"),[Ee,ln]=d.useState(null),lr=ve?Re:He(Re,Tt),cr=Qr(lr,(i=Ee==null?void 0:Ee.rect)!=null?i:null,x),_t=d.useRef(null),cn=d.useCallback((K,ne)=>{let{sensor:re,options:Ie}=ne;if(Z.current==null)return;const le=h.get(Z.current);if(!le)return;const se=K.nativeEvent,me=new re({active:Z.current,activeNode:le,event:se,options:Ie,context:T,onAbort(Y){if(!h.get(Y))return;const{onDragAbort:be}=$.current,De={id:Y};be==null||be(De),y({type:"onDragAbort",event:De})},onPending(Y,Oe,be,De){if(!h.get(Y))return;const{onDragPending:Qe}=$.current,Ae={id:Y,constraint:Oe,initialCoordinates:be,offset:De};Qe==null||Qe(Ae),y({type:"onDragPending",event:Ae})},onStart(Y){const Oe=Z.current;if(Oe==null)return;const be=h.get(Oe);if(!be)return;const{onDragStart:De}=$.current,Je={activatorEvent:se,active:{id:Oe,data:be.data,rect:L}};Ye.unstable_batchedUpdates(()=>{De==null||De(Je),j(Te.Initializing),D({type:B.DragStart,initialCoordinates:Y,active:Oe}),y({type:"onDragStart",event:Je}),ue(_t.current),z(se)})},onMove(Y){D({type:B.DragMove,coordinates:Y})},onEnd:Ue(B.DragEnd),onCancel:Ue(B.DragCancel)});_t.current=me;function Ue(Y){return async function(){const{active:be,collisions:De,over:Je,scrollAdjustedTranslate:Qe}=T.current;let Ae=null;if(be&&Qe){const{cancelDrop:Ze}=$.current;Ae={activatorEvent:se,active:be,collisions:De,delta:Qe,over:Je},Y===B.DragEnd&&typeof Ze=="function"&&await Promise.resolve(Ze(Ae))&&(Y=B.DragCancel)}Z.current=null,Ye.unstable_batchedUpdates(()=>{D({type:Y}),j(Te.Uninitialized),ln(null),ue(null),z(null),_t.current=null;const Ze=Y===B.DragEnd?"onDragEnd":"onDragCancel";if(Ae){const kt=$.current[Ze];kt==null||kt(Ae),y({type:Ze,event:Ae})}})}}},[h]),dr=d.useCallback((K,ne)=>(re,Ie)=>{const le=re.nativeEvent,se=h.get(Ie);if(Z.current!==null||!se||le.dndKit||le.defaultPrevented)return;const me={active:se};K(re,ne.options,me)===!0&&(le.dndKit={capturedBy:ne.sensor},Z.current=Ie,cn(re,ne))},[h,cn]),dn=Ss(u,dr);As(u),pe(()=>{x&&N===Te.Initializing&&j(Te.Initialized)},[x,N]),d.useEffect(()=>{const{onDragMove:K}=$.current,{active:ne,activatorEvent:re,collisions:Ie,over:le}=T.current;if(!ne||!re)return;const se={active:ne,activatorEvent:re,collisions:Ie,delta:{x:We.x,y:We.y},over:le};Ye.unstable_batchedUpdates(()=>{K==null||K(se),y({type:"onDragMove",event:se})})},[We.x,We.y]),d.useEffect(()=>{const{active:K,activatorEvent:ne,collisions:re,droppableContainers:Ie,scrollAdjustedTranslate:le}=T.current;if(!K||Z.current==null||!ne||!le)return;const{onDragOver:se}=$.current,me=Ie.get(an),Ue=me&&me.rect.current?{id:me.id,rect:me.rect.current,data:me.data,disabled:me.disabled}:null,Y={active:K,activatorEvent:ne,collisions:re,delta:{x:le.x,y:le.y},over:Ue};Ye.unstable_batchedUpdates(()=>{ln(Ue),se==null||se(Y),y({type:"onDragOver",event:Y})})},[an]),pe(()=>{T.current={activatorEvent:A,active:G,activeNode:ee,collisionRect:Xe,collisions:qe,droppableRects:fe,draggableNodes:h,draggingNode:te,draggingNodeRect:U,droppableContainers:k,over:Ee,scrollableAncestors:oe,scrollAdjustedTranslate:We},L.current={initial:U,translated:Xe}},[G,ee,qe,Xe,h,te,U,fe,k,Ee,oe,We]),xs({...ie,delta:O,draggingRect:Xe,pointerCoordinates:J,scrollableAncestors:oe,scrollableAncestorRects:q});const ur=d.useMemo(()=>({active:G,activeNode:ee,activeNodeRect:x,activatorEvent:A,collisions:qe,containerNodeRect:M,dragOverlay:F,draggableNodes:h,droppableContainers:k,droppableRects:fe,over:Ee,measureDroppableContainers:Se,scrollableAncestors:oe,scrollableAncestorRects:q,measuringConfiguration:V,measuringScheduled:ke,windowRect:je}),[G,ee,x,A,qe,M,F,h,k,fe,Ee,Se,oe,q,V,ke,je]),fr=d.useMemo(()=>({activatorEvent:A,activators:dn,active:G,activeNodeRect:x,ariaDescribedById:{draggable:Ce},dispatch:D,draggableNodes:h,over:Ee,measureDroppableContainers:Se}),[A,dn,G,x,D,Ce,h,Ee,Se]);return _.createElement(Tn.Provider,{value:S},_.createElement(dt.Provider,{value:fr},_.createElement(Kn.Provider,{value:ur},_.createElement(Ot.Provider,{value:cr},p)),_.createElement(Bs,{disabled:(l==null?void 0:l.restoreFocus)===!1})),_.createElement($r,{...l,hiddenTextDescribedById:Ce}));function hr(){const K=(de==null?void 0:de.autoScrollEnabled)===!1,ne=typeof c=="object"?c.enabled===!1:c===!1,re=E&&!K&&!ne;return typeof c=="object"?{...c,enabled:re}:{enabled:re}}}),Us=d.createContext(null),jn="button",Ys="Draggable";function Hs(e){let{id:t,data:n,disabled:r=!1,attributes:s}=e;const i=ct(Ys),{activators:o,activatorEvent:l,active:c,activeNodeRect:p,ariaDescribedById:u,draggableNodes:f,over:m}=d.useContext(dt),{role:v=jn,roleDescription:C="draggable",tabIndex:g=0}=s??{},b=(c==null?void 0:c.id)===t,D=d.useContext(b?Ot:Us),[y,S]=xt(),[N,j]=xt(),E=Ms(o,t),R=st(n);pe(()=>(f.set(t,{id:t,key:i,node:y,activatorNode:N,data:R}),()=>{const O=f.get(t);O&&O.key===i&&f.delete(t)}),[f,t]);const h=d.useMemo(()=>({role:v,tabIndex:g,"aria-disabled":r,"aria-pressed":b&&v===jn?!0:void 0,"aria-roledescription":C,"aria-describedby":u.draggable}),[r,v,g,b,C,u.draggable]);return{active:c,activatorEvent:l,activeNodeRect:p,attributes:h,isDragging:b,listeners:r?void 0:E,node:y,over:m,setNodeRef:S,setActivatorNodeRef:j,transform:D}}function qn(){return d.useContext(Kn)}const Vs="Droppable",Ks={timeout:25};function Gs(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:s}=e;const i=ct(Vs),{active:o,dispatch:l,over:c,measureDroppableContainers:p}=d.useContext(dt),u=d.useRef({disabled:n}),f=d.useRef(!1),m=d.useRef(null),v=d.useRef(null),{disabled:C,updateMeasurementsFor:g,timeout:b}={...Ks,...s},D=st(g??r),y=d.useCallback(()=>{if(!f.current){f.current=!0;return}v.current!=null&&clearTimeout(v.current),v.current=setTimeout(()=>{p(Array.isArray(D.current)?D.current:[D.current]),v.current=null},b)},[b]),S=It({callback:y,disabled:C||!o}),N=d.useCallback((h,O)=>{S&&(O&&(S.unobserve(O),f.current=!1),h&&S.observe(h))},[S]),[j,E]=xt(N),R=st(t);return d.useEffect(()=>{!S||!j.current||(S.disconnect(),f.current=!1,S.observe(j.current))},[j,S]),d.useEffect(()=>(l({type:B.RegisterDroppable,element:{id:r,key:i,disabled:n,node:j,rect:m,data:R}}),()=>l({type:B.UnregisterDroppable,key:i,id:r})),[r]),d.useEffect(()=>{n!==u.current.disabled&&(l({type:B.SetDroppableDisabled,id:r,key:i,disabled:n}),u.current.disabled=n)},[r,i,n,l]),{active:o,rect:m,isOver:(c==null?void 0:c.id)===r,node:j,over:c,setNodeRef:E}}function qs(e){let{animation:t,children:n}=e;const[r,s]=d.useState(null),[i,o]=d.useState(null),l=yt(n);return!n&&!r&&l&&s(l),pe(()=>{if(!i)return;const c=r==null?void 0:r.key,p=r==null?void 0:r.props.id;if(c==null||p==null){s(null);return}Promise.resolve(t(p,i)).then(()=>{s(null)})},[t,r,i]),_.createElement(_.Fragment,null,n,r?d.cloneElement(r,{ref:o}):null)}const Js={x:0,y:0,scaleX:1,scaleY:1};function Qs(e){let{children:t}=e;return _.createElement(dt.Provider,{value:Vn},_.createElement(Ot.Provider,{value:Js},t))}const Zs={position:"fixed",touchAction:"none"},ei=e=>Et(e)?"transform 250ms ease":void 0,ti=d.forwardRef((e,t)=>{let{as:n,activatorEvent:r,adjustScale:s,children:i,className:o,rect:l,style:c,transform:p,transition:u=ei}=e;if(!l)return null;const f=s?p:{...p,scaleX:1,scaleY:1},m={...Zs,width:l.width,height:l.height,top:l.top,left:l.left,transform:ye.Transform.toString(f),transformOrigin:s&&r?Yr(r,l):void 0,transition:typeof u=="function"?u(r):u,...c};return _.createElement(n,{className:o,style:m,ref:t},i)}),ni=e=>t=>{let{active:n,dragOverlay:r}=t;const s={},{styles:i,className:o}=e;if(i!=null&&i.active)for(const[l,c]of Object.entries(i.active))c!==void 0&&(s[l]=n.node.style.getPropertyValue(l),n.node.style.setProperty(l,c));if(i!=null&&i.dragOverlay)for(const[l,c]of Object.entries(i.dragOverlay))c!==void 0&&r.node.style.setProperty(l,c);return o!=null&&o.active&&n.node.classList.add(o.active),o!=null&&o.dragOverlay&&r.node.classList.add(o.dragOverlay),function(){for(const[c,p]of Object.entries(s))n.node.style.setProperty(c,p);o!=null&&o.active&&n.node.classList.remove(o.active)}},ri=e=>{let{transform:{initial:t,final:n}}=e;return[{transform:ye.Transform.toString(t)},{transform:ye.Transform.toString(n)}]},Jt={duration:250,easing:"ease",keyframes:ri,sideEffects:ni({styles:{active:{opacity:"0"}}})};function si(e){let{config:t,draggableNodes:n,droppableContainers:r,measuringConfiguration:s}=e;return Rt((i,o)=>{if(t===null)return;const l=n.get(i);if(!l)return;const c=l.node.current;if(!c)return;const p=Hn(o);if(!p)return;const{transform:u}=Q(o).getComputedStyle(o),f=kn(u);if(!f)return;const m=typeof t=="function"?t:ii(t);return Wn(c,s.draggable.measure),m({active:{id:i,data:l.data,node:c,rect:s.draggable.measure(c)},draggableNodes:n,dragOverlay:{node:o,rect:s.dragOverlay.measure(p)},droppableContainers:r,measuringConfiguration:s,transform:f})})}function ii(e){const{duration:t,easing:n,sideEffects:r,keyframes:s}={...Jt,...e};return i=>{let{active:o,dragOverlay:l,transform:c,...p}=i;if(!t)return;const u={x:l.rect.left-o.rect.left,y:l.rect.top-o.rect.top},f={scaleX:c.scaleX!==1?o.rect.width*c.scaleX/l.rect.width:1,scaleY:c.scaleY!==1?o.rect.height*c.scaleY/l.rect.height:1},m={x:c.x-u.x,y:c.y-u.y,...f},v=s({...p,active:o,dragOverlay:l,transform:{initial:c,final:m}}),[C]=v,g=v[v.length-1];if(JSON.stringify(C)===JSON.stringify(g))return;const b=r==null?void 0:r({active:o,dragOverlay:l,...p}),D=l.node.animate(v,{duration:t,easing:n,fill:"forwards"});return new Promise(y=>{D.onfinish=()=>{b==null||b(),y()}})}}let Dn=0;function oi(e){return d.useMemo(()=>{if(e!=null)return Dn++,Dn},[e])}const ai=_.memo(e=>{let{adjustScale:t=!1,children:n,dropAnimation:r,style:s,transition:i,modifiers:o,wrapperElement:l="div",className:c,zIndex:p=999}=e;const{activatorEvent:u,active:f,activeNodeRect:m,containerNodeRect:v,draggableNodes:C,droppableContainers:g,dragOverlay:b,over:D,measuringConfiguration:y,scrollableAncestors:S,scrollableAncestorRects:N,windowRect:j}=qn(),E=d.useContext(Ot),R=oi(f==null?void 0:f.id),h=Gn(o,{activatorEvent:u,active:f,activeNodeRect:m,containerNodeRect:v,draggingNodeRect:b.rect,over:D,overlayNodeRect:b.rect,scrollableAncestors:S,scrollableAncestorRects:N,transform:E,windowRect:j}),O=sn(m),k=si({config:r,draggableNodes:C,droppableContainers:g,measuringConfiguration:y}),I=O?b.setRef:void 0;return _.createElement(Qs,null,_.createElement(qs,{animation:k},f&&R?_.createElement(ti,{key:R,id:f.id,ref:I,as:l,activatorEvent:u,adjustScale:t,className:c,transition:i,rect:O,style:{zIndex:p,...s},transform:h},n):null))});function ot(e,t,n){const r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function li(e,t){return e.reduce((n,r,s)=>{const i=t.get(r);return i&&(n[s]=i),n},Array(e.length))}function ut(e){return e!==null&&e>=0}function ci(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function di(e){return typeof e=="boolean"?{draggable:e,droppable:e}:e}const Jn=e=>{let{rects:t,activeIndex:n,overIndex:r,index:s}=e;const i=ot(t,r,n),o=t[s],l=i[s];return!l||!o?null:{x:l.left-o.left,y:l.top-o.top,scaleX:l.width/o.width,scaleY:l.height/o.height}},ft={scaleX:1,scaleY:1},ui=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:s,rects:i,overIndex:o}=e;const l=(t=i[n])!=null?t:r;if(!l)return null;if(s===n){const p=i[o];return p?{x:0,y:n<o?p.top+p.height-(l.top+l.height):p.top-l.top,...ft}:null}const c=fi(i,s,n);return s>n&&s<=o?{x:0,y:-l.height-c,...ft}:s<n&&s>=o?{x:0,y:l.height+c,...ft}:{x:0,y:0,...ft}};function fi(e,t,n){const r=e[t],s=e[t-1],i=e[t+1];return r?n<t?s?r.top-(s.top+s.height):i?i.top-(r.top+r.height):0:i?i.top-(r.top+r.height):s?r.top-(s.top+s.height):0:0}const Qn="Sortable",Zn=_.createContext({activeIndex:-1,containerId:Qn,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:Jn,disabled:{draggable:!1,droppable:!1}});function hi(e){let{children:t,id:n,items:r,strategy:s=Jn,disabled:i=!1}=e;const{active:o,dragOverlay:l,droppableRects:c,over:p,measureDroppableContainers:u}=qn(),f=ct(Qn,n),m=l.rect!==null,v=d.useMemo(()=>r.map(E=>typeof E=="object"&&"id"in E?E.id:E),[r]),C=o!=null,g=o?v.indexOf(o.id):-1,b=p?v.indexOf(p.id):-1,D=d.useRef(v),y=!ci(v,D.current),S=b!==-1&&g===-1||y,N=di(i);pe(()=>{y&&C&&u(v)},[y,v,C,u]),d.useEffect(()=>{D.current=v},[v]);const j=d.useMemo(()=>({activeIndex:g,containerId:f,disabled:N,disableTransforms:S,items:v,overIndex:b,useDragOverlay:m,sortedRects:li(v,c),strategy:s}),[g,f,N.draggable,N.droppable,S,v,b,c,m,s]);return _.createElement(Zn.Provider,{value:j},t)}const pi=e=>{let{id:t,items:n,activeIndex:r,overIndex:s}=e;return ot(n,r,s).indexOf(t)},gi=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:s,items:i,newIndex:o,previousItems:l,previousContainerId:c,transition:p}=e;return!p||!r||l!==i&&s===o?!1:n?!0:o!==s&&t===c},vi={duration:200,easing:"ease"},er="transform",mi=ye.Transition.toString({property:er,duration:0,easing:"linear"}),bi={roleDescription:"sortable"};function xi(e){let{disabled:t,index:n,node:r,rect:s}=e;const[i,o]=d.useState(null),l=d.useRef(n);return pe(()=>{if(!t&&n!==l.current&&r.current){const c=s.current;if(c){const p=Ge(r.current,{ignoreTransform:!0}),u={x:c.left-p.left,y:c.top-p.top,scaleX:c.width/p.width,scaleY:c.height/p.height};(u.x||u.y)&&o(u)}}n!==l.current&&(l.current=n)},[t,n,r,s]),d.useEffect(()=>{i&&o(null)},[i]),i}function yi(e){let{animateLayoutChanges:t=gi,attributes:n,disabled:r,data:s,getNewIndex:i=pi,id:o,strategy:l,resizeObserverConfig:c,transition:p=vi}=e;const{items:u,containerId:f,activeIndex:m,disabled:v,disableTransforms:C,sortedRects:g,overIndex:b,useDragOverlay:D,strategy:y}=d.useContext(Zn),S=wi(r,v),N=u.indexOf(o),j=d.useMemo(()=>({sortable:{containerId:f,index:N,items:u},...s}),[f,s,N,u]),E=d.useMemo(()=>u.slice(u.indexOf(o)),[u,o]),{rect:R,node:h,isOver:O,setNodeRef:k}=Gs({id:o,data:j,disabled:S.droppable,resizeObserverConfig:{updateMeasurementsFor:E,...c}}),{active:I,activatorEvent:L,activeNodeRect:G,attributes:Z,setNodeRef:de,listeners:ue,isDragging:A,over:z,setActivatorNodeRef:$,transform:Ce}=Hs({id:o,data:j,attributes:{...bi,...n},disabled:S.draggable}),Ne=Er(k,de),V=!!I,fe=V&&!C&&ut(m)&&ut(b),Se=!D&&A,ke=Se&&fe?Ce:null,Le=fe?ke??(l??y)({rects:g,activeNodeRect:G,activeIndex:m,overIndex:b,index:N}):null,ie=ut(m)&&ut(b)?i({id:o,items:u,activeIndex:m,overIndex:b}):N,w=I==null?void 0:I.id,x=d.useRef({activeId:w,items:u,newIndex:ie,containerId:f}),M=u!==x.current.items,T=t({active:I,containerId:f,isDragging:A,isSorting:V,id:o,index:N,items:u,newIndex:x.current.newIndex,previousItems:x.current.items,previousContainerId:x.current.containerId,transition:p,wasDragging:x.current.activeId!=null}),W=xi({disabled:!T,index:N,node:h,rect:R});return d.useEffect(()=>{V&&x.current.newIndex!==ie&&(x.current.newIndex=ie),f!==x.current.containerId&&(x.current.containerId=f),u!==x.current.items&&(x.current.items=u)},[V,ie,f,u]),d.useEffect(()=>{if(w===x.current.activeId)return;if(w!=null&&x.current.activeId==null){x.current.activeId=w;return}const te=setTimeout(()=>{x.current.activeId=w},50);return()=>clearTimeout(te)},[w]),{active:I,activeIndex:m,attributes:Z,data:j,rect:R,index:N,newIndex:ie,items:u,isOver:O,isSorting:V,isDragging:A,listeners:ue,node:h,overIndex:b,over:z,setNodeRef:Ne,setActivatorNodeRef:$,setDroppableNodeRef:k,setDraggableNodeRef:de,transform:W??Le,transition:F()};function F(){if(W||M&&x.current.newIndex===N)return mi;if(!(Se&&!Et(L)||!p)&&(V||T))return ye.Transition.toString({...p,property:er})}}function wi(e,t){var n,r;return typeof e=="boolean"?{draggable:e,droppable:!1}:{draggable:(n=e==null?void 0:e.draggable)!=null?n:t.draggable,droppable:(r=e==null?void 0:e.droppable)!=null?r:t.droppable}}P.Down,P.Right,P.Up,P.Left;function we(){return we=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},we.apply(this,arguments)}function on(e,t){if(e==null)return{};var n={},r=Object.keys(e),s,i;for(i=0;i<r.length;i++)s=r[i],!(t.indexOf(s)>=0)&&(n[s]=e[s]);return n}function Ci(e,t){if(e){if(typeof e=="string")return Nn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Nn(e,t)}}function Nn(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function At(e,t){var n=typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=Ci(e))||t){n&&(e=n);var r=0;return function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function tr(e,t){t===void 0&&(t={});var n=t.insertAt;if(!(!e||typeof document>"u")){var r=document.head||document.getElementsByTagName("head")[0],s=document.createElement("style");s.type="text/css",n==="top"&&r.firstChild?r.insertBefore(s,r.firstChild):r.appendChild(s),s.styleSheet?s.styleSheet.cssText=e:s.appendChild(document.createTextNode(e))}}var Si=`.dnd-sortable-tree_simple_wrapper{box-sizing:border-box;list-style:none;margin-bottom:-1px}.dnd-sortable-tree_simple_tree-item{align-items:center;border:1px solid #dedede;box-sizing:border-box;color:#222;display:flex;padding:10px;position:relative}.dnd-sortable-tree_simple_clone{display:inline-block;padding:5px;pointer-events:none}.dnd-sortable-tree_simple_clone>.dnd-sortable-tree_simple_tree-item{border-radius:4px;box-shadow:0 15px 15px 0 rgba(34,33,81,.1);padding-bottom:5px;padding-right:24px;padding-top:5px}.dnd-sortable-tree_simple_ghost{opacity:.5}.dnd-sortable-tree_simple_disable-selection{-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-user-select:none}.dnd-sortable-tree_simple_disable-interaction{pointer-events:none}.dnd-sortable-tree_folder_tree-item-collapse_button{align-self:stretch;background:url("data:image/svg+xml;utf8,<svg width='10' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 70 41'><path d='M30.76 39.2402C31.885 40.3638 33.41 40.995 35 40.995C36.59 40.995 38.115 40.3638 39.24 39.2402L68.24 10.2402C69.2998 9.10284 69.8768 7.59846 69.8494 6.04406C69.822 4.48965 69.1923 3.00657 68.093 1.90726C66.9937 0.807959 65.5106 0.178263 63.9562 0.150837C62.4018 0.123411 60.8974 0.700397 59.76 1.76024L35 26.5102L10.24 1.76024C9.10259 0.700397 7.59822 0.123411 6.04381 0.150837C4.4894 0.178263 3.00632 0.807959 1.90702 1.90726C0.807714 3.00657 0.178019 4.48965 0.150593 6.04406C0.123167 7.59846 0.700153 9.10284 1.75999 10.2402L30.76 39.2402Z' /></svg>") no-repeat 50%;border:0;transition:transform .25s ease;width:20px}.dnd-sortable-tree_folder_tree-item-collapse_button-collapsed{transform:rotate(-90deg)}.dnd-sortable-tree_simple_handle{align-self:stretch;background:url("data:image/svg+xml;utf8,<svg  xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' width='12'><path d='M7 2a2 2 0 1 0 .001 4.001A2 2 0 0 0 7 2zm0 6a2 2 0 1 0 .001 4.001A2 2 0 0 0 7 8zm0 6a2 2 0 1 0 .001 4.001A2 2 0 0 0 7 14zm6-8a2 2 0 1 0-.001-4.001A2 2 0 0 0 13 6zm0 2a2 2 0 1 0 .001 4.001A2 2 0 0 0 13 8zm0 6a2 2 0 1 0 .001 4.001A2 2 0 0 0 13 14z'></path></svg>") no-repeat 50%;cursor:pointer;width:20px}.dnd-sortable-tree_simple_tree-item-collapse_button{align-self:stretch;background:url("data:image/svg+xml;utf8,<svg width='10' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 70 41'><path d='M30.76 39.2402C31.885 40.3638 33.41 40.995 35 40.995C36.59 40.995 38.115 40.3638 39.24 39.2402L68.24 10.2402C69.2998 9.10284 69.8768 7.59846 69.8494 6.04406C69.822 4.48965 69.1923 3.00657 68.093 1.90726C66.9937 0.807959 65.5106 0.178263 63.9562 0.150837C62.4018 0.123411 60.8974 0.700397 59.76 1.76024L35 26.5102L10.24 1.76024C9.10259 0.700397 7.59822 0.123411 6.04381 0.150837C4.4894 0.178263 3.00632 0.807959 1.90702 1.90726C0.807714 3.00657 0.178019 4.48965 0.150593 6.04406C0.123167 7.59846 0.700153 9.10284 1.75999 10.2402L30.76 39.2402Z' /></svg>") no-repeat 50%;border:0;transition:transform .25s ease;width:20px}.dnd-sortable-tree_folder_simple-item-collapse_button-collapsed{transform:rotate(-90deg)}`;tr(Si);var ji=["clone","depth","disableSelection","disableInteraction","disableSorting","ghost","handleProps","indentationWidth","indicator","collapsed","onCollapse","onRemove","item","wrapperRef","style","hideCollapseButton","childCount","manualDrag","showDragHandle","disableCollapseOnItemClick","isLast","parent","className","contentClassName","isOver","isOverParent"],Di=d.forwardRef(function(e,t){var n=e.clone,r=e.depth,s=e.disableSelection,i=e.disableInteraction,o=e.disableSorting,l=e.ghost,c=e.handleProps,p=e.indentationWidth,u=e.collapsed,f=e.onCollapse,m=e.wrapperRef,v=e.style,C=e.hideCollapseButton,g=e.childCount,b=e.manualDrag,D=e.showDragHandle,y=e.disableCollapseOnItemClick,S=e.className,N=e.contentClassName,j=on(e,ji);return _.createElement("li",Object.assign({ref:m},j,{className:Ft("dnd-sortable-tree_simple_wrapper",n&&"dnd-sortable-tree_simple_clone",l&&"dnd-sortable-tree_simple_ghost",s&&"dnd-sortable-tree_simple_disable-selection",i&&"dnd-sortable-tree_simple_disable-interaction",S),style:we({},v,{paddingLeft:n?p:p*r})}),_.createElement("div",Object.assign({className:Ft("dnd-sortable-tree_simple_tree-item",N),ref:t},b?void 0:c,{onClick:y?void 0:f}),!o&&D!==!1&&_.createElement("div",Object.assign({className:"dnd-sortable-tree_simple_handle"},c)),!b&&!C&&!!f&&!!g&&_.createElement("button",{onClick:function(R){y&&(R.preventDefault(),f==null||f())},className:Ft("dnd-sortable-tree_simple_tree-item-collapse_button",u&&"dnd-sortable-tree_folder_simple-item-collapse_button-collapsed")}),e.children))}),Ni=`.dnd-sortable-tree_folder_wrapper{box-sizing:border-box;display:flex;flex-direction:row;list-style:none;margin-bottom:-1px}.dnd-sortable-tree_folder_tree-item{align-items:center;box-sizing:border-box;display:flex;padding:7px 0;position:relative}.dnd-sortable-tree_folder_clone{display:inline-block;padding:5px;pointer-events:none}.dnd-sortable-tree_folder_clone>.dnd-sortable-tree_folder_tree-item{border-radius:4px;padding-bottom:5px;padding-right:24px;padding-top:5px}.dnd-sortable-tree_folder_ghost{opacity:.5}.dnd-sortable-tree_folder_disable-selection{-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-user-select:none}.dnd-sortable-tree_folder_disable-interaction{pointer-events:none}.dnd-sortable-tree_folder_line{align-self:stretch;background:url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg'><line stroke='black' style='stroke-width: 1px;' x1='50%' y1='0' x2='50%' y2='100%'/></svg>");width:20px}.dnd-sortable-tree_folder_line-last{align-self:stretch;width:20px}.dnd-sortable-tree_folder_line-to_self{align-self:stretch;background:url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg'><line stroke='black' style='stroke-width: 1px;' x1='50%' y1='0' x2='50%' y2='100%'/><line stroke='black' style='stroke-width: 1px;' x1='50%' y1='50%' x2='100%' y2='50%'/></svg>");width:20px}.dnd-sortable-tree_folder_line-to_self-last{align-self:stretch;background:url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg'><line stroke='black' style='stroke-width: 1px;' x1='50%' y1='0' x2='50%' y2='50%'/><line stroke='black' style='stroke-width: 1px;' x1='50%' y1='50%' x2='100%' y2='50%'/></svg>");width:20px}.dnd-sortable-tree_folder_tree-item-collapse_button{align-self:stretch;background:url("data:image/svg+xml;utf8,<svg width='10' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 70 41'><path d='M30.76 39.2402C31.885 40.3638 33.41 40.995 35 40.995C36.59 40.995 38.115 40.3638 39.24 39.2402L68.24 10.2402C69.2998 9.10284 69.8768 7.59846 69.8494 6.04406C69.822 4.48965 69.1923 3.00657 68.093 1.90726C66.9937 0.807959 65.5106 0.178263 63.9562 0.150837C62.4018 0.123411 60.8974 0.700397 59.76 1.76024L35 26.5102L10.24 1.76024C9.10259 0.700397 7.59822 0.123411 6.04381 0.150837C4.4894 0.178263 3.00632 0.807959 1.90702 1.90726C0.807714 3.00657 0.178019 4.48965 0.150593 6.04406C0.123167 7.59846 0.700153 9.10284 1.75999 10.2402L30.76 39.2402Z' /></svg>") no-repeat 50%;border:0;transition:transform .25s ease;width:20px}.dnd-sortable-tree_folder_tree-item-collapse_button-collapsed{transform:rotate(-90deg)}.dnd-sortable-tree_folder_handle{align-self:stretch;background:url("data:image/svg+xml;utf8,<svg  xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' width='12'><path d='M7 2a2 2 0 1 0 .001 4.001A2 2 0 0 0 7 2zm0 6a2 2 0 1 0 .001 4.001A2 2 0 0 0 7 8zm0 6a2 2 0 1 0 .001 4.001A2 2 0 0 0 7 14zm6-8a2 2 0 1 0-.001-4.001A2 2 0 0 0 13 6zm0 2a2 2 0 1 0 .001 4.001A2 2 0 0 0 13 8zm0 6a2 2 0 1 0 .001 4.001A2 2 0 0 0 13 14z'></path></svg>") no-repeat 50%;cursor:pointer;width:20px}`;tr(Ni);var Ri=typeof window<"u"?/iPad|iPhone|iPod/.test(navigator.platform):!1;function Ei(e,t){return Math.round(e/t)}var Ut=function(){};function Ii(e,t,n,r,s,i,o){var l,c,p;if(Ut(),Ut=function(){},!t||!n)return null;var u=e.findIndex(function(A){var z=A.id;return z===n}),f=e.findIndex(function(A){var z=A.id;return z===t}),m=e[f];if(i){var v,C,g,b,D,y=e[u];return y=de(y,m,o),y===void 0?null:{depth:(v=(C=y)==null?void 0:C.depth)!=null?v:1,parentId:(g=(b=y)==null?void 0:b.id)!=null?g:null,parent:y,isLast:!!((D=y)!=null&&D.isLast)}}var S=ot(e,f,u),N=S[u-1],j=S[u+1],E=Ei(r,s),R=m.depth+E,h=R,O=Z(h-1,N),k=de(O,m,o);if(k===void 0)return null;var I=((l=k==null?void 0:k.depth)!=null?l:-1)+1,L=(c=j==null?void 0:j.depth)!=null?c:0;if(L>I)return null;h>=I?h=I:h<L&&(h=L);var G=((p=j==null?void 0:j.depth)!=null?p:-1)<h;return k&&k.isLast&&(Ut=function(){k.isLast=!0},k.isLast=!1),{depth:h,parentId:ue(),parent:k,isLast:G};function Z(A,z){if(!z)return null;for(;A<z.depth;){if(z.parent===null)return null;z=z.parent}return z}function de(A,z,$){if(!A){var Ce=typeof $=="function"?$(z):$;return Ce===!1?void 0:A}var Ne=typeof A.canHaveChildren=="function"?A.canHaveChildren(z):A.canHaveChildren;return Ne===!1?de(A.parent,m,$):A}function ue(){var A;if(h===0||!N)return null;if(h===N.depth)return N.parentId;if(h>N.depth)return N.id;var z=(A=S.slice(0,u).reverse().find(function($){return $.depth===h}))==null?void 0:A.parentId;return z??null}}function nr(e,t,n,r){return t===void 0&&(t=null),n===void 0&&(n=0),r===void 0&&(r=null),e.reduce(function(s,i,o){var l,c=we({},i,{parentId:t,depth:n,index:o,isLast:e.length===o+1,parent:r});return[].concat(s,[c],nr((l=i.children)!=null?l:[],i.id,n+1,c))},[])}function Yt(e){return nr(e)}function Oi(e){for(var t,n,r={id:"root",children:[]},s=(t={},t[r.id]=r,t),i=e.map(function(g){return we({},g,{children:[]})}),o=At(i),l;!(l=o()).done;){var c,p,u,f=l.value,m=f.id,v=(c=f.parentId)!=null?c:r.id,C=(p=s[v])!=null?p:Ai(i,v);f.parent=null,s[m]=f,C==null||(u=C.children)==null||u.push(f)}return(n=r.children)!=null?n:[]}function Ai(e,t){return e.find(function(n){var r=n.id;return r===t})}function Dt(e,t){for(var n=At(e),r;!(r=n()).done;){var s=r.value,i=s.id,o=s.children;if(i===t)return s;if(o!=null&&o.length){var l=Dt(o,t);if(l)return l}}}function rr(e,t){for(var n=[],r=At(e),s;!(s=r()).done;){var i,o=s.value;o.id!==t&&((i=o.children)!=null&&i.length&&(o.children=rr(o.children,t)),n.push(o))}return n}function sr(e,t,n,r){for(var s=At(e),i;!(i=s()).done;){var o,l=i.value;if(l.id===t){l[n]=r(l[n]);continue}(o=l.children)!=null&&o.length&&(l.children=sr(l.children,t,n,r))}return[].concat(e)}function ir(e,t){return t===void 0&&(t=0),e.reduce(function(n,r){var s=r.children;return s!=null&&s.length?ir(s,n+1):n+1},t)}function Mi(e,t){var n;if(!t)return 0;var r=Dt(e,t);return r?ir((n=r.children)!=null?n:[]):0}function Ti(e,t){var n=[].concat(t);return e.filter(function(r){if(r.parentId&&n.includes(r.parentId)){var s;return(s=r.children)!=null&&s.length&&n.push(r.id),!1}return!0})}function or(e,t){return!e||!t?!1:e.id===t?!0:or(e.parent,t)}var _i=["id","depth","isLast","TreeItemComponent","parent","disableSorting","sortableProps","keepGhostInPlace"],ki=function(t){var n=t.isSorting,r=t.isDragging;return!(n||r)},Li=function(t){var n=t.id,r=t.depth,s=t.isLast,i=t.TreeItemComponent,o=t.parent,l=t.disableSorting,c=t.sortableProps,p=t.keepGhostInPlace,u=on(t,_i),f=yi(we({id:n,animateLayoutChanges:ki,disabled:l},c)),m=f.attributes,v=f.isDragging,C=f.isSorting,g=f.listeners,b=f.setDraggableNodeRef,D=f.setDroppableNodeRef,y=f.transform,S=f.transition,N=f.isOver,j=f.over,E=d.useMemo(function(){return!!(j!=null&&j.id)&&or(o,j.id)},[j==null?void 0:j.id]),R={transform:ye.Translate.toString(y),transition:S??void 0},h=d.useMemo(function(){if(u.onCollapse)return function(){return u.onCollapse==null?void 0:u.onCollapse(u.item.id)}},[u.item.id,u.onCollapse]),O=d.useMemo(function(){if(u.onRemove)return function(){return u.onRemove==null?void 0:u.onRemove(u.item.id)}},[u.item.id,u.onRemove]);return _.createElement(i,Object.assign({},u,{ref:b,wrapperRef:D,style:p?void 0:R,depth:r,ghost:v,disableSelection:Ri,disableInteraction:C,isLast:s,parent:o,handleProps:we({},m,g),onCollapse:h,onRemove:O,disableSorting:l,isOver:N,isOverParent:E}))},Pi=_.memo(Li),zi=function(t){var n=function(s){var i=s.activeIndex,o=s.activeNodeRect,l=s.index,c=s.rects,p=s.overIndex;return t(i,p)?ui({activeIndex:i,activeNodeRect:o,index:l,rects:c,overIndex:p}):null};return n},Fi=["items","indicator","indentationWidth","onItemsChanged","TreeItemComponent","pointerSensorOptions","disableSorting","dropAnimation","dndContextProps","sortableProps","keepGhostInPlace","canRootHaveChildren"],Bi={activationConstraint:{distance:3}},$i={keyframes:function(t){var n=t.transform;return[{opacity:1,transform:ye.Transform.toString(n.initial)},{opacity:0,transform:ye.Transform.toString(we({},n.final,{x:n.final.x+5,y:n.final.y+5}))}]},easing:"ease-out",sideEffects:function(t){var n=t.active;n.node.animate([{opacity:0},{opacity:1}],{duration:Jt.duration,easing:Jt.easing})}};function Wi(e){var t=e.items,n=e.indicator,r=e.indentationWidth,s=r===void 0?20:r,i=e.onItemsChanged,o=e.TreeItemComponent,l=e.pointerSensorOptions,c=e.disableSorting,p=e.dropAnimation,u=e.dndContextProps,f=e.sortableProps,m=e.keepGhostInPlace,v=e.canRootHaveChildren,C=on(e,Fi),g=d.useState(null),b=g[0],D=g[1],y=d.useState(null),S=y[0],N=y[1],j=d.useState(0),E=j[0],R=j[1],h=d.useState(null),O=h[0],k=h[1],I=d.useMemo(function(){var w=Yt(t),x=w.reduce(function(T,W){var F=W.children,te=W.collapsed,U=W.id;return te&&F!=null&&F.length?[].concat(T,[U]):T},[]),M=Ti(w,b?[b].concat(x):x);return M},[b,t]),L=Ii(I,b,S,E,s,m??!1,v),G=d.useRef({items:I,offset:E}),Z=Xr(Wr(rn,l??Bi)),de=d.useMemo(function(){return I.map(function(w){var x=w.id;return x})},[I]),ue=b?I.find(function(w){var x=w.id;return x===b}):null;d.useEffect(function(){G.current={items:I,offset:E}},[I,E]);var A=d.useRef(t);A.current=t;var z=d.useCallback(function(w){var x=Dt(A.current,w);i(rr(A.current,w),{type:"removed",item:x})},[i]),$=d.useCallback(function(x){var M=Dt(A.current,x);i(sr(A.current,x,"collapsed",function(T){return!T}),{type:M.collapsed?"collapsed":"expanded",item:M})},[i]),Ce=d.useMemo(function(){return{onDragStart:function(x){var M=x.active;return"Picked up "+M.id+"."},onDragMove:function(x){var M=x.active,T=x.over;return ie("onDragMove",M.id,T==null?void 0:T.id)},onDragOver:function(x){var M=x.active,T=x.over;return ie("onDragOver",M.id,T==null?void 0:T.id)},onDragEnd:function(x){var M=x.active,T=x.over;return ie("onDragEnd",M.id,T==null?void 0:T.id)},onDragCancel:function(x){var M=x.active;return"Moving was cancelled. "+M.id+" was dropped in its original position."}}},[]),Ne=d.useCallback(function(){return!!L},[L]);return _.createElement(Xs,Object.assign({accessibility:{announcements:Ce},sensors:c?void 0:Z,modifiers:n?Ui:void 0,collisionDetection:Gr,onDragStart:c?void 0:V,onDragMove:c?void 0:fe,onDragOver:c?void 0:Se,onDragEnd:c?void 0:ke,onDragCancel:c?void 0:ee},u),_.createElement(hi,{items:de,strategy:c?void 0:zi(Ne)},I.map(function(w){var x,M,T;return _.createElement(Pi,Object.assign({},C,{key:w.id,id:w.id,item:w,childCount:(x=w.children)==null?void 0:x.length,depth:w.id===b&&L&&!m?L.depth:w.depth,indentationWidth:s,indicator:n,collapsed:!!(w.collapsed&&((M=w.children)!=null&&M.length)),onCollapse:(T=w.children)!=null&&T.length?$:void 0,onRemove:z,isLast:w.id===b&&L?L.isLast:w.isLast,parent:w.id===b&&L?L.parent:w.parent,TreeItemComponent:o,disableSorting:c,sortableProps:f,keepGhostInPlace:m}))}),Ye.createPortal(_.createElement(ai,{dropAnimation:p===void 0?$i:p},b&&ue?_.createElement(o,Object.assign({},C,{item:ue,children:[],depth:ue.depth,clone:!0,childCount:Mi(t,b)+1,indentationWidth:s,isLast:!1,parent:ue.parent,isOver:!1,isOverParent:!1})):null),document.body)));function V(w){var x=w.active.id;D(x),N(x);var M=I.find(function(T){var W=T.id;return W===x});M&&k({parentId:M.parentId,overId:x}),document.body.style.setProperty("cursor","grabbing")}function fe(w){var x=w.delta;R(x.x)}function Se(w){var x,M=w.over;N((x=M==null?void 0:M.id)!=null?x:null)}function ke(w){var x=w.active,M=w.over;if(Le(),L&&M){var T=L.depth,W=L.parentId;if(m&&M.id===x.id)return;var F=Yt(t),te=F.findIndex(function(J){var ae=J.id;return ae===M.id}),U=F.findIndex(function(J){var ae=J.id;return ae===x.id}),ve=F[U];F[U]=we({},ve,{depth:T,parentId:W});var Pe=ve.parent,je=ot(F,U,te),oe=Oi(je),q=je.find(function(J){return J.id===x.id}),Re=q.parentId?je.find(function(J){return J.id===q.parentId}):null;setTimeout(function(){return i(oe,{type:"dropped",draggedItem:q,draggedFromParent:Pe,droppedToParent:Re})})}}function ee(){Le()}function Le(){N(null),D(null),R(0),k(null),document.body.style.setProperty("cursor","")}function ie(w,x,M){if(M&&L){if(w!=="onDragEnd"){if(O&&L.parentId===O.parentId&&M===O.overId)return;k({parentId:L.parentId,overId:M})}var T=Yt(t),W=T.findIndex(function(J){var ae=J.id;return ae===M}),F=T.findIndex(function(J){var ae=J.id;return ae===x}),te=ot(T,F,W),U=te[W-1],ve,Pe=w==="onDragEnd"?"dropped":"moved",je=w==="onDragEnd"?"dropped":"nested";if(U)if(L.depth>U.depth)ve=x+" was "+je+" under "+U.id+".";else{for(var q=U,Re=function(){var ae=q.parentId;q=te.find(function(Mt){var Tt=Mt.id;return Tt===ae})};q&&L.depth<q.depth;)Re();q&&(ve=x+" was "+Pe+" after "+q.id+".")}else{var oe=te[W+1];ve=x+" was "+Pe+" before "+oe.id+"."}return ve}}}var Xi=function(t){var n=t.transform;return we({},n,{y:n.y-25})},Ui=[Xi];function Rn(e){const t=new Map,n=[];e.forEach(s=>{t.set(s.id,{...s,children:[]})}),e.forEach(s=>{const i=t.get(s.id);if(s.parent_id===null)n.push(i);else{const o=t.get(s.parent_id);o?o.children.push(i):n.push(i)}});const r=s=>s.sort((i,o)=>i.order-o.order).map(i=>({...i,children:r(i.children)}));return r(n)}function Yi(e){const t=[],n=(r,s=null)=>{r.forEach((i,o)=>{t.push({id:i.id,order:o+1,parent_id:s}),i.children&&i.children.length>0&&n(i.children,i.id)})};return n(e),t}const ar=d.forwardRef(({item:e,itemTypes:t,onEdit:n,onDelete:r,isUpdating:s,...i},o)=>{const l=u=>t[u]||u,c=u=>{u.stopPropagation(),n(e)},p=u=>{u.stopPropagation(),r(e)};return a.jsx(Di,{...i,ref:o,children:a.jsxs("div",{className:vr("flex items-center justify-between p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors",!e.is_active&&"opacity-60",s&&"pointer-events-none opacity-75"),children:[a.jsxs("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[a.jsx("div",{...i.handleProps,className:"flex items-center justify-center w-6 h-6 text-muted-foreground hover:text-foreground cursor-grab active:cursor-grabbing",children:a.jsx(In,{className:"h-4 w-4"})}),a.jsxs("div",{className:"flex-1 min-w-0",children:[a.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[a.jsx("h4",{className:"font-medium text-sm truncate",children:e.title}),!e.is_active&&a.jsx(yr,{className:"h-3 w-3 text-muted-foreground"})]}),a.jsxs("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[a.jsx(et,{variant:"outline",className:"text-xs",children:l(e.type)}),e.url&&a.jsxs("span",{className:"flex items-center gap-1 truncate max-w-[200px]",children:[a.jsx(wr,{className:"h-3 w-3"}),e.url]}),e.target==="_blank"&&a.jsx(et,{variant:"secondary",className:"text-xs",children:"New Window"}),e.icon&&a.jsxs(et,{variant:"secondary",className:"text-xs",children:["Icon: ",e.icon]}),e.css_class&&a.jsxs(et,{variant:"secondary",className:"text-xs",children:["Class: ",e.css_class]})]})]})]}),a.jsxs("div",{className:"flex items-center gap-1 ml-2",children:[a.jsxs(he,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:c,disabled:s,title:"Edit menu item",children:[a.jsx(Rr,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"Edit"})]}),a.jsxs(he,{variant:"ghost",size:"icon",className:"h-8 w-8 text-destructive hover:text-destructive",onClick:p,disabled:s,title:"Delete menu item",children:[a.jsx(Cr,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"Delete"})]})]})]})})});ar.displayName="MenuTreeItem";function Hi({menuId:e,items:t,itemTypes:n,onEditItem:r,onAddItem:s}){const[i,o]=d.useState(()=>Rn(t)),[l,c]=d.useState(!1),p=d.useCallback(async f=>{var m;o(f),c(!0);try{const v=Yi(f),C=await fetch(`/admin/menus/${e}/order`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":((m=document.querySelector('meta[name="csrf-token"]'))==null?void 0:m.getAttribute("content"))||"",Accept:"application/json"},body:JSON.stringify({items:v})});if(!C.ok){const b=await C.json();throw new Error(b.message||"Failed to update menu order")}const g=await C.json();_e.success(g.message||"Menu order updated successfully")}catch(v){console.error("Error updating menu order:",v),_e.error(v instanceof Error?v.message:"Failed to update menu order"),o(Rn(t))}finally{c(!1)}},[e,t]),u=d.useCallback(f=>{window.confirm(`Are you sure you want to delete "${f.title}"? This will also delete all child items.`)&&Ht.delete(`/admin/menus/${e}/items/${f.id}`,{onSuccess:()=>{_e.success("Menu item deleted successfully")},onError:m=>{_e.error("Failed to delete menu item",{description:m.message||"An error occurred while deleting the menu item"})}})},[e]);return t.length===0?a.jsxs(pt,{children:[a.jsxs(gt,{children:[a.jsxs(vt,{className:"flex items-center gap-2",children:[a.jsx(zt,{className:"h-5 w-5"}),"Menu Structure"]}),a.jsx(mt,{children:"Manage menu items and their hierarchy with drag & drop"})]}),a.jsx(bt,{children:a.jsxs("div",{className:"text-center py-8 text-muted-foreground",children:[a.jsx(zt,{className:"h-12 w-12 mx-auto mb-4 opacity-20"}),a.jsx("h3",{className:"text-lg font-medium mb-2",children:"No Menu Items"}),a.jsx("p",{className:"max-w-md mx-auto mb-4",children:"This menu doesn't have any items yet. Add your first menu item to get started."}),a.jsxs(he,{onClick:s,children:[a.jsx(Vt,{className:"mr-2 h-4 w-4"}),"Add First Menu Item"]})]})})]}):a.jsxs(pt,{children:[a.jsxs(gt,{children:[a.jsxs(vt,{className:"flex items-center gap-2",children:[a.jsx(zt,{className:"h-5 w-5"}),"Menu Structure",l&&a.jsx(et,{variant:"secondary",className:"ml-2",children:"Updating..."})]}),a.jsx(mt,{children:"Drag and drop menu items to reorder them or change their hierarchy. Items can be nested up to multiple levels."})]}),a.jsx(bt,{children:a.jsxs("div",{className:"space-y-2",children:[a.jsxs("div",{className:"flex justify-between items-center mb-4",children:[a.jsxs("div",{className:"text-sm text-muted-foreground",children:[a.jsx(In,{className:"inline h-4 w-4 mr-1"}),"Drag items to reorder or nest them"]}),a.jsxs(he,{onClick:s,size:"sm",children:[a.jsx(Vt,{className:"mr-2 h-4 w-4"}),"Add Menu Item"]})]}),a.jsx(Wi,{items:i,onItemsChanged:p,TreeItemComponent:_.forwardRef((f,m)=>a.jsx(ar,{...f,ref:m,itemTypes:n,onEdit:r,onDelete:u,isUpdating:l})),indentationWidth:24,pointerSensorOptions:{activationConstraint:{distance:8}}})]})})]})}function Oo({menu:e,availableItems:t,itemTypes:n,targetOptions:r}){var j,E,R;const[s,i]=d.useState("items"),[o,l]=d.useState(!1),[c,p]=d.useState(null),{data:u,setData:f,post:m,put:v,processing:C,errors:g,reset:b}=pr({title:"",type:"custom",url:"",target:"_self",icon:"",css_class:"",reference_id:null,parent_id:null,is_active:!0});d.useEffect(()=>{c&&f({title:c.title,type:c.type,url:c.url||"",target:c.target,icon:c.icon||"",css_class:c.css_class||"",reference_id:c.reference_id,parent_id:c.parent_id,is_active:c.is_active})},[c]);const D=h=>{h.preventDefault(),m(`/admin/menus/${e.id}/items`,{onSuccess:()=>{_e.success("Menu item added successfully"),l(!1),b(),Ht.reload()},onError:()=>{_e.error("Failed to add menu item",{description:"Please check the form for errors and try again."})}})},y=h=>{h.preventDefault(),c&&v(`/admin/menus/${e.id}/items/${c.id}`,{onSuccess:()=>{_e.success("Menu item updated successfully"),p(null),b(),Ht.reload()},onError:()=>{_e.error("Failed to update menu item",{description:"Please check the form for errors and try again."})}})},S=()=>{switch(u.type){case"page":return t.pages.map(h=>a.jsx(xe,{value:h.id.toString(),children:h.title},h.id));case"category":return t.categories.map(h=>a.jsx(xe,{value:h.id.toString(),children:h.name},h.id));case"brand":return t.brands.map(h=>a.jsx(xe,{value:h.id.toString(),children:h.name},h.id));case"model":return t.models.map(h=>a.jsx(xe,{value:h.id.toString(),children:h.name},h.id));default:return null}},N=()=>e.items.map(h=>a.jsx(xe,{value:h.id.toString(),children:h.title},h.id));return a.jsxs(xr,{children:[a.jsx(gr,{title:`Menu: ${e.name}`}),a.jsxs("div",{className:"p-6 space-y-6",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{className:"flex items-center gap-4",children:[a.jsx(he,{variant:"ghost",size:"icon",asChild:!0,children:a.jsxs(un,{href:"/admin/menus",children:[a.jsx(Sr,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"Back to menus"})]})}),a.jsxs("div",{children:[a.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:e.name}),a.jsx("p",{className:"text-muted-foreground",children:e.description||`Menu for ${e.location} location`})]})]}),a.jsxs("div",{className:"flex gap-2",children:[a.jsx(he,{asChild:!0,variant:"outline",children:a.jsxs(un,{href:`/admin/menus/${e.id}/edit`,children:[a.jsx(jr,{className:"mr-2 h-4 w-4"}),"Menu Settings"]})}),a.jsxs(he,{onClick:()=>{l(!0),p(null),b()},children:[a.jsx(Vt,{className:"mr-2 h-4 w-4"}),"Add Menu Item"]})]})]}),a.jsxs(mr,{defaultValue:"items",value:s,onValueChange:i,children:[a.jsxs(br,{children:[a.jsx(Lt,{value:"items",children:"Menu Items"}),o&&a.jsx(Lt,{value:"add-item",children:"Add Item"}),c&&a.jsx(Lt,{value:"edit-item",children:"Edit Item"})]}),a.jsx(Pt,{value:"items",className:"space-y-4",children:a.jsx(Hi,{menuId:e.id,items:e.items,itemTypes:n,onEditItem:h=>{p(h),f({title:h.title,type:h.type,url:h.url||"",target:h.target,icon:h.icon||"",css_class:h.css_class||"",reference_id:h.reference_id,parent_id:h.parent_id,is_active:h.is_active}),i("edit-item")},onAddItem:()=>{l(!0),p(null),b(),i("add-item")}})}),a.jsx(Pt,{value:"add-item",children:a.jsxs(pt,{children:[a.jsxs(gt,{children:[a.jsx(vt,{children:"Add Menu Item"}),a.jsx(mt,{children:"Create a new item for this menu"})]}),a.jsx(bt,{children:a.jsxs("form",{onSubmit:D,className:"space-y-4",children:[a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsxs("div",{className:"space-y-2",children:[a.jsx(H,{htmlFor:"title",children:"Title *"}),a.jsx(Me,{id:"title",value:u.title,onChange:h=>f("title",h.target.value),placeholder:"Menu item title",className:g.title?"border-red-500":""}),g.title&&a.jsx("p",{className:"text-sm text-red-500",children:g.title})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(H,{htmlFor:"type",children:"Type *"}),a.jsxs(ze,{value:u.type,onValueChange:h=>f("type",h),children:[a.jsx(Fe,{className:g.type?"border-red-500":"",children:a.jsx(Be,{placeholder:"Select type"})}),a.jsx($e,{children:Object.entries(n).map(([h,O])=>a.jsx(xe,{value:h,children:O},h))})]}),g.type&&a.jsx("p",{className:"text-sm text-red-500",children:g.type})]}),u.type==="custom"&&a.jsxs("div",{className:"space-y-2",children:[a.jsx(H,{htmlFor:"url",children:"URL *"}),a.jsx(Me,{id:"url",value:u.url,onChange:h=>f("url",h.target.value),placeholder:"https://example.com",className:g.url?"border-red-500":""}),g.url&&a.jsx("p",{className:"text-sm text-red-500",children:g.url})]}),u.type!=="custom"&&a.jsxs("div",{className:"space-y-2",children:[a.jsxs(H,{htmlFor:"reference_id",children:["Select ",u.type," *"]}),a.jsxs(ze,{value:((j=u.reference_id)==null?void 0:j.toString())||"",onValueChange:h=>f("reference_id",parseInt(h)),children:[a.jsx(Fe,{className:g.reference_id?"border-red-500":"",children:a.jsx(Be,{placeholder:`Select ${u.type}`})}),a.jsx($e,{children:S()})]}),g.reference_id&&a.jsx("p",{className:"text-sm text-red-500",children:g.reference_id})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(H,{htmlFor:"target",children:"Open in"}),a.jsxs(ze,{value:u.target,onValueChange:h=>f("target",h),children:[a.jsx(Fe,{className:g.target?"border-red-500":"",children:a.jsx(Be,{placeholder:"Select target"})}),a.jsx($e,{children:Object.entries(r).map(([h,O])=>a.jsx(xe,{value:h,children:O},h))})]}),g.target&&a.jsx("p",{className:"text-sm text-red-500",children:g.target})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(H,{htmlFor:"parent_id",children:"Parent Item (Optional)"}),a.jsxs(ze,{value:((E=u.parent_id)==null?void 0:E.toString())||"none",onValueChange:h=>f("parent_id",h&&h!=="none"?parseInt(h):null),children:[a.jsx(Fe,{className:g.parent_id?"border-red-500":"",children:a.jsx(Be,{placeholder:"No parent (top level)"})}),a.jsxs($e,{children:[a.jsx(xe,{value:"none",children:"No parent (top level)"}),N()]})]}),g.parent_id&&a.jsx("p",{className:"text-sm text-red-500",children:g.parent_id})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(H,{htmlFor:"icon",children:"Icon (Optional)"}),a.jsx(Me,{id:"icon",value:u.icon,onChange:h=>f("icon",h.target.value),placeholder:"Icon class or name",className:g.icon?"border-red-500":""}),g.icon&&a.jsx("p",{className:"text-sm text-red-500",children:g.icon})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(H,{htmlFor:"css_class",children:"CSS Class (Optional)"}),a.jsx(Me,{id:"css_class",value:u.css_class,onChange:h=>f("css_class",h.target.value),placeholder:"Additional CSS classes",className:g.css_class?"border-red-500":""}),g.css_class&&a.jsx("p",{className:"text-sm text-red-500",children:g.css_class})]}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(fn,{id:"is_active",checked:u.is_active,onCheckedChange:h=>f("is_active",h)}),a.jsx(H,{htmlFor:"is_active",children:"Active"})]})]}),a.jsxs("div",{className:"flex justify-end gap-2",children:[a.jsx(he,{type:"button",variant:"outline",onClick:()=>{l(!1),i("items"),b()},children:"Cancel"}),a.jsx(he,{type:"submit",disabled:C,children:"Add Menu Item"})]})]})})]})}),a.jsx(Pt,{value:"edit-item",children:c&&a.jsxs(pt,{children:[a.jsxs(gt,{children:[a.jsx(vt,{children:"Edit Menu Item"}),a.jsx(mt,{children:"Update the selected menu item"})]}),a.jsx(bt,{children:a.jsxs("form",{onSubmit:y,className:"space-y-4",children:[a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsxs("div",{className:"space-y-2",children:[a.jsx(H,{htmlFor:"title",children:"Title *"}),a.jsx(Me,{id:"title",value:u.title,onChange:h=>f("title",h.target.value),placeholder:"Menu item title",className:g.title?"border-red-500":""}),g.title&&a.jsx("p",{className:"text-sm text-red-500",children:g.title})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(H,{htmlFor:"type",children:"Type *"}),a.jsxs(ze,{value:u.type,onValueChange:h=>f("type",h),children:[a.jsx(Fe,{className:g.type?"border-red-500":"",children:a.jsx(Be,{placeholder:"Select type"})}),a.jsx($e,{children:Object.entries(n).map(([h,O])=>a.jsx(xe,{value:h,children:O},h))})]}),g.type&&a.jsx("p",{className:"text-sm text-red-500",children:g.type})]}),u.type==="custom"&&a.jsxs("div",{className:"space-y-2",children:[a.jsx(H,{htmlFor:"url",children:"URL *"}),a.jsx(Me,{id:"url",value:u.url,onChange:h=>f("url",h.target.value),placeholder:"https://example.com",className:g.url?"border-red-500":""}),g.url&&a.jsx("p",{className:"text-sm text-red-500",children:g.url})]}),u.type!=="custom"&&a.jsxs("div",{className:"space-y-2",children:[a.jsxs(H,{htmlFor:"reference_id",children:["Select ",u.type," *"]}),a.jsxs(ze,{value:((R=u.reference_id)==null?void 0:R.toString())||"",onValueChange:h=>f("reference_id",parseInt(h)),children:[a.jsx(Fe,{className:g.reference_id?"border-red-500":"",children:a.jsx(Be,{placeholder:`Select ${u.type}`})}),a.jsx($e,{children:S()})]}),g.reference_id&&a.jsx("p",{className:"text-sm text-red-500",children:g.reference_id})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(H,{htmlFor:"target",children:"Open in"}),a.jsxs(ze,{value:u.target,onValueChange:h=>f("target",h),children:[a.jsx(Fe,{className:g.target?"border-red-500":"",children:a.jsx(Be,{placeholder:"Select target"})}),a.jsx($e,{children:Object.entries(r).map(([h,O])=>a.jsx(xe,{value:h,children:O},h))})]}),g.target&&a.jsx("p",{className:"text-sm text-red-500",children:g.target})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(H,{htmlFor:"icon",children:"Icon (Optional)"}),a.jsx(Me,{id:"icon",value:u.icon,onChange:h=>f("icon",h.target.value),placeholder:"Icon class or name",className:g.icon?"border-red-500":""}),g.icon&&a.jsx("p",{className:"text-sm text-red-500",children:g.icon})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(H,{htmlFor:"css_class",children:"CSS Class (Optional)"}),a.jsx(Me,{id:"css_class",value:u.css_class,onChange:h=>f("css_class",h.target.value),placeholder:"Additional CSS classes",className:g.css_class?"border-red-500":""}),g.css_class&&a.jsx("p",{className:"text-sm text-red-500",children:g.css_class})]}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(fn,{id:"is_active",checked:u.is_active,onCheckedChange:h=>f("is_active",h)}),a.jsx(H,{htmlFor:"is_active",children:"Active"})]})]}),a.jsxs("div",{className:"flex justify-end gap-2",children:[a.jsx(he,{type:"button",variant:"outline",onClick:()=>{p(null),i("items"),b()},children:"Cancel"}),a.jsx(he,{type:"submit",disabled:C,children:"Update Item"})]})]})})]})})]})]})]})}export{Oo as default};
