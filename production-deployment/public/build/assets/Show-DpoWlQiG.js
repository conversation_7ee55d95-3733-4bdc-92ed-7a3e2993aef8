import{j as e,r as u,Q as Se,t as w,S as G}from"./app-J5EqS6dS.js";import{c as De,B as l,S as K}from"./smartphone-GGiwNneF.js";import{C as M,a as I,b as P,c as _,d as Ie}from"./card-9XCADs-4.js";import{B as j}from"./badge-BucYuCBs.js";import{I as Pe}from"./input-Bo8dOn9p.js";import{L as _e}from"./label-BlOrdc-X.js";import{X as me,k as Te,l as $e,o as Me,w as L,U as J,v as ze,D as ee,b as se,c as qe,d as ae,e as Fe,f as Ve,a as te,I as Ae,t as N}from"./ImpersonationBanner-CYn5eDk6.js";import{A as Ee}from"./app-layout-ox1kAwY6.js";import{C as re}from"./CompatibleModelsProtection-BfRsG4tU.js";import{C as Le}from"./check-C7SdgHPn.js";import{A as Re}from"./arrow-left-D4U9AVF9.js";import{P as R}from"./package-CoyvngX8.js";import{B as le}from"./building-Dgyml3QN.js";import{C as ie}from"./calendar-B-u_QN2Q.js";import{S as ne}from"./square-pen-Bepbg6wc.js";import{S as z}from"./users-RYmOyic9.js";import{E as Be}from"./ellipsis-Bwr8pvFI.js";import{D as de}from"./download-fvx_BKiV.js";import{E as oe}from"./external-link-A4n9PP4e.js";import{C as ce}from"./chevron-left-C6ZNA5qQ.js";import{H as Oe}from"./hash-Bk6gEERd.js";import{F as Ue}from"./file-text-Dx6bYLtE.js";import{T as He}from"./table-gSl3ppmW.js";import{L as Qe}from"./list-CNjrM85i.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./index-BzZWUWqx.js";import"./crown-UDSxMtlm.js";import"./shield-D9nQfigG.js";import"./user-DCnDRzMf.js";import"./search-DBK6jUoc.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./eye-D-fsmYB2.js";import"./lock-Tx_yfI4R.js";import"./mail-CDon-vZy.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const We=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]],Xe=De("FileSpreadsheet",We),Ye=(s,c=30)=>s.length<=c?s:s.substring(0,c)+"...";function Ze({columns:s,onColumnToggle:c,onSelectAll:v,onDeselectAll:f}){const d=s.filter(r=>r.isSelected).length,g=s.length,b=s.filter(r=>r.isRequired&&r.isSelected).length,m=s.filter(r=>r.isRequired).length;return e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex items-center justify-start",children:e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(l,{variant:"outline",size:"sm",onClick:v,disabled:d===g,children:[e.jsx(Le,{className:"h-4 w-4 mr-1"}),"Select All"]}),e.jsxs(l,{variant:"outline",size:"sm",onClick:f,disabled:d===0,children:[e.jsx(me,{className:"h-4 w-4 mr-1"}),"Deselect All"]})]})}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"flex flex-wrap gap-3 p-3 bg-muted/30 rounded-lg border",children:s.map(r=>e.jsxs("label",{className:"flex items-center gap-2 cursor-pointer hover:bg-background/50 px-2 py-1 rounded transition-colors",children:[e.jsx("input",{type:"checkbox",checked:r.isSelected,onChange:k=>c(r.name,k.target.checked),disabled:r.isRequired&&r.isSelected,className:"rounded border-gray-300 text-primary focus:ring-primary focus:ring-offset-0"}),e.jsxs("span",{className:`text-sm ${r.isRequired?"font-medium":""}`,children:[r.name,r.isRequired&&e.jsx("span",{className:"text-red-500 ml-1",children:"*"})]})]},r.name))}),e.jsx("div",{className:"space-y-3",children:s.map(r=>e.jsxs("div",{className:"border rounded-lg p-3 bg-background",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs("h4",{className:"font-medium text-sm",children:[r.name,r.isRequired&&e.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),r.mappedTo!==r.name&&e.jsxs("span",{className:"text-xs text-muted-foreground",children:["Maps to: ",r.mappedTo]})]}),e.jsx("p",{className:"text-xs text-muted-foreground mb-2",children:r.purpose}),r.sampleData&&r.sampleData.length>0&&e.jsxs("div",{className:"space-y-1",children:[e.jsx("p",{className:"text-xs font-medium text-muted-foreground",children:"Sample Data:"}),e.jsxs("div",{className:"flex flex-wrap gap-1",children:[r.sampleData.slice(0,3).map((k,i)=>e.jsx(j,{variant:"outline",className:"text-xs",children:Ye(k)},i)),r.sampleData.length>3&&e.jsxs(j,{variant:"outline",className:"text-xs",children:["+",r.sampleData.length-3," more"]})]})]})]},`${r.name}-details`))})]}),e.jsxs("div",{className:"flex items-center gap-4 text-sm",children:[e.jsxs(j,{variant:"secondary",children:[d," of ",g," columns selected"]}),e.jsxs(j,{variant:b===m?"default":"destructive",children:[b," of ",m," required columns selected"]})]}),b<m&&e.jsx("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:e.jsxs("p",{className:"text-sm text-red-700",children:[e.jsx("strong",{children:"Warning:"})," You must select all required columns (Brand, Model, Compatible) to proceed with the import."]})})]})}function Ge({columns:s,data:c,maxRows:v=5,totalRows:f}){const d=c.slice(0,v);return s.length===0||d.length===0?e.jsx("div",{className:"border rounded-lg p-8 text-center",children:e.jsx("p",{className:"text-gray-500",children:"No data to preview"})}):e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h4",{className:"text-md font-medium",children:"Data Preview"}),e.jsxs(j,{variant:"secondary",children:["Showing ",d.length," of ",f??c.length," rows"]})]}),e.jsxs("div",{className:"hidden md:block border rounded-lg overflow-hidden",children:[e.jsx("div",{className:"bg-gray-50 border-b",children:e.jsx("div",{className:"grid gap-4 p-3",style:{gridTemplateColumns:`repeat(${s.length}, minmax(120px, 1fr))`},children:s.map((g,b)=>e.jsx("div",{className:"text-sm font-medium text-gray-700 truncate",children:g},b))})}),e.jsx("div",{className:"divide-y max-h-64 overflow-y-auto",children:d.map((g,b)=>e.jsx("div",{className:"grid gap-4 p-3 hover:bg-gray-50",style:{gridTemplateColumns:`repeat(${s.length}, minmax(120px, 1fr))`},children:s.map((m,r)=>e.jsx("div",{className:"text-sm text-gray-600 truncate",children:g[m]||e.jsx("span",{className:"text-gray-400 italic",children:"empty"})},r))},b))})]}),e.jsx("div",{className:"md:hidden space-y-3 max-h-64 overflow-y-auto",children:d.map((g,b)=>e.jsxs("div",{className:"border rounded-lg p-4 space-y-2",children:[e.jsxs("div",{className:"text-sm font-medium text-gray-500 mb-2",children:["Row ",b+1]}),s.map((m,r)=>e.jsxs("div",{className:"flex justify-between items-start gap-2",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-700 min-w-0 flex-shrink-0",children:[m,":"]}),e.jsx("span",{className:"text-sm text-gray-600 text-right min-w-0 break-words",children:g[m]||e.jsx("span",{className:"text-gray-400 italic",children:"empty"})})]},r))]},b))}),(f??c.length)>v&&e.jsxs("div",{className:"text-center text-sm text-gray-500 pt-2",children:["... and ",(f??c.length)-v," more rows"]})]})}function As({part:s,showVerificationStatus:c=!0}){var Y,Z;const[v,f]=u.useState(!1),[d,g]=u.useState(null),[b,m]=u.useState(!1),[r,k]=u.useState("table"),[i,S]=u.useState(null),[T,B]=u.useState(!1),[x,y]=u.useState("file"),[q,C]=u.useState([]),[xe,F]=u.useState([]),[he,V]=u.useState(0),[D,A]=u.useState(!1),ge=t=>new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),O=()=>{window.location.href=`/admin/parts/${s.id}/export`,N.success("Export started. Your download will begin shortly.")},be=()=>{window.location.href=`/admin/parts/${s.id}/compatibility/template`,N.success("Template download started.")},ue=t=>{var n;const a=(n=t.target.files)==null?void 0:n[0];a&&(g(a),pe(a))},pe=async t=>{var p;A(!0),y("columns");const a=new FormData;a.append("file",t);const n=(p=document.querySelector('meta[name="csrf-token"]'))==null?void 0:p.getAttribute("content");n&&a.append("_token",n);try{const h=await(await fetch(`/admin/parts/${s.id}/compatibility/preview`,{method:"POST",body:a,headers:{"X-Requested-With":"XMLHttpRequest"}})).json();h.success?(C(h.columns),F(h.previewRows),V(h.totalRows),N.success("File preview loaded successfully!")):(N.error(h.message||"Failed to preview file"),y("file"))}catch(o){console.error("Preview error:",o),N.error("Failed to preview file. Please try again."),y("file")}finally{A(!1)}},fe=(t,a)=>{C(n=>n.map(p=>p.name===t?{...p,isSelected:a}:p))},je=()=>{C(t=>t.map(a=>({...a,isSelected:!0})))},Ne=()=>{C(t=>t.map(a=>({...a,isSelected:a.isRequired})))},$=()=>q.filter(t=>t.isSelected).map(t=>t.name),U=()=>q.filter(a=>a.isRequired).every(a=>a.isSelected),H=()=>{f(!1),g(null),m(!1),y("file"),C([]),F([]),V(0),A(!1)},Q=()=>{b||H()},ve=()=>{y("file"),g(null),C([]),F([]),V(0)},ye=()=>{var p;if(!d){N.error("Please select a CSV file to import.");return}if(!U()){N.error("Please select all required columns (Brand, Model, Compatible) to proceed.");return}console.log("Starting import...",{partId:s.id,fileName:d.name,fileSize:d.size,fileType:d.type,selectedColumns:$()}),m(!0),y("processing");const t=new FormData;t.append("file",d),$().forEach(o=>{t.append("selected_columns[]",o)});const n=(p=document.querySelector('meta[name="csrf-token"]'))==null?void 0:p.getAttribute("content");n&&t.append("_token",n),console.log("FormData created, making request to:",`/admin/parts/${s.id}/compatibility/import`),G.post(`/admin/parts/${s.id}/compatibility/import`,t,{onSuccess:o=>{console.log("Import successful!",o),H(),G.reload({only:["part"]})},onError:o=>{console.error("Import errors:",o);let h="Import failed. Please check your CSV file and try again.";o.file?(h=Array.isArray(o.file)?o.file[0]:o.file,(h.includes("model field is required")||h.includes("compatible field is required"))&&(h="Invalid CSV format. Please download and use the compatibility import template. Do not use the parts export CSV format.")):o.message?h=o.message:typeof o=="string"&&(h=o),N.error(h,{duration:6e3}),m(!1),y("columns")},onFinish:()=>{m(!1)}})},we=t=>{S(t),B(!0)},E=()=>{B(!1),S(null)},W=()=>{i!==null&&s.images&&i>0&&S(i-1)},X=()=>{i!==null&&s.images&&i<s.images.length-1&&S(i+1)};u.useEffect(()=>{const t=a=>{if(!(!T||i===null||!s.images))switch(a.key){case"ArrowLeft":a.preventDefault(),W();break;case"ArrowRight":a.preventDefault(),X();break;case"Escape":a.preventDefault(),E();break}};return T&&(document.addEventListener("keydown",t),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",t),document.body.style.overflow="unset"}},[T,i,s.images]);const ke=()=>{var t;return e.jsx(re,{children:e.jsx("div",{className:"border border-blue-200/50 dark:border-blue-800/50 rounded-lg overflow-hidden bg-white/50 dark:bg-gray-900/50",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-gradient-to-r from-blue-100/80 to-indigo-100/80 dark:from-blue-900/40 dark:to-indigo-900/40",children:e.jsxs("tr",{children:[e.jsx("th",{className:"text-left p-3 font-semibold text-blue-900 dark:text-blue-100",children:"Brand"}),e.jsx("th",{className:"text-left p-3 font-semibold text-blue-900 dark:text-blue-100",children:"Model"}),e.jsx("th",{className:"text-left p-3 font-semibold text-blue-900 dark:text-blue-100 hidden sm:table-cell",children:"Model Number"}),e.jsx("th",{className:"text-left p-3 font-semibold text-blue-900 dark:text-blue-100 hidden lg:table-cell",children:"Notes"}),c&&e.jsx("th",{className:"text-left p-3 font-semibold text-blue-900 dark:text-blue-100",children:"Status"})]})}),e.jsx("tbody",{children:(t=s.models)==null?void 0:t.map((a,n)=>e.jsxs("tr",{className:`border-b border-blue-200/30 dark:border-blue-800/30 hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors ${n%2===0?"bg-white/60 dark:bg-gray-800/30":"bg-blue-50/30 dark:bg-blue-950/10"}`,children:[e.jsx("td",{className:"p-3",children:e.jsxs("div",{className:"flex items-center gap-2",children:[a.brand.logo_url&&e.jsx("div",{className:"w-6 h-6 rounded bg-white dark:bg-gray-800 p-1 border border-gray-200 dark:border-gray-700 flex items-center justify-center flex-shrink-0",children:e.jsx("img",{src:a.brand.logo_url,alt:a.brand.name,className:"w-4 h-4 object-contain"})}),e.jsx("span",{className:"font-semibold text-gray-900 dark:text-gray-100 text-sm",children:a.brand.name})]})}),e.jsx("td",{className:"p-3",children:e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-800 dark:text-gray-200 text-sm",children:a.name}),a.model_number&&e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 font-mono sm:hidden",children:a.model_number}),a.pivot.compatibility_notes&&e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 lg:hidden truncate mt-1",children:a.pivot.compatibility_notes})]})}),e.jsx("td",{className:"p-3 hidden sm:table-cell",children:e.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400 font-mono",children:a.model_number||"-"})}),e.jsx("td",{className:"p-3 hidden lg:table-cell",children:e.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:a.pivot.compatibility_notes||"-"})}),c&&e.jsx("td",{className:"p-3",children:e.jsx(j,{variant:a.pivot.is_verified?"default":"secondary",className:`text-xs font-medium ${a.pivot.is_verified?"bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800":"bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800"}`,children:a.pivot.is_verified?"Verified":"Unverified"})})]},a.id))})]})})})})},Ce=()=>{var t;return e.jsx(re,{children:e.jsx("div",{className:"space-y-2",children:(t=s.models)==null?void 0:t.map((a,n)=>e.jsxs("div",{className:`flex items-center justify-between p-3 border border-blue-200/50 dark:border-blue-800/50 rounded-lg hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-all duration-200 ${n%2===0?"bg-white/60 dark:bg-gray-800/30":"bg-blue-50/30 dark:bg-blue-950/10"}`,children:[e.jsxs("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[a.brand.logo_url&&e.jsx("div",{className:"w-8 h-8 rounded bg-white dark:bg-gray-800 p-1 border border-gray-200 dark:border-gray-700 flex items-center justify-center flex-shrink-0",children:e.jsx("img",{src:a.brand.logo_url,alt:a.brand.name,className:"w-6 h-6 object-contain"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("p",{className:"font-semibold text-gray-900 dark:text-gray-100 truncate text-sm",children:a.brand.name}),e.jsx("span",{className:"text-gray-400 dark:text-gray-500",children:"•"}),e.jsx("p",{className:"font-medium text-gray-800 dark:text-gray-200 truncate text-sm",children:a.name})]}),e.jsxs("div",{className:"flex items-center gap-4 text-xs text-gray-600 dark:text-gray-400",children:[a.model_number&&e.jsx("span",{className:"font-mono",children:a.model_number}),a.pivot.compatibility_notes&&e.jsx("span",{className:"truncate",children:a.pivot.compatibility_notes})]})]})]}),c&&e.jsx("div",{className:"flex items-center gap-2 flex-shrink-0 ml-3",children:e.jsx(j,{variant:a.pivot.is_verified?"default":"secondary",className:`text-xs font-medium ${a.pivot.is_verified?"bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800":"bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800"}`,children:a.pivot.is_verified?"Verified":"Unverified"})})]},a.id))})})};return e.jsxs(Ee,{children:[e.jsx(Se,{title:`${s.name} - Parts - Admin`}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-3 rounded-xl p-3 sm:p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-3 sm:space-y-4",children:[e.jsxs("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-xl border border-blue-200/50 dark:border-blue-800/50 p-4 mb-4",children:[e.jsx("div",{className:"mb-3",children:e.jsx(w,{href:"/admin/parts",children:e.jsxs(l,{variant:"ghost",size:"sm",className:"text-muted-foreground hover:text-foreground",children:[e.jsx(Re,{className:"w-4 h-4 mr-2"}),"Back to Parts"]})})}),e.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-3 mb-2",children:[e.jsx("h1",{className:"text-xl sm:text-2xl font-bold tracking-tight text-gray-900 dark:text-gray-100 truncate",children:s.name}),e.jsxs("div",{className:"flex items-center gap-2 flex-shrink-0",children:[e.jsx(j,{variant:s.is_active?"default":"secondary",className:s.is_active?"bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800":"",children:s.is_active?"Active":"Inactive"}),s.part_number&&e.jsxs(j,{variant:"outline",className:"font-mono text-xs",children:["#",s.part_number]})]})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-x-6 gap-y-2 text-sm text-muted-foreground mb-3",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(R,{className:"w-3 h-3"}),e.jsx("span",{className:"font-medium",children:s.category.name})]}),s.manufacturer&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(le,{className:"w-3 h-3"}),e.jsx("span",{className:"font-medium",children:s.manufacturer})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(K,{className:"w-3 h-3"}),e.jsxs("span",{className:"font-medium",children:[((Y=s.models)==null?void 0:Y.length)||0," models"]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(ie,{className:"w-3 h-3"}),e.jsx("span",{className:"font-medium",children:new Date(s.created_at).toLocaleDateString()})]})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2 flex-shrink-0",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(w,{href:`/admin/parts/${s.id}/edit`,children:e.jsxs(l,{size:"sm",className:"bg-blue-600 hover:bg-blue-700 text-white",children:[e.jsx(ne,{className:"w-4 h-4 mr-2"}),"Edit Part"]})}),e.jsx(w,{href:`/admin/parts/${s.id}/compatibility`,children:e.jsxs(l,{variant:"outline",size:"sm",children:[e.jsx(z,{className:"w-4 h-4 mr-2"}),e.jsx("span",{className:"hidden sm:inline",children:"Manage Compatibility"}),e.jsx("span",{className:"sm:hidden",children:"Manage"})]})})]}),e.jsxs(Te,{children:[e.jsx($e,{asChild:!0,children:e.jsxs(l,{variant:"outline",size:"sm",children:[e.jsx(Be,{className:"w-4 h-4"}),e.jsx("span",{className:"sr-only",children:"More actions"})]})}),e.jsxs(Me,{align:"end",className:"w-56",children:[e.jsxs(L,{onClick:()=>f(!0),children:[e.jsx(J,{className:"w-4 h-4 mr-2"}),"Import Compatibility CSV"]}),e.jsxs(L,{onClick:O,children:[e.jsx(de,{className:"w-4 h-4 mr-2"}),"Export Compatibility CSV"]}),e.jsx(ze,{}),e.jsx(L,{asChild:!0,children:e.jsxs(w,{href:route("parts.show",s.slug||s.id),children:[e.jsx(oe,{className:"w-4 h-4 mr-2"}),"View Public Page"]})})]})]})]})]})]}),e.jsx(ee,{open:v,onOpenChange:Q,children:e.jsxs(se,{className:`${x==="columns"?"sm:max-w-6xl":"sm:max-w-[425px]"} max-h-[90vh] overflow-y-auto`,children:[e.jsxs(qe,{children:[e.jsxs(ae,{children:[x==="file"&&"Import Compatibility Data",x==="columns"&&"Select Columns to Import",x==="processing"&&"Importing Data..."]}),e.jsxs(Fe,{children:[x==="file"&&e.jsxs(e.Fragment,{children:["Upload a CSV file to update compatibility information for ",s.name,".",e.jsx("br",{}),e.jsx("span",{className:"text-sm font-medium",children:"Important:"})," Use the compatibility CSV format, not the parts export format."]}),x==="columns"&&e.jsxs(e.Fragment,{children:["Choose which columns from your CSV file to include in the import. Required columns are marked with ",e.jsx("span",{className:"text-red-500",children:"*"})]}),x==="processing"&&"Please wait while your data is being imported..."]})]}),x==="file"&&e.jsxs("div",{className:"grid gap-4 py-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(_e,{htmlFor:"csv-file",children:"CSV File"}),e.jsx(Pe,{id:"csv-file",type:"file",accept:".csv",onChange:ue,disabled:D}),d&&e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Selected: ",d.name]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(l,{variant:"outline",size:"sm",onClick:be,className:"w-full",children:[e.jsx(Xe,{className:"w-4 h-4 mr-2"}),"Download Template"]}),e.jsxs("div",{className:"text-xs text-muted-foreground space-y-1",children:[e.jsx("p",{children:"Download the CSV template with sample data and correct column format."}),e.jsx("p",{className:"text-xs",children:"The template includes all supported columns with example data to guide your import."})]})]})]}),x==="columns"&&e.jsx("div",{className:"py-4 space-y-6",children:D?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Analyzing CSV file..."})]})}):e.jsxs(e.Fragment,{children:[e.jsx(Ze,{columns:q,onColumnToggle:fe,onSelectAll:je,onDeselectAll:Ne}),$().length>0&&e.jsx(Ge,{columns:$(),data:xe,totalRows:he,maxRows:3})]})}),x==="processing"&&e.jsx("div",{className:"py-8",children:e.jsx("div",{className:"flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),e.jsx("p",{className:"text-lg font-medium",children:"Importing compatibility data..."}),e.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"This may take a few moments"})]})})}),e.jsxs(Ve,{className:"sm:justify-start",children:[x==="file"&&e.jsx(l,{variant:"outline",onClick:Q,disabled:D,children:"Cancel"}),x==="columns"&&e.jsxs(e.Fragment,{children:[e.jsxs(l,{variant:"outline",onClick:ve,disabled:D,children:[e.jsx(ce,{className:"w-4 h-4 mr-1"}),"Back"]}),e.jsxs(l,{onClick:ye,disabled:!U()||D,children:["Import Selected Columns",e.jsx(te,{className:"w-4 h-4 ml-1"})]})]}),x==="processing"&&e.jsx(l,{variant:"outline",disabled:!0,children:"Processing..."})]})]})}),e.jsx(ee,{open:T,onOpenChange:E,children:e.jsxs(se,{className:"max-w-7xl w-[95vw] h-[95vh] p-0 overflow-hidden border-0 bg-transparent shadow-2xl",children:[e.jsxs(ae,{className:"sr-only",children:[s.name," - Image Gallery"]}),e.jsxs("div",{className:"relative w-full h-full bg-gradient-to-br from-gray-900 via-black to-gray-900 rounded-xl overflow-hidden",children:[e.jsx("div",{className:"absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-black/80 via-black/40 to-transparent p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"text-white",children:[e.jsx("h3",{className:"text-lg font-semibold",children:s.name}),s.images&&s.images.length>1&&i!==null&&e.jsxs("p",{className:"text-sm text-gray-300",children:["Image ",i+1," of ",s.images.length]})]}),e.jsx(l,{variant:"ghost",size:"sm",className:"text-white hover:bg-white/20 rounded-full w-10 h-10 p-0 transition-all duration-200 hover:scale-110",onClick:E,children:e.jsx(me,{className:"w-5 h-5"})})]})}),s.images&&s.images.length>1&&e.jsxs(e.Fragment,{children:[e.jsx(l,{variant:"ghost",size:"sm",className:"absolute left-4 top-1/2 -translate-y-1/2 z-20 text-white hover:bg-white/20 rounded-full w-12 h-12 p-0 disabled:opacity-30 transition-all duration-200 hover:scale-110 disabled:hover:scale-100",onClick:W,disabled:i===0,children:e.jsx(ce,{className:"w-6 h-6"})}),e.jsx(l,{variant:"ghost",size:"sm",className:"absolute right-4 top-1/2 -translate-y-1/2 z-20 text-white hover:bg-white/20 rounded-full w-12 h-12 p-0 disabled:opacity-30 transition-all duration-200 hover:scale-110 disabled:hover:scale-100",onClick:X,disabled:i===(((Z=s.images)==null?void 0:Z.length)||0)-1,children:e.jsx(te,{className:"w-6 h-6"})})]}),i!==null&&s.images&&e.jsx("div",{className:"flex items-center justify-center w-full h-full p-8 pt-20 pb-16",children:e.jsx("div",{className:"relative max-w-full max-h-full",children:e.jsx("img",{src:s.images[i],alt:`${s.name} - Image ${i+1}`,className:"max-w-full max-h-full object-contain rounded-lg shadow-2xl transition-all duration-300",onError:t=>{t.currentTarget.src="/placeholder-image.svg"}})})}),s.images&&s.images.length>1&&i!==null&&e.jsx("div",{className:"absolute bottom-0 left-0 right-0 z-20 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-4",children:e.jsx("div",{className:"flex items-center justify-center space-x-2",children:e.jsx("div",{className:"flex items-center space-x-2 bg-black/50 rounded-full px-4 py-2",children:s.images.map((t,a)=>e.jsx("button",{onClick:()=>S(a),className:`w-2 h-2 rounded-full transition-all duration-200 ${a===i?"bg-white scale-125":"bg-white/50 hover:bg-white/75"}`},a))})})}),s.images&&s.images.length>1&&e.jsx("div",{className:"absolute bottom-4 right-4 z-20 text-white/60 text-xs bg-black/30 rounded px-2 py-1",children:"Use ← → keys to navigate"})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-3 lg:gap-4",children:[e.jsxs("div",{className:"lg:col-span-3 space-y-3 lg:space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 xl:grid-cols-2 gap-3 lg:gap-4",children:[e.jsxs(M,{className:"border-blue-200/50 dark:border-blue-800/50",children:[e.jsx(I,{className:"bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3",children:e.jsxs(P,{className:"flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg",children:[e.jsx(R,{className:"w-4 h-4"}),"Basic Information"]})}),e.jsxs(_,{className:"p-0",children:[e.jsx("div",{className:"border border-blue-200/50 dark:border-blue-800/50 rounded-lg overflow-hidden bg-white/50 dark:bg-gray-900/50 mx-4 mb-4",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsx("table",{className:"w-full",children:e.jsxs("tbody",{children:[e.jsxs("tr",{className:"border-b border-blue-200/30 dark:border-blue-800/30 hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors bg-white/60 dark:bg-gray-800/30",children:[e.jsx("td",{className:"p-3 w-1/3",children:e.jsxs("span",{className:"font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2",children:[e.jsx(R,{className:"w-3 h-3 text-blue-600 dark:text-blue-400"}),"Category"]})}),e.jsx("td",{className:"p-3",children:e.jsx("span",{className:"text-gray-800 dark:text-gray-200 font-medium",children:s.category.name})})]}),s.part_number&&e.jsxs("tr",{className:"border-b border-blue-200/30 dark:border-blue-800/30 hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors bg-blue-50/30 dark:bg-blue-950/10",children:[e.jsx("td",{className:"p-3",children:e.jsxs("span",{className:"font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2",children:[e.jsx(Oe,{className:"w-3 h-3 text-green-600 dark:text-green-400"}),"Part Number"]})}),e.jsx("td",{className:"p-3",children:e.jsx("span",{className:"text-gray-800 dark:text-gray-200 font-medium font-mono",children:s.part_number})})]}),s.manufacturer&&e.jsxs("tr",{className:"border-b border-blue-200/30 dark:border-blue-800/30 hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors bg-white/60 dark:bg-gray-800/30",children:[e.jsx("td",{className:"p-3",children:e.jsxs("span",{className:"font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2",children:[e.jsx(le,{className:"w-3 h-3 text-purple-600 dark:text-purple-400"}),"Manufacturer"]})}),e.jsx("td",{className:"p-3",children:e.jsx("span",{className:"text-gray-800 dark:text-gray-200 font-medium",children:s.manufacturer})})]}),e.jsxs("tr",{className:"hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors bg-blue-50/30 dark:bg-blue-950/10",children:[e.jsx("td",{className:"p-3",children:e.jsxs("span",{className:"font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2",children:[e.jsx(ie,{className:"w-3 h-3 text-orange-600 dark:text-orange-400"}),"Created"]})}),e.jsx("td",{className:"p-3",children:e.jsx("span",{className:"text-gray-800 dark:text-gray-200 font-medium",children:ge(s.created_at)})})]})]})})})}),s.description&&e.jsxs("div",{className:"mx-4 mb-4 p-3 bg-gradient-to-br from-gray-50 to-slate-50 dark:from-gray-900/50 dark:to-slate-900/50 rounded-lg border border-gray-200 dark:border-gray-700",children:[e.jsxs(Ke,{className:"text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-1 mb-2",children:[e.jsx(Ue,{className:"w-3 h-3"}),"Description"]}),e.jsx("p",{className:"text-sm leading-relaxed whitespace-pre-wrap text-gray-800 dark:text-gray-200",children:s.description})]})]})]}),s.specifications&&Object.keys(s.specifications).length>0&&e.jsxs(M,{className:"border-blue-200/50 dark:border-blue-800/50",children:[e.jsx(I,{className:"bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3",children:e.jsxs(P,{className:"flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg",children:[e.jsx(z,{className:"w-4 h-4"}),"Specifications"]})}),e.jsx(_,{className:"p-0",children:e.jsx("div",{className:"border border-blue-200/50 dark:border-blue-800/50 rounded-lg overflow-hidden bg-white/50 dark:bg-gray-900/50 mx-4 mb-4",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-gradient-to-r from-blue-100/80 to-indigo-100/80 dark:from-blue-900/40 dark:to-indigo-900/40",children:e.jsxs("tr",{children:[e.jsx("th",{className:"text-left p-3 font-semibold text-blue-900 dark:text-blue-100 w-1/2",children:"Specification"}),e.jsx("th",{className:"text-left p-3 font-semibold text-blue-900 dark:text-blue-100",children:"Value"})]})}),e.jsx("tbody",{children:Object.entries(s.specifications).map(([t,a],n)=>e.jsxs("tr",{className:`border-b border-blue-200/30 dark:border-blue-800/30 hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors ${n%2===0?"bg-white/60 dark:bg-gray-800/30":"bg-blue-50/30 dark:bg-blue-950/10"}`,children:[e.jsx("td",{className:"p-3",children:e.jsx("span",{className:"font-semibold text-gray-900 dark:text-gray-100 capitalize",children:t.replace(/([A-Z])/g," $1").replace(/^./,p=>p.toUpperCase())})}),e.jsx("td",{className:"p-3",children:e.jsx("span",{className:"text-gray-800 dark:text-gray-200 font-medium",children:a})})]},t))})]})})})})]})]}),s.models&&s.models.length>0&&e.jsxs(M,{className:"border-blue-200/50 dark:border-blue-800/50",children:[e.jsx(I,{className:"bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs(P,{className:"flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg",children:[e.jsx(K,{className:"w-4 h-4"}),"Compatible Models (",s.models.length,")"]}),e.jsx(Ie,{className:"mt-1 text-blue-700/70 dark:text-blue-300/70 text-sm",children:"Mobile device models that are compatible with this part"})]}),e.jsxs("div",{className:"flex items-center gap-1 bg-white/60 dark:bg-gray-800/60 border border-blue-200 dark:border-blue-700 rounded-lg p-1",children:[e.jsxs(l,{variant:r==="table"?"default":"ghost",size:"sm",onClick:()=>k("table"),className:`h-7 px-2 ${r==="table"?"bg-blue-600 hover:bg-blue-700 text-white shadow-sm":"hover:bg-blue-100 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300"}`,title:"Table View",children:[e.jsx(He,{className:"h-3 w-3"}),e.jsx("span",{className:"ml-1 hidden sm:inline text-xs",children:"Table"})]}),e.jsxs(l,{variant:r==="list"?"default":"ghost",size:"sm",onClick:()=>k("list"),className:`h-7 px-2 ${r==="list"?"bg-blue-600 hover:bg-blue-700 text-white shadow-sm":"hover:bg-blue-100 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300"}`,title:"List View",children:[e.jsx(Qe,{className:"h-3 w-3"}),e.jsx("span",{className:"ml-1 hidden sm:inline text-xs",children:"List"})]})]})]})}),e.jsx(_,{className:"p-4",children:r==="table"?e.jsx(ke,{}):e.jsx(Ce,{})})]})]}),e.jsx("div",{className:"space-y-4",children:e.jsxs(M,{className:"border-blue-200/50 dark:border-blue-800/50",children:[s.images&&s.images.length>0&&e.jsxs(e.Fragment,{children:[e.jsx(I,{className:"bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3",children:e.jsxs(P,{className:"flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg",children:[e.jsx(Ae,{className:"w-4 h-4"}),"Images (",s.images.length,")"]})}),e.jsx(_,{className:"p-3 pb-0",children:e.jsx("div",{className:"grid grid-cols-3 gap-2",children:s.images.map((t,a)=>e.jsx("div",{className:"aspect-square w-full max-w-[80px] mx-auto border border-blue-200/50 dark:border-blue-800/50 rounded-lg overflow-hidden group cursor-pointer hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300",onClick:()=>we(a),children:e.jsx("img",{src:t,alt:`${s.name} - Image ${a+1}`,className:"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300",onError:n=>{n.currentTarget.src="/placeholder-image.svg"}})},a))})})]}),e.jsx(I,{className:`bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3 ${s.images&&s.images.length>0?"border-t border-blue-200/50 dark:border-blue-800/50 mt-3":""}`,children:e.jsxs(P,{className:"flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg",children:[e.jsx(z,{className:"w-4 h-4"}),"Quick Actions"]})}),e.jsxs(_,{className:"space-y-2 p-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(l,{variant:"outline",size:"sm",className:"w-full justify-start hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-950/20 transition-colors text-sm",onClick:()=>f(!0),children:[e.jsx(J,{className:"w-3 h-3 mr-2"}),"Import Compatibility CSV"]}),e.jsxs(l,{variant:"outline",size:"sm",className:"w-full justify-start hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-950/20 transition-colors text-sm",onClick:O,children:[e.jsx(de,{className:"w-3 h-3 mr-2"}),"Export Compatibility CSV"]})]}),e.jsx("div",{className:"border-t border-gray-200 dark:border-gray-700 my-2"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(w,{href:route("parts.show",s.slug||s.id),className:"block",children:e.jsxs(l,{variant:"outline",size:"sm",className:"w-full justify-start hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-950/20 transition-colors text-sm",children:[e.jsx(oe,{className:"w-3 h-3 mr-2"}),"View Public Page"]})}),e.jsx(w,{href:`/admin/parts/${s.id}/edit`,className:"block",children:e.jsxs(l,{variant:"outline",size:"sm",className:"w-full justify-start hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-950/20 transition-colors text-sm",children:[e.jsx(ne,{className:"w-3 h-3 mr-2"}),"Edit Part"]})}),e.jsx(w,{href:`/admin/parts/${s.id}/compatibility`,className:"block",children:e.jsxs(l,{variant:"outline",size:"sm",className:"w-full justify-start hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-950/20 transition-colors text-sm",children:[e.jsx(z,{className:"w-3 h-3 mr-2"}),"Manage Compatibility"]})})]})]})]})})]})]})})]})}function Ke({children:s,className:c=""}){return e.jsx("label",{className:`block text-sm font-medium ${c}`,children:s})}export{As as default};
