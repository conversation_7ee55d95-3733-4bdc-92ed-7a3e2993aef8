import{x,j as e,Q as h,t as l}from"./app-J5EqS6dS.js";import{B as i}from"./smartphone-GGiwNneF.js";import{C as j,a as u,b as f,d as g,c as v}from"./card-9XCADs-4.js";import{I as C}from"./input-Bo8dOn9p.js";import{L as o}from"./label-BlOrdc-X.js";import{S as b,a as y,b as N,c as S,d}from"./select-CIhY0l9J.js";import{A as w}from"./app-layout-ox1kAwY6.js";import{A as _}from"./arrow-left-D4U9AVF9.js";import{S as A}from"./save-DfhL0V-C.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./ImpersonationBanner-CYn5eDk6.js";import"./index-BzZWUWqx.js";import"./badge-BucYuCBs.js";import"./crown-UDSxMtlm.js";import"./shield-D9nQfigG.js";import"./user-DCnDRzMf.js";import"./search-DBK6jUoc.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./eye-D-fsmYB2.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";function oe({parentCategories:c}){const{data:a,setData:s,post:m,processing:n,errors:t}=x({name:"",description:"",parent_id:"",sort_order:0,is_active:!0}),p=r=>{r.preventDefault(),m("/admin/categories")};return e.jsxs(w,{children:[e.jsx(h,{title:"Create Category - Admin"}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(l,{href:"/admin/categories",children:e.jsxs(i,{variant:"outline",size:"sm",children:[e.jsx(_,{className:"w-4 h-4 mr-2"}),"Back to Categories"]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Create Category"}),e.jsx("p",{className:"text-muted-foreground",children:"Add a new category to organize parts"})]})]}),e.jsxs(j,{children:[e.jsxs(u,{children:[e.jsx(f,{children:"Category Details"}),e.jsx(g,{children:"Enter the information for the new category"})]}),e.jsx(v,{children:e.jsxs("form",{onSubmit:p,className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"name",children:"Category Name *"}),e.jsx(C,{id:"name",type:"text",value:a.name,onChange:r=>s("name",r.target.value),placeholder:"e.g., Display, Battery, Camera",className:t.name?"border-red-500":""}),t.name&&e.jsx("p",{className:"text-sm text-red-600",children:t.name})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"description",children:"Description"}),e.jsx("textarea",{id:"description",value:a.description,onChange:r=>s("description",r.target.value),placeholder:"Optional description for this category",rows:3,className:"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"}),t.description&&e.jsx("p",{className:"text-sm text-red-600",children:t.description})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"parent_id",children:"Parent Category"}),e.jsxs(b,{value:a.parent_id||"none",onValueChange:r=>s("parent_id",r==="none"?"":r),children:[e.jsx(y,{className:t.parent_id?"border-red-500":"",children:e.jsx(N,{placeholder:"Select parent category (optional)"})}),e.jsxs(S,{children:[e.jsx(d,{value:"none",children:"No parent (top-level category)"}),c.map(r=>e.jsx(d,{value:r.id.toString(),children:r.name},r.id))]})]}),t.parent_id&&e.jsx("p",{className:"text-sm text-red-600",children:t.parent_id})]}),e.jsxs("div",{className:"flex justify-end gap-4 pt-6 border-t",children:[e.jsx(l,{href:"/admin/categories",children:e.jsx(i,{variant:"outline",type:"button",children:"Cancel"})}),e.jsxs(i,{type:"submit",disabled:n,children:[e.jsx(A,{className:"w-4 h-4 mr-2"}),n?"Creating...":"Create Category"]})]})]})})]})]})})]})}export{oe as default};
