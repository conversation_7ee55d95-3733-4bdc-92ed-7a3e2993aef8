var to=Object.defineProperty;var no=(e,t,n)=>t in e?to(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var $e=(e,t,n)=>no(e,typeof t!="symbol"?t+"":t,n);import{r as l,j as r,U as v,J as ct,a as ro,t as Ae,S as Gt}from"./app-J5EqS6dS.js";import{c as Y,u as pe,e as Fn,a as j,g as ao,b as $n,d as Bn,B as zt,h as Ne,S as Rt}from"./smartphone-GGiwNneF.js";import{u as dt,c as Le,a as k,d as oo,b as It}from"./index-D86BnqlV.js";import{a as ce,P as Kt,h as Hn,f as Gn,e as zn,F as Kn,D as Vt,c as ut,R as Vn,A as Un,g as Yn,C as Wn,i as so,b as qn,u as Xn,d as ft,S as Ut,U as io,j as lo}from"./users-RYmOyic9.js";import{P as xe}from"./index-BzZWUWqx.js";import{P as B,R as co,d as uo}from"./index-CJpBU2i9.js";import{B as Pe}from"./badge-BucYuCBs.js";import{C as fo}from"./crown-UDSxMtlm.js";import{S as Ve}from"./shield-D9nQfigG.js";import{U as Zn}from"./user-DCnDRzMf.js";import{S as Ue}from"./search-DBK6jUoc.js";import{P as Tt}from"./package-CoyvngX8.js";import{D as mo}from"./database-s9JOA0jY.js";import{Z as po}from"./zap-BcmHRR4K.js";import{E as go}from"./eye-D-fsmYB2.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ho=[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]],vo=Y("Activity",ho);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xo=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],Jn=Y("Bell",xo);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bo=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],Yt=Y("ChartColumn",bo);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yo=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],wo=Y("ChevronRight",yo);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Co=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]],Qn=Y("CircleHelp",Co);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Eo=[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]],No=Y("CreditCard",Eo);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const So=[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]],er=Y("Heart",So);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jo=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]],_o=Y("History",jo);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mo=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]],ko=Y("Image",Mo);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ro=[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]],Io=Y("Key",Ro);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const To=[["path",{d:"M10 8h.01",key:"1r9ogq"}],["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M14 8h.01",key:"1primd"}],["path",{d:"M16 12h.01",key:"1l6xoz"}],["path",{d:"M18 8h.01",key:"emo2bl"}],["path",{d:"M6 8h.01",key:"x9i8wu"}],["path",{d:"M7 16h10",key:"wp8him"}],["path",{d:"M8 12h.01",key:"czm47f"}],["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}]],Do=Y("Keyboard",To);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ao=[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]],tr=Y("LayoutGrid",Ao);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Po=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]],nr=Y("LogOut",Po);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oo=[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]],Lo=Y("Menu",Oo);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fo=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],rr=Y("Plus",Fo);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $o=[["path",{d:"m15 5 6.3 6.3a2.4 2.4 0 0 1 0 3.4L17 19",key:"1cbfv1"}],["path",{d:"M9.586 5.586A2 2 0 0 0 8.172 5H3a1 1 0 0 0-1 1v5.172a2 2 0 0 0 .586 1.414L8.29 18.29a2.426 2.426 0 0 0 3.42 0l3.58-3.58a2.426 2.426 0 0 0 0-3.42z",key:"135mg7"}],["circle",{cx:"6.5",cy:"9.5",r:".5",fill:"currentColor",key:"5pm5xn"}]],Sn=Y("Tags",$o);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bo=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]],ar=Y("Upload",Bo);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ho=[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]],Go=Y("Wallet",Ho);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zo=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],or=Y("X",zo),Et=768;function Ko(){const[e,t]=l.useState();return l.useEffect(()=>{const n=window.matchMedia(`(max-width: ${Et-1}px)`),a=()=>{t(window.innerWidth<Et)};return n.addEventListener("change",a),t(window.innerWidth<Et),()=>n.removeEventListener("change",a)},[]),!!e}var mt="Dialog",[sr,xd]=Le(mt),[Vo,ge]=sr(mt),ir=e=>{const{__scopeDialog:t,children:n,open:a,defaultOpen:s,onOpenChange:i,modal:c=!0}=e,u=l.useRef(null),m=l.useRef(null),[d,f]=dt({prop:a,defaultProp:s??!1,onChange:i,caller:mt});return r.jsx(Vo,{scope:t,triggerRef:u,contentRef:m,contentId:ce(),titleId:ce(),descriptionId:ce(),open:d,onOpenChange:f,onOpenToggle:l.useCallback(()=>f(o=>!o),[f]),modal:c,children:n})};ir.displayName=mt;var lr="DialogTrigger",cr=l.forwardRef((e,t)=>{const{__scopeDialog:n,...a}=e,s=ge(lr,n),i=pe(t,s.triggerRef);return r.jsx(B.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":Xt(s.open),...a,ref:i,onClick:k(e.onClick,s.onOpenToggle)})});cr.displayName=lr;var Wt="DialogPortal",[Uo,dr]=sr(Wt,{forceMount:void 0}),ur=e=>{const{__scopeDialog:t,forceMount:n,children:a,container:s}=e,i=ge(Wt,t);return r.jsx(Uo,{scope:t,forceMount:n,children:l.Children.map(a,c=>r.jsx(xe,{present:n||i.open,children:r.jsx(Kt,{asChild:!0,container:s,children:c})}))})};ur.displayName=Wt;var st="DialogOverlay",fr=l.forwardRef((e,t)=>{const n=dr(st,e.__scopeDialog),{forceMount:a=n.forceMount,...s}=e,i=ge(st,e.__scopeDialog);return i.modal?r.jsx(xe,{present:a||i.open,children:r.jsx(Wo,{...s,ref:t})}):null});fr.displayName=st;var Yo=Fn("DialogOverlay.RemoveScroll"),Wo=l.forwardRef((e,t)=>{const{__scopeDialog:n,...a}=e,s=ge(st,n);return r.jsx(Gn,{as:Yo,allowPinchZoom:!0,shards:[s.contentRef],children:r.jsx(B.div,{"data-state":Xt(s.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),Se="DialogContent",mr=l.forwardRef((e,t)=>{const n=dr(Se,e.__scopeDialog),{forceMount:a=n.forceMount,...s}=e,i=ge(Se,e.__scopeDialog);return r.jsx(xe,{present:a||i.open,children:i.modal?r.jsx(qo,{...s,ref:t}):r.jsx(Xo,{...s,ref:t})})});mr.displayName=Se;var qo=l.forwardRef((e,t)=>{const n=ge(Se,e.__scopeDialog),a=l.useRef(null),s=pe(t,n.contentRef,a);return l.useEffect(()=>{const i=a.current;if(i)return Hn(i)},[]),r.jsx(pr,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:k(e.onCloseAutoFocus,i=>{var c;i.preventDefault(),(c=n.triggerRef.current)==null||c.focus()}),onPointerDownOutside:k(e.onPointerDownOutside,i=>{const c=i.detail.originalEvent,u=c.button===0&&c.ctrlKey===!0;(c.button===2||u)&&i.preventDefault()}),onFocusOutside:k(e.onFocusOutside,i=>i.preventDefault())})}),Xo=l.forwardRef((e,t)=>{const n=ge(Se,e.__scopeDialog),a=l.useRef(!1),s=l.useRef(!1);return r.jsx(pr,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:i=>{var c,u;(c=e.onCloseAutoFocus)==null||c.call(e,i),i.defaultPrevented||(a.current||(u=n.triggerRef.current)==null||u.focus(),i.preventDefault()),a.current=!1,s.current=!1},onInteractOutside:i=>{var m,d;(m=e.onInteractOutside)==null||m.call(e,i),i.defaultPrevented||(a.current=!0,i.detail.originalEvent.type==="pointerdown"&&(s.current=!0));const c=i.target;((d=n.triggerRef.current)==null?void 0:d.contains(c))&&i.preventDefault(),i.detail.originalEvent.type==="focusin"&&s.current&&i.preventDefault()}})}),pr=l.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:a,onOpenAutoFocus:s,onCloseAutoFocus:i,...c}=e,u=ge(Se,n),m=l.useRef(null),d=pe(t,m);return zn(),r.jsxs(r.Fragment,{children:[r.jsx(Kn,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:s,onUnmountAutoFocus:i,children:r.jsx(Vt,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":Xt(u.open),...c,ref:d,onDismiss:()=>u.onOpenChange(!1)})}),r.jsxs(r.Fragment,{children:[r.jsx(Zo,{titleId:u.titleId}),r.jsx(Qo,{contentRef:m,descriptionId:u.descriptionId})]})]})}),qt="DialogTitle",gr=l.forwardRef((e,t)=>{const{__scopeDialog:n,...a}=e,s=ge(qt,n);return r.jsx(B.h2,{id:s.titleId,...a,ref:t})});gr.displayName=qt;var hr="DialogDescription",vr=l.forwardRef((e,t)=>{const{__scopeDialog:n,...a}=e,s=ge(hr,n);return r.jsx(B.p,{id:s.descriptionId,...a,ref:t})});vr.displayName=hr;var xr="DialogClose",br=l.forwardRef((e,t)=>{const{__scopeDialog:n,...a}=e,s=ge(xr,n);return r.jsx(B.button,{type:"button",...a,ref:t,onClick:k(e.onClick,()=>s.onOpenChange(!1))})});br.displayName=xr;function Xt(e){return e?"open":"closed"}var yr="DialogTitleWarning",[bd,wr]=oo(yr,{contentName:Se,titleName:qt,docsSlug:"dialog"}),Zo=({titleId:e})=>{const t=wr(yr),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return l.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},Jo="DialogDescriptionWarning",Qo=({contentRef:e,descriptionId:t})=>{const a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${wr(Jo).contentName}}.`;return l.useEffect(()=>{var i;const s=(i=e.current)==null?void 0:i.getAttribute("aria-describedby");t&&s&&(document.getElementById(t)||console.warn(a))},[a,e,t]),null},Zt=ir,Cr=cr,Jt=ur,Qt=fr,en=mr,Er=gr,Nr=vr,tn=br;function es({...e}){return r.jsx(Zt,{"data-slot":"sheet",...e})}function yd({...e}){return r.jsx(Cr,{"data-slot":"sheet-trigger",...e})}function ts({...e}){return r.jsx(Jt,{"data-slot":"sheet-portal",...e})}function ns({className:e,...t}){return r.jsx(Qt,{"data-slot":"sheet-overlay",className:j("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",e),...t})}function rs({className:e,children:t,side:n="right",...a}){return r.jsxs(ts,{children:[r.jsx(ns,{}),r.jsxs(en,{"data-slot":"sheet-content",className:j("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",n==="right"&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",n==="left"&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",n==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",n==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...a,children:[t,r.jsxs(tn,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[r.jsx(or,{className:"size-4"}),r.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function as({className:e,...t}){return r.jsx("div",{"data-slot":"sheet-header",className:j("flex flex-col gap-1.5 p-4",e),...t})}function os({className:e,...t}){return r.jsx(Er,{"data-slot":"sheet-title",className:j("text-foreground font-semibold",e),...t})}function ss({className:e,...t}){return r.jsx(Nr,{"data-slot":"sheet-description",className:j("text-muted-foreground text-sm",e),...t})}var[pt,wd]=Le("Tooltip",[ut]),gt=ut(),Sr="TooltipProvider",is=700,Dt="tooltip.open",[ls,nn]=pt(Sr),jr=e=>{const{__scopeTooltip:t,delayDuration:n=is,skipDelayDuration:a=300,disableHoverableContent:s=!1,children:i}=e,c=l.useRef(!0),u=l.useRef(!1),m=l.useRef(0);return l.useEffect(()=>{const d=m.current;return()=>window.clearTimeout(d)},[]),r.jsx(ls,{scope:t,isOpenDelayedRef:c,delayDuration:n,onOpen:l.useCallback(()=>{window.clearTimeout(m.current),c.current=!1},[]),onClose:l.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>c.current=!0,a)},[a]),isPointerInTransitRef:u,onPointerInTransitChange:l.useCallback(d=>{u.current=d},[]),disableHoverableContent:s,children:i})};jr.displayName=Sr;var Ye="Tooltip",[cs,Xe]=pt(Ye),_r=e=>{const{__scopeTooltip:t,children:n,open:a,defaultOpen:s,onOpenChange:i,disableHoverableContent:c,delayDuration:u}=e,m=nn(Ye,e.__scopeTooltip),d=gt(t),[f,o]=l.useState(null),g=ce(),h=l.useRef(0),b=c??m.disableHoverableContent,M=u??m.delayDuration,x=l.useRef(!1),[N,T]=dt({prop:a,defaultProp:s??!1,onChange:F=>{F?(m.onOpen(),document.dispatchEvent(new CustomEvent(Dt))):m.onClose(),i==null||i(F)},caller:Ye}),P=l.useMemo(()=>N?x.current?"delayed-open":"instant-open":"closed",[N]),$=l.useCallback(()=>{window.clearTimeout(h.current),h.current=0,x.current=!1,T(!0)},[T]),L=l.useCallback(()=>{window.clearTimeout(h.current),h.current=0,T(!1)},[T]),A=l.useCallback(()=>{window.clearTimeout(h.current),h.current=window.setTimeout(()=>{x.current=!0,T(!0),h.current=0},M)},[M,T]);return l.useEffect(()=>()=>{h.current&&(window.clearTimeout(h.current),h.current=0)},[]),r.jsx(Vn,{...d,children:r.jsx(cs,{scope:t,contentId:g,open:N,stateAttribute:P,trigger:f,onTriggerChange:o,onTriggerEnter:l.useCallback(()=>{m.isOpenDelayedRef.current?A():$()},[m.isOpenDelayedRef,A,$]),onTriggerLeave:l.useCallback(()=>{b?L():(window.clearTimeout(h.current),h.current=0)},[L,b]),onOpen:$,onClose:L,disableHoverableContent:b,children:n})})};_r.displayName=Ye;var At="TooltipTrigger",Mr=l.forwardRef((e,t)=>{const{__scopeTooltip:n,...a}=e,s=Xe(At,n),i=nn(At,n),c=gt(n),u=l.useRef(null),m=pe(t,u,s.onTriggerChange),d=l.useRef(!1),f=l.useRef(!1),o=l.useCallback(()=>d.current=!1,[]);return l.useEffect(()=>()=>document.removeEventListener("pointerup",o),[o]),r.jsx(Un,{asChild:!0,...c,children:r.jsx(B.button,{"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute,...a,ref:m,onPointerMove:k(e.onPointerMove,g=>{g.pointerType!=="touch"&&!f.current&&!i.isPointerInTransitRef.current&&(s.onTriggerEnter(),f.current=!0)}),onPointerLeave:k(e.onPointerLeave,()=>{s.onTriggerLeave(),f.current=!1}),onPointerDown:k(e.onPointerDown,()=>{s.open&&s.onClose(),d.current=!0,document.addEventListener("pointerup",o,{once:!0})}),onFocus:k(e.onFocus,()=>{d.current||s.onOpen()}),onBlur:k(e.onBlur,s.onClose),onClick:k(e.onClick,s.onClose)})})});Mr.displayName=At;var rn="TooltipPortal",[ds,us]=pt(rn,{forceMount:void 0}),kr=e=>{const{__scopeTooltip:t,forceMount:n,children:a,container:s}=e,i=Xe(rn,t);return r.jsx(ds,{scope:t,forceMount:n,children:r.jsx(xe,{present:n||i.open,children:r.jsx(Kt,{asChild:!0,container:s,children:a})})})};kr.displayName=rn;var Oe="TooltipContent",Rr=l.forwardRef((e,t)=>{const n=us(Oe,e.__scopeTooltip),{forceMount:a=n.forceMount,side:s="top",...i}=e,c=Xe(Oe,e.__scopeTooltip);return r.jsx(xe,{present:a||c.open,children:c.disableHoverableContent?r.jsx(Ir,{side:s,...i,ref:t}):r.jsx(fs,{side:s,...i,ref:t})})}),fs=l.forwardRef((e,t)=>{const n=Xe(Oe,e.__scopeTooltip),a=nn(Oe,e.__scopeTooltip),s=l.useRef(null),i=pe(t,s),[c,u]=l.useState(null),{trigger:m,onClose:d}=n,f=s.current,{onPointerInTransitChange:o}=a,g=l.useCallback(()=>{u(null),o(!1)},[o]),h=l.useCallback((b,M)=>{const x=b.currentTarget,N={x:b.clientX,y:b.clientY},T=hs(N,x.getBoundingClientRect()),P=vs(N,T),$=xs(M.getBoundingClientRect()),L=ys([...P,...$]);u(L),o(!0)},[o]);return l.useEffect(()=>()=>g(),[g]),l.useEffect(()=>{if(m&&f){const b=x=>h(x,f),M=x=>h(x,m);return m.addEventListener("pointerleave",b),f.addEventListener("pointerleave",M),()=>{m.removeEventListener("pointerleave",b),f.removeEventListener("pointerleave",M)}}},[m,f,h,g]),l.useEffect(()=>{if(c){const b=M=>{const x=M.target,N={x:M.clientX,y:M.clientY},T=(m==null?void 0:m.contains(x))||(f==null?void 0:f.contains(x)),P=!bs(N,c);T?g():P&&(g(),d())};return document.addEventListener("pointermove",b),()=>document.removeEventListener("pointermove",b)}},[m,f,c,d,g]),r.jsx(Ir,{...e,ref:i})}),[ms,ps]=pt(Ye,{isInside:!1}),gs=ao("TooltipContent"),Ir=l.forwardRef((e,t)=>{const{__scopeTooltip:n,children:a,"aria-label":s,onEscapeKeyDown:i,onPointerDownOutside:c,...u}=e,m=Xe(Oe,n),d=gt(n),{onClose:f}=m;return l.useEffect(()=>(document.addEventListener(Dt,f),()=>document.removeEventListener(Dt,f)),[f]),l.useEffect(()=>{if(m.trigger){const o=g=>{const h=g.target;h!=null&&h.contains(m.trigger)&&f()};return window.addEventListener("scroll",o,{capture:!0}),()=>window.removeEventListener("scroll",o,{capture:!0})}},[m.trigger,f]),r.jsx(Vt,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:c,onFocusOutside:o=>o.preventDefault(),onDismiss:f,children:r.jsxs(Wn,{"data-state":m.stateAttribute,...d,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[r.jsx(gs,{children:a}),r.jsx(ms,{scope:n,isInside:!0,children:r.jsx(so,{id:m.contentId,role:"tooltip",children:s||a})})]})})});Rr.displayName=Oe;var Tr="TooltipArrow",Dr=l.forwardRef((e,t)=>{const{__scopeTooltip:n,...a}=e,s=gt(n);return ps(Tr,n).isInside?null:r.jsx(Yn,{...s,...a,ref:t})});Dr.displayName=Tr;function hs(e,t){const n=Math.abs(t.top-e.y),a=Math.abs(t.bottom-e.y),s=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,a,s,i)){case i:return"left";case s:return"right";case n:return"top";case a:return"bottom";default:throw new Error("unreachable")}}function vs(e,t,n=5){const a=[];switch(t){case"top":a.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":a.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":a.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":a.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return a}function xs(e){const{top:t,right:n,bottom:a,left:s}=e;return[{x:s,y:t},{x:n,y:t},{x:n,y:a},{x:s,y:a}]}function bs(e,t){const{x:n,y:a}=e;let s=!1;for(let i=0,c=t.length-1;i<t.length;c=i++){const u=t[i],m=t[c],d=u.x,f=u.y,o=m.x,g=m.y;f>a!=g>a&&n<(o-d)*(a-f)/(g-f)+d&&(s=!s)}return s}function ys(e){const t=e.slice();return t.sort((n,a)=>n.x<a.x?-1:n.x>a.x?1:n.y<a.y?-1:n.y>a.y?1:0),ws(t)}function ws(e){if(e.length<=1)return e.slice();const t=[];for(let a=0;a<e.length;a++){const s=e[a];for(;t.length>=2;){const i=t[t.length-1],c=t[t.length-2];if((i.x-c.x)*(s.y-c.y)>=(i.y-c.y)*(s.x-c.x))t.pop();else break}t.push(s)}t.pop();const n=[];for(let a=e.length-1;a>=0;a--){const s=e[a];for(;n.length>=2;){const i=n[n.length-1],c=n[n.length-2];if((i.x-c.x)*(s.y-c.y)>=(i.y-c.y)*(s.x-c.x))n.pop();else break}n.push(s)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var Cs=jr,Es=_r,Ns=Mr,Ss=kr,js=Rr,_s=Dr;function Ar({delayDuration:e=0,...t}){return r.jsx(Cs,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function Ms({...e}){return r.jsx(Ar,{children:r.jsx(Es,{"data-slot":"tooltip",...e})})}function ks({...e}){return r.jsx(Ns,{"data-slot":"tooltip-trigger",...e})}function Rs({className:e,sideOffset:t=4,children:n,...a}){return r.jsx(Ss,{children:r.jsxs(js,{"data-slot":"tooltip-content",sideOffset:t,className:j("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-w-sm rounded-md px-3 py-1.5 text-xs",e),...a,children:[n,r.jsx(_s,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}const Is="sidebar_state",Ts=60*60*24*7,Ds="16rem",As="18rem",Ps="3rem",Os="b",Pr=l.createContext(null);function an(){const e=l.useContext(Pr);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}function Ls({defaultOpen:e=!0,open:t,onOpenChange:n,className:a,style:s,children:i,...c}){const u=Ko(),[m,d]=l.useState(!1),[f,o]=l.useState(e),g=t??f,h=l.useCallback(N=>{const T=typeof N=="function"?N(g):N;n?n(T):o(T),document.cookie=`${Is}=${T}; path=/; max-age=${Ts}`},[n,g]),b=l.useCallback(()=>u?d(N=>!N):h(N=>!N),[u,h,d]);l.useEffect(()=>{const N=T=>{T.key===Os&&(T.metaKey||T.ctrlKey)&&(T.preventDefault(),b())};return window.addEventListener("keydown",N),()=>window.removeEventListener("keydown",N)},[b]);const M=g?"expanded":"collapsed",x=l.useMemo(()=>({state:M,open:g,setOpen:h,isMobile:u,openMobile:m,setOpenMobile:d,toggleSidebar:b}),[M,g,h,u,m,d,b]);return r.jsx(Pr.Provider,{value:x,children:r.jsx(Ar,{delayDuration:0,children:r.jsx("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":Ds,"--sidebar-width-icon":Ps,...s},className:j("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",a),...c,children:i})})})}function Cd({side:e="left",variant:t="sidebar",collapsible:n="offcanvas",className:a,children:s,...i}){const{isMobile:c,state:u,openMobile:m,setOpenMobile:d}=an();return n==="none"?r.jsx("div",{"data-slot":"sidebar",className:j("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",a),...i,children:s}):c?r.jsxs(es,{open:m,onOpenChange:d,...i,children:[r.jsxs(as,{className:"sr-only",children:[r.jsx(os,{children:"Sidebar"}),r.jsx(ss,{children:"Displays the mobile sidebar."})]}),r.jsx(rs,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":As},side:e,children:r.jsx("div",{className:"flex h-full w-full flex-col",children:s})})]}):r.jsxs("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":u,"data-collapsible":u==="collapsed"?n:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[r.jsx("div",{className:j("relative h-svh w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),r.jsx("div",{className:j("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",a),...i,children:r.jsx("div",{"data-sidebar":"sidebar",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:s})})]})}function Ed({className:e,onClick:t,...n}){const{toggleSidebar:a,state:s}=an();return r.jsxs(zt,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:j("h-9 w-9 rounded-lg transition-all duration-200 hover:bg-accent/80 hover:scale-105 active:scale-95","border border-transparent hover:border-border/50","shadow-sm hover:shadow-md",e),onClick:i=>{t==null||t(i),a()},...n,children:[r.jsx("div",{className:"relative",children:r.jsx(Lo,{className:j("h-5 w-5 transition-all duration-200",s==="collapsed"?"rotate-0":"rotate-90")})}),r.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function Fs({className:e,...t}){return r.jsx("main",{"data-slot":"sidebar-inset",className:j("bg-background relative flex max-w-full min-h-svh flex-1 flex-col","peer-data-[variant=inset]:min-h-[calc(100svh-(--spacing(4)))] md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-0",e),...t})}function Nd({className:e,...t}){return r.jsx("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:j("flex flex-col gap-2 p-2",e),...t})}function Sd({className:e,...t}){return r.jsx("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:j("flex flex-col gap-2 p-2",e),...t})}function jd({className:e,...t}){return r.jsx("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:j("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function _d({className:e,...t}){return r.jsx("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:j("relative flex w-full min-w-0 flex-col p-2",e),...t})}function Md({className:e,...t}){return r.jsx("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:j("flex w-full min-w-0 flex-col gap-1",e),...t})}function kd({className:e,...t}){return r.jsx("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:j("group/menu-item relative",e),...t})}const $s=Bn("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function Rd({asChild:e=!1,isActive:t=!1,variant:n="default",size:a="default",tooltip:s,className:i,...c}){const u=e?$n:"button",{isMobile:m,state:d}=an(),f=r.jsx(u,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":a,"data-active":t,className:j($s({variant:n,size:a}),i),...c});return s?(typeof s=="string"&&(s={children:s}),r.jsxs(Ms,{children:[r.jsx(ks,{asChild:!0,children:f}),r.jsx(Rs,{side:"right",align:"center",hidden:d!=="collapsed"||m,...s})]})):f}function Id({variant:e="header",children:t,...n}){return e==="sidebar"?r.jsx(Fs,{...n,children:t}):r.jsx("main",{className:"mx-auto flex h-full w-full max-w-7xl flex-1 flex-col gap-4 rounded-xl",...n,children:t})}function Bs(e){if(typeof document>"u")return;let t=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css",t.appendChild(n),n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e))}const Hs=e=>{switch(e){case"success":return Ks;case"info":return Us;case"warning":return Vs;case"error":return Ys;default:return null}},Gs=Array(12).fill(0),zs=({visible:e,className:t})=>v.createElement("div",{className:["sonner-loading-wrapper",t].filter(Boolean).join(" "),"data-visible":e},v.createElement("div",{className:"sonner-spinner"},Gs.map((n,a)=>v.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${a}`})))),Ks=v.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},v.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),Vs=v.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},v.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),Us=v.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},v.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),Ys=v.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},v.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),Ws=v.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},v.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),v.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),qs=()=>{const[e,t]=v.useState(document.hidden);return v.useEffect(()=>{const n=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",n),()=>window.removeEventListener("visibilitychange",n)},[]),e};let Pt=1;class Xs{constructor(){this.subscribe=t=>(this.subscribers.push(t),()=>{const n=this.subscribers.indexOf(t);this.subscribers.splice(n,1)}),this.publish=t=>{this.subscribers.forEach(n=>n(t))},this.addToast=t=>{this.publish(t),this.toasts=[...this.toasts,t]},this.create=t=>{var n;const{message:a,...s}=t,i=typeof(t==null?void 0:t.id)=="number"||((n=t.id)==null?void 0:n.length)>0?t.id:Pt++,c=this.toasts.find(m=>m.id===i),u=t.dismissible===void 0?!0:t.dismissible;return this.dismissedToasts.has(i)&&this.dismissedToasts.delete(i),c?this.toasts=this.toasts.map(m=>m.id===i?(this.publish({...m,...t,id:i,title:a}),{...m,...t,id:i,dismissible:u,title:a}):m):this.addToast({title:a,...s,dismissible:u,id:i}),i},this.dismiss=t=>(t?(this.dismissedToasts.add(t),requestAnimationFrame(()=>this.subscribers.forEach(n=>n({id:t,dismiss:!0})))):this.toasts.forEach(n=>{this.subscribers.forEach(a=>a({id:n.id,dismiss:!0}))}),t),this.message=(t,n)=>this.create({...n,message:t}),this.error=(t,n)=>this.create({...n,message:t,type:"error"}),this.success=(t,n)=>this.create({...n,type:"success",message:t}),this.info=(t,n)=>this.create({...n,type:"info",message:t}),this.warning=(t,n)=>this.create({...n,type:"warning",message:t}),this.loading=(t,n)=>this.create({...n,type:"loading",message:t}),this.promise=(t,n)=>{if(!n)return;let a;n.loading!==void 0&&(a=this.create({...n,promise:t,type:"loading",message:n.loading,description:typeof n.description!="function"?n.description:void 0}));const s=Promise.resolve(t instanceof Function?t():t);let i=a!==void 0,c;const u=s.then(async d=>{if(c=["resolve",d],v.isValidElement(d))i=!1,this.create({id:a,type:"default",message:d});else if(Js(d)&&!d.ok){i=!1;const o=typeof n.error=="function"?await n.error(`HTTP error! status: ${d.status}`):n.error,g=typeof n.description=="function"?await n.description(`HTTP error! status: ${d.status}`):n.description,b=typeof o=="object"&&!v.isValidElement(o)?o:{message:o};this.create({id:a,type:"error",description:g,...b})}else if(d instanceof Error){i=!1;const o=typeof n.error=="function"?await n.error(d):n.error,g=typeof n.description=="function"?await n.description(d):n.description,b=typeof o=="object"&&!v.isValidElement(o)?o:{message:o};this.create({id:a,type:"error",description:g,...b})}else if(n.success!==void 0){i=!1;const o=typeof n.success=="function"?await n.success(d):n.success,g=typeof n.description=="function"?await n.description(d):n.description,b=typeof o=="object"&&!v.isValidElement(o)?o:{message:o};this.create({id:a,type:"success",description:g,...b})}}).catch(async d=>{if(c=["reject",d],n.error!==void 0){i=!1;const f=typeof n.error=="function"?await n.error(d):n.error,o=typeof n.description=="function"?await n.description(d):n.description,h=typeof f=="object"&&!v.isValidElement(f)?f:{message:f};this.create({id:a,type:"error",description:o,...h})}}).finally(()=>{i&&(this.dismiss(a),a=void 0),n.finally==null||n.finally.call(n)}),m=()=>new Promise((d,f)=>u.then(()=>c[0]==="reject"?f(c[1]):d(c[1])).catch(f));return typeof a!="string"&&typeof a!="number"?{unwrap:m}:Object.assign(a,{unwrap:m})},this.custom=(t,n)=>{const a=(n==null?void 0:n.id)||Pt++;return this.create({jsx:t(a),id:a,...n}),a},this.getActiveToasts=()=>this.toasts.filter(t=>!this.dismissedToasts.has(t.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}}const ne=new Xs,Zs=(e,t)=>{const n=(t==null?void 0:t.id)||Pt++;return ne.addToast({title:e,...t,id:n}),n},Js=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",Qs=Zs,ei=()=>ne.toasts,ti=()=>ne.getActiveToasts(),Be=Object.assign(Qs,{success:ne.success,info:ne.info,warning:ne.warning,error:ne.error,custom:ne.custom,message:ne.message,promise:ne.promise,dismiss:ne.dismiss,loading:ne.loading},{getHistory:ei,getToasts:ti});Bs("[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}");function at(e){return e.label!==void 0}const ni=3,ri="24px",ai="16px",jn=4e3,oi=356,si=14,ii=45,li=200;function he(...e){return e.filter(Boolean).join(" ")}function ci(e){const[t,n]=e.split("-"),a=[];return t&&a.push(t),n&&a.push(n),a}const di=e=>{var t,n,a,s,i,c,u,m,d;const{invert:f,toast:o,unstyled:g,interacting:h,setHeights:b,visibleToasts:M,heights:x,index:N,toasts:T,expanded:P,removeToast:$,defaultRichColors:L,closeButton:A,style:F,cancelButtonStyle:H,actionButtonStyle:ae,className:Q="",descriptionClassName:V="",duration:Z,position:oe,gap:ee,expandByDefault:K,classNames:_,icons:q,closeButtonAriaLabel:U="Close toast"}=e,[E,z]=v.useState(null),[I,p]=v.useState(null),[y,S]=v.useState(!1),[w,C]=v.useState(!1),[D,G]=v.useState(!1),[O,se]=v.useState(!1),[Ga,gn]=v.useState(!1),[za,yt]=v.useState(0),[Ka,hn]=v.useState(0),Fe=v.useRef(o.duration||Z||jn),vn=v.useRef(null),ve=v.useRef(null),Va=N===0,Ua=N+1<=M,ie=o.type,ke=o.dismissible!==!1,Ya=o.className||"",Wa=o.descriptionClassName||"",tt=v.useMemo(()=>x.findIndex(R=>R.toastId===o.id)||0,[x,o.id]),qa=v.useMemo(()=>{var R;return(R=o.closeButton)!=null?R:A},[o.closeButton,A]),xn=v.useMemo(()=>o.duration||Z||jn,[o.duration,Z]),wt=v.useRef(0),Re=v.useRef(0),bn=v.useRef(0),Ie=v.useRef(null),[Xa,Za]=oe.split("-"),yn=v.useMemo(()=>x.reduce((R,W,J)=>J>=tt?R:R+W.height,0),[x,tt]),wn=qs(),Ja=o.invert||f,Ct=ie==="loading";Re.current=v.useMemo(()=>tt*ee+yn,[tt,yn]),v.useEffect(()=>{Fe.current=xn},[xn]),v.useEffect(()=>{S(!0)},[]),v.useEffect(()=>{const R=ve.current;if(R){const W=R.getBoundingClientRect().height;return hn(W),b(J=>[{toastId:o.id,height:W,position:o.position},...J]),()=>b(J=>J.filter(le=>le.toastId!==o.id))}},[b,o.id]),v.useLayoutEffect(()=>{if(!y)return;const R=ve.current,W=R.style.height;R.style.height="auto";const J=R.getBoundingClientRect().height;R.style.height=W,hn(J),b(le=>le.find(X=>X.toastId===o.id)?le.map(X=>X.toastId===o.id?{...X,height:J}:X):[{toastId:o.id,height:J,position:o.position},...le])},[y,o.title,o.description,b,o.id,o.jsx,o.action,o.cancel]);const be=v.useCallback(()=>{C(!0),yt(Re.current),b(R=>R.filter(W=>W.toastId!==o.id)),setTimeout(()=>{$(o)},li)},[o,$,b,Re]);v.useEffect(()=>{if(o.promise&&ie==="loading"||o.duration===1/0||o.type==="loading")return;let R;return P||h||wn?(()=>{if(bn.current<wt.current){const le=new Date().getTime()-wt.current;Fe.current=Fe.current-le}bn.current=new Date().getTime()})():(()=>{Fe.current!==1/0&&(wt.current=new Date().getTime(),R=setTimeout(()=>{o.onAutoClose==null||o.onAutoClose.call(o,o),be()},Fe.current))})(),()=>clearTimeout(R)},[P,h,o,ie,wn,be]),v.useEffect(()=>{o.delete&&(be(),o.onDismiss==null||o.onDismiss.call(o,o))},[be,o.delete]);function Qa(){var R;if(q!=null&&q.loading){var W;return v.createElement("div",{className:he(_==null?void 0:_.loader,o==null||(W=o.classNames)==null?void 0:W.loader,"sonner-loader"),"data-visible":ie==="loading"},q.loading)}return v.createElement(zs,{className:he(_==null?void 0:_.loader,o==null||(R=o.classNames)==null?void 0:R.loader),visible:ie==="loading"})}const eo=o.icon||(q==null?void 0:q[ie])||Hs(ie);var Cn,En;return v.createElement("li",{tabIndex:0,ref:ve,className:he(Q,Ya,_==null?void 0:_.toast,o==null||(t=o.classNames)==null?void 0:t.toast,_==null?void 0:_.default,_==null?void 0:_[ie],o==null||(n=o.classNames)==null?void 0:n[ie]),"data-sonner-toast":"","data-rich-colors":(Cn=o.richColors)!=null?Cn:L,"data-styled":!(o.jsx||o.unstyled||g),"data-mounted":y,"data-promise":!!o.promise,"data-swiped":Ga,"data-removed":w,"data-visible":Ua,"data-y-position":Xa,"data-x-position":Za,"data-index":N,"data-front":Va,"data-swiping":D,"data-dismissible":ke,"data-type":ie,"data-invert":Ja,"data-swipe-out":O,"data-swipe-direction":I,"data-expanded":!!(P||K&&y),style:{"--index":N,"--toasts-before":N,"--z-index":T.length-N,"--offset":`${w?za:Re.current}px`,"--initial-height":K?"auto":`${Ka}px`,...F,...o.style},onDragEnd:()=>{G(!1),z(null),Ie.current=null},onPointerDown:R=>{R.button!==2&&(Ct||!ke||(vn.current=new Date,yt(Re.current),R.target.setPointerCapture(R.pointerId),R.target.tagName!=="BUTTON"&&(G(!0),Ie.current={x:R.clientX,y:R.clientY})))},onPointerUp:()=>{var R,W,J;if(O||!ke)return;Ie.current=null;const le=Number(((R=ve.current)==null?void 0:R.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),nt=Number(((W=ve.current)==null?void 0:W.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),X=new Date().getTime()-((J=vn.current)==null?void 0:J.getTime()),de=E==="x"?le:nt,rt=Math.abs(de)/X;if(Math.abs(de)>=ii||rt>.11){yt(Re.current),o.onDismiss==null||o.onDismiss.call(o,o),p(E==="x"?le>0?"right":"left":nt>0?"down":"up"),be(),se(!0);return}else{var fe,me;(fe=ve.current)==null||fe.style.setProperty("--swipe-amount-x","0px"),(me=ve.current)==null||me.style.setProperty("--swipe-amount-y","0px")}gn(!1),G(!1),z(null)},onPointerMove:R=>{var W,J,le;if(!Ie.current||!ke||((W=window.getSelection())==null?void 0:W.toString().length)>0)return;const X=R.clientY-Ie.current.y,de=R.clientX-Ie.current.x;var rt;const fe=(rt=e.swipeDirections)!=null?rt:ci(oe);!E&&(Math.abs(de)>1||Math.abs(X)>1)&&z(Math.abs(de)>Math.abs(X)?"x":"y");let me={x:0,y:0};const Nn=Ce=>1/(1.5+Math.abs(Ce)/20);if(E==="y"){if(fe.includes("top")||fe.includes("bottom"))if(fe.includes("top")&&X<0||fe.includes("bottom")&&X>0)me.y=X;else{const Ce=X*Nn(X);me.y=Math.abs(Ce)<Math.abs(X)?Ce:X}}else if(E==="x"&&(fe.includes("left")||fe.includes("right")))if(fe.includes("left")&&de<0||fe.includes("right")&&de>0)me.x=de;else{const Ce=de*Nn(de);me.x=Math.abs(Ce)<Math.abs(de)?Ce:de}(Math.abs(me.x)>0||Math.abs(me.y)>0)&&gn(!0),(J=ve.current)==null||J.style.setProperty("--swipe-amount-x",`${me.x}px`),(le=ve.current)==null||le.style.setProperty("--swipe-amount-y",`${me.y}px`)}},qa&&!o.jsx&&ie!=="loading"?v.createElement("button",{"aria-label":U,"data-disabled":Ct,"data-close-button":!0,onClick:Ct||!ke?()=>{}:()=>{be(),o.onDismiss==null||o.onDismiss.call(o,o)},className:he(_==null?void 0:_.closeButton,o==null||(a=o.classNames)==null?void 0:a.closeButton)},(En=q==null?void 0:q.close)!=null?En:Ws):null,(ie||o.icon||o.promise)&&o.icon!==null&&((q==null?void 0:q[ie])!==null||o.icon)?v.createElement("div",{"data-icon":"",className:he(_==null?void 0:_.icon,o==null||(s=o.classNames)==null?void 0:s.icon)},o.promise||o.type==="loading"&&!o.icon?o.icon||Qa():null,o.type!=="loading"?eo:null):null,v.createElement("div",{"data-content":"",className:he(_==null?void 0:_.content,o==null||(i=o.classNames)==null?void 0:i.content)},v.createElement("div",{"data-title":"",className:he(_==null?void 0:_.title,o==null||(c=o.classNames)==null?void 0:c.title)},o.jsx?o.jsx:typeof o.title=="function"?o.title():o.title),o.description?v.createElement("div",{"data-description":"",className:he(V,Wa,_==null?void 0:_.description,o==null||(u=o.classNames)==null?void 0:u.description)},typeof o.description=="function"?o.description():o.description):null),v.isValidElement(o.cancel)?o.cancel:o.cancel&&at(o.cancel)?v.createElement("button",{"data-button":!0,"data-cancel":!0,style:o.cancelButtonStyle||H,onClick:R=>{at(o.cancel)&&ke&&(o.cancel.onClick==null||o.cancel.onClick.call(o.cancel,R),be())},className:he(_==null?void 0:_.cancelButton,o==null||(m=o.classNames)==null?void 0:m.cancelButton)},o.cancel.label):null,v.isValidElement(o.action)?o.action:o.action&&at(o.action)?v.createElement("button",{"data-button":!0,"data-action":!0,style:o.actionButtonStyle||ae,onClick:R=>{at(o.action)&&(o.action.onClick==null||o.action.onClick.call(o.action,R),!R.defaultPrevented&&be())},className:he(_==null?void 0:_.actionButton,o==null||(d=o.classNames)==null?void 0:d.actionButton)},o.action.label):null)};function _n(){if(typeof window>"u"||typeof document>"u")return"ltr";const e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}function ui(e,t){const n={};return[e,t].forEach((a,s)=>{const i=s===1,c=i?"--mobile-offset":"--offset",u=i?ai:ri;function m(d){["top","right","bottom","left"].forEach(f=>{n[`${c}-${f}`]=typeof d=="number"?`${d}px`:d})}typeof a=="number"||typeof a=="string"?m(a):typeof a=="object"?["top","right","bottom","left"].forEach(d=>{a[d]===void 0?n[`${c}-${d}`]=u:n[`${c}-${d}`]=typeof a[d]=="number"?`${a[d]}px`:a[d]}):m(u)}),n}const fi=v.forwardRef(function(t,n){const{invert:a,position:s="bottom-right",hotkey:i=["altKey","KeyT"],expand:c,closeButton:u,className:m,offset:d,mobileOffset:f,theme:o="light",richColors:g,duration:h,style:b,visibleToasts:M=ni,toastOptions:x,dir:N=_n(),gap:T=si,icons:P,containerAriaLabel:$="Notifications"}=t,[L,A]=v.useState([]),F=v.useMemo(()=>Array.from(new Set([s].concat(L.filter(I=>I.position).map(I=>I.position)))),[L,s]),[H,ae]=v.useState([]),[Q,V]=v.useState(!1),[Z,oe]=v.useState(!1),[ee,K]=v.useState(o!=="system"?o:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),_=v.useRef(null),q=i.join("+").replace(/Key/g,"").replace(/Digit/g,""),U=v.useRef(null),E=v.useRef(!1),z=v.useCallback(I=>{A(p=>{var y;return(y=p.find(S=>S.id===I.id))!=null&&y.delete||ne.dismiss(I.id),p.filter(({id:S})=>S!==I.id)})},[]);return v.useEffect(()=>ne.subscribe(I=>{if(I.dismiss){requestAnimationFrame(()=>{A(p=>p.map(y=>y.id===I.id?{...y,delete:!0}:y))});return}setTimeout(()=>{co.flushSync(()=>{A(p=>{const y=p.findIndex(S=>S.id===I.id);return y!==-1?[...p.slice(0,y),{...p[y],...I},...p.slice(y+1)]:[I,...p]})})})}),[L]),v.useEffect(()=>{if(o!=="system"){K(o);return}if(o==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?K("dark"):K("light")),typeof window>"u")return;const I=window.matchMedia("(prefers-color-scheme: dark)");try{I.addEventListener("change",({matches:p})=>{K(p?"dark":"light")})}catch{I.addListener(({matches:y})=>{try{K(y?"dark":"light")}catch(S){console.error(S)}})}},[o]),v.useEffect(()=>{L.length<=1&&V(!1)},[L]),v.useEffect(()=>{const I=p=>{var y;if(i.every(C=>p[C]||p.code===C)){var w;V(!0),(w=_.current)==null||w.focus()}p.code==="Escape"&&(document.activeElement===_.current||(y=_.current)!=null&&y.contains(document.activeElement))&&V(!1)};return document.addEventListener("keydown",I),()=>document.removeEventListener("keydown",I)},[i]),v.useEffect(()=>{if(_.current)return()=>{U.current&&(U.current.focus({preventScroll:!0}),U.current=null,E.current=!1)}},[_.current]),v.createElement("section",{ref:n,"aria-label":`${$} ${q}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},F.map((I,p)=>{var y;const[S,w]=I.split("-");return L.length?v.createElement("ol",{key:I,dir:N==="auto"?_n():N,tabIndex:-1,ref:_,className:m,"data-sonner-toaster":!0,"data-sonner-theme":ee,"data-y-position":S,"data-x-position":w,style:{"--front-toast-height":`${((y=H[0])==null?void 0:y.height)||0}px`,"--width":`${oi}px`,"--gap":`${T}px`,...b,...ui(d,f)},onBlur:C=>{E.current&&!C.currentTarget.contains(C.relatedTarget)&&(E.current=!1,U.current&&(U.current.focus({preventScroll:!0}),U.current=null))},onFocus:C=>{C.target instanceof HTMLElement&&C.target.dataset.dismissible==="false"||E.current||(E.current=!0,U.current=C.relatedTarget)},onMouseEnter:()=>V(!0),onMouseMove:()=>V(!0),onMouseLeave:()=>{Z||V(!1)},onDragEnd:()=>V(!1),onPointerDown:C=>{C.target instanceof HTMLElement&&C.target.dataset.dismissible==="false"||oe(!0)},onPointerUp:()=>oe(!1)},L.filter(C=>!C.position&&p===0||C.position===I).map((C,D)=>{var G,O;return v.createElement(di,{key:C.id,icons:P,index:D,toast:C,defaultRichColors:g,duration:(G=x==null?void 0:x.duration)!=null?G:h,className:x==null?void 0:x.className,descriptionClassName:x==null?void 0:x.descriptionClassName,invert:a,visibleToasts:M,closeButton:(O=x==null?void 0:x.closeButton)!=null?O:u,interacting:Z,position:I,style:x==null?void 0:x.style,unstyled:x==null?void 0:x.unstyled,classNames:x==null?void 0:x.classNames,cancelButtonStyle:x==null?void 0:x.cancelButtonStyle,actionButtonStyle:x==null?void 0:x.actionButtonStyle,closeButtonAriaLabel:x==null?void 0:x.closeButtonAriaLabel,removeToast:z,toasts:L.filter(se=>se.position==C.position),heights:H.filter(se=>se.position==C.position),setHeights:ae,expandByDefault:c,gap:T,expanded:Q,swipeDirections:t.swipeDirections})})):null}))}),Mn=({...e})=>{const[t,n]=l.useState("system");return l.useEffect(()=>{const a=document.documentElement.classList.contains("dark");n(a?"dark":"light");const s=new MutationObserver(i=>{i.forEach(c=>{if(c.type==="attributes"&&c.attributeName==="class"){const u=document.documentElement.classList.contains("dark");n(u?"dark":"light")}})});return s.observe(document.documentElement,{attributes:!0,attributeFilter:["class"]}),()=>s.disconnect()},[]),r.jsx(fi,{theme:t,className:"toaster group",position:"top-center",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})},Or=l.createContext(null);function mi({children:e,storageKey:t="sidebar_accordion_state",defaultExpandedGroupId:n=null}){const[a,s]=l.useState(()=>{if(t)try{const u=localStorage.getItem(t);return u?JSON.parse(u):n}catch{return n}return n});l.useEffect(()=>{if(t)try{localStorage.setItem(t,JSON.stringify(a))}catch{}},[a,t]);const c={expandedGroupId:a,setExpandedGroupId:s,expandGroup:u=>{a!==u&&s(u)}};return r.jsx(Or.Provider,{value:c,children:e})}function Td(){const e=l.useContext(Or);if(!e)throw new Error("useAccordion must be used within an AccordionProvider");return e}function kn(){const{flash:e}=ct().props;return l.useEffect(()=>{e&&(e.success&&Be.success(e.success),e.error&&Be.error(e.error),e.warning&&Be.warning(e.warning),e.info&&Be.info(e.info),e.message&&Be.info(e.message))},[e]),null}function pi(){const{auth:e}=ct().props;try{return e!=null&&e.user?"isAdmin"in e.user&&typeof e.user.isAdmin=="boolean"?!!e.user.isAdmin:["<EMAIL>","<EMAIL>","<EMAIL>"].includes(e.user.email||""):!1}catch(t){return console.error("Error in admin detection:",t),!1}}function Dd({children:e,variant:t="header"}){const{sidebarOpen:n}=ct().props,s=pi()?"admin-core":"user-platform";return t==="header"?r.jsxs("div",{className:"flex min-h-screen w-full flex-col",children:[e,r.jsx(kn,{}),r.jsx(Mn,{})]}):r.jsx(Ls,{defaultOpen:n,children:r.jsxs(mi,{defaultExpandedGroupId:s,children:[e,r.jsx(kn,{}),r.jsx(Mn,{})]})})}var Nt="rovingFocusGroup.onEntryFocus",gi={bubbles:!1,cancelable:!0},Ze="RovingFocusGroup",[Ot,Lr,hi]=qn(Ze),[vi,Fr]=Le(Ze,[hi]),[xi,bi]=vi(Ze),$r=l.forwardRef((e,t)=>r.jsx(Ot.Provider,{scope:e.__scopeRovingFocusGroup,children:r.jsx(Ot.Slot,{scope:e.__scopeRovingFocusGroup,children:r.jsx(yi,{...e,ref:t})})}));$r.displayName=Ze;var yi=l.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:a,loop:s=!1,dir:i,currentTabStopId:c,defaultCurrentTabStopId:u,onCurrentTabStopIdChange:m,onEntryFocus:d,preventScrollOnEntryFocus:f=!1,...o}=e,g=l.useRef(null),h=pe(t,g),b=Xn(i),[M,x]=dt({prop:c,defaultProp:u??null,onChange:m,caller:Ze}),[N,T]=l.useState(!1),P=ft(d),$=Lr(n),L=l.useRef(!1),[A,F]=l.useState(0);return l.useEffect(()=>{const H=g.current;if(H)return H.addEventListener(Nt,P),()=>H.removeEventListener(Nt,P)},[P]),r.jsx(xi,{scope:n,orientation:a,dir:b,loop:s,currentTabStopId:M,onItemFocus:l.useCallback(H=>x(H),[x]),onItemShiftTab:l.useCallback(()=>T(!0),[]),onFocusableItemAdd:l.useCallback(()=>F(H=>H+1),[]),onFocusableItemRemove:l.useCallback(()=>F(H=>H-1),[]),children:r.jsx(B.div,{tabIndex:N||A===0?-1:0,"data-orientation":a,...o,ref:h,style:{outline:"none",...e.style},onMouseDown:k(e.onMouseDown,()=>{L.current=!0}),onFocus:k(e.onFocus,H=>{const ae=!L.current;if(H.target===H.currentTarget&&ae&&!N){const Q=new CustomEvent(Nt,gi);if(H.currentTarget.dispatchEvent(Q),!Q.defaultPrevented){const V=$().filter(_=>_.focusable),Z=V.find(_=>_.active),oe=V.find(_=>_.id===M),K=[Z,oe,...V].filter(Boolean).map(_=>_.ref.current);Gr(K,f)}}L.current=!1}),onBlur:k(e.onBlur,()=>T(!1))})})}),Br="RovingFocusGroupItem",Hr=l.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:a=!0,active:s=!1,tabStopId:i,children:c,...u}=e,m=ce(),d=i||m,f=bi(Br,n),o=f.currentTabStopId===d,g=Lr(n),{onFocusableItemAdd:h,onFocusableItemRemove:b,currentTabStopId:M}=f;return l.useEffect(()=>{if(a)return h(),()=>b()},[a,h,b]),r.jsx(Ot.ItemSlot,{scope:n,id:d,focusable:a,active:s,children:r.jsx(B.span,{tabIndex:o?0:-1,"data-orientation":f.orientation,...u,ref:t,onMouseDown:k(e.onMouseDown,x=>{a?f.onItemFocus(d):x.preventDefault()}),onFocus:k(e.onFocus,()=>f.onItemFocus(d)),onKeyDown:k(e.onKeyDown,x=>{if(x.key==="Tab"&&x.shiftKey){f.onItemShiftTab();return}if(x.target!==x.currentTarget)return;const N=Ei(x,f.orientation,f.dir);if(N!==void 0){if(x.metaKey||x.ctrlKey||x.altKey||x.shiftKey)return;x.preventDefault();let P=g().filter($=>$.focusable).map($=>$.ref.current);if(N==="last")P.reverse();else if(N==="prev"||N==="next"){N==="prev"&&P.reverse();const $=P.indexOf(x.currentTarget);P=f.loop?Ni(P,$+1):P.slice($+1)}setTimeout(()=>Gr(P))}}),children:typeof c=="function"?c({isCurrentTabStop:o,hasTabStop:M!=null}):c})})});Hr.displayName=Br;var wi={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Ci(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function Ei(e,t,n){const a=Ci(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(a))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(a)))return wi[a]}function Gr(e,t=!1){const n=document.activeElement;for(const a of e)if(a===n||(a.focus({preventScroll:t}),document.activeElement!==n))return}function Ni(e,t){return e.map((n,a)=>e[(t+a)%e.length])}var Si=$r,ji=Hr,Lt=["Enter"," "],_i=["ArrowDown","PageUp","Home"],zr=["ArrowUp","PageDown","End"],Mi=[..._i,...zr],ki={ltr:[...Lt,"ArrowRight"],rtl:[...Lt,"ArrowLeft"]},Ri={ltr:["ArrowLeft"],rtl:["ArrowRight"]},Je="Menu",[We,Ii,Ti]=qn(Je),[_e,Kr]=Le(Je,[Ti,ut,Fr]),ht=ut(),Vr=Fr(),[Di,Me]=_e(Je),[Ai,Qe]=_e(Je),Ur=e=>{const{__scopeMenu:t,open:n=!1,children:a,dir:s,onOpenChange:i,modal:c=!0}=e,u=ht(t),[m,d]=l.useState(null),f=l.useRef(!1),o=ft(i),g=Xn(s);return l.useEffect(()=>{const h=()=>{f.current=!0,document.addEventListener("pointerdown",b,{capture:!0,once:!0}),document.addEventListener("pointermove",b,{capture:!0,once:!0})},b=()=>f.current=!1;return document.addEventListener("keydown",h,{capture:!0}),()=>{document.removeEventListener("keydown",h,{capture:!0}),document.removeEventListener("pointerdown",b,{capture:!0}),document.removeEventListener("pointermove",b,{capture:!0})}},[]),r.jsx(Vn,{...u,children:r.jsx(Di,{scope:t,open:n,onOpenChange:o,content:m,onContentChange:d,children:r.jsx(Ai,{scope:t,onClose:l.useCallback(()=>o(!1),[o]),isUsingKeyboardRef:f,dir:g,modal:c,children:a})})})};Ur.displayName=Je;var Pi="MenuAnchor",on=l.forwardRef((e,t)=>{const{__scopeMenu:n,...a}=e,s=ht(n);return r.jsx(Un,{...s,...a,ref:t})});on.displayName=Pi;var sn="MenuPortal",[Oi,Yr]=_e(sn,{forceMount:void 0}),Wr=e=>{const{__scopeMenu:t,forceMount:n,children:a,container:s}=e,i=Me(sn,t);return r.jsx(Oi,{scope:t,forceMount:n,children:r.jsx(xe,{present:n||i.open,children:r.jsx(Kt,{asChild:!0,container:s,children:a})})})};Wr.displayName=sn;var ue="MenuContent",[Li,ln]=_e(ue),qr=l.forwardRef((e,t)=>{const n=Yr(ue,e.__scopeMenu),{forceMount:a=n.forceMount,...s}=e,i=Me(ue,e.__scopeMenu),c=Qe(ue,e.__scopeMenu);return r.jsx(We.Provider,{scope:e.__scopeMenu,children:r.jsx(xe,{present:a||i.open,children:r.jsx(We.Slot,{scope:e.__scopeMenu,children:c.modal?r.jsx(Fi,{...s,ref:t}):r.jsx($i,{...s,ref:t})})})})}),Fi=l.forwardRef((e,t)=>{const n=Me(ue,e.__scopeMenu),a=l.useRef(null),s=pe(t,a);return l.useEffect(()=>{const i=a.current;if(i)return Hn(i)},[]),r.jsx(cn,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:k(e.onFocusOutside,i=>i.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),$i=l.forwardRef((e,t)=>{const n=Me(ue,e.__scopeMenu);return r.jsx(cn,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Bi=Fn("MenuContent.ScrollLock"),cn=l.forwardRef((e,t)=>{const{__scopeMenu:n,loop:a=!1,trapFocus:s,onOpenAutoFocus:i,onCloseAutoFocus:c,disableOutsidePointerEvents:u,onEntryFocus:m,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:o,onInteractOutside:g,onDismiss:h,disableOutsideScroll:b,...M}=e,x=Me(ue,n),N=Qe(ue,n),T=ht(n),P=Vr(n),$=Ii(n),[L,A]=l.useState(null),F=l.useRef(null),H=pe(t,F,x.onContentChange),ae=l.useRef(0),Q=l.useRef(""),V=l.useRef(0),Z=l.useRef(null),oe=l.useRef("right"),ee=l.useRef(0),K=b?Gn:l.Fragment,_=b?{as:Bi,allowPinchZoom:!0}:void 0,q=E=>{var D,G;const z=Q.current+E,I=$().filter(O=>!O.disabled),p=document.activeElement,y=(D=I.find(O=>O.ref.current===p))==null?void 0:D.textValue,S=I.map(O=>O.textValue),w=Ji(S,z,y),C=(G=I.find(O=>O.textValue===w))==null?void 0:G.ref.current;(function O(se){Q.current=se,window.clearTimeout(ae.current),se!==""&&(ae.current=window.setTimeout(()=>O(""),1e3))})(z),C&&setTimeout(()=>C.focus())};l.useEffect(()=>()=>window.clearTimeout(ae.current),[]),zn();const U=l.useCallback(E=>{var I,p;return oe.current===((I=Z.current)==null?void 0:I.side)&&el(E,(p=Z.current)==null?void 0:p.area)},[]);return r.jsx(Li,{scope:n,searchRef:Q,onItemEnter:l.useCallback(E=>{U(E)&&E.preventDefault()},[U]),onItemLeave:l.useCallback(E=>{var z;U(E)||((z=F.current)==null||z.focus(),A(null))},[U]),onTriggerLeave:l.useCallback(E=>{U(E)&&E.preventDefault()},[U]),pointerGraceTimerRef:V,onPointerGraceIntentChange:l.useCallback(E=>{Z.current=E},[]),children:r.jsx(K,{..._,children:r.jsx(Kn,{asChild:!0,trapped:s,onMountAutoFocus:k(i,E=>{var z;E.preventDefault(),(z=F.current)==null||z.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:r.jsx(Vt,{asChild:!0,disableOutsidePointerEvents:u,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:o,onInteractOutside:g,onDismiss:h,children:r.jsx(Si,{asChild:!0,...P,dir:N.dir,orientation:"vertical",loop:a,currentTabStopId:L,onCurrentTabStopIdChange:A,onEntryFocus:k(m,E=>{N.isUsingKeyboardRef.current||E.preventDefault()}),preventScrollOnEntryFocus:!0,children:r.jsx(Wn,{role:"menu","aria-orientation":"vertical","data-state":ua(x.open),"data-radix-menu-content":"",dir:N.dir,...T,...M,ref:H,style:{outline:"none",...M.style},onKeyDown:k(M.onKeyDown,E=>{const I=E.target.closest("[data-radix-menu-content]")===E.currentTarget,p=E.ctrlKey||E.altKey||E.metaKey,y=E.key.length===1;I&&(E.key==="Tab"&&E.preventDefault(),!p&&y&&q(E.key));const S=F.current;if(E.target!==S||!Mi.includes(E.key))return;E.preventDefault();const C=$().filter(D=>!D.disabled).map(D=>D.ref.current);zr.includes(E.key)&&C.reverse(),Xi(C)}),onBlur:k(e.onBlur,E=>{E.currentTarget.contains(E.target)||(window.clearTimeout(ae.current),Q.current="")}),onPointerMove:k(e.onPointerMove,qe(E=>{const z=E.target,I=ee.current!==E.clientX;if(E.currentTarget.contains(z)&&I){const p=E.clientX>ee.current?"right":"left";oe.current=p,ee.current=E.clientX}}))})})})})})})});qr.displayName=ue;var Hi="MenuGroup",dn=l.forwardRef((e,t)=>{const{__scopeMenu:n,...a}=e;return r.jsx(B.div,{role:"group",...a,ref:t})});dn.displayName=Hi;var Gi="MenuLabel",Xr=l.forwardRef((e,t)=>{const{__scopeMenu:n,...a}=e;return r.jsx(B.div,{...a,ref:t})});Xr.displayName=Gi;var it="MenuItem",Rn="menu.itemSelect",vt=l.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:a,...s}=e,i=l.useRef(null),c=Qe(it,e.__scopeMenu),u=ln(it,e.__scopeMenu),m=pe(t,i),d=l.useRef(!1),f=()=>{const o=i.current;if(!n&&o){const g=new CustomEvent(Rn,{bubbles:!0,cancelable:!0});o.addEventListener(Rn,h=>a==null?void 0:a(h),{once:!0}),uo(o,g),g.defaultPrevented?d.current=!1:c.onClose()}};return r.jsx(Zr,{...s,ref:m,disabled:n,onClick:k(e.onClick,f),onPointerDown:o=>{var g;(g=e.onPointerDown)==null||g.call(e,o),d.current=!0},onPointerUp:k(e.onPointerUp,o=>{var g;d.current||(g=o.currentTarget)==null||g.click()}),onKeyDown:k(e.onKeyDown,o=>{const g=u.searchRef.current!=="";n||g&&o.key===" "||Lt.includes(o.key)&&(o.currentTarget.click(),o.preventDefault())})})});vt.displayName=it;var Zr=l.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:a=!1,textValue:s,...i}=e,c=ln(it,n),u=Vr(n),m=l.useRef(null),d=pe(t,m),[f,o]=l.useState(!1),[g,h]=l.useState("");return l.useEffect(()=>{const b=m.current;b&&h((b.textContent??"").trim())},[i.children]),r.jsx(We.ItemSlot,{scope:n,disabled:a,textValue:s??g,children:r.jsx(ji,{asChild:!0,...u,focusable:!a,children:r.jsx(B.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...i,ref:d,onPointerMove:k(e.onPointerMove,qe(b=>{a?c.onItemLeave(b):(c.onItemEnter(b),b.defaultPrevented||b.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:k(e.onPointerLeave,qe(b=>c.onItemLeave(b))),onFocus:k(e.onFocus,()=>o(!0)),onBlur:k(e.onBlur,()=>o(!1))})})})}),zi="MenuCheckboxItem",Jr=l.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:a,...s}=e;return r.jsx(ra,{scope:e.__scopeMenu,checked:n,children:r.jsx(vt,{role:"menuitemcheckbox","aria-checked":lt(n)?"mixed":n,...s,ref:t,"data-state":fn(n),onSelect:k(s.onSelect,()=>a==null?void 0:a(lt(n)?!0:!n),{checkForDefaultPrevented:!1})})})});Jr.displayName=zi;var Qr="MenuRadioGroup",[Ki,Vi]=_e(Qr,{value:void 0,onValueChange:()=>{}}),ea=l.forwardRef((e,t)=>{const{value:n,onValueChange:a,...s}=e,i=ft(a);return r.jsx(Ki,{scope:e.__scopeMenu,value:n,onValueChange:i,children:r.jsx(dn,{...s,ref:t})})});ea.displayName=Qr;var ta="MenuRadioItem",na=l.forwardRef((e,t)=>{const{value:n,...a}=e,s=Vi(ta,e.__scopeMenu),i=n===s.value;return r.jsx(ra,{scope:e.__scopeMenu,checked:i,children:r.jsx(vt,{role:"menuitemradio","aria-checked":i,...a,ref:t,"data-state":fn(i),onSelect:k(a.onSelect,()=>{var c;return(c=s.onValueChange)==null?void 0:c.call(s,n)},{checkForDefaultPrevented:!1})})})});na.displayName=ta;var un="MenuItemIndicator",[ra,Ui]=_e(un,{checked:!1}),aa=l.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:a,...s}=e,i=Ui(un,n);return r.jsx(xe,{present:a||lt(i.checked)||i.checked===!0,children:r.jsx(B.span,{...s,ref:t,"data-state":fn(i.checked)})})});aa.displayName=un;var Yi="MenuSeparator",oa=l.forwardRef((e,t)=>{const{__scopeMenu:n,...a}=e;return r.jsx(B.div,{role:"separator","aria-orientation":"horizontal",...a,ref:t})});oa.displayName=Yi;var Wi="MenuArrow",sa=l.forwardRef((e,t)=>{const{__scopeMenu:n,...a}=e,s=ht(n);return r.jsx(Yn,{...s,...a,ref:t})});sa.displayName=Wi;var qi="MenuSub",[Ad,ia]=_e(qi),Ge="MenuSubTrigger",la=l.forwardRef((e,t)=>{const n=Me(Ge,e.__scopeMenu),a=Qe(Ge,e.__scopeMenu),s=ia(Ge,e.__scopeMenu),i=ln(Ge,e.__scopeMenu),c=l.useRef(null),{pointerGraceTimerRef:u,onPointerGraceIntentChange:m}=i,d={__scopeMenu:e.__scopeMenu},f=l.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return l.useEffect(()=>f,[f]),l.useEffect(()=>{const o=u.current;return()=>{window.clearTimeout(o),m(null)}},[u,m]),r.jsx(on,{asChild:!0,...d,children:r.jsx(Zr,{id:s.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":s.contentId,"data-state":ua(n.open),...e,ref:Ne(t,s.onTriggerChange),onClick:o=>{var g;(g=e.onClick)==null||g.call(e,o),!(e.disabled||o.defaultPrevented)&&(o.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:k(e.onPointerMove,qe(o=>{i.onItemEnter(o),!o.defaultPrevented&&!e.disabled&&!n.open&&!c.current&&(i.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100))})),onPointerLeave:k(e.onPointerLeave,qe(o=>{var h,b;f();const g=(h=n.content)==null?void 0:h.getBoundingClientRect();if(g){const M=(b=n.content)==null?void 0:b.dataset.side,x=M==="right",N=x?-5:5,T=g[x?"left":"right"],P=g[x?"right":"left"];i.onPointerGraceIntentChange({area:[{x:o.clientX+N,y:o.clientY},{x:T,y:g.top},{x:P,y:g.top},{x:P,y:g.bottom},{x:T,y:g.bottom}],side:M}),window.clearTimeout(u.current),u.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(o),o.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:k(e.onKeyDown,o=>{var h;const g=i.searchRef.current!=="";e.disabled||g&&o.key===" "||ki[a.dir].includes(o.key)&&(n.onOpenChange(!0),(h=n.content)==null||h.focus(),o.preventDefault())})})})});la.displayName=Ge;var ca="MenuSubContent",da=l.forwardRef((e,t)=>{const n=Yr(ue,e.__scopeMenu),{forceMount:a=n.forceMount,...s}=e,i=Me(ue,e.__scopeMenu),c=Qe(ue,e.__scopeMenu),u=ia(ca,e.__scopeMenu),m=l.useRef(null),d=pe(t,m);return r.jsx(We.Provider,{scope:e.__scopeMenu,children:r.jsx(xe,{present:a||i.open,children:r.jsx(We.Slot,{scope:e.__scopeMenu,children:r.jsx(cn,{id:u.contentId,"aria-labelledby":u.triggerId,...s,ref:d,align:"start",side:c.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:f=>{var o;c.isUsingKeyboardRef.current&&((o=m.current)==null||o.focus()),f.preventDefault()},onCloseAutoFocus:f=>f.preventDefault(),onFocusOutside:k(e.onFocusOutside,f=>{f.target!==u.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:k(e.onEscapeKeyDown,f=>{c.onClose(),f.preventDefault()}),onKeyDown:k(e.onKeyDown,f=>{var h;const o=f.currentTarget.contains(f.target),g=Ri[c.dir].includes(f.key);o&&g&&(i.onOpenChange(!1),(h=u.trigger)==null||h.focus(),f.preventDefault())})})})})})});da.displayName=ca;function ua(e){return e?"open":"closed"}function lt(e){return e==="indeterminate"}function fn(e){return lt(e)?"indeterminate":e?"checked":"unchecked"}function Xi(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function Zi(e,t){return e.map((n,a)=>e[(t+a)%e.length])}function Ji(e,t,n){const s=t.length>1&&Array.from(t).every(d=>d===t[0])?t[0]:t,i=n?e.indexOf(n):-1;let c=Zi(e,Math.max(i,0));s.length===1&&(c=c.filter(d=>d!==n));const m=c.find(d=>d.toLowerCase().startsWith(s.toLowerCase()));return m!==n?m:void 0}function Qi(e,t){const{x:n,y:a}=e;let s=!1;for(let i=0,c=t.length-1;i<t.length;c=i++){const u=t[i],m=t[c],d=u.x,f=u.y,o=m.x,g=m.y;f>a!=g>a&&n<(o-d)*(a-f)/(g-f)+d&&(s=!s)}return s}function el(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return Qi(n,t)}function qe(e){return t=>t.pointerType==="mouse"?e(t):void 0}var tl=Ur,nl=on,rl=Wr,al=qr,ol=dn,sl=Xr,il=vt,ll=Jr,cl=ea,dl=na,ul=aa,fl=oa,ml=sa,pl=la,gl=da,xt="DropdownMenu",[hl,Pd]=Le(xt,[Kr]),te=Kr(),[vl,fa]=hl(xt),ma=e=>{const{__scopeDropdownMenu:t,children:n,dir:a,open:s,defaultOpen:i,onOpenChange:c,modal:u=!0}=e,m=te(t),d=l.useRef(null),[f,o]=dt({prop:s,defaultProp:i??!1,onChange:c,caller:xt});return r.jsx(vl,{scope:t,triggerId:ce(),triggerRef:d,contentId:ce(),open:f,onOpenChange:o,onOpenToggle:l.useCallback(()=>o(g=>!g),[o]),modal:u,children:r.jsx(tl,{...m,open:f,onOpenChange:o,dir:a,modal:u,children:n})})};ma.displayName=xt;var pa="DropdownMenuTrigger",ga=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:a=!1,...s}=e,i=fa(pa,n),c=te(n);return r.jsx(nl,{asChild:!0,...c,children:r.jsx(B.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":a?"":void 0,disabled:a,...s,ref:Ne(t,i.triggerRef),onPointerDown:k(e.onPointerDown,u=>{!a&&u.button===0&&u.ctrlKey===!1&&(i.onOpenToggle(),i.open||u.preventDefault())}),onKeyDown:k(e.onKeyDown,u=>{a||(["Enter"," "].includes(u.key)&&i.onOpenToggle(),u.key==="ArrowDown"&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(u.key)&&u.preventDefault())})})})});ga.displayName=pa;var xl="DropdownMenuPortal",ha=e=>{const{__scopeDropdownMenu:t,...n}=e,a=te(t);return r.jsx(rl,{...a,...n})};ha.displayName=xl;var va="DropdownMenuContent",xa=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...a}=e,s=fa(va,n),i=te(n),c=l.useRef(!1);return r.jsx(al,{id:s.contentId,"aria-labelledby":s.triggerId,...i,...a,ref:t,onCloseAutoFocus:k(e.onCloseAutoFocus,u=>{var m;c.current||(m=s.triggerRef.current)==null||m.focus(),c.current=!1,u.preventDefault()}),onInteractOutside:k(e.onInteractOutside,u=>{const m=u.detail.originalEvent,d=m.button===0&&m.ctrlKey===!0,f=m.button===2||d;(!s.modal||f)&&(c.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});xa.displayName=va;var bl="DropdownMenuGroup",ba=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...a}=e,s=te(n);return r.jsx(ol,{...s,...a,ref:t})});ba.displayName=bl;var yl="DropdownMenuLabel",ya=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...a}=e,s=te(n);return r.jsx(sl,{...s,...a,ref:t})});ya.displayName=yl;var wl="DropdownMenuItem",wa=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...a}=e,s=te(n);return r.jsx(il,{...s,...a,ref:t})});wa.displayName=wl;var Cl="DropdownMenuCheckboxItem",El=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...a}=e,s=te(n);return r.jsx(ll,{...s,...a,ref:t})});El.displayName=Cl;var Nl="DropdownMenuRadioGroup",Sl=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...a}=e,s=te(n);return r.jsx(cl,{...s,...a,ref:t})});Sl.displayName=Nl;var jl="DropdownMenuRadioItem",_l=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...a}=e,s=te(n);return r.jsx(dl,{...s,...a,ref:t})});_l.displayName=jl;var Ml="DropdownMenuItemIndicator",kl=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...a}=e,s=te(n);return r.jsx(ul,{...s,...a,ref:t})});kl.displayName=Ml;var Rl="DropdownMenuSeparator",Ca=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...a}=e,s=te(n);return r.jsx(fl,{...s,...a,ref:t})});Ca.displayName=Rl;var Il="DropdownMenuArrow",Tl=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...a}=e,s=te(n);return r.jsx(ml,{...s,...a,ref:t})});Tl.displayName=Il;var Dl="DropdownMenuSubTrigger",Al=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...a}=e,s=te(n);return r.jsx(pl,{...s,...a,ref:t})});Al.displayName=Dl;var Pl="DropdownMenuSubContent",Ol=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...a}=e,s=te(n);return r.jsx(gl,{...s,...a,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Ol.displayName=Pl;var Ll=ma,Fl=ga,$l=ha,Bl=xa,Hl=ba,Gl=ya,zl=wa,Kl=Ca;function Vl({...e}){return r.jsx(Ll,{"data-slot":"dropdown-menu",...e})}function Ul({...e}){return r.jsx(Fl,{"data-slot":"dropdown-menu-trigger",...e})}function Yl({className:e,sideOffset:t=4,...n}){return r.jsx($l,{children:r.jsx(Bl,{"data-slot":"dropdown-menu-content",sideOffset:t,className:j("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-md",e),...n})})}function In({...e}){return r.jsx(Hl,{"data-slot":"dropdown-menu-group",...e})}function ye({className:e,inset:t,variant:n="default",...a}){return r.jsx(zl,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":n,className:j("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...a})}function ot({className:e,inset:t,...n}){return r.jsx(Gl,{"data-slot":"dropdown-menu-label","data-inset":t,className:j("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...n})}function ze({className:e,...t}){return r.jsx(Kl,{"data-slot":"dropdown-menu-separator",className:j("bg-border -mx-1 my-1 h-px",e),...t})}var St={exports:{}},jt={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Tn;function Wl(){if(Tn)return jt;Tn=1;var e=ro();function t(o,g){return o===g&&(o!==0||1/o===1/g)||o!==o&&g!==g}var n=typeof Object.is=="function"?Object.is:t,a=e.useState,s=e.useEffect,i=e.useLayoutEffect,c=e.useDebugValue;function u(o,g){var h=g(),b=a({inst:{value:h,getSnapshot:g}}),M=b[0].inst,x=b[1];return i(function(){M.value=h,M.getSnapshot=g,m(M)&&x({inst:M})},[o,h,g]),s(function(){return m(M)&&x({inst:M}),o(function(){m(M)&&x({inst:M})})},[o]),c(h),h}function m(o){var g=o.getSnapshot;o=o.value;try{var h=g();return!n(o,h)}catch{return!0}}function d(o,g){return g()}var f=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?d:u;return jt.useSyncExternalStore=e.useSyncExternalStore!==void 0?e.useSyncExternalStore:f,jt}var Dn;function ql(){return Dn||(Dn=1,St.exports=Wl()),St.exports}var Xl=ql();function Zl(){return Xl.useSyncExternalStore(Jl,()=>!0,()=>!1)}function Jl(){return()=>{}}var mn="Avatar",[Ql,Od]=Le(mn),[ec,Ea]=Ql(mn),Na=l.forwardRef((e,t)=>{const{__scopeAvatar:n,...a}=e,[s,i]=l.useState("idle");return r.jsx(ec,{scope:n,imageLoadingStatus:s,onImageLoadingStatusChange:i,children:r.jsx(B.span,{...a,ref:t})})});Na.displayName=mn;var Sa="AvatarImage",ja=l.forwardRef((e,t)=>{const{__scopeAvatar:n,src:a,onLoadingStatusChange:s=()=>{},...i}=e,c=Ea(Sa,n),u=tc(a,i),m=ft(d=>{s(d),c.onImageLoadingStatusChange(d)});return It(()=>{u!=="idle"&&m(u)},[u,m]),u==="loaded"?r.jsx(B.img,{...i,ref:t,src:a}):null});ja.displayName=Sa;var _a="AvatarFallback",Ma=l.forwardRef((e,t)=>{const{__scopeAvatar:n,delayMs:a,...s}=e,i=Ea(_a,n),[c,u]=l.useState(a===void 0);return l.useEffect(()=>{if(a!==void 0){const m=window.setTimeout(()=>u(!0),a);return()=>window.clearTimeout(m)}},[a]),c&&i.imageLoadingStatus!=="loaded"?r.jsx(B.span,{...s,ref:t}):null});Ma.displayName=_a;function An(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function tc(e,{referrerPolicy:t,crossOrigin:n}){const a=Zl(),s=l.useRef(null),i=a?(s.current||(s.current=new window.Image),s.current):null,[c,u]=l.useState(()=>An(i,e));return It(()=>{u(An(i,e))},[i,e]),It(()=>{const m=o=>()=>{u(o)};if(!i)return;const d=m("loaded"),f=m("error");return i.addEventListener("load",d),i.addEventListener("error",f),t&&(i.referrerPolicy=t),typeof n=="string"&&(i.crossOrigin=n),()=>{i.removeEventListener("load",d),i.removeEventListener("error",f)}},[i,n,t]),c}var nc=Na,rc=ja,ac=Ma;function oc({className:e,...t}){return r.jsx(nc,{"data-slot":"avatar",className:j("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function sc({className:e,...t}){return r.jsx(rc,{"data-slot":"avatar-image",className:j("aspect-square size-full",e),...t})}function ic({className:e,...t}){return r.jsx(ac,{"data-slot":"avatar-fallback",className:j("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}function lc(){return l.useCallback(e=>{const t=e.trim().split(" ");if(t.length===0)return"";if(t.length===1)return t[0].charAt(0).toUpperCase();const n=t[0].charAt(0),a=t[t.length-1].charAt(0);return`${n}${a}`.toUpperCase()},[])}function cc({user:e,showEmail:t=!1}){const n=lc();return r.jsxs(r.Fragment,{children:[r.jsxs(oc,{className:"h-8 w-8 overflow-hidden rounded-full",children:[r.jsx(sc,{src:e.avatar,alt:e.name}),r.jsx(ic,{className:"rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white",children:n(e.name)})]}),r.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[r.jsx("span",{className:"truncate font-medium",children:e.name}),t&&r.jsx("span",{className:"truncate text-xs text-muted-foreground",children:e.email})]})]})}function dc(){return l.useCallback(()=>{document.body.style.removeProperty("pointer-events")},[])}function Ld({user:e,isAdmin:t=!1,isAdminView:n=!1,setIsAdminView:a}){const s=dc(),i=()=>{s(),Gt.flushAll()},c=()=>{a&&a(u=>!u),s()};return r.jsxs(r.Fragment,{children:[r.jsx(ot,{className:"p-0 font-normal",children:r.jsxs("div",{className:"flex items-center gap-3 px-2 py-2 text-left",children:[r.jsx(cc,{user:e,showEmail:!0}),r.jsxs("div",{className:"flex flex-col gap-1",children:[e.subscription_plan==="premium"&&r.jsxs(Pe,{variant:"outline",className:"text-xs bg-gradient-to-r from-amber-50 to-yellow-50 text-amber-700 border-amber-200 dark:from-amber-950 dark:to-yellow-950 dark:text-amber-300 dark:border-amber-800",children:[r.jsx(fo,{className:"w-3 h-3 mr-1"}),"Premium"]}),t&&r.jsxs(Pe,{variant:"outline",className:"text-xs bg-gradient-to-r from-orange-50 to-red-50 text-orange-700 border-orange-200 dark:from-orange-950 dark:to-red-950 dark:text-orange-300 dark:border-orange-800",children:[r.jsx(Ve,{className:"w-3 h-3 mr-1"}),"Admin"]})]})]})}),r.jsx(ze,{}),t&&r.jsxs(r.Fragment,{children:[r.jsx(In,{children:r.jsx(ye,{onSelect:c,className:"flex items-center gap-3 p-3",children:n?r.jsxs(r.Fragment,{children:[r.jsx(Zn,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"}),r.jsxs("div",{className:"flex-1",children:[r.jsx("div",{className:"font-medium",children:"Switch to User View"}),r.jsx("div",{className:"text-xs text-muted-foreground",children:"Experience as a regular user"})]})]}):r.jsxs(r.Fragment,{children:[r.jsx(Ve,{className:"h-4 w-4 text-orange-600 dark:text-orange-400"}),r.jsxs("div",{className:"flex-1",children:[r.jsx("div",{className:"font-medium",children:"Switch to Admin View"}),r.jsx("div",{className:"text-xs text-muted-foreground",children:"Access admin features"})]})]})})}),r.jsx(ze,{})]}),r.jsxs(In,{children:[r.jsx(ye,{asChild:!0,children:r.jsxs(Ae,{className:"flex items-center gap-3 p-3",href:route("notifications.index"),as:"button",prefetch:!0,onClick:s,children:[r.jsx(Jn,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"}),r.jsxs("div",{className:"flex-1",children:[r.jsx("div",{className:"font-medium",children:"Notifications"}),r.jsx("div",{className:"text-xs text-muted-foreground",children:"View your alerts"})]})]})}),t&&r.jsx(ye,{asChild:!0,children:r.jsxs(Ae,{className:"flex items-center gap-3 p-3",href:route("admin.analytics.index"),as:"button",prefetch:!0,onClick:s,children:[r.jsx(Yt,{className:"h-4 w-4 text-green-600 dark:text-green-400"}),r.jsxs("div",{className:"flex-1",children:[r.jsx("div",{className:"font-medium",children:"Analytics"}),r.jsx("div",{className:"text-xs text-muted-foreground",children:"System insights"})]})]})}),r.jsx(ye,{asChild:!0,children:r.jsxs(Ae,{className:"flex items-center gap-3 p-3",href:route("profile.edit"),as:"button",prefetch:!0,onClick:s,children:[r.jsx(Ut,{className:"h-4 w-4 text-purple-600 dark:text-purple-400"}),r.jsxs("div",{className:"flex-1",children:[r.jsx("div",{className:"font-medium",children:"Settings"}),r.jsx("div",{className:"text-xs text-muted-foreground",children:"Manage your account"})]})]})}),r.jsxs(ye,{className:"flex items-center gap-3 p-3",children:[r.jsx(Qn,{className:"h-4 w-4 text-gray-600 dark:text-gray-400"}),r.jsxs("div",{className:"flex-1",children:[r.jsx("div",{className:"font-medium",children:"Help & Support"}),r.jsx("div",{className:"text-xs text-muted-foreground",children:"Get assistance"})]})]}),r.jsxs(ye,{className:"flex items-center gap-3 p-3",children:[r.jsx(Do,{className:"h-4 w-4 text-indigo-600 dark:text-indigo-400"}),r.jsxs("div",{className:"flex-1",children:[r.jsx("div",{className:"font-medium",children:"Keyboard Shortcuts"}),r.jsx("div",{className:"text-xs text-muted-foreground",children:"Press ⌘K to search"})]})]})]}),r.jsx(ze,{}),r.jsx(ye,{asChild:!0,children:r.jsxs(Ae,{className:"flex items-center gap-3 p-3 text-red-600 dark:text-red-400 focus:text-red-600 dark:focus:text-red-400",method:"post",href:route("logout"),as:"button",onClick:i,children:[r.jsx(nr,{className:"h-4 w-4"}),r.jsxs("div",{className:"flex-1",children:[r.jsx("div",{className:"font-medium",children:"Log out"}),r.jsx("div",{className:"text-xs text-muted-foreground",children:"Sign out of your account"})]})]})})]})}function uc(e){return r.jsx("svg",{...e,viewBox:"0 0 40 42",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M17.2 5.63325L8.6 0.855469L0 5.63325V32.1434L16.2 41.1434L32.4 32.1434V23.699L40 19.4767V9.85547L31.4 5.07769L22.8 9.85547V18.2999L17.2 21.411V5.63325ZM38 18.2999L32.4 21.411V15.2545L38 12.1434V18.2999ZM36.9409 10.4439L31.4 13.5221L25.8591 10.4439L31.4 7.36561L36.9409 10.4439ZM24.8 18.2999V12.1434L30.4 15.2545V21.411L24.8 18.2999ZM23.8 20.0323L29.3409 23.1105L16.2 30.411L10.6591 27.3328L23.8 20.0323ZM7.6 27.9212L15.2 32.1434V38.2999L2 30.9666V7.92116L7.6 11.0323V27.9212ZM8.6 9.29991L3.05913 6.22165L8.6 3.14339L14.1409 6.22165L8.6 9.29991ZM30.4 24.8101L17.2 32.1434V38.2999L30.4 30.9666V24.8101ZM9.6 11.0323L15.2 7.92117V22.5221L9.6 25.6333V11.0323Z"})})}function Fd(){const[e,t]=l.useState({}),[n,a]=l.useState(!0);l.useEffect(()=>{fetch("/api/branding").then(m=>{if(m.ok)return m.json();throw new Error("Failed to fetch branding settings")}).then(m=>{t(m),a(!1)}).catch(()=>{a(!1)})},[]);const s=e.site_logo_url,i=e.site_logo_alt||"FixHaat",c=e.site_logo_width||32,u=e.site_logo_height||32;return r.jsxs(r.Fragment,{children:[r.jsx("div",{className:"flex aspect-square size-8 items-center justify-center rounded-md bg-sidebar-primary text-sidebar-primary-foreground",children:!n&&s?r.jsx("img",{src:s,alt:i,style:{width:`${Math.min(c,32)}px`,height:`${Math.min(u,32)}px`,objectFit:"contain"},className:"max-w-full max-h-full"}):r.jsx(uc,{className:"size-5 fill-current text-white dark:text-black"})}),r.jsx("div",{className:"ml-1 grid flex-1 text-left text-sm",children:r.jsx("span",{className:"mb-0.5 truncate leading-tight font-semibold",children:"FixHaat"})})]})}function fc({...e}){return r.jsx("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function mc({className:e,...t}){return r.jsx("ol",{"data-slot":"breadcrumb-list",className:j("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...t})}function pc({className:e,...t}){return r.jsx("li",{"data-slot":"breadcrumb-item",className:j("inline-flex items-center gap-1.5",e),...t})}function gc({asChild:e,className:t,...n}){const a=e?$n:"a";return r.jsx(a,{"data-slot":"breadcrumb-link",className:j("hover:text-foreground transition-colors",t),...n})}function hc({className:e,...t}){return r.jsx("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:j("text-foreground font-normal",e),...t})}function vc({children:e,className:t,...n}){return r.jsx("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:j("[&>svg]:size-3.5",t),...n,children:e??r.jsx(wo,{})})}function $d({breadcrumbs:e}){return r.jsx(r.Fragment,{children:e.length>0&&r.jsx(fc,{children:r.jsx(mc,{children:e.map((t,n)=>{const a=n===e.length-1;return r.jsxs(l.Fragment,{children:[r.jsx(pc,{children:a?r.jsx(hc,{children:t.title}):r.jsx(gc,{asChild:!0,children:r.jsx(Ae,{href:t.href,children:t.title})})}),!a&&r.jsx(vc,{})]},n)})})})})}var Pn=1,xc=.9,bc=.8,yc=.17,_t=.1,Mt=.999,wc=.9999,Cc=.99,Ec=/[\\\/_+.#"@\[\(\{&]/,Nc=/[\\\/_+.#"@\[\(\{&]/g,Sc=/[\s-]/,ka=/[\s-]/g;function Ft(e,t,n,a,s,i,c){if(i===t.length)return s===e.length?Pn:Cc;var u=`${s},${i}`;if(c[u]!==void 0)return c[u];for(var m=a.charAt(i),d=n.indexOf(m,s),f=0,o,g,h,b;d>=0;)o=Ft(e,t,n,a,d+1,i+1,c),o>f&&(d===s?o*=Pn:Ec.test(e.charAt(d-1))?(o*=bc,h=e.slice(s,d-1).match(Nc),h&&s>0&&(o*=Math.pow(Mt,h.length))):Sc.test(e.charAt(d-1))?(o*=xc,b=e.slice(s,d-1).match(ka),b&&s>0&&(o*=Math.pow(Mt,b.length))):(o*=yc,s>0&&(o*=Math.pow(Mt,d-s))),e.charAt(d)!==t.charAt(i)&&(o*=wc)),(o<_t&&n.charAt(d-1)===a.charAt(i+1)||a.charAt(i+1)===a.charAt(i)&&n.charAt(d-1)!==a.charAt(i))&&(g=Ft(e,t,n,a,d+1,i+2,c),g*_t>o&&(o=g*_t)),o>f&&(f=o),d=n.indexOf(m,d+1);return c[u]=f,f}function On(e){return e.toLowerCase().replace(ka," ")}function jc(e,t,n){return e=n&&n.length>0?`${e+" "+n.join(" ")}`:e,Ft(e,t,On(e),On(t),0,0,{})}var He='[cmdk-group=""]',kt='[cmdk-group-items=""]',_c='[cmdk-group-heading=""]',Ra='[cmdk-item=""]',Ln=`${Ra}:not([aria-disabled="true"])`,$t="cmdk-item-select",Te="data-value",Mc=(e,t,n)=>jc(e,t,n),Ia=l.createContext(void 0),et=()=>l.useContext(Ia),Ta=l.createContext(void 0),pn=()=>l.useContext(Ta),Da=l.createContext(void 0),Aa=l.forwardRef((e,t)=>{let n=De(()=>{var p,y;return{search:"",value:(y=(p=e.value)!=null?p:e.defaultValue)!=null?y:"",selectedItemId:void 0,filtered:{count:0,items:new Map,groups:new Set}}}),a=De(()=>new Set),s=De(()=>new Map),i=De(()=>new Map),c=De(()=>new Set),u=Pa(e),{label:m,children:d,value:f,onValueChange:o,filter:g,shouldFilter:h,loop:b,disablePointerSelection:M=!1,vimBindings:x=!0,...N}=e,T=ce(),P=ce(),$=ce(),L=l.useRef(null),A=$c();je(()=>{if(f!==void 0){let p=f.trim();n.current.value=p,F.emit()}},[f]),je(()=>{A(6,oe)},[]);let F=l.useMemo(()=>({subscribe:p=>(c.current.add(p),()=>c.current.delete(p)),snapshot:()=>n.current,setState:(p,y,S)=>{var w,C,D,G;if(!Object.is(n.current[p],y)){if(n.current[p]=y,p==="search")Z(),Q(),A(1,V);else if(p==="value"){if(document.activeElement.hasAttribute("cmdk-input")||document.activeElement.hasAttribute("cmdk-root")){let O=document.getElementById($);O?O.focus():(w=document.getElementById(T))==null||w.focus()}if(A(7,()=>{var O;n.current.selectedItemId=(O=ee())==null?void 0:O.id,F.emit()}),S||A(5,oe),((C=u.current)==null?void 0:C.value)!==void 0){let O=y??"";(G=(D=u.current).onValueChange)==null||G.call(D,O);return}}F.emit()}},emit:()=>{c.current.forEach(p=>p())}}),[]),H=l.useMemo(()=>({value:(p,y,S)=>{var w;y!==((w=i.current.get(p))==null?void 0:w.value)&&(i.current.set(p,{value:y,keywords:S}),n.current.filtered.items.set(p,ae(y,S)),A(2,()=>{Q(),F.emit()}))},item:(p,y)=>(a.current.add(p),y&&(s.current.has(y)?s.current.get(y).add(p):s.current.set(y,new Set([p]))),A(3,()=>{Z(),Q(),n.current.value||V(),F.emit()}),()=>{i.current.delete(p),a.current.delete(p),n.current.filtered.items.delete(p);let S=ee();A(4,()=>{Z(),(S==null?void 0:S.getAttribute("id"))===p&&V(),F.emit()})}),group:p=>(s.current.has(p)||s.current.set(p,new Set),()=>{i.current.delete(p),s.current.delete(p)}),filter:()=>u.current.shouldFilter,label:m||e["aria-label"],getDisablePointerSelection:()=>u.current.disablePointerSelection,listId:T,inputId:$,labelId:P,listInnerRef:L}),[]);function ae(p,y){var S,w;let C=(w=(S=u.current)==null?void 0:S.filter)!=null?w:Mc;return p?C(p,n.current.search,y):0}function Q(){if(!n.current.search||u.current.shouldFilter===!1)return;let p=n.current.filtered.items,y=[];n.current.filtered.groups.forEach(w=>{let C=s.current.get(w),D=0;C.forEach(G=>{let O=p.get(G);D=Math.max(O,D)}),y.push([w,D])});let S=L.current;K().sort((w,C)=>{var D,G;let O=w.getAttribute("id"),se=C.getAttribute("id");return((D=p.get(se))!=null?D:0)-((G=p.get(O))!=null?G:0)}).forEach(w=>{let C=w.closest(kt);C?C.appendChild(w.parentElement===C?w:w.closest(`${kt} > *`)):S.appendChild(w.parentElement===S?w:w.closest(`${kt} > *`))}),y.sort((w,C)=>C[1]-w[1]).forEach(w=>{var C;let D=(C=L.current)==null?void 0:C.querySelector(`${He}[${Te}="${encodeURIComponent(w[0])}"]`);D==null||D.parentElement.appendChild(D)})}function V(){let p=K().find(S=>S.getAttribute("aria-disabled")!=="true"),y=p==null?void 0:p.getAttribute(Te);F.setState("value",y||void 0)}function Z(){var p,y,S,w;if(!n.current.search||u.current.shouldFilter===!1){n.current.filtered.count=a.current.size;return}n.current.filtered.groups=new Set;let C=0;for(let D of a.current){let G=(y=(p=i.current.get(D))==null?void 0:p.value)!=null?y:"",O=(w=(S=i.current.get(D))==null?void 0:S.keywords)!=null?w:[],se=ae(G,O);n.current.filtered.items.set(D,se),se>0&&C++}for(let[D,G]of s.current)for(let O of G)if(n.current.filtered.items.get(O)>0){n.current.filtered.groups.add(D);break}n.current.filtered.count=C}function oe(){var p,y,S;let w=ee();w&&(((p=w.parentElement)==null?void 0:p.firstChild)===w&&((S=(y=w.closest(He))==null?void 0:y.querySelector(_c))==null||S.scrollIntoView({block:"nearest"})),w.scrollIntoView({block:"nearest"}))}function ee(){var p;return(p=L.current)==null?void 0:p.querySelector(`${Ra}[aria-selected="true"]`)}function K(){var p;return Array.from(((p=L.current)==null?void 0:p.querySelectorAll(Ln))||[])}function _(p){let y=K()[p];y&&F.setState("value",y.getAttribute(Te))}function q(p){var y;let S=ee(),w=K(),C=w.findIndex(G=>G===S),D=w[C+p];(y=u.current)!=null&&y.loop&&(D=C+p<0?w[w.length-1]:C+p===w.length?w[0]:w[C+p]),D&&F.setState("value",D.getAttribute(Te))}function U(p){let y=ee(),S=y==null?void 0:y.closest(He),w;for(;S&&!w;)S=p>0?Lc(S,He):Fc(S,He),w=S==null?void 0:S.querySelector(Ln);w?F.setState("value",w.getAttribute(Te)):q(p)}let E=()=>_(K().length-1),z=p=>{p.preventDefault(),p.metaKey?E():p.altKey?U(1):q(1)},I=p=>{p.preventDefault(),p.metaKey?_(0):p.altKey?U(-1):q(-1)};return l.createElement(B.div,{ref:t,tabIndex:-1,...N,"cmdk-root":"",onKeyDown:p=>{var y;(y=N.onKeyDown)==null||y.call(N,p);let S=p.nativeEvent.isComposing||p.keyCode===229;if(!(p.defaultPrevented||S))switch(p.key){case"n":case"j":{x&&p.ctrlKey&&z(p);break}case"ArrowDown":{z(p);break}case"p":case"k":{x&&p.ctrlKey&&I(p);break}case"ArrowUp":{I(p);break}case"Home":{p.preventDefault(),_(0);break}case"End":{p.preventDefault(),E();break}case"Enter":{p.preventDefault();let w=ee();if(w){let C=new Event($t);w.dispatchEvent(C)}}}}},l.createElement("label",{"cmdk-label":"",htmlFor:H.inputId,id:H.labelId,style:Hc},m),bt(e,p=>l.createElement(Ta.Provider,{value:F},l.createElement(Ia.Provider,{value:H},p))))}),kc=l.forwardRef((e,t)=>{var n,a;let s=ce(),i=l.useRef(null),c=l.useContext(Da),u=et(),m=Pa(e),d=(a=(n=m.current)==null?void 0:n.forceMount)!=null?a:c==null?void 0:c.forceMount;je(()=>{if(!d)return u.item(s,c==null?void 0:c.id)},[d]);let f=Oa(s,i,[e.value,e.children,i],e.keywords),o=pn(),g=we(A=>A.value&&A.value===f.current),h=we(A=>d||u.filter()===!1?!0:A.search?A.filtered.items.get(s)>0:!0);l.useEffect(()=>{let A=i.current;if(!(!A||e.disabled))return A.addEventListener($t,b),()=>A.removeEventListener($t,b)},[h,e.onSelect,e.disabled]);function b(){var A,F;M(),(F=(A=m.current).onSelect)==null||F.call(A,f.current)}function M(){o.setState("value",f.current,!0)}if(!h)return null;let{disabled:x,value:N,onSelect:T,forceMount:P,keywords:$,...L}=e;return l.createElement(B.div,{ref:Ne(i,t),...L,id:s,"cmdk-item":"",role:"option","aria-disabled":!!x,"aria-selected":!!g,"data-disabled":!!x,"data-selected":!!g,onPointerMove:x||u.getDisablePointerSelection()?void 0:M,onClick:x?void 0:b},e.children)}),Rc=l.forwardRef((e,t)=>{let{heading:n,children:a,forceMount:s,...i}=e,c=ce(),u=l.useRef(null),m=l.useRef(null),d=ce(),f=et(),o=we(h=>s||f.filter()===!1?!0:h.search?h.filtered.groups.has(c):!0);je(()=>f.group(c),[]),Oa(c,u,[e.value,e.heading,m]);let g=l.useMemo(()=>({id:c,forceMount:s}),[s]);return l.createElement(B.div,{ref:Ne(u,t),...i,"cmdk-group":"",role:"presentation",hidden:o?void 0:!0},n&&l.createElement("div",{ref:m,"cmdk-group-heading":"","aria-hidden":!0,id:d},n),bt(e,h=>l.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":n?d:void 0},l.createElement(Da.Provider,{value:g},h))))}),Ic=l.forwardRef((e,t)=>{let{alwaysRender:n,...a}=e,s=l.useRef(null),i=we(c=>!c.search);return!n&&!i?null:l.createElement(B.div,{ref:Ne(s,t),...a,"cmdk-separator":"",role:"separator"})}),Tc=l.forwardRef((e,t)=>{let{onValueChange:n,...a}=e,s=e.value!=null,i=pn(),c=we(d=>d.search),u=we(d=>d.selectedItemId),m=et();return l.useEffect(()=>{e.value!=null&&i.setState("search",e.value)},[e.value]),l.createElement(B.input,{ref:t,...a,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":m.listId,"aria-labelledby":m.labelId,"aria-activedescendant":u,id:m.inputId,type:"text",value:s?e.value:c,onChange:d=>{s||i.setState("search",d.target.value),n==null||n(d.target.value)}})}),Dc=l.forwardRef((e,t)=>{let{children:n,label:a="Suggestions",...s}=e,i=l.useRef(null),c=l.useRef(null),u=we(d=>d.selectedItemId),m=et();return l.useEffect(()=>{if(c.current&&i.current){let d=c.current,f=i.current,o,g=new ResizeObserver(()=>{o=requestAnimationFrame(()=>{let h=d.offsetHeight;f.style.setProperty("--cmdk-list-height",h.toFixed(1)+"px")})});return g.observe(d),()=>{cancelAnimationFrame(o),g.unobserve(d)}}},[]),l.createElement(B.div,{ref:Ne(i,t),...s,"cmdk-list":"",role:"listbox",tabIndex:-1,"aria-activedescendant":u,"aria-label":a,id:m.listId},bt(e,d=>l.createElement("div",{ref:Ne(c,m.listInnerRef),"cmdk-list-sizer":""},d)))}),Ac=l.forwardRef((e,t)=>{let{open:n,onOpenChange:a,overlayClassName:s,contentClassName:i,container:c,...u}=e;return l.createElement(Zt,{open:n,onOpenChange:a},l.createElement(Jt,{container:c},l.createElement(Qt,{"cmdk-overlay":"",className:s}),l.createElement(en,{"aria-label":e.label,"cmdk-dialog":"",className:i},l.createElement(Aa,{ref:t,...u}))))}),Pc=l.forwardRef((e,t)=>we(n=>n.filtered.count===0)?l.createElement(B.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null),Oc=l.forwardRef((e,t)=>{let{progress:n,children:a,label:s="Loading...",...i}=e;return l.createElement(B.div,{ref:t,...i,"cmdk-loading":"",role:"progressbar","aria-valuenow":n,"aria-valuemin":0,"aria-valuemax":100,"aria-label":s},bt(e,c=>l.createElement("div",{"aria-hidden":!0},c)))}),re=Object.assign(Aa,{List:Dc,Item:kc,Input:Tc,Group:Rc,Separator:Ic,Dialog:Ac,Empty:Pc,Loading:Oc});function Lc(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return n;n=n.nextElementSibling}}function Fc(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return n;n=n.previousElementSibling}}function Pa(e){let t=l.useRef(e);return je(()=>{t.current=e}),t}var je=typeof window>"u"?l.useEffect:l.useLayoutEffect;function De(e){let t=l.useRef();return t.current===void 0&&(t.current=e()),t}function we(e){let t=pn(),n=()=>e(t.snapshot());return l.useSyncExternalStore(t.subscribe,n,n)}function Oa(e,t,n,a=[]){let s=l.useRef(),i=et();return je(()=>{var c;let u=(()=>{var d;for(let f of n){if(typeof f=="string")return f.trim();if(typeof f=="object"&&"current"in f)return f.current?(d=f.current.textContent)==null?void 0:d.trim():s.current}})(),m=a.map(d=>d.trim());i.value(e,u,m),(c=t.current)==null||c.setAttribute(Te,u),s.current=u}),s}var $c=()=>{let[e,t]=l.useState(),n=De(()=>new Map);return je(()=>{n.current.forEach(a=>a()),n.current=new Map},[e]),(a,s)=>{n.current.set(a,s),t({})}};function Bc(e){let t=e.type;return typeof t=="function"?t(e.props):"render"in t?t.render(e.props):e}function bt({asChild:e,children:t},n){return e&&l.isValidElement(t)?l.cloneElement(Bc(t),{ref:t.ref},n(t.props.children)):n(t)}var Hc={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};function Gc({...e}){return r.jsx(Zt,{"data-slot":"dialog",...e})}function Bd({...e}){return r.jsx(Cr,{"data-slot":"dialog-trigger",...e})}function zc({...e}){return r.jsx(Jt,{"data-slot":"dialog-portal",...e})}function Hd({...e}){return r.jsx(tn,{"data-slot":"dialog-close",...e})}function Kc({className:e,...t}){return r.jsx(Qt,{"data-slot":"dialog-overlay",className:j("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",e),...t})}function Vc({className:e,children:t,...n}){return r.jsxs(zc,{"data-slot":"dialog-portal",children:[r.jsx(Kc,{}),r.jsxs(en,{"data-slot":"dialog-content",className:j("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...n,children:[t,r.jsxs(tn,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[r.jsx(or,{}),r.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function Gd({className:e,...t}){return r.jsx("div",{"data-slot":"dialog-header",className:j("flex flex-col gap-2 text-center sm:text-left",e),...t})}function zd({className:e,...t}){return r.jsx("div",{"data-slot":"dialog-footer",className:j("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function Uc({className:e,...t}){return r.jsx(Er,{"data-slot":"dialog-title",className:j("text-lg leading-none font-semibold",e),...t})}function Yc({className:e,...t}){return r.jsx(Nr,{"data-slot":"dialog-description",className:j("text-muted-foreground text-sm",e),...t})}const La=l.forwardRef(({className:e,...t},n)=>r.jsx(re,{ref:n,className:j("flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",e),...t}));La.displayName=re.displayName;const Wc=({children:e,...t})=>r.jsx(Gc,{...t,children:r.jsx(Vc,{className:"overflow-hidden p-0 shadow-lg",children:r.jsx(La,{className:"[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5",children:e})})}),Fa=l.forwardRef(({className:e,...t},n)=>r.jsxs("div",{className:"flex items-center border-b px-3","cmdk-input-wrapper":"",children:[r.jsx(Ue,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),r.jsx(re.Input,{ref:n,className:j("flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",e),...t})]}));Fa.displayName=re.Input.displayName;const $a=l.forwardRef(({className:e,...t},n)=>r.jsx(re.List,{ref:n,className:j("max-h-[300px] overflow-y-auto overflow-x-hidden",e),...t}));$a.displayName=re.List.displayName;const Ba=l.forwardRef(({className:e,...t},n)=>r.jsx(re.Empty,{ref:n,className:j("py-6 text-center text-sm",e),...t}));Ba.displayName=re.Empty.displayName;const Bt=l.forwardRef(({className:e,...t},n)=>r.jsx(re.Group,{ref:n,className:j("overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",e),...t}));Bt.displayName=re.Group.displayName;const Ha=l.forwardRef(({className:e,...t},n)=>r.jsx(re.Separator,{ref:n,className:j("-mx-1 h-px bg-border",e),...t}));Ha.displayName=re.Separator.displayName;const Ht=l.forwardRef(({className:e,...t},n)=>r.jsx(re.Item,{ref:n,className:j("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50",e),...t}));Ht.displayName=re.Item.displayName;const qc=[{id:"dashboard",title:"Dashboard",href:"/dashboard",icon:tr,category:"Core",description:"Main dashboard overview"},{id:"admin-dashboard",title:"Admin Dashboard",href:"/admin/dashboard",icon:Ve,category:"Admin",description:"Administrative dashboard",adminOnly:!0},{id:"search",title:"Search Parts",href:"/search",icon:Ue,category:"Search",description:"Find mobile parts",keywords:["find","lookup","parts"]},{id:"parts-index",title:"Parts Index",href:"/parts",icon:Tt,category:"Search",description:"Browse all parts"},{id:"brands",title:"Brands",href:"/brands",icon:Rt,category:"Search",description:"Browse by brand"},{id:"categories",title:"Categories",href:"/categories",icon:Sn,category:"Search",description:"Browse by category"},{id:"favorites",title:"Favorites",href:"/favorites",icon:er,category:"Activity",description:"Your saved parts"},{id:"search-history",title:"Search History",href:"/search-history",icon:_o,category:"Activity",description:"Recent searches"},{id:"analytics",title:"Analytics",href:"/analytics",icon:Yt,category:"Activity",description:"Usage analytics"},{id:"admin-users",title:"User Management",href:"/admin/users",icon:io,category:"Admin",description:"Manage users",adminOnly:!0},{id:"admin-analytics",title:"Advanced Analytics",href:route("admin.analytics.index"),icon:vo,category:"Admin",description:"System analytics",adminOnly:!0},{id:"admin-search-config",title:"Search Configuration",href:"/admin/search-config",icon:Ue,category:"Admin",description:"Configure search limits and settings",adminOnly:!0},{id:"admin-subscriptions",title:"Subscriptions",href:"/admin/subscriptions",icon:No,category:"Admin",description:"Manage subscriptions",adminOnly:!0},{id:"admin-payments",title:"Payment Gateways",href:"/admin/payment-gateways",icon:Go,category:"Admin",description:"Payment configuration",adminOnly:!0},{id:"admin-parts",title:"Manage Parts",href:"/admin/parts",icon:Tt,category:"Admin",description:"Manage parts database",adminOnly:!0},{id:"admin-categories",title:"Manage Categories",href:"/admin/categories",icon:Sn,category:"Admin",description:"Manage categories",adminOnly:!0},{id:"admin-media",title:"Media Library",href:"/admin/media",icon:ko,category:"Admin",description:"Manage media files",adminOnly:!0},{id:"admin-brands",title:"Manage Brands",href:"/admin/brands",icon:Rt,category:"Admin",description:"Manage brands",adminOnly:!0},{id:"admin-models",title:"Manage Models",href:"/admin/models",icon:mo,category:"Admin",description:"Manage device models",adminOnly:!0},{id:"admin-bulk-import",title:"Bulk Import",href:"/admin/bulk-import",icon:ar,category:"Admin",description:"Import data in bulk",adminOnly:!0},{id:"profile",title:"Profile Settings",href:"/settings/profile",icon:Zn,category:"Settings",description:"Manage your profile"},{id:"password",title:"Password Settings",href:"/settings/password",icon:Io,category:"Settings",description:"Change password"},{id:"two-factor",title:"Two-Factor Auth",href:"/settings/two-factor",icon:Ve,category:"Settings",description:"Security settings"},{id:"appearance",title:"Appearance",href:"/settings/appearance",icon:Ut,category:"Settings",description:"Theme and display"}],Ee=class Ee{constructor(){$e(this,"isOpen",!1);$e(this,"listeners",new Set);$e(this,"keydownHandler",null)}static getInstance(){return Ee.instance||(Ee.instance=new Ee),Ee.instance}subscribe(t){return this.listeners.add(t),()=>this.listeners.delete(t)}toggle(){this.isOpen=!this.isOpen,this.notifyListeners()}close(){this.isOpen=!1,this.notifyListeners()}notifyListeners(){this.listeners.forEach(t=>t(this.isOpen))}initKeyboardListener(){this.keydownHandler||(this.keydownHandler=t=>{t.key==="k"&&(t.metaKey||t.ctrlKey)&&(t.preventDefault(),t.stopPropagation(),this.toggle())},document.addEventListener("keydown",this.keydownHandler,{capture:!0}))}removeKeyboardListener(){this.keydownHandler&&(document.removeEventListener("keydown",this.keydownHandler,{capture:!0}),this.keydownHandler=null)}};$e(Ee,"instance",null);let Ke=Ee;function Xc({isAdmin:e=!1}){const[t,n]=l.useState(!1),[a,s]=l.useState(!1),i=l.useRef(null),u=qc.filter(f=>!f.adminOnly||e).reduce((f,o)=>(f[o.category]||(f[o.category]=[]),f[o.category].push(o),f),{});l.useEffect(()=>{const f=Ke.getInstance(),o=f.subscribe(n);return f.initKeyboardListener(),()=>{o()}},[]),l.useEffect(()=>()=>{},[]);const m=l.useCallback(f=>{i.current&&clearTimeout(i.current),i.current=setTimeout(async()=>{if(!a)try{s(!0),Ke.getInstance().close(),await Gt.visit(f,{onError:g=>{console.error("Navigation error:",g)},onFinish:()=>{s(!1)},onCancel:()=>{s(!1)}})}catch(o){console.error("Search navigation error:",o),s(!1)}},300)},[a]);l.useEffect(()=>()=>{i.current&&clearTimeout(i.current)},[]);const d=f=>{const o=Ke.getInstance();f?o.toggle():o.close()};return r.jsxs(Wc,{open:t,onOpenChange:d,children:[r.jsxs(lo,{children:[r.jsx(Uc,{children:"Global Search"}),r.jsx(Yc,{children:"Search for pages, features, and more using keyboard shortcuts"})]}),r.jsx(Fa,{placeholder:"Search for pages, features, and more..."}),r.jsxs($a,{children:[r.jsx(Ba,{children:"No results found."}),Object.entries(u).map(([f,o])=>r.jsxs("div",{children:[r.jsx(Bt,{heading:f,children:o.map(g=>{var h;return r.jsxs(Ht,{value:`${g.title} ${g.description} ${((h=g.keywords)==null?void 0:h.join(" "))||""}`,onSelect:()=>m(g.href),className:"flex items-center gap-3 p-3",children:[r.jsx(g.icon,{className:"h-4 w-4 text-muted-foreground"}),r.jsxs("div",{className:"flex-1",children:[r.jsx("div",{className:"font-medium",children:g.title}),g.description&&r.jsx("div",{className:"text-sm text-muted-foreground",children:g.description})]}),g.adminOnly&&r.jsx(Pe,{variant:"outline",className:"text-xs",children:"Admin"})]},g.id)})}),r.jsx(Ha,{})]},f)),r.jsx(Bt,{heading:"Tips",children:r.jsxs(Ht,{disabled:!0,className:"text-sm text-muted-foreground",children:[r.jsx(Ue,{className:"h-4 w-4 mr-2"}),"Press ",r.jsx(Pe,{variant:"outline",className:"mx-1 text-xs",children:"⌘K"})," to open search anytime"]})})]})]})}const Kd=Xc,Zc=[{title:"Search Parts",href:"/search",icon:Ue,description:"Find mobile parts quickly",shortcut:"⌘K",category:"Core"},{title:"Dashboard",href:"/dashboard",icon:tr,description:"Go to main dashboard",category:"Core"},{title:"Favorites",href:"/favorites",icon:er,description:"Your saved parts",category:"Core"},{title:"Add New Part",href:"/admin/parts/create",icon:rr,description:"Create a new part entry",adminOnly:!0,category:"Admin"},{title:"Admin Dashboard",href:"/admin/dashboard",icon:Yt,description:"Administrative overview",adminOnly:!0,category:"Admin"},{title:"Manage Parts",href:"/admin/parts",icon:Tt,description:"Manage parts database",adminOnly:!0,category:"Admin"},{title:"Manage Brands",href:"/admin/brands",icon:Rt,description:"Manage brands",adminOnly:!0,category:"Admin"},{title:"Bulk Import",href:"/admin/bulk-import",icon:ar,description:"Import data in bulk",adminOnly:!0,category:"Admin"},{title:"Settings",href:"/settings/profile",icon:Ut,description:"Manage your account",category:"Settings"},{title:"Notifications",href:"/notifications",icon:Jn,description:"View your alerts",category:"Settings"},{title:"Help & Support",href:"/help",icon:Qn,description:"Get assistance",category:"Settings"}];function Vd(){var u,m;const e=ct(),{auth:t}=e.props,n=((u=t.user)==null?void 0:u.email)==="<EMAIL>"||((m=t.user)==null?void 0:m.email)==="<EMAIL>",[a,s]=l.useState(!1),c=Zc.filter(d=>!d.adminOnly||n).reduce((d,f)=>(d[f.category]||(d[f.category]=[]),d[f.category].push(f),d),{});return r.jsx("div",{className:"fixed bottom-6 right-6 z-50 md:hidden",children:r.jsxs(Vl,{open:a,onOpenChange:s,children:[r.jsx(Ul,{asChild:!0,children:r.jsxs(zt,{size:"lg",className:`h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 ${a?"rotate-45":"rotate-0"} bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80`,children:[r.jsx(rr,{className:`h-6 w-6 transition-transform duration-300 ${a?"rotate-45":"rotate-0"}`}),r.jsx("span",{className:"sr-only",children:"Quick Actions"})]})}),r.jsxs(Yl,{align:"end",side:"top",className:"w-72 mb-4 max-h-96 overflow-y-auto",children:[r.jsxs(ot,{className:"flex items-center gap-2 px-3 py-2",children:[r.jsx(po,{className:"h-4 w-4 text-primary"}),r.jsx("span",{className:"font-semibold",children:"Quick Actions"}),n&&r.jsx(Pe,{variant:"outline",className:"ml-auto text-xs bg-gradient-to-r from-orange-50 to-red-50 text-orange-700 border-orange-200 dark:from-orange-950 dark:to-red-950 dark:text-orange-300 dark:border-orange-800",children:"Admin"})]}),r.jsx(ze,{}),Object.entries(c).map(([d,f])=>r.jsxs("div",{children:[r.jsx(ot,{className:"px-3 py-1 text-xs font-medium text-muted-foreground uppercase tracking-wide",children:d}),f.map(o=>r.jsx(ye,{asChild:!0,children:r.jsxs(Ae,{href:o.href,className:"flex items-center gap-3 p-3 cursor-pointer",onClick:()=>s(!1),children:[r.jsx("div",{className:`p-2 rounded-lg ${o.category==="Admin"?"bg-orange-100 dark:bg-orange-900/20":o.category==="Settings"?"bg-purple-100 dark:bg-purple-900/20":"bg-blue-100 dark:bg-blue-900/20"}`,children:r.jsx(o.icon,{className:`h-4 w-4 ${o.category==="Admin"?"text-orange-600 dark:text-orange-400":o.category==="Settings"?"text-purple-600 dark:text-purple-400":"text-blue-600 dark:text-blue-400"}`})}),r.jsxs("div",{className:"flex-1 min-w-0",children:[r.jsx("div",{className:"font-medium truncate",children:o.title}),r.jsx("div",{className:"text-xs text-muted-foreground truncate",children:o.description})]}),o.shortcut&&r.jsx(Pe,{variant:"outline",className:"text-xs font-mono",children:o.shortcut})]})},o.href)),r.jsx(ze,{})]},d)),r.jsx(ot,{className:"px-3 py-2 text-xs text-muted-foreground",children:"💡 Tip: Press ⌘K to search from anywhere"})]})]})})}const Jc=Bn("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-background text-foreground",destructive:"text-destructive-foreground [&>svg]:text-current *:data-[slot=alert-description]:text-destructive-foreground/80"}},defaultVariants:{variant:"default"}});function Qc({className:e,variant:t,...n}){return r.jsx("div",{"data-slot":"alert",role:"alert",className:j(Jc({variant:t}),e),...n})}function ed({className:e,...t}){return r.jsx("div",{"data-slot":"alert-description",className:j("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}function Ud(){const[e,t]=l.useState(null),[n,a]=l.useState(!0);l.useEffect(()=>{fetch("/impersonation/status").then(i=>i.json()).then(i=>{t(i),a(!1)}).catch(()=>{a(!1)})},[]);const s=()=>{Gt.post("/admin/impersonate/end",{},{onSuccess:()=>{t({is_impersonating:!1})},onError:i=>{var c,u;console.error("Failed to end impersonation:",i),i&&((c=i.message)!=null&&c.includes("CSRF")||(u=i.message)!=null&&u.includes("419"))?(console.log("CSRF error detected, refreshing page..."),window.location.reload()):window.location.href="/admin/dashboard"}})};return n||!(e!=null&&e.is_impersonating)?null:r.jsx("div",{className:"fixed top-0 left-0 right-0 z-50 bg-orange-500 text-white shadow-lg",children:r.jsx("div",{className:"container mx-auto px-4 py-2",children:r.jsxs(Qc,{className:"border-orange-600 bg-orange-500 text-white",children:[r.jsx(Ve,{className:"h-4 w-4"}),r.jsxs(ed,{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx(go,{className:"h-4 w-4"}),r.jsx("span",{className:"font-medium",children:"You are currently impersonating a user"}),e.impersonating_user_id&&r.jsxs("span",{className:"text-orange-100",children:["(User ID: ",e.impersonating_user_id,")"]}),e.remaining_minutes!==void 0&&r.jsxs("span",{className:"text-orange-100 text-sm",children:["• ",e.remaining_minutes," min remaining"]})]}),r.jsxs(zt,{onClick:s,variant:"outline",size:"sm",className:"bg-white text-orange-600 hover:bg-orange-50 border-white",children:[r.jsx(nr,{className:"h-4 w-4 mr-2"}),"Return to Admin"]})]})]})})})}export{Vd as $,vo as A,Jn as B,Yt as C,Gc as D,jd as E,No as F,_o as G,er as H,ko as I,Sd as J,Io as K,tr as L,Lo as M,pi as N,Ed as O,rr as P,$d as Q,Qn as R,Md as S,Sn as T,ar as U,Ud as V,Go as W,or as X,Dd as Y,Id as Z,Kd as _,wo as a,Fr as a0,Si as a1,ji as a2,Bd as a3,ql as a4,lc as a5,es as a6,yd as a7,rs as a8,os as a9,as as aa,uc as ab,Ar as ac,Ms as ad,ks as ae,Rs as af,oc as ag,sc as ah,ic as ai,Hd as aj,nr as ak,Vc as b,Gd as c,Uc as d,Yc as e,zd as f,Qc as g,ed as h,Ko as i,kd as j,Vl as k,Ul as l,Rd as m,cc as n,Yl as o,Ld as p,Td as q,_d as r,ot as s,Be as t,an as u,ze as v,ye as w,Cd as x,Nd as y,Fd as z};
