import{j as e,Q as u,t as r}from"./app-J5EqS6dS.js";import{c as p,S as o,B as a}from"./smartphone-GGiwNneF.js";import{C as d,c as l}from"./card-9XCADs-4.js";import{A as N}from"./arrow-left-D4U9AVF9.js";import{S as f}from"./search-DBK6jUoc.js";import{T as v,M as k,I as y}from"./triangle-alert-BW76NKO9.js";import{C as h}from"./clock-Brl7_5s7.js";import{C as g}from"./crown-UDSxMtlm.js";import{S as w}from"./shield-D9nQfigG.js";import{G as S}from"./globe-zfFlVOSX.js";import{C as i}from"./circle-check-big-DOFoatRy.js";/* empty css            *//**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=[["path",{d:"M12 10a2 2 0 0 0-2 2c0 1.02-.1 2.51-.26 4",key:"1nerag"}],["path",{d:"M14 13.12c0 2.38 0 6.38-1 8.88",key:"o46ks0"}],["path",{d:"M17.29 21.02c.12-.6.43-2.3.5-3.02",key:"ptglia"}],["path",{d:"M2 12a10 10 0 0 1 18-6",key:"ydlgp0"}],["path",{d:"M2 16h.01",key:"1gqxmh"}],["path",{d:"M21.8 16c.2-2 .131-5.354 0-6",key:"drycrb"}],["path",{d:"M5 19.5C5.5 18 6 15 6 12a6 6 0 0 1 .34-2",key:"1tidbn"}],["path",{d:"M8.65 22c.21-.66.45-1.32.57-2",key:"13wd9y"}],["path",{d:"M9 6.8a6 6 0 0 1 9 5.2v2",key:"1fr1j5"}]],M=p("Fingerprint",C);function R({message:b,signup_url:x,login_url:c,searches_used:j,search_limit:t,reset_hours:n,tracking_layers:s,suspicious_activity:m}){return e.jsxs(e.Fragment,{children:[e.jsx(u,{title:"Search Limit Exceeded - Mobile Parts Database",children:e.jsx("meta",{name:"description",content:"You have reached your free search limit. Sign up for unlimited access to our comprehensive mobile parts database."})}),e.jsx("nav",{className:"sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200 dark:bg-gray-900/80 dark:border-gray-800",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center h-16",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(o,{className:"h-8 w-8 text-blue-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"FixHaat"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(r,{href:c,children:e.jsx(a,{variant:"ghost",size:"sm",children:"Log in"})}),e.jsx(r,{href:x,children:e.jsx(a,{size:"sm",children:"Sign up"})})]})]})})}),e.jsx("main",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[e.jsxs("div",{className:"mb-8 flex flex-col sm:flex-row gap-3 sm:gap-4",children:[e.jsx(r,{href:route("home"),children:e.jsxs(a,{variant:"ghost",className:"text-blue-600 hover:text-blue-700",children:[e.jsx(N,{className:"w-4 h-4 mr-2"}),"Back to Home"]})}),e.jsx(r,{href:route("home"),children:e.jsxs(a,{variant:"outline",className:"bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 hover:border-blue-300 dark:bg-blue-950/20 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-950/30",children:[e.jsx(f,{className:"w-4 h-4 mr-2"}),"Try New Search"]})})]}),e.jsx(d,{className:"mb-8 border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950/20 rounded-xl",children:e.jsxs(l,{className:"p-8 text-center",children:[e.jsx("div",{className:"flex justify-center mb-6",children:e.jsx("div",{className:"p-4 bg-orange-100 dark:bg-orange-900/30 rounded-full",children:e.jsx(v,{className:"h-12 w-12 text-orange-600 dark:text-orange-400"})})}),e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Search Limit Reached"}),e.jsx("p",{className:"text-lg text-gray-700 dark:text-gray-300 mb-6 max-w-2xl mx-auto",children:b}),e.jsxs("div",{className:"flex justify-center items-center gap-6 mb-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-orange-600 dark:text-orange-400",children:[j,"/",t]}),e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Searches Used"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400 flex items-center justify-center gap-1",children:[e.jsx(h,{className:"h-5 w-5"}),n,"h"]}),e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Reset Time"})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[e.jsx(r,{href:x,children:e.jsxs(a,{size:"lg",className:"bg-blue-600 hover:bg-blue-700 text-white",children:[e.jsx(g,{className:"w-5 h-5 mr-2"}),"Get Unlimited Access"]})}),e.jsx(r,{href:c,children:e.jsx(a,{size:"lg",variant:"outline",className:"border-blue-600 text-blue-600 hover:bg-blue-50",children:"Already have an account? Sign In"})})]})]})}),s&&e.jsx(d,{className:"mb-8 border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20",children:e.jsxs(l,{className:"p-6",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[e.jsx(w,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),e.jsx("h3",{className:"text-lg font-semibold text-blue-900 dark:text-blue-100",children:"Enhanced Security Protection"})]}),e.jsx("p",{className:"text-blue-700 dark:text-blue-300 mb-4 text-sm",children:"Our multi-layer security system tracks usage across different methods to prevent abuse while ensuring fair access for all users."}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[s.ip&&e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg border",children:[e.jsx(S,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"IP Tracking"}),e.jsxs("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:[s.ip.searches_used,"/",t," used"]})]})]}),s.fingerprint&&e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg border",children:[e.jsx(M,{className:"h-4 w-4 text-purple-600 dark:text-purple-400"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Browser ID"}),e.jsxs("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:[s.fingerprint.searches_used,"/",t," used"]})]})]}),s.session&&e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg border",children:[e.jsx(k,{className:"h-4 w-4 text-green-600 dark:text-green-400"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Session"}),e.jsxs("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:[s.session.searches_used,"/",t," used"]})]})]}),s.device&&e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg border",children:[e.jsx(o,{className:"h-4 w-4 text-orange-600 dark:text-orange-400"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Device ID"}),e.jsxs("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:[s.device.searches_used,"/",t," used"]})]})]})]}),m&&m.length>0&&e.jsxs("div",{className:"mt-4 p-3 bg-orange-100 dark:bg-orange-900/30 rounded-lg border border-orange-200 dark:border-orange-800",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(y,{className:"h-4 w-4 text-orange-600 dark:text-orange-400"}),e.jsx("span",{className:"text-sm font-medium text-orange-900 dark:text-orange-100",children:"Security Notice"})]}),e.jsx("p",{className:"text-xs text-orange-700 dark:text-orange-300",children:"Unusual activity patterns detected. This helps us maintain fair access for all users."})]})]})}),e.jsx(d,{className:"mb-8",children:e.jsxs(l,{className:"p-8",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center",children:"Why Upgrade to Premium?"}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"p-2 bg-green-100 dark:bg-green-900/30 rounded-lg",children:e.jsx(i,{className:"h-6 w-6 text-green-600 dark:text-green-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900 dark:text-white mb-2",children:"Unlimited Searches"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Search as much as you want without any daily limits"})]})]}),e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"p-2 bg-green-100 dark:bg-green-900/30 rounded-lg",children:e.jsx(i,{className:"h-6 w-6 text-green-600 dark:text-green-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900 dark:text-white mb-2",children:"Advanced Filters"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Filter by brand, category, compatibility, and more"})]})]}),e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"p-2 bg-green-100 dark:bg-green-900/30 rounded-lg",children:e.jsx(i,{className:"h-6 w-6 text-green-600 dark:text-green-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900 dark:text-white mb-2",children:"Save Favorites"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Save parts to your favorites for quick access later"})]})]}),e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"p-2 bg-green-100 dark:bg-green-900/30 rounded-lg",children:e.jsx(i,{className:"h-6 w-6 text-green-600 dark:text-green-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900 dark:text-white mb-2",children:"Priority Support"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Get priority customer support and faster response times"})]})]})]})]})}),e.jsx(d,{className:"bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800",children:e.jsxs(l,{className:"p-6 text-center",children:[e.jsx("h3",{className:"text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3",children:"Need to search right now?"}),e.jsxs("p",{className:"text-blue-700 dark:text-blue-300 mb-4",children:["Your search limit will reset in ",n," hours, or you can sign up for immediate unlimited access."]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[e.jsx(r,{href:route("home"),children:e.jsxs(a,{variant:"outline",className:"border-blue-600 text-blue-600 hover:bg-blue-100",children:[e.jsx(h,{className:"w-4 h-4 mr-2"}),"Wait for Reset"]})}),e.jsx(r,{href:x,children:e.jsxs(a,{className:"bg-blue-600 hover:bg-blue-700",children:[e.jsx(g,{className:"w-4 h-4 mr-2"}),"Sign Up Now"]})})]})]})})]})})]})}export{R as default};
