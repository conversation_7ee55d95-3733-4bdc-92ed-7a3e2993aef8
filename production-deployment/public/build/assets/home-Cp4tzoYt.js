import{J as v,r,j as e,t as o,Q as P}from"./app-J5EqS6dS.js";import{U as D}from"./unified-search-interface-CjLSucUK.js";import{D as F}from"./DynamicFooter-8iBTp4-u.js";import{B as l,S as A}from"./smartphone-GGiwNneF.js";import{C as u,a as y,c as b,e as N,b as w,d as k}from"./card-9XCADs-4.js";import{B as S}from"./badge-BucYuCBs.js";import{C as E}from"./crown-UDSxMtlm.js";import{Z as C}from"./zap-BcmHRR4K.js";import{C as G}from"./check-C7SdgHPn.js";import{A as _}from"./arrow-right-CCfGNWZ9.js";import{D as z}from"./database-s9JOA0jY.js";import{S as I}from"./shield-D9nQfigG.js";import{U as L}from"./users-RYmOyic9.js";import{C as U}from"./clock-Brl7_5s7.js";import{G as $}from"./globe-zfFlVOSX.js";/* empty css            */import"./input-Bo8dOn9p.js";import"./select-CIhY0l9J.js";import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./category-utils-DblfPn34.js";import"./search-DBK6jUoc.js";import"./map-pin-BdPUntxP.js";import"./hard-drive-BTn_ba7c.js";import"./circle-ButWjt_D.js";import"./package-CoyvngX8.js";import"./building-Dgyml3QN.js";import"./tag-C9-9psxB.js";import"./mail-CDon-vZy.js";function M(){const{auth:t}=v().props,[i,h]=r.useState(null),[d,j]=r.useState(!0),[c,g]=r.useState(null);r.useEffect(()=>{(async()=>{try{const a=await(await fetch("/api/pricing-plans")).json();a.success?h(a.data):g(a.message||"Failed to load pricing plans")}catch(n){g("Failed to load pricing plans"),console.error("Error fetching pricing plans:",n)}finally{j(!1)}})()},[]);const f=s=>{t.user?window.location.href=route("subscription.checkout",{plan:s.name}):window.location.href=route("register")};return d?e.jsx("section",{className:"px-4 py-16 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsx("div",{className:"text-center mb-16",children:e.jsxs("div",{className:"animate-pulse",children:[e.jsx("div",{className:"h-8 bg-gray-300 rounded w-64 mx-auto mb-4"}),e.jsx("div",{className:"h-4 bg-gray-300 rounded w-96 mx-auto"})]})}),e.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto",children:[1,2,3].map(s=>e.jsx("div",{className:"animate-pulse",children:e.jsxs(u,{className:"h-96",children:[e.jsxs(y,{children:[e.jsx("div",{className:"h-6 bg-gray-300 rounded mb-4"}),e.jsx("div",{className:"h-8 bg-gray-300 rounded mb-2"}),e.jsx("div",{className:"h-4 bg-gray-300 rounded"})]}),e.jsx(b,{children:e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"h-4 bg-gray-300 rounded"}),e.jsx("div",{className:"h-4 bg-gray-300 rounded"}),e.jsx("div",{className:"h-4 bg-gray-300 rounded"})]})}),e.jsx(N,{children:e.jsx("div",{className:"h-10 bg-gray-300 rounded w-full"})})]})},s))})]})}):c?e.jsx("section",{className:"px-4 py-16 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:e.jsx("div",{className:"max-w-7xl mx-auto",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Choose Your Plan"}),e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto",children:[e.jsx("p",{className:"text-red-600 mb-4",children:c}),e.jsx(l,{onClick:()=>window.location.reload(),variant:"outline",children:"Try Again"})]})]})})}):!i||i.plans.length===0?null:e.jsx("section",{className:"px-4 py-16 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white sm:text-4xl",children:"Choose Your Plan"}),e.jsx("p",{className:"mt-4 text-lg text-gray-600 dark:text-gray-300",children:"Get access to our comprehensive mobile parts database with the plan that fits your needs"})]}),e.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto",children:i.plans.map(s=>e.jsxs(u,{className:`relative ${s.is_popular?"border-blue-500 shadow-xl scale-105":"border-gray-200 shadow-lg"} hover:shadow-xl transition-all duration-300 bg-white dark:bg-gray-800`,children:[s.is_popular&&e.jsx("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:e.jsxs(S,{className:"bg-blue-500 text-white px-4 py-1",children:[e.jsx(E,{className:"w-4 h-4 mr-1"}),"Most Popular"]})}),e.jsxs(y,{className:"text-center pb-6",children:[e.jsxs(w,{className:"text-2xl font-bold flex items-center justify-center gap-2 text-gray-900 dark:text-white",children:[s.is_popular&&e.jsx(C,{className:"w-6 h-6 text-blue-500"}),s.display_name]}),e.jsxs("div",{className:"mt-4",children:[e.jsx("span",{className:"text-4xl font-bold text-gray-900 dark:text-white",children:s.formatted_price||`$${s.price}`}),!s.formatted_price&&s.price>0&&e.jsxs("span",{className:"text-gray-600 dark:text-gray-400",children:["/",s.interval]})]}),e.jsx(k,{className:"mt-2",children:s.description||(s.search_limit===-1?"Unlimited access for professionals":`Perfect for occasional searches (${s.search_limit} per day)`)})]}),e.jsx(b,{className:"flex-grow",children:e.jsxs("ul",{className:"space-y-3",children:[s.features.slice(0,4).map((n,a)=>e.jsxs("li",{className:"flex items-center gap-3",children:[e.jsx(G,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),e.jsx("span",{className:"text-gray-700 dark:text-gray-300 text-sm",children:n})]},a)),s.features.length>4&&e.jsxs("li",{className:"text-gray-500 dark:text-gray-400 text-sm",children:["+",s.features.length-4," more features"]})]})}),e.jsx(N,{children:e.jsx(l,{className:`w-full ${s.is_popular?"bg-blue-600 hover:bg-blue-700":"bg-gray-900 hover:bg-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600"}`,onClick:()=>f(s),children:t.user?"Upgrade Now":"Get Started"})})]},s.id))}),i.hasMorePlans&&e.jsx("div",{className:"text-center mt-12",children:e.jsx(o,{href:route("pricing"),children:e.jsxs(l,{variant:"outline",size:"lg",className:"bg-white dark:bg-gray-800 border-2 border-blue-600 text-blue-600 hover:bg-blue-50 dark:hover:bg-gray-700",children:["View All Plans",e.jsx(_,{className:"ml-2 h-5 w-5"})]})})}),e.jsx("div",{className:"text-center mt-8",children:e.jsxs("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:["Need help choosing? ",e.jsx(o,{href:"/contact",className:"text-blue-600 hover:underline",children:"Contact our support team"})]})})]})})}function be(){const{auth:t}=v().props,[i,h]=r.useState(""),[d,j]=r.useState(""),[c,g]=r.useState(null),[f,s]=r.useState(!1),n=a=>{a&&!t.user&&fetch(`/guest/search/status?device_id=${a}`).then(x=>x.json()).then(x=>g(x)).catch(()=>{})};return r.useEffect(()=>{const x=(()=>{let p=localStorage.getItem("mobile_parts_device_id");return p||(p="device_"+Date.now()+"_"+Math.random().toString(36).substring(2,11),localStorage.setItem("mobile_parts_device_id",p)),p})();j(x),n(x)},[t.user]),r.useEffect(()=>{const a=()=>{!document.hidden&&d&&n(d)};return document.addEventListener("visibilitychange",a),()=>document.removeEventListener("visibilitychange",a)},[d,t.user]),e.jsxs(e.Fragment,{children:[e.jsxs(P,{title:"Mobile Parts Database - Find Compatible Parts for Any Device",children:[e.jsx("meta",{name:"description",content:"Search our comprehensive database of mobile phone parts. Find compatible components for repairs, replacements, and upgrades. Free search available."}),e.jsx("link",{rel:"preconnect",href:"https://fonts.bunny.net"}),e.jsx("link",{href:"https://fonts.bunny.net/css?family=instrument-sans:400,500,600",rel:"stylesheet"})]}),e.jsx("nav",{className:"sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200 dark:bg-gray-900/80 dark:border-gray-800",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center h-16",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(A,{className:"h-8 w-8 text-blue-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"FixHaat"})]}),e.jsx("div",{className:"flex items-center space-x-4",children:t.user?e.jsx(o,{href:route("dashboard"),children:e.jsx(l,{variant:"outline",size:"sm",children:"Dashboard"})}):e.jsxs(e.Fragment,{children:[e.jsx(o,{href:route("login"),children:e.jsx(l,{variant:"ghost",size:"sm",className:"text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white font-medium",children:"Log in"})}),e.jsx(o,{href:route("register"),children:e.jsx(l,{size:"sm",children:"Sign up"})})]})})]})})}),e.jsxs("main",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[e.jsx("section",{className:"relative px-4 pt-16 pb-12 sm:px-6 lg:px-8 lg:pt-24 lg:pb-16",children:e.jsx("div",{className:"max-w-7xl mx-auto",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("h1",{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl md:text-6xl dark:text-white",children:["Find the Right ",e.jsx("span",{className:"text-blue-600",children:"Mobile Parts"}),e.jsx("span",{className:"text-gray-600 dark:text-gray-300 text-2xl sm:text-3xl md:text-4xl font-normal block mt-2",children:"for Any Device"})]}),e.jsx("p",{className:"mt-6 max-w-2xl mx-auto text-lg text-gray-600 dark:text-gray-300 sm:text-xl",children:"Search our comprehensive database of mobile phone parts. Find compatible components for repairs, replacements, and upgrades with detailed specifications and compatibility information."}),!t.user&&c&&e.jsx("div",{className:"mt-6 max-w-md mx-auto",children:e.jsx(S,{variant:c.has_searched?"destructive":"default",className:"text-sm px-3 py-1",children:c.message})})]})})}),e.jsx("section",{className:"relative px-4 pb-16 sm:px-6 lg:px-8 -mt-8",children:e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsxs(u,{className:"shadow-xl border-0 bg-white/80 backdrop-blur-sm dark:bg-gray-800/80",children:[e.jsxs(y,{className:"text-center pb-6",children:[e.jsx(w,{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Start Your Search"}),e.jsx(k,{className:"text-lg",children:t.user?"Search unlimited parts in our database":"Get 1 free search to explore our database"})]}),e.jsx(b,{className:"space-y-6",children:e.jsx(D,{searchQuery:i,setSearchQuery:h,deviceId:d,isAuthenticated:!!t.user,searchStatus:c,isLoading:f,setIsLoading:s,size:"lg",showSuggestions:!0})})]})})}),e.jsx("section",{className:"px-4 py-16 sm:px-6 lg:px-8 bg-white dark:bg-gray-800",children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white sm:text-4xl",children:"Why Choose FixHaat?"}),e.jsx("p",{className:"mt-4 text-lg text-gray-600 dark:text-gray-300",children:"The most comprehensive mobile parts database for professionals and enthusiasts"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[e.jsx(m,{icon:e.jsx(z,{className:"h-8 w-8 text-blue-600"}),title:"Comprehensive Database",description:"300+ mobile models with detailed parts information and specifications"}),e.jsx(m,{icon:e.jsx(I,{className:"h-8 w-8 text-green-600"}),title:"Verified Compatibility",description:"All part compatibility information is verified and regularly updated"}),e.jsx(m,{icon:e.jsx(C,{className:"h-8 w-8 text-yellow-600"}),title:"Fast Search",description:"Lightning-fast search results with advanced filtering options"}),e.jsx(m,{icon:e.jsx(L,{className:"h-8 w-8 text-purple-600"}),title:"Professional Grade",description:"Trusted by repair shops, technicians, and parts suppliers worldwide"}),e.jsx(m,{icon:e.jsx(U,{className:"h-8 w-8 text-red-600"}),title:"Always Updated",description:"Regular updates with new models and parts as they become available"}),e.jsx(m,{icon:e.jsx($,{className:"h-8 w-8 text-indigo-600"}),title:"Global Coverage",description:"Parts information for devices from all major manufacturers worldwide"})]})]})}),e.jsx(M,{}),!t.user&&e.jsx("section",{className:"px-4 py-16 sm:px-6 lg:px-8 bg-blue-600",children:e.jsxs("div",{className:"max-w-4xl mx-auto text-center",children:[e.jsx("h2",{className:"text-3xl font-bold text-white sm:text-4xl",children:"Ready to Get Started?"}),e.jsx("p",{className:"mt-4 text-xl text-blue-100",children:"Sign up now for unlimited access to our mobile parts database"}),e.jsxs("div",{className:"mt-8 flex flex-col sm:flex-row gap-4 justify-center",children:[e.jsx(o,{href:route("register"),children:e.jsxs(l,{size:"lg",className:"bg-white text-blue-600 hover:bg-gray-100",children:["Start Free Trial",e.jsx(_,{className:"ml-2 h-5 w-5"})]})}),e.jsx(o,{href:route("login"),children:e.jsx(l,{size:"lg",variant:"outline",className:"border-2 border-white text-blue-600 hover:bg-white hover:text-blue-400 font-semibold",children:"Sign In"})})]})]})}),e.jsx(F,{})]})]})}function m({icon:t,title:i,description:h}){return e.jsx(u,{className:"text-center p-6 hover:shadow-lg transition-shadow",children:e.jsxs(b,{className:"space-y-4",children:[e.jsx("div",{className:"flex justify-center",children:t}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:i}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:h})]})})}export{be as default};
