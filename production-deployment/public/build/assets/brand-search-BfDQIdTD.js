import{r as g,j as e,Q as T,t as b,S as N}from"./app-J5EqS6dS.js";import{c as H,B as c}from"./smartphone-GGiwNneF.js";import{C as o,c as d,a as $,b as D,d as Q}from"./card-9XCADs-4.js";import{B as j}from"./badge-BucYuCBs.js";import{U as G}from"./unified-search-interface-CjLSucUK.js";import{A as O,G as Y}from"./app-layout-ox1kAwY6.js";import{A as L}from"./Watermark-BujLnmGI.js";import{A as K}from"./arrow-left-D4U9AVF9.js";import{L as W}from"./list-CNjrM85i.js";import{S as Z}from"./sliders-horizontal-UvqkUz7X.js";import{C as q}from"./chevron-left-C6ZNA5qQ.js";import{a as J,H as A}from"./ImpersonationBanner-CYn5eDk6.js";import{E as F}from"./eye-D-fsmYB2.js";import{P as X}from"./package-CoyvngX8.js";/* empty css            */import"./input-Bo8dOn9p.js";import"./select-CIhY0l9J.js";import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./category-utils-DblfPn34.js";import"./search-DBK6jUoc.js";import"./map-pin-BdPUntxP.js";import"./hard-drive-BTn_ba7c.js";import"./circle-ButWjt_D.js";import"./zap-BcmHRR4K.js";import"./building-Dgyml3QN.js";import"./tag-C9-9psxB.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./crown-UDSxMtlm.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ee=[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]],I=H("Building2",ee);function De({brand:a,filters:h,results:l,applied_filters:p={},search_type:se="all",query:f="",remaining_searches:v}){var _,z;const[M,y]=g.useState(f),[u,C]=g.useState("grid"),[k,B]=g.useState(!1),[P,x]=g.useState(!1);g.useEffect(()=>{y(f)},[f]);const V=(s,t,i)=>{if(!s.trim())return;x(!0);const m=new URLSearchParams({q:s,type:t,...Object.fromEntries(Object.entries({...p,...i}).filter(([,r])=>r!=="all"&&r!==""&&r!==!1&&r!==null&&r!=="null"&&r!==void 0))}),n=route("search.brand",a.slug||a.id)+"?"+m.toString();N.visit(n,{onStart:()=>x(!0),onFinish:()=>x(!1),onError:r=>{console.error("Brand search navigation error:",r),x(!1)},onCancel:()=>x(!1)})},E=()=>e.jsx(G,{searchQuery:M,setSearchQuery:y,isAuthenticated:!0,isLoading:P,setIsLoading:x,showFilters:!0,showSuggestions:!0,size:"lg",placeholder:`Search for ${a.name} parts...`,filters:{categories:h.categories,manufacturers:h.manufacturers,release_years:h.release_years},onCustomSearch:V}),S=s=>{const t=new URLSearchParams(window.location.search),i=new URLSearchParams;for(const[m,n]of t.entries())n!=="all"&&n!==""&&n!=="false"&&n!=="null"&&n!==null&&n!==void 0&&i.set(m,n);i.set("page",s.toString()),N.get(window.location.pathname+"?"+i.toString())},w=(s,t)=>{const i=new URLSearchParams(window.location.search),m=new URLSearchParams;for(const[n,r]of i.entries())r!=="all"&&r!==""&&r!=="false"&&r!=="null"&&r!==null&&r!==void 0&&m.set(n,r);t&&t!=="all"?m.set(s,t):m.delete(s),m.delete("page"),N.get(window.location.pathname+"?"+m.toString())},R=({part:s})=>e.jsxs("div",{className:"relative",children:[e.jsx(o,{className:"hover:shadow-lg transition-shadow",children:e.jsxs(d,{className:"p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-semibold text-lg text-gray-900 mb-1",children:s.name}),s.part_number&&e.jsxs("p",{className:"text-sm text-gray-600 mb-1",children:["Part #: ",s.part_number]}),s.manufacturer&&e.jsxs("p",{className:"text-sm text-gray-600 mb-2",children:["by ",s.manufacturer]})]}),e.jsx(c,{variant:"ghost",size:"sm",children:e.jsx(A,{className:"w-4 h-4"})})]}),e.jsxs("div",{className:"mb-3",children:[e.jsx(j,{variant:"outline",className:"mb-2",children:s.category.name}),s.description&&e.jsx("p",{className:"text-sm text-gray-600 line-clamp-2",children:s.description})]}),s.models.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"text-xs text-gray-500 mb-1",children:"Compatible with:"}),e.jsxs("div",{className:"flex flex-wrap gap-1",children:[s.models.slice(0,3).map(t=>e.jsxs(j,{variant:"secondary",className:"text-xs",children:[t.brand.name," ",t.name]},t.id)),s.models.length>3&&e.jsxs(j,{variant:"secondary",className:"text-xs",children:["+",s.models.length-3," more"]})]})]}),e.jsx("div",{className:"flex justify-between items-center",children:e.jsx(b,{href:route("parts.show",s.slug||s.id),children:e.jsxs(c,{size:"sm",children:[e.jsx(F,{className:"w-4 h-4 mr-2"}),"View Details"]})})})]})}),e.jsx(L,{})]}),U=({part:s})=>e.jsxs("div",{className:"relative",children:[e.jsx(o,{children:e.jsx(d,{className:"p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center",children:e.jsx(X,{className:"w-8 h-8 text-gray-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-semibold text-lg text-gray-900 mb-1",children:s.name}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-600 mb-2",children:[s.part_number&&e.jsxs("span",{children:["Part #: ",s.part_number]}),s.manufacturer&&e.jsxs("span",{children:["by ",s.manufacturer]}),e.jsx(j,{variant:"outline",children:s.category.name})]}),s.description&&e.jsx("p",{className:"text-sm text-gray-600 mb-2 line-clamp-1",children:s.description}),s.models.length>0&&e.jsxs("div",{className:"flex flex-wrap gap-1",children:[s.models.slice(0,5).map(t=>e.jsxs(j,{variant:"secondary",className:"text-xs",children:[t.brand.name," ",t.name]},t.id)),s.models.length>5&&e.jsxs(j,{variant:"secondary",className:"text-xs",children:["+",s.models.length-5," more"]})]})]})]})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(c,{variant:"ghost",size:"sm",children:e.jsx(A,{className:"w-4 h-4"})}),e.jsx(b,{href:route("parts.show",s.slug||s.id),children:e.jsxs(c,{size:"sm",children:[e.jsx(F,{className:"w-4 h-4 mr-2"}),"View Details"]})})]})]})})}),e.jsx(L,{})]});return e.jsxs(O,{children:[e.jsx(T,{title:`Search ${a.name} Parts`}),e.jsx("div",{className:"py-12",children:e.jsxs("div",{className:"max-w-7xl mx-auto sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"flex items-center gap-2 mb-4",children:e.jsx(b,{href:route("brands.show",a.slug||a.id),children:e.jsxs(c,{variant:"ghost",size:"sm",children:[e.jsx(K,{className:"w-4 h-4 mr-2"}),"Back to ",a.name]})})}),e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(I,{className:"w-8 h-8 text-blue-600"}),e.jsxs("h1",{className:"text-3xl font-bold text-gray-900",children:["Search ",a.name," Parts"]})]}),e.jsxs("p",{className:"text-gray-600",children:["Find parts specifically for ",a.name," devices",v!==void 0&&v!==-1&&e.jsxs("span",{className:"ml-2",children:["• ",v," searches remaining today"]})]})]}),e.jsx(o,{className:"mb-6",children:e.jsx(d,{className:"p-6",children:e.jsx(E,{})})}),P?e.jsx(o,{children:e.jsxs(d,{className:"text-center py-12",children:[e.jsx("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Searching..."}),e.jsxs("p",{className:"text-gray-600",children:["Finding ",a.name," parts for you"]})]})}):l?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Search Results"}),e.jsxs("p",{className:"text-gray-600",children:[l.total," ",a.name,' parts found for "',f,'"']})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(c,{variant:u==="grid"?"default":"outline",size:"sm",onClick:()=>C("grid"),children:e.jsx(Y,{className:"w-4 h-4"})}),e.jsx(c,{variant:u==="list"?"default":"outline",size:"sm",onClick:()=>C("list"),children:e.jsx(W,{className:"w-4 h-4"})}),e.jsxs(c,{variant:"outline",size:"sm",onClick:()=>B(!k),children:[e.jsx(Z,{className:"w-4 h-4 mr-2"}),"Filters"]})]})]}),k&&e.jsxs(o,{className:"mb-6",children:[e.jsxs($,{children:[e.jsx(D,{children:"Additional Filters"}),e.jsxs(Q,{children:["Narrow down your search within ",a.name," parts"]})]}),e.jsx(d,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs(Select,{value:p.category_id||"all",onValueChange:s=>w("category_id",s),children:[e.jsx(SelectTrigger,{children:e.jsx(SelectValue,{placeholder:"Category"})}),e.jsxs(SelectContent,{children:[e.jsx(SelectItem,{value:"all",children:"All Categories"}),h.categories.map(s=>e.jsx(SelectItem,{value:s.id.toString(),children:s.name},s.id))]})]}),e.jsxs(Select,{value:p.manufacturer||"all",onValueChange:s=>w("manufacturer",s),children:[e.jsx(SelectTrigger,{children:e.jsx(SelectValue,{placeholder:"Manufacturer"})}),e.jsxs(SelectContent,{children:[e.jsx(SelectItem,{value:"all",children:"All Manufacturers"}),(_=h.manufacturers)==null?void 0:_.filter(s=>s&&s.trim()!=="").map(s=>e.jsx(SelectItem,{value:s,children:s},s))]})]}),e.jsxs(Select,{value:p.release_year||"all",onValueChange:s=>w("release_year",s),children:[e.jsx(SelectTrigger,{children:e.jsx(SelectValue,{placeholder:"Year"})}),e.jsxs(SelectContent,{children:[e.jsx(SelectItem,{value:"all",children:"All Years"}),(z=h.release_years)==null?void 0:z.filter(s=>s&&s.toString().trim()!=="").map(s=>e.jsx(SelectItem,{value:s.toString(),children:s},s))]})]})]})})]}),l.data.length>0?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:u==="grid"?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8":"space-y-4 mb-8",children:l.data.map(s=>u==="grid"?e.jsx(R,{part:s},s.id):e.jsx(U,{part:s},s.id))}),l.last_page>1&&e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm text-gray-600",children:["Showing ",l.from," to ",l.to," of ",l.total," results"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(c,{variant:"outline",size:"sm",onClick:()=>S(l.current_page-1),disabled:l.current_page===1,children:[e.jsx(q,{className:"w-4 h-4"}),"Previous"]}),Array.from({length:Math.min(5,l.last_page)},(s,t)=>{const i=t+1;return e.jsx(c,{variant:i===l.current_page?"default":"outline",size:"sm",onClick:()=>S(i),children:i},i)}),e.jsxs(c,{variant:"outline",size:"sm",onClick:()=>S(l.current_page+1),disabled:l.current_page===l.last_page,children:["Next",e.jsx(J,{className:"w-4 h-4"})]})]})]})]}):e.jsx(o,{children:e.jsxs(d,{className:"text-center py-12",children:[e.jsx(Search,{className:"w-16 h-16 mx-auto mb-4 text-gray-300"}),e.jsxs("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:["No ",a.name," parts found"]}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Try adjusting your search terms or filters"}),e.jsxs(c,{onClick:()=>{y(""),setSearchType("all"),setIsSearching(!1),setShowSuggestions(!1),setSuggestions([]),N.get(route("search.brand",a.slug||a.id))},children:[e.jsx(Search,{className:"w-4 h-4 mr-2"}),"Clear Search"]})]})})]}):e.jsx(o,{children:e.jsxs(d,{className:"text-center py-12",children:[e.jsx(I,{className:"w-16 h-16 mx-auto mb-4 text-blue-600"}),e.jsxs("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:["Search ",a.name," Parts"]}),e.jsx("p",{className:"text-gray-600 mb-6",children:a.description||`Find parts specifically for ${a.name} devices`}),e.jsxs("p",{className:"text-sm text-gray-500",children:["Enter a search term above to find ",a.name," parts"]})]})})]})})]},`brand-search-${a.id}`)}export{De as default};
