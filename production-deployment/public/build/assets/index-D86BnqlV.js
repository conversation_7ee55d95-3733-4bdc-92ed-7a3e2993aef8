import{r as u,j as m,b as p}from"./app-J5EqS6dS.js";function _(t,c,{checkForDefaultPrevented:o=!0}={}){return function(n){if(t==null||t(n),o===!1||!n.defaultPrevented)return c==null?void 0:c(n)}}function E(t,c){const o=u.createContext(c),i=s=>{const{children:e,...r}=s,f=u.useMemo(()=>r,Object.values(r));return m.jsx(o.Provider,{value:f,children:e})};i.displayName=t+"Provider";function n(s){const e=u.useContext(o);if(e)return e;if(c!==void 0)return c;throw new Error(`\`${s}\` must be used within \`${t}\``)}return[i,n]}function R(t,c=[]){let o=[];function i(s,e){const r=u.createContext(e),f=o.length;o=[...o,e];const x=d=>{var S;const{scope:l,children:h,...b}=d,v=((S=l==null?void 0:l[t])==null?void 0:S[f])||r,C=u.useMemo(()=>b,Object.values(b));return m.jsx(v.Provider,{value:C,children:h})};x.displayName=s+"Provider";function a(d,l){var v;const h=((v=l==null?void 0:l[t])==null?void 0:v[f])||r,b=u.useContext(h);if(b)return b;if(e!==void 0)return e;throw new Error(`\`${d}\` must be used within \`${s}\``)}return[x,a]}const n=()=>{const s=o.map(e=>u.createContext(e));return function(r){const f=(r==null?void 0:r[t])||s;return u.useMemo(()=>({[`__scope${t}`]:{...r,[t]:f}}),[r,f])}};return n.scopeName=t,[i,y(n,...c)]}function y(...t){const c=t[0];if(t.length===1)return c;const o=()=>{const i=t.map(n=>({useScope:n(),scopeName:n.scopeName}));return function(s){const e=i.reduce((r,{useScope:f,scopeName:x})=>{const d=f(s)[`__scope${x}`];return{...r,...d}},{});return u.useMemo(()=>({[`__scope${c.scopeName}`]:e}),[e])}};return o.scopeName=c.scopeName,o}var w=globalThis!=null&&globalThis.document?u.useLayoutEffect:()=>{};function g(t){const[c,o]=u.useState(void 0);return w(()=>{if(t){o({width:t.offsetWidth,height:t.offsetHeight});const i=new ResizeObserver(n=>{if(!Array.isArray(n)||!n.length)return;const s=n[0];let e,r;if("borderBoxSize"in s){const f=s.borderBoxSize,x=Array.isArray(f)?f[0]:f;e=x.inlineSize,r=x.blockSize}else e=t.offsetWidth,r=t.offsetHeight;o({width:e,height:r})});return i.observe(t,{box:"border-box"}),()=>i.unobserve(t)}else o(void 0)},[t]),c}var z=p[" useInsertionEffect ".trim().toString()]||w;function A({prop:t,defaultProp:c,onChange:o=()=>{},caller:i}){const[n,s,e]=P({defaultProp:c,onChange:o}),r=t!==void 0,f=r?t:n;{const a=u.useRef(t!==void 0);u.useEffect(()=>{const d=a.current;d!==r&&console.warn(`${i} is changing from ${d?"controlled":"uncontrolled"} to ${r?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),a.current=r},[r,i])}const x=u.useCallback(a=>{var d;if(r){const l=$(a)?a(t):a;l!==t&&((d=e.current)==null||d.call(e,l))}else s(a)},[r,t,s,e]);return[f,x]}function P({defaultProp:t,onChange:c}){const[o,i]=u.useState(t),n=u.useRef(o),s=u.useRef(c);return z(()=>{s.current=c},[c]),u.useEffect(()=>{var e;n.current!==o&&((e=s.current)==null||e.call(s,o),n.current=o)},[o,n]),[o,i,s]}function $(t){return typeof t=="function"}export{_ as a,w as b,R as c,E as d,g as e,A as u};
