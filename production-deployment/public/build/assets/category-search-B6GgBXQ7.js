import{r as f,j as e,Q as K,t as C,S as b}from"./app-J5EqS6dS.js";import{B as n}from"./smartphone-GGiwNneF.js";import{C as d,c as x,a as W,b as J,d as X}from"./card-9XCADs-4.js";import{B as j}from"./badge-BucYuCBs.js";import{S as L,a as P,b as _,c as z,d as g}from"./select-CIhY0l9J.js";import{U as Z}from"./unified-search-interface-CjLSucUK.js";import{A as q,G as ee}from"./app-layout-ox1kAwY6.js";import{g as R,b as U}from"./category-utils-DblfPn34.js";import{A as V}from"./Watermark-BujLnmGI.js";import{A as se}from"./arrow-left-D4U9AVF9.js";import{T as B}from"./tag-C9-9psxB.js";import{L as ae}from"./list-CNjrM85i.js";import{S as re}from"./sliders-horizontal-UvqkUz7X.js";import{C as te}from"./chevron-left-C6ZNA5qQ.js";import{a as ie,H as $}from"./ImpersonationBanner-CYn5eDk6.js";import{S as M}from"./search-DBK6jUoc.js";import{E as T}from"./eye-D-fsmYB2.js";import{P as le}from"./package-CoyvngX8.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./input-Bo8dOn9p.js";import"./building-Dgyml3QN.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./map-pin-BdPUntxP.js";import"./hard-drive-BTn_ba7c.js";import"./circle-ButWjt_D.js";import"./crown-UDSxMtlm.js";function Ke({category:r,filters:o,results:i,applied_filters:p={},query:u="",remaining_searches:w}){var E,I;const[H,v]=f.useState(u),[N,A]=f.useState("grid"),[F,D]=f.useState(!1),[k,h]=f.useState(!1);f.useEffect(()=>{v(u)},[u]);const Q=(s,a,l)=>{if(!s.trim())return;h(!0);const m=new URLSearchParams({q:s,type:a,...Object.fromEntries(Object.entries({...p,...l}).filter(([,t])=>t!=="all"&&t!==""&&t!==!1&&t!==null&&t!=="null"&&t!==void 0))}),c=route("search.category",r.slug||r.id)+"?"+m.toString();b.visit(c,{onStart:()=>h(!0),onFinish:()=>h(!1),onError:t=>{console.error("Category search navigation error:",t),h(!1)},onCancel:()=>h(!1)})},G=()=>e.jsx(Z,{searchQuery:H,setSearchQuery:v,isAuthenticated:!0,isLoading:k,setIsLoading:h,showFilters:!0,showSuggestions:!0,size:"lg",placeholder:`Search for ${r.name.toLowerCase()} parts...`,filters:{categories:o.categories,brands:o.brands,manufacturers:o.manufacturers,release_years:o.release_years},onCustomSearch:Q}),y=s=>{const a=new URLSearchParams(window.location.search),l=new URLSearchParams;for(const[m,c]of a.entries())c!=="all"&&c!==""&&c!=="false"&&c!=="null"&&c!==null&&c!==void 0&&l.set(m,c);l.set("page",s.toString()),b.get(window.location.pathname+"?"+l.toString())},S=(s,a)=>{const l=new URLSearchParams(window.location.search),m=new URLSearchParams;for(const[c,t]of l.entries())t!=="all"&&t!==""&&t!=="false"&&t!=="null"&&t!==null&&t!==void 0&&m.set(c,t);a&&a!=="all"?m.set(s,a):m.delete(s),m.delete("page"),b.get(window.location.pathname+"?"+m.toString())},O=({part:s})=>e.jsxs("div",{className:"relative",children:[e.jsx(d,{className:"hover:shadow-lg transition-shadow",children:e.jsxs(x,{className:"p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-semibold text-lg text-gray-900 mb-1",children:s.name}),s.part_number&&e.jsxs("p",{className:"text-sm text-gray-600 mb-1",children:["Part #: ",s.part_number]}),s.manufacturer&&e.jsxs("p",{className:"text-sm text-gray-600 mb-2",children:["by ",s.manufacturer]})]}),e.jsx(n,{variant:"ghost",size:"sm",children:e.jsx($,{className:"w-4 h-4"})})]}),e.jsxs("div",{className:"mb-3",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[(()=>{const a=R(s.category.name);return e.jsx(a,{className:"w-4 h-4 text-gray-600"})})(),e.jsx(j,{variant:"outline",className:`${U(s.category.name,"badge")} font-medium border-2`,children:s.category.name})]}),s.description&&e.jsx("p",{className:"text-sm text-gray-600 line-clamp-2",children:s.description})]}),s.models.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"text-xs text-gray-500 mb-1",children:"Compatible with:"}),e.jsxs("div",{className:"flex flex-wrap gap-1",children:[s.models.slice(0,3).map(a=>e.jsxs(j,{variant:"secondary",className:"text-xs",children:[a.brand.name," ",a.name]},a.id)),s.models.length>3&&e.jsxs(j,{variant:"secondary",className:"text-xs",children:["+",s.models.length-3," more"]})]})]}),e.jsx("div",{className:"flex justify-between items-center",children:e.jsx(C,{href:route("parts.show",s.slug||s.id),children:e.jsxs(n,{size:"sm",children:[e.jsx(T,{className:"w-4 h-4 mr-2"}),"View Details"]})})})]})}),e.jsx(V,{})]}),Y=({part:s})=>e.jsxs("div",{className:"relative",children:[e.jsx(d,{children:e.jsx(x,{className:"p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center",children:e.jsx(le,{className:"w-8 h-8 text-gray-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-semibold text-lg text-gray-900 mb-1",children:s.name}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-600 mb-2",children:[s.part_number&&e.jsxs("span",{children:["Part #: ",s.part_number]}),s.manufacturer&&e.jsxs("span",{children:["by ",s.manufacturer]}),e.jsxs("div",{className:"flex items-center gap-1",children:[(()=>{const a=R(s.category.name);return e.jsx(a,{className:"w-3 h-3 text-gray-500"})})(),e.jsx(j,{variant:"outline",className:`${U(s.category.name,"badge")} text-xs font-medium border`,children:s.category.name})]})]}),s.description&&e.jsx("p",{className:"text-sm text-gray-600 mb-2 line-clamp-1",children:s.description}),s.models.length>0&&e.jsxs("div",{className:"flex flex-wrap gap-1",children:[s.models.slice(0,5).map(a=>e.jsxs(j,{variant:"secondary",className:"text-xs",children:[a.brand.name," ",a.name]},a.id)),s.models.length>5&&e.jsxs(j,{variant:"secondary",className:"text-xs",children:["+",s.models.length-5," more"]})]})]})]})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(n,{variant:"ghost",size:"sm",children:e.jsx($,{className:"w-4 h-4"})}),e.jsx(C,{href:route("parts.show",s.slug||s.id),children:e.jsxs(n,{size:"sm",children:[e.jsx(T,{className:"w-4 h-4 mr-2"}),"View Details"]})})]})]})})}),e.jsx(V,{})]});return e.jsxs(q,{children:[e.jsx(K,{title:`Search ${r.name} Parts`}),e.jsx("div",{className:"py-12",children:e.jsxs("div",{className:"max-w-7xl mx-auto sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"flex items-center gap-2 mb-4",children:e.jsx(C,{href:route("categories.show",r.slug||r.id),children:e.jsxs(n,{variant:"ghost",size:"sm",children:[e.jsx(se,{className:"w-4 h-4 mr-2"}),"Back to ",r.name]})})}),e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(B,{className:"w-8 h-8 text-blue-600"}),e.jsxs("h1",{className:"text-3xl font-bold text-gray-900",children:["Search ",r.name," Parts"]})]}),e.jsxs("p",{className:"text-gray-600",children:["Find parts specifically in the ",r.name," category",w!==void 0&&w!==-1&&e.jsxs("span",{className:"ml-2",children:["• ",w," searches remaining today"]})]})]}),e.jsx(d,{className:"mb-6",children:e.jsx(x,{className:"p-6",children:e.jsx(G,{})})}),k?e.jsx(d,{children:e.jsxs(x,{className:"text-center py-12",children:[e.jsx("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Searching..."}),e.jsxs("p",{className:"text-gray-600",children:["Finding ",r.name.toLowerCase()," parts for you"]})]})}):i?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Search Results"}),e.jsxs("p",{className:"text-gray-600",children:[i.total," ",r.name.toLowerCase(),' parts found for "',u,'"']})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(n,{variant:N==="grid"?"default":"outline",size:"sm",onClick:()=>A("grid"),children:e.jsx(ee,{className:"w-4 h-4"})}),e.jsx(n,{variant:N==="list"?"default":"outline",size:"sm",onClick:()=>A("list"),children:e.jsx(ae,{className:"w-4 h-4"})}),e.jsxs(n,{variant:"outline",size:"sm",onClick:()=>D(!F),children:[e.jsx(re,{className:"w-4 h-4 mr-2"}),"Filters"]})]})]}),F&&e.jsxs(d,{className:"mb-6",children:[e.jsxs(W,{children:[e.jsx(J,{children:"Additional Filters"}),e.jsxs(X,{children:["Narrow down your search within ",r.name]})]}),e.jsx(x,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs(L,{value:p.brand_id||"all",onValueChange:s=>S("brand_id",s),children:[e.jsx(P,{children:e.jsx(_,{placeholder:"Brand"})}),e.jsxs(z,{children:[e.jsx(g,{value:"all",children:"All Brands"}),o.brands.map(s=>e.jsx(g,{value:s.id.toString(),children:s.name},s.id))]})]}),e.jsxs(L,{value:p.manufacturer||"all",onValueChange:s=>S("manufacturer",s),children:[e.jsx(P,{children:e.jsx(_,{placeholder:"Manufacturer"})}),e.jsxs(z,{children:[e.jsx(g,{value:"all",children:"All Manufacturers"}),(E=o.manufacturers)==null?void 0:E.filter(s=>s&&s.trim()!=="").map(s=>e.jsx(g,{value:s,children:s},s))]})]}),e.jsxs(L,{value:p.release_year||"all",onValueChange:s=>S("release_year",s),children:[e.jsx(P,{children:e.jsx(_,{placeholder:"Year"})}),e.jsxs(z,{children:[e.jsx(g,{value:"all",children:"All Years"}),(I=o.release_years)==null?void 0:I.filter(s=>s&&s.toString().trim()!=="").map(s=>e.jsx(g,{value:s.toString(),children:s},s))]})]})]})})]}),i.data.length>0?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:N==="grid"?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8":"space-y-4 mb-8",children:i.data.map(s=>N==="grid"?e.jsx(O,{part:s},s.id):e.jsx(Y,{part:s},s.id))}),i.last_page>1&&e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm text-gray-600",children:["Showing ",i.from," to ",i.to," of ",i.total," results"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(n,{variant:"outline",size:"sm",onClick:()=>y(i.current_page-1),disabled:i.current_page===1,children:[e.jsx(te,{className:"w-4 h-4"}),"Previous"]}),Array.from({length:Math.min(5,i.last_page)},(s,a)=>{const l=a+1;return e.jsx(n,{variant:l===i.current_page?"default":"outline",size:"sm",onClick:()=>y(l),children:l},l)}),e.jsxs(n,{variant:"outline",size:"sm",onClick:()=>y(i.current_page+1),disabled:i.current_page===i.last_page,children:["Next",e.jsx(ie,{className:"w-4 h-4"})]})]})]})]}):e.jsx(d,{children:e.jsxs(x,{className:"text-center py-12",children:[e.jsx(M,{className:"w-16 h-16 mx-auto mb-4 text-gray-300"}),e.jsxs("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:["No ",r.name.toLowerCase()," parts found"]}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Try adjusting your search terms or filters"}),e.jsxs(n,{onClick:()=>{v(""),b.get(route("search.category",r.slug||r.id))},children:[e.jsx(M,{className:"w-4 h-4 mr-2"}),"Clear Search"]})]})})]}):e.jsx(d,{children:e.jsxs(x,{className:"text-center py-12",children:[e.jsx(B,{className:"w-16 h-16 mx-auto mb-4 text-blue-600"}),e.jsxs("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:["Search ",r.name," Parts"]}),e.jsx("p",{className:"text-gray-600 mb-6",children:r.description||`Find parts specifically in the ${r.name} category`}),e.jsxs("p",{className:"text-sm text-gray-500",children:["Enter a search term above to find ",r.name.toLowerCase()," parts"]})]})})]})})]},`category-search-${r.id}`)}export{Ke as default};
