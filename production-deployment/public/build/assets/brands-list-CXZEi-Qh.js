import{r as h,j as e,Q as f,t as j}from"./app-J5EqS6dS.js";import{S as r,B as t}from"./smartphone-GGiwNneF.js";import{C as l,c as i}from"./card-9XCADs-4.js";import{B as c}from"./badge-BucYuCBs.js";import{I as N}from"./input-Bo8dOn9p.js";import{A as y,G as v}from"./app-layout-ox1kAwY6.js";import{S as m}from"./search-DBK6jUoc.js";import{L as w}from"./list-CNjrM85i.js";import{A as C}from"./arrow-right-CCfGNWZ9.js";/* empty css            */import"./ImpersonationBanner-CYn5eDk6.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./crown-UDSxMtlm.js";import"./shield-D9nQfigG.js";import"./user-DCnDRzMf.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./eye-D-fsmYB2.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";function Y({brands:p}){const[o,x]=h.useState(""),[a,d]=h.useState("grid"),n=p.filter(s=>s.name.toLowerCase().includes(o.toLowerCase())||s.country&&s.country.toLowerCase().includes(o.toLowerCase())),g=({brand:s})=>e.jsx(l,{className:"hover:shadow-lg transition-shadow group",children:e.jsxs(i,{className:"p-6",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center",children:s.logo_url?e.jsx("img",{src:s.logo_url,alt:s.name,className:"w-8 h-8 object-contain"}):e.jsx(r,{className:"w-6 h-6 text-orange-600"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-lg text-gray-900 mb-1",children:s.name}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(c,{variant:"outline",className:"text-xs",children:[s.models_count," models"]}),s.country&&e.jsx(c,{variant:"secondary",className:"text-xs",children:s.country})]})]})]}),e.jsx(C,{className:"w-5 h-5 text-gray-400 group-hover:text-orange-600 transition-colors"})]}),e.jsx(j,{href:route("search.brand",s.slug||s.id),children:e.jsxs(t,{className:"w-full",children:[e.jsx(m,{className:"w-4 h-4 mr-2"}),"Search ",s.name," Parts"]})})]})}),u=({brand:s})=>e.jsx(l,{className:"hover:shadow-md transition-shadow",children:e.jsx(i,{className:"p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center",children:s.logo_url?e.jsx("img",{src:s.logo_url,alt:s.name,className:"w-6 h-6 object-contain"}):e.jsx(r,{className:"w-5 h-5 text-orange-600"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-semibold text-gray-900 mb-1",children:s.name}),e.jsxs("div",{className:"flex items-center gap-3 text-sm text-gray-600",children:[e.jsxs(c,{variant:"outline",className:"text-xs",children:[s.models_count," models"]}),s.country&&e.jsx(c,{variant:"secondary",className:"text-xs",children:s.country})]})]})]}),e.jsx(j,{href:route("search.brand",s.slug||s.id),children:e.jsxs(t,{children:[e.jsx(m,{className:"w-4 h-4 mr-2"}),"Search"]})})]})})});return e.jsxs(y,{children:[e.jsx(f,{title:"Search in Brand"}),e.jsx("div",{className:"py-12",children:e.jsxs("div",{className:"max-w-7xl mx-auto sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx(r,{className:"w-8 h-8 text-orange-600"}),e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Search in Brand"})]}),e.jsx("p",{className:"text-gray-600 text-lg",children:"Select a brand to search for parts from that specific manufacturer"})]}),e.jsx(l,{className:"mb-6",children:e.jsx(i,{className:"p-6",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4 items-center justify-between",children:[e.jsxs("div",{className:"flex-1 relative",children:[e.jsx(m,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),e.jsx(N,{type:"text",placeholder:"Search brands...",value:o,onChange:s=>x(s.target.value),className:"pl-10"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(t,{variant:a==="grid"?"default":"outline",size:"sm",onClick:()=>d("grid"),children:e.jsx(v,{className:"w-4 h-4"})}),e.jsx(t,{variant:a==="list"?"default":"outline",size:"sm",onClick:()=>d("list"),children:e.jsx(w,{className:"w-4 h-4"})})]})]})})}),e.jsx("div",{className:"flex items-center justify-between mb-6",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Available Brands"}),e.jsxs("p",{className:"text-gray-600",children:[n.length," brands found"]})]})}),n.length>0?e.jsx("div",{className:a==="grid"?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:n.map(s=>a==="grid"?e.jsx(g,{brand:s},s.id):e.jsx(u,{brand:s},s.id))}):e.jsx(l,{children:e.jsxs(i,{className:"text-center py-12",children:[e.jsx(r,{className:"w-16 h-16 mx-auto mb-4 text-gray-300"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No brands found"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Try adjusting your search terms"}),e.jsxs(t,{onClick:()=>x(""),children:[e.jsx(m,{className:"w-4 h-4 mr-2"}),"Clear Search"]})]})})]})})]})}export{Y as default};
