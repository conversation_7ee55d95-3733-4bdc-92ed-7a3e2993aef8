import{j as e,r as i,J as ie,t as L}from"./app-J5EqS6dS.js";import{a5 as Oe,a6 as ce,a7 as le,M as ue,a8 as de,a9 as fe,aa as ve,ab as Ke,L as Ve,z as me,ac as ze,ad as $e,ae as Ge,af as Be,k as Ue,l as He,ag as We,ah as Ye,ai as Qe,o as Ze,p as qe,Q as Je,N as Xe,V as et,Y as tt,Z as nt,_ as ot,$ as rt}from"./ImpersonationBanner-CYn5eDk6.js";import{c as he,a as S,u as O,h as at,d as st,B as F}from"./smartphone-GGiwNneF.js";import{P as I,d as ne,R as it}from"./index-CJpBU2i9.js";import{u as xe,c as ct,a as M,b as B}from"./index-D86BnqlV.js";import{u as lt,b as ge,a as pe,d as P,D as ut,i as dt}from"./users-RYmOyic9.js";import{P as $}from"./index-BzZWUWqx.js";import{u as ft}from"./index-Ba8m9N9L.js";import{S as Ne}from"./search-DBK6jUoc.js";import{D as vt}from"./DynamicFooter-8iBTp4-u.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mt=[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]],ht=he("BookOpen",mt);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xt=[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]],gt=he("Folder",xt);function K({iconNode:t,className:o,...a}){return e.jsx(t,{className:S("h-4 w-4",o),...a})}var D="NavigationMenu",[Z,we,pt]=ge(D),[U,Nt,wt]=ge(D),[q,on]=ct(D,[pt,wt]),[bt,E]=q(D),[jt,yt]=q(D),be=i.forwardRef((t,o)=>{const{__scopeNavigationMenu:a,value:r,onValueChange:n,defaultValue:s,delayDuration:l=200,skipDelayDuration:v=300,orientation:f="horizontal",dir:N,...d}=t,[x,j]=i.useState(null),y=O(o,p=>j(p)),g=lt(N),h=i.useRef(0),w=i.useRef(0),C=i.useRef(0),[_,m]=i.useState(!0),[c,u]=xe({prop:r,onChange:p=>{const k=p!=="",G=v>0;k?(window.clearTimeout(C.current),G&&m(!1)):(window.clearTimeout(C.current),C.current=window.setTimeout(()=>m(!0),v)),n==null||n(p)},defaultProp:s??"",caller:D}),b=i.useCallback(()=>{window.clearTimeout(w.current),w.current=window.setTimeout(()=>u(""),150)},[u]),R=i.useCallback(p=>{window.clearTimeout(w.current),u(p)},[u]),T=i.useCallback(p=>{c===p?window.clearTimeout(w.current):h.current=window.setTimeout(()=>{window.clearTimeout(w.current),u(p)},l)},[c,u,l]);return i.useEffect(()=>()=>{window.clearTimeout(h.current),window.clearTimeout(w.current),window.clearTimeout(C.current)},[]),e.jsx(je,{scope:a,isRootMenu:!0,value:c,dir:g,orientation:f,rootNavigationMenu:x,onTriggerEnter:p=>{window.clearTimeout(h.current),_?T(p):R(p)},onTriggerLeave:()=>{window.clearTimeout(h.current),b()},onContentEnter:()=>window.clearTimeout(w.current),onContentLeave:b,onItemSelect:p=>{u(k=>k===p?"":p)},onItemDismiss:()=>u(""),children:e.jsx(I.nav,{"aria-label":"Main","data-orientation":f,dir:g,...d,ref:y})})});be.displayName=D;var H="NavigationMenuSub",Ct=i.forwardRef((t,o)=>{const{__scopeNavigationMenu:a,value:r,onValueChange:n,defaultValue:s,orientation:l="horizontal",...v}=t,f=E(H,a),[N,d]=xe({prop:r,onChange:n,defaultProp:s??"",caller:H});return e.jsx(je,{scope:a,isRootMenu:!1,value:N,dir:f.dir,orientation:l,rootNavigationMenu:f.rootNavigationMenu,onTriggerEnter:x=>d(x),onItemSelect:x=>d(x),onItemDismiss:()=>d(""),children:e.jsx(I.div,{"data-orientation":l,...v,ref:o})})});Ct.displayName=H;var je=t=>{const{scope:o,isRootMenu:a,rootNavigationMenu:r,dir:n,orientation:s,children:l,value:v,onItemSelect:f,onItemDismiss:N,onTriggerEnter:d,onTriggerLeave:x,onContentEnter:j,onContentLeave:y}=t,[g,h]=i.useState(null),[w,C]=i.useState(new Map),[_,m]=i.useState(null);return e.jsx(bt,{scope:o,isRootMenu:a,rootNavigationMenu:r,value:v,previousValue:ft(v),baseId:pe(),dir:n,orientation:s,viewport:g,onViewportChange:h,indicatorTrack:_,onIndicatorTrackChange:m,onTriggerEnter:P(d),onTriggerLeave:P(x),onContentEnter:P(j),onContentLeave:P(y),onItemSelect:P(f),onItemDismiss:P(N),onViewportContentChange:i.useCallback((c,u)=>{C(b=>(b.set(c,u),new Map(b)))},[]),onViewportContentRemove:i.useCallback(c=>{C(u=>u.has(c)?(u.delete(c),new Map(u)):u)},[]),children:e.jsx(Z.Provider,{scope:o,children:e.jsx(jt,{scope:o,items:w,children:l})})})},ye="NavigationMenuList",Ce=i.forwardRef((t,o)=>{const{__scopeNavigationMenu:a,...r}=t,n=E(ye,a),s=e.jsx(I.ul,{"data-orientation":n.orientation,...r,ref:o});return e.jsx(I.div,{style:{position:"relative"},ref:n.onIndicatorTrackChange,children:e.jsx(Z.Slot,{scope:a,children:n.isRootMenu?e.jsx(Te,{asChild:!0,children:s}):s})})});Ce.displayName=ye;var Me="NavigationMenuItem",[Mt,Re]=q(Me),Ee=i.forwardRef((t,o)=>{const{__scopeNavigationMenu:a,value:r,...n}=t,s=pe(),l=r||s||"LEGACY_REACT_AUTO_VALUE",v=i.useRef(null),f=i.useRef(null),N=i.useRef(null),d=i.useRef(()=>{}),x=i.useRef(!1),j=i.useCallback((g="start")=>{if(v.current){d.current();const h=Y(v.current);h.length&&ee(g==="start"?h:h.reverse())}},[]),y=i.useCallback(()=>{if(v.current){const g=Y(v.current);g.length&&(d.current=Lt(g))}},[]);return e.jsx(Mt,{scope:a,value:l,triggerRef:f,contentRef:v,focusProxyRef:N,wasEscapeCloseRef:x,onEntryKeyDown:j,onFocusProxyEnter:j,onRootContentClose:y,onContentFocusOutside:y,children:e.jsx(I.li,{...n,ref:o})})});Ee.displayName=Me;var W="NavigationMenuTrigger",Rt=i.forwardRef((t,o)=>{const{__scopeNavigationMenu:a,disabled:r,...n}=t,s=E(W,t.__scopeNavigationMenu),l=Re(W,t.__scopeNavigationMenu),v=i.useRef(null),f=O(v,l.triggerRef,o),N=Pe(s.baseId,l.value),d=Se(s.baseId,l.value),x=i.useRef(!1),j=i.useRef(!1),y=l.value===s.value;return e.jsxs(e.Fragment,{children:[e.jsx(Z.ItemSlot,{scope:a,value:l.value,children:e.jsx(ke,{asChild:!0,children:e.jsx(I.button,{id:N,disabled:r,"data-disabled":r?"":void 0,"data-state":te(y),"aria-expanded":y,"aria-controls":d,...n,ref:f,onPointerEnter:M(t.onPointerEnter,()=>{j.current=!1,l.wasEscapeCloseRef.current=!1}),onPointerMove:M(t.onPointerMove,z(()=>{r||j.current||l.wasEscapeCloseRef.current||x.current||(s.onTriggerEnter(l.value),x.current=!0)})),onPointerLeave:M(t.onPointerLeave,z(()=>{r||(s.onTriggerLeave(),x.current=!1)})),onClick:M(t.onClick,()=>{s.onItemSelect(l.value),j.current=y}),onKeyDown:M(t.onKeyDown,g=>{const w={horizontal:"ArrowDown",vertical:s.dir==="rtl"?"ArrowLeft":"ArrowRight"}[s.orientation];y&&g.key===w&&(l.onEntryKeyDown(),g.preventDefault())})})})}),y&&e.jsxs(e.Fragment,{children:[e.jsx(dt,{"aria-hidden":!0,tabIndex:0,ref:l.focusProxyRef,onFocus:g=>{const h=l.contentRef.current,w=g.relatedTarget,C=w===v.current,_=h==null?void 0:h.contains(w);(C||!_)&&l.onFocusProxyEnter(C?"start":"end")}}),s.viewport&&e.jsx("span",{"aria-owns":d})]})]})});Rt.displayName=W;var Et="NavigationMenuLink",oe="navigationMenu.linkSelect",_t=i.forwardRef((t,o)=>{const{__scopeNavigationMenu:a,active:r,onSelect:n,...s}=t;return e.jsx(ke,{asChild:!0,children:e.jsx(I.a,{"data-active":r?"":void 0,"aria-current":r?"page":void 0,...s,ref:o,onClick:M(t.onClick,l=>{const v=l.target,f=new CustomEvent(oe,{bubbles:!0,cancelable:!0});if(v.addEventListener(oe,N=>n==null?void 0:n(N),{once:!0}),ne(v,f),!f.defaultPrevented&&!l.metaKey){const N=new CustomEvent(V,{bubbles:!0,cancelable:!0});ne(v,N)}},{checkForDefaultPrevented:!1})})})});_t.displayName=Et;var J="NavigationMenuIndicator",It=i.forwardRef((t,o)=>{const{forceMount:a,...r}=t,n=E(J,t.__scopeNavigationMenu),s=!!n.value;return n.indicatorTrack?it.createPortal(e.jsx($,{present:a||s,children:e.jsx(Tt,{...r,ref:o})}),n.indicatorTrack):null});It.displayName=J;var Tt=i.forwardRef((t,o)=>{const{__scopeNavigationMenu:a,...r}=t,n=E(J,a),s=we(a),[l,v]=i.useState(null),[f,N]=i.useState(null),d=n.orientation==="horizontal",x=!!n.value;i.useEffect(()=>{var h;const g=(h=s().find(w=>w.value===n.value))==null?void 0:h.ref.current;g&&v(g)},[s,n.value]);const j=()=>{l&&N({size:d?l.offsetWidth:l.offsetHeight,offset:d?l.offsetLeft:l.offsetTop})};return Q(l,j),Q(n.indicatorTrack,j),f?e.jsx(I.div,{"aria-hidden":!0,"data-state":x?"visible":"hidden","data-orientation":n.orientation,...r,ref:o,style:{position:"absolute",...d?{left:0,width:f.size+"px",transform:`translateX(${f.offset}px)`}:{top:0,height:f.size+"px",transform:`translateY(${f.offset}px)`},...r.style}}):null}),A="NavigationMenuContent",kt=i.forwardRef((t,o)=>{const{forceMount:a,...r}=t,n=E(A,t.__scopeNavigationMenu),s=Re(A,t.__scopeNavigationMenu),l=O(s.contentRef,o),v=s.value===n.value,f={value:s.value,triggerRef:s.triggerRef,focusProxyRef:s.focusProxyRef,wasEscapeCloseRef:s.wasEscapeCloseRef,onContentFocusOutside:s.onContentFocusOutside,onRootContentClose:s.onRootContentClose,...r};return n.viewport?e.jsx(Pt,{forceMount:a,...f,ref:l}):e.jsx($,{present:a||v,children:e.jsx(_e,{"data-state":te(v),...f,ref:l,onPointerEnter:M(t.onPointerEnter,n.onContentEnter),onPointerLeave:M(t.onPointerLeave,z(n.onContentLeave)),style:{pointerEvents:!v&&n.isRootMenu?"none":void 0,...f.style}})})});kt.displayName=A;var Pt=i.forwardRef((t,o)=>{const a=E(A,t.__scopeNavigationMenu),{onViewportContentChange:r,onViewportContentRemove:n}=a;return B(()=>{r(t.value,{ref:o,...t})},[t,o,r]),B(()=>()=>n(t.value),[t.value,n]),null}),V="navigationMenu.rootContentDismiss",_e=i.forwardRef((t,o)=>{const{__scopeNavigationMenu:a,value:r,triggerRef:n,focusProxyRef:s,wasEscapeCloseRef:l,onRootContentClose:v,onContentFocusOutside:f,...N}=t,d=E(A,a),x=i.useRef(null),j=O(x,o),y=Pe(d.baseId,r),g=Se(d.baseId,r),h=we(a),w=i.useRef(null),{onItemDismiss:C}=d;i.useEffect(()=>{const m=x.current;if(d.isRootMenu&&m){const c=()=>{var u;C(),v(),m.contains(document.activeElement)&&((u=n.current)==null||u.focus())};return m.addEventListener(V,c),()=>m.removeEventListener(V,c)}},[d.isRootMenu,t.value,n,C,v]);const _=i.useMemo(()=>{const c=h().map(k=>k.value);d.dir==="rtl"&&c.reverse();const u=c.indexOf(d.value),b=c.indexOf(d.previousValue),R=r===d.value,T=b===c.indexOf(r);if(!R&&!T)return w.current;const p=(()=>{if(u!==b){if(R&&b!==-1)return u>b?"from-end":"from-start";if(T&&u!==-1)return u>b?"to-start":"to-end"}return null})();return w.current=p,p},[d.previousValue,d.value,d.dir,h,r]);return e.jsx(Te,{asChild:!0,children:e.jsx(ut,{id:g,"aria-labelledby":y,"data-motion":_,"data-orientation":d.orientation,...N,ref:j,disableOutsidePointerEvents:!1,onDismiss:()=>{var c;const m=new Event(V,{bubbles:!0,cancelable:!0});(c=x.current)==null||c.dispatchEvent(m)},onFocusOutside:M(t.onFocusOutside,m=>{var u;f();const c=m.target;(u=d.rootNavigationMenu)!=null&&u.contains(c)&&m.preventDefault()}),onPointerDownOutside:M(t.onPointerDownOutside,m=>{var R;const c=m.target,u=h().some(T=>{var p;return(p=T.ref.current)==null?void 0:p.contains(c)}),b=d.isRootMenu&&((R=d.viewport)==null?void 0:R.contains(c));(u||b||!d.isRootMenu)&&m.preventDefault()}),onKeyDown:M(t.onKeyDown,m=>{var b;const c=m.altKey||m.ctrlKey||m.metaKey;if(m.key==="Tab"&&!c){const R=Y(m.currentTarget),T=document.activeElement,p=R.findIndex(Fe=>Fe===T),G=m.shiftKey?R.slice(0,p).reverse():R.slice(p+1,R.length);ee(G)?m.preventDefault():(b=s.current)==null||b.focus()}}),onEscapeKeyDown:M(t.onEscapeKeyDown,m=>{l.current=!0})})})}),X="NavigationMenuViewport",Ie=i.forwardRef((t,o)=>{const{forceMount:a,...r}=t,s=!!E(X,t.__scopeNavigationMenu).value;return e.jsx($,{present:a||s,children:e.jsx(St,{...r,ref:o})})});Ie.displayName=X;var St=i.forwardRef((t,o)=>{const{__scopeNavigationMenu:a,children:r,...n}=t,s=E(X,a),l=O(o,s.onViewportChange),v=yt(A,t.__scopeNavigationMenu),[f,N]=i.useState(null),[d,x]=i.useState(null),j=f?(f==null?void 0:f.width)+"px":void 0,y=f?(f==null?void 0:f.height)+"px":void 0,g=!!s.value,h=g?s.value:s.previousValue;return Q(d,()=>{d&&N({width:d.offsetWidth,height:d.offsetHeight})}),e.jsx(I.div,{"data-state":te(g),"data-orientation":s.orientation,...n,ref:l,style:{pointerEvents:!g&&s.isRootMenu?"none":void 0,"--radix-navigation-menu-viewport-width":j,"--radix-navigation-menu-viewport-height":y,...n.style},onPointerEnter:M(t.onPointerEnter,s.onContentEnter),onPointerLeave:M(t.onPointerLeave,z(s.onContentLeave)),children:Array.from(v.items).map(([C,{ref:_,forceMount:m,...c}])=>{const u=h===C;return e.jsx($,{present:m||u,children:e.jsx(_e,{...c,ref:at(_,b=>{u&&b&&x(b)})})},C)})})}),Dt="FocusGroup",Te=i.forwardRef((t,o)=>{const{__scopeNavigationMenu:a,...r}=t,n=E(Dt,a);return e.jsx(U.Provider,{scope:a,children:e.jsx(U.Slot,{scope:a,children:e.jsx(I.div,{dir:n.dir,...r,ref:o})})})}),re=["ArrowRight","ArrowLeft","ArrowUp","ArrowDown"],At="FocusGroupItem",ke=i.forwardRef((t,o)=>{const{__scopeNavigationMenu:a,...r}=t,n=Nt(a),s=E(At,a);return e.jsx(U.ItemSlot,{scope:a,children:e.jsx(I.button,{...r,ref:o,onKeyDown:M(t.onKeyDown,l=>{if(["Home","End",...re].includes(l.key)){let f=n().map(x=>x.ref.current);if([s.dir==="rtl"?"ArrowRight":"ArrowLeft","ArrowUp","End"].includes(l.key)&&f.reverse(),re.includes(l.key)){const x=f.indexOf(l.currentTarget);f=f.slice(x+1)}setTimeout(()=>ee(f)),l.preventDefault()}})})})});function Y(t){const o=[],a=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const n=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||n?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;a.nextNode();)o.push(a.currentNode);return o}function ee(t){const o=document.activeElement;return t.some(a=>a===o?!0:(a.focus(),document.activeElement!==o))}function Lt(t){return t.forEach(o=>{o.dataset.tabindex=o.getAttribute("tabindex")||"",o.setAttribute("tabindex","-1")}),()=>{t.forEach(o=>{const a=o.dataset.tabindex;o.setAttribute("tabindex",a)})}}function Q(t,o){const a=P(o);B(()=>{let r=0;if(t){const n=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(a)});return n.observe(t),()=>{window.cancelAnimationFrame(r),n.unobserve(t)}}},[t,a])}function te(t){return t?"open":"closed"}function Pe(t,o){return`${t}-trigger-${o}`}function Se(t,o){return`${t}-content-${o}`}function z(t){return o=>o.pointerType==="mouse"?t(o):void 0}var Ft=be,Ot=Ce,Kt=Ee,Vt=Ie;function De({className:t,children:o,viewport:a=!0,...r}){return e.jsxs(Ft,{"data-slot":"navigation-menu","data-viewport":a,className:S("group/navigation-menu relative flex max-w-max flex-1 items-center justify-center",t),...r,children:[o,a&&e.jsx($t,{})]})}function Ae({className:t,...o}){return e.jsx(Ot,{"data-slot":"navigation-menu-list",className:S("group flex flex-1 list-none items-center justify-center gap-1",t),...o})}function Le({className:t,...o}){return e.jsx(Kt,{"data-slot":"navigation-menu-item",className:S("relative",t),...o})}const zt=st("group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[active=true]:bg-accent/50 data-[state=open]:bg-accent/50 data-[active=true]:text-accent-foreground ring-ring/10 dark:ring-ring/20 dark:outline-ring/40 outline-ring/50 transition-[color,box-shadow] focus-visible:ring-4 focus-visible:outline-1");function $t({className:t,...o}){return e.jsx("div",{className:S("absolute top-full left-0 isolate z-50 flex justify-center"),children:e.jsx(Vt,{"data-slot":"navigation-menu-viewport",className:S("origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]",t),...o})})}const ae=[{title:"Dashboard",href:"/dashboard",icon:Ve}],se=[{title:"Repository",href:"https://github.com/laravel/react-starter-kit",icon:gt},{title:"Documentation",href:"https://laravel.com/docs/starter-kits#react",icon:ht}],Gt="text-neutral-900 dark:bg-neutral-800 dark:text-neutral-100";function Bt({breadcrumbs:t=[]}){const o=ie(),{auth:a}=o.props,r=Oe();return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"border-b border-sidebar-border/80",children:e.jsxs("div",{className:"mx-auto flex h-16 items-center px-4 md:max-w-7xl",children:[e.jsx("div",{className:"lg:hidden",children:e.jsxs(ce,{children:[e.jsx(le,{asChild:!0,children:e.jsxs(F,{variant:"ghost",size:"icon",className:"mr-2 h-9 w-9 rounded-lg transition-all duration-200 hover:bg-accent/80 hover:scale-105 active:scale-95 border border-transparent hover:border-border/50 shadow-sm hover:shadow-md",children:[e.jsx(ue,{className:"h-5 w-5 transition-transform duration-200 hover:rotate-90"}),e.jsx("span",{className:"sr-only",children:"Open navigation menu"})]})}),e.jsxs(de,{side:"left",className:"flex h-full w-64 flex-col items-stretch justify-between bg-sidebar",children:[e.jsx(fe,{className:"sr-only",children:"Navigation Menu"}),e.jsx(ve,{className:"flex justify-start text-left",children:e.jsx(Ke,{className:"h-6 w-6 fill-current text-black dark:text-white"})}),e.jsx("div",{className:"flex h-full flex-1 flex-col space-y-4 p-4",children:e.jsxs("div",{className:"flex h-full flex-col justify-between text-sm",children:[e.jsx("div",{className:"flex flex-col space-y-4",children:ae.map(n=>e.jsxs(L,{href:n.href,className:"flex items-center space-x-2 font-medium",children:[n.icon&&e.jsx(K,{iconNode:n.icon,className:"h-5 w-5"}),e.jsx("span",{children:n.title})]},n.title))}),e.jsx("div",{className:"flex flex-col space-y-4",children:se.map(n=>e.jsxs("a",{href:n.href,target:"_blank",rel:"noopener noreferrer",className:"flex items-center space-x-2 font-medium",children:[n.icon&&e.jsx(K,{iconNode:n.icon,className:"h-5 w-5"}),e.jsx("span",{children:n.title})]},n.title))})]})})]})]})}),e.jsx(L,{href:"/dashboard",prefetch:!0,className:"flex items-center space-x-2",children:e.jsx(me,{})}),e.jsx("div",{className:"ml-6 hidden h-full items-center space-x-6 lg:flex",children:e.jsx(De,{className:"flex h-full items-stretch",children:e.jsx(Ae,{className:"flex h-full items-stretch space-x-2",children:ae.map((n,s)=>e.jsxs(Le,{className:"relative flex h-full items-center",children:[e.jsxs(L,{href:n.href,className:S(zt(),o.url===n.href&&Gt,"h-9 cursor-pointer px-3"),children:[n.icon&&e.jsx(K,{iconNode:n.icon,className:"mr-2 h-4 w-4"}),n.title]}),o.url===n.href&&e.jsx("div",{className:"absolute bottom-0 left-0 h-0.5 w-full translate-y-px bg-black dark:bg-white"})]},s))})})}),e.jsxs("div",{className:"ml-auto flex items-center space-x-2",children:[e.jsxs("div",{className:"relative flex items-center space-x-1",children:[e.jsx(F,{variant:"ghost",size:"icon",className:"group h-9 w-9 cursor-pointer",children:e.jsx(Ne,{className:"!size-5 opacity-80 group-hover:opacity-100"})}),e.jsx("div",{className:"hidden lg:flex",children:se.map(n=>e.jsx(ze,{delayDuration:0,children:e.jsxs($e,{children:[e.jsx(Ge,{children:e.jsxs("a",{href:n.href,target:"_blank",rel:"noopener noreferrer",className:"group ml-1 inline-flex h-9 w-9 items-center justify-center rounded-md bg-transparent p-0 text-sm font-medium text-accent-foreground ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50",children:[e.jsx("span",{className:"sr-only",children:n.title}),n.icon&&e.jsx(K,{iconNode:n.icon,className:"size-5 opacity-80 group-hover:opacity-100"})]})}),e.jsx(Be,{children:e.jsx("p",{children:n.title})})]})},n.title))})]}),e.jsxs(Ue,{children:[e.jsx(He,{asChild:!0,children:e.jsx(F,{variant:"ghost",className:"size-10 rounded-full p-1",children:e.jsxs(We,{className:"size-8 overflow-hidden rounded-full",children:[e.jsx(Ye,{src:a.user.avatar,alt:a.user.name}),e.jsx(Qe,{className:"rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white",children:r(a.user.name)})]})})}),e.jsx(Ze,{className:"w-56",align:"end",children:e.jsx(qe,{user:a.user})})]})]})]})}),t.length>1&&e.jsx("div",{className:"flex w-full border-b border-sidebar-border/70",children:e.jsx("div",{className:"mx-auto flex h-12 w-full items-center justify-start px-4 text-neutral-500 md:max-w-7xl",children:e.jsx(Je,{breadcrumbs:t})})})]})}function Ut({className:t="",showSearch:o=!0,showUserMenu:a=!0}){const[r,n]=i.useState(null),[s,l]=i.useState(!0),[v,f]=i.useState(null),N=ie(),{auth:d}=N.props;if(i.useEffect(()=>{(async()=>{try{const u=await fetch("/api/navbar-config");if(!u.ok)throw new Error("Failed to fetch navbar configuration");const b=await u.json();n(b)}catch(u){console.error("Error fetching navbar config:",u),f(u instanceof Error?u.message:"Unknown error")}finally{l(!1)}})()},[]),s||!r||!r.navbar_enabled)return null;const x={backgroundColor:r.navbar_background_color,color:r.navbar_text_color},j=r.navbar_sticky?"sticky top-0 z-50":"",y=r.navbar_style==="minimal"?"border-b-0":"border-b border-gray-200 dark:border-gray-700",g=()=>e.jsx(L,{href:"/",className:"flex items-center space-x-2",children:e.jsx(me,{})}),h=(c,u=!1)=>c.children&&c.children.length>0?e.jsxs("div",{className:u?"py-2":"",children:[e.jsx("span",{className:`font-medium ${u?"block text-lg":""}`,children:c.title}),c.children&&e.jsx("div",{className:u?"ml-4 mt-2 space-y-2":"hidden",children:c.children.map(R=>h(R,u))})]},c.id):e.jsx(L,{href:c.url,target:c.target,className:`hover:opacity-80 transition-opacity ${u?"block py-2 text-lg":""}`,children:c.title},c.id),w=()=>!r.menu_items||r.menu_items.length===0?null:e.jsx(De,{className:"hidden lg:flex",children:e.jsx(Ae,{className:"space-x-6",children:r.menu_items.map(c=>e.jsx(Le,{children:h(c)},c.id))})}),C=()=>!r.menu_items||r.menu_items.length===0?null:e.jsx("div",{className:"lg:hidden",children:e.jsxs(ce,{children:[e.jsx(le,{asChild:!0,children:e.jsxs(F,{variant:"ghost",size:"icon",className:"h-9 w-9 rounded-lg",children:[e.jsx(ue,{className:"h-5 w-5"}),e.jsx("span",{className:"sr-only",children:"Open navigation menu"})]})}),e.jsxs(de,{side:"left",className:"w-80",children:[e.jsx(ve,{children:e.jsx(fe,{children:"Navigation"})}),e.jsx("div",{className:"mt-6 space-y-4",children:r.menu_items.map(c=>h(c,!0))})]})]})}),_=()=>!r.navbar_show_search||!o?null:e.jsxs(F,{variant:"ghost",size:"icon",className:"h-9 w-9 rounded-lg",onClick:()=>{const c=new KeyboardEvent("keydown",{key:"k",ctrlKey:!0,metaKey:!0});document.dispatchEvent(c)},children:[e.jsx(Ne,{className:"h-5 w-5"}),e.jsx("span",{className:"sr-only",children:"Search"})]}),m=()=>{const c=r.navbar_logo_position;return e.jsxs("div",{className:"mx-auto flex h-16 items-center justify-between px-4 md:max-w-7xl",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[c==="left"&&g(),C()]}),e.jsxs("div",{className:"flex items-center space-x-6",children:[c==="center"&&g(),w()]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[c==="right"&&g(),_(),a&&d.user&&e.jsxs("div",{className:"text-sm",children:["Welcome, ",d.user.name]})]})]})};return e.jsx("nav",{className:`${j} ${y} ${t}`,style:x,role:"navigation",children:m()})}function Ht({children:t,breadcrumbs:o,useDynamicNavbar:a=!1}){const r=Xe();return e.jsxs(e.Fragment,{children:[e.jsx(et,{}),e.jsxs(tt,{children:[a?e.jsx(Ut,{}):e.jsx(Bt,{breadcrumbs:o}),e.jsx(nt,{children:t})]}),e.jsx(ot,{isAdmin:r}),e.jsx(rt,{})]})}function rn({children:t,breadcrumbs:o,...a}){return e.jsxs(e.Fragment,{children:[e.jsx(Ht,{breadcrumbs:o,useDynamicNavbar:!0,...a,children:t}),e.jsx(vt,{})]})}export{rn as P};
