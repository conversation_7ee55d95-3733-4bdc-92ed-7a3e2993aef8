import{J as w,j as e,Q as S,t}from"./app-J5EqS6dS.js";import{C as l,a as c,b as d,c as n,d as x}from"./card-9XCADs-4.js";import{B as s}from"./smartphone-GGiwNneF.js";import{B as h}from"./badge-BucYuCBs.js";import{A as k}from"./app-layout-ox1kAwY6.js";import{C as m,A as j,H as C,G as A,B as T}from"./ImpersonationBanner-CYn5eDk6.js";import{S as o}from"./search-DBK6jUoc.js";import{C as u}from"./crown-UDSxMtlm.js";import{C as f}from"./circle-check-big-DOFoatRy.js";import{Z as g}from"./zap-BcmHRR4K.js";import{A as N}from"./arrow-up-right-9Qf6vHK9.js";import{T as P}from"./target-BJCwZ93C.js";import{E as R}from"./eye-D-fsmYB2.js";import{C as B}from"./clock-Brl7_5s7.js";import{T as _}from"./trending-up-BtixJGWw.js";import{S as D}from"./users-RYmOyic9.js";import{S as U}from"./star-D0YOm-Sd.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";const F=[{title:"Dashboard",href:"/dashboard"}];function xe(){const{auth:b}=w().props,i=b.user,r={totalSearches:156,searchesToday:12,successRate:89,remainingSearches:i.subscription_plan==="premium"?-1:Math.max(0,12),favoriteItems:23,recentActivity:5},p=[{id:1,query:"iPhone 15 Pro Max Display",type:"part",results:15,date:"2 hours ago"},{id:2,query:"Samsung Galaxy S24",type:"model",results:8,date:"5 hours ago"},{id:3,query:"Battery",type:"category",results:142,date:"1 day ago"},{id:4,query:"Xiaomi Redmi Note 13",type:"model",results:12,date:"2 days ago"}],v=[{name:"Display",count:45,percentage:28},{name:"Battery",count:38,percentage:24},{name:"Camera",count:32,percentage:20},{name:"Charging Port",count:25,percentage:16},{name:"Speaker",count:16,percentage:12}];return e.jsxs(k,{breadcrumbs:F,children:[e.jsx(S,{title:"Dashboard"}),e.jsx("div",{className:"bg-gradient-to-br from-background via-muted/20 to-background min-h-screen",children:e.jsxs("div",{className:"container mx-auto px-4 py-6 max-w-7xl",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-3xl font-bold text-foreground mb-2 flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:e.jsx(m,{className:"w-6 h-6 text-primary"})}),"Welcome back, ",i.name,"!"]}),e.jsx("p",{className:"text-muted-foreground text-lg",children:"Comprehensive insights into your mobile parts search activity"})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx(t,{href:route("search.index"),children:e.jsxs(s,{className:"gap-2",children:[e.jsx(o,{className:"w-4 h-4"}),"Start Searching"]})}),e.jsx(t,{href:route("subscription.plans"),children:e.jsxs(s,{variant:"outline",className:"gap-2",children:[e.jsx(u,{className:"w-4 h-4"}),"View Plans"]})})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsxs(l,{className:"shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:[e.jsxs(c,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(d,{className:"text-sm font-medium text-muted-foreground",children:"Current Plan"}),e.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:e.jsx(u,{className:"h-4 w-4 text-primary"})})]}),e.jsxs(n,{children:[e.jsx("div",{className:"text-2xl font-bold text-foreground mb-1",children:i.subscription_plan==="premium"?"Premium":"Free"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(h,{variant:i.subscription_plan==="premium"?"default":"secondary",className:"text-xs",children:i.subscription_plan==="premium"?"Active":"Basic"}),i.subscription_plan==="premium"&&e.jsx(f,{className:"w-3 h-3 text-green-500"})]})]})]}),e.jsxs(l,{className:"shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:[e.jsxs(c,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(d,{className:"text-sm font-medium text-muted-foreground",children:"Today's Searches"}),e.jsx("div",{className:"p-2 bg-blue-500/10 rounded-lg",children:e.jsx(g,{className:"h-4 w-4 text-blue-500"})})]}),e.jsxs(n,{children:[e.jsx("div",{className:"text-2xl font-bold text-foreground mb-1",children:r.searchesToday}),e.jsx("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:i.subscription_plan==="premium"?e.jsx("span",{className:"text-green-600 font-medium",children:"Unlimited remaining"}):e.jsxs("span",{children:[r.remainingSearches," remaining today"]})})]})]}),e.jsxs(l,{className:"shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:[e.jsxs(c,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(d,{className:"text-sm font-medium text-muted-foreground",children:"Total Searches"}),e.jsx("div",{className:"p-2 bg-green-500/10 rounded-lg",children:e.jsx(m,{className:"h-4 w-4 text-green-500"})})]}),e.jsxs(n,{children:[e.jsx("div",{className:"text-2xl font-bold text-foreground mb-1",children:r.totalSearches}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[e.jsx(N,{className:"w-3 h-3 text-green-500"}),e.jsx("span",{className:"text-green-600 font-medium",children:"+12% from last week"})]})]})]}),e.jsxs(l,{className:"shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:[e.jsxs(c,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(d,{className:"text-sm font-medium text-muted-foreground",children:"Success Rate"}),e.jsx("div",{className:"p-2 bg-purple-500/10 rounded-lg",children:e.jsx(P,{className:"h-4 w-4 text-purple-500"})})]}),e.jsxs(n,{children:[e.jsxs("div",{className:"text-2xl font-bold text-foreground mb-1",children:[r.successRate,"%"]}),e.jsx("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:e.jsx("span",{children:"Searches with results"})})]})]})]}),e.jsxs("div",{className:"grid lg:grid-cols-3 gap-6 mb-8",children:[e.jsxs(l,{className:"lg:col-span-2 shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:[e.jsx(c,{className:"pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(d,{className:"flex items-center gap-3 text-xl",children:[e.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:e.jsx(j,{className:"w-5 h-5 text-primary"})}),"Recent Search Activity"]}),e.jsx(x,{className:"text-base mt-1",children:"Your latest searches and their results"})]}),e.jsx(t,{href:route("dashboard.history"),children:e.jsxs(s,{variant:"outline",size:"sm",className:"gap-2",children:[e.jsx(R,{className:"w-4 h-4"}),"View All"]})})]})}),e.jsxs(n,{children:[e.jsx("div",{className:"space-y-1",children:p.map(a=>e.jsxs("div",{className:"group flex items-center justify-between p-4 rounded-lg border border-border/50 hover:border-primary/30 hover:bg-muted/30 transition-all duration-200",children:[e.jsxs("div",{className:"flex items-center gap-4 flex-1 min-w-0",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center",children:e.jsx(o,{className:"w-4 h-4 text-primary"})})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h4",{className:"font-semibold text-foreground truncate",children:a.query}),e.jsx(h,{variant:"outline",className:"text-xs px-2 py-0.5 flex-shrink-0",children:a.type})]}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(m,{className:"w-3 h-3"}),a.results," results"]}),e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(B,{className:"w-3 h-3"}),a.date]})]})]})]}),e.jsx("div",{className:"flex-shrink-0 ml-4",children:e.jsx(s,{variant:"ghost",size:"sm",className:"opacity-0 group-hover:opacity-100 transition-opacity",children:e.jsx(N,{className:"w-4 h-4"})})})]},a.id))}),p.length===0&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"w-20 h-20 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(o,{className:"w-8 h-8 text-muted-foreground"})}),e.jsx("h3",{className:"text-lg font-semibold text-foreground mb-2",children:"No searches yet"}),e.jsx("p",{className:"text-muted-foreground mb-6 max-w-md mx-auto",children:"Start exploring our mobile parts database to see your search history here"}),e.jsx(t,{href:route("search.index"),children:e.jsxs(s,{className:"gap-2",children:[e.jsx(o,{className:"w-4 h-4"}),"Start Your First Search"]})})]})]})]}),e.jsxs(l,{className:"shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:[e.jsxs(c,{className:"pb-4",children:[e.jsxs(d,{className:"flex items-center gap-3 text-xl",children:[e.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:e.jsx(g,{className:"w-5 h-5 text-primary"})}),"Quick Actions"]}),e.jsx(x,{className:"text-base",children:"Common tasks and shortcuts"})]}),e.jsxs(n,{className:"space-y-3",children:[e.jsx(t,{href:route("search.index"),className:"block",children:e.jsxs(s,{variant:"outline",className:"w-full justify-start gap-3 h-12",children:[e.jsx(o,{className:"w-5 h-5"}),e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"font-medium",children:"Search Parts"}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Find mobile components"})]})]})}),e.jsx(t,{href:route("dashboard.favorites"),className:"block",children:e.jsxs(s,{variant:"outline",className:"w-full justify-start gap-3 h-12",children:[e.jsx(C,{className:"w-5 h-5"}),e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"font-medium",children:"Favorites"}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:[r.favoriteItems," saved items"]})]})]})}),e.jsx(t,{href:route("dashboard.history"),className:"block",children:e.jsxs(s,{variant:"outline",className:"w-full justify-start gap-3 h-12",children:[e.jsx(A,{className:"w-5 h-5"}),e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"font-medium",children:"Search History"}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:[r.totalSearches," total searches"]})]})]})}),e.jsx(t,{href:route("subscription.search-stats"),className:"block",children:e.jsxs(s,{variant:"outline",className:"w-full justify-start gap-3 h-12",children:[e.jsx(_,{className:"w-5 h-5"}),e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"font-medium",children:"Usage Analytics"}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Detailed insights"})]})]})}),e.jsx(t,{href:route("notifications.index"),className:"block",children:e.jsxs(s,{variant:"outline",className:"w-full justify-start gap-3 h-12",children:[e.jsx(T,{className:"w-5 h-5"}),e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"font-medium",children:"Notifications"}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:[r.recentActivity," new updates"]})]})]})}),e.jsx(t,{href:route("activity.index"),className:"block",children:e.jsxs(s,{variant:"outline",className:"w-full justify-start gap-3 h-12",children:[e.jsx(j,{className:"w-5 h-5"}),e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"font-medium",children:"Activity Log"}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Recent actions"})]})]})})]})]})]}),e.jsxs("div",{className:"grid lg:grid-cols-2 gap-6",children:[e.jsxs(l,{className:"shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:[e.jsx(c,{className:"pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(d,{className:"flex items-center gap-3 text-xl",children:[e.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:e.jsx(m,{className:"w-5 h-5 text-primary"})}),"Top Search Categories"]}),e.jsx(x,{className:"text-base mt-1",children:"Most searched part categories"})]}),e.jsx(h,{variant:"secondary",className:"text-sm px-3 py-1",children:"This Month"})]})}),e.jsx(n,{children:e.jsx("div",{className:"space-y-4",children:v.map((a,y)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-sm font-medium text-primary",children:y+1}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-foreground",children:a.name}),e.jsxs("div",{className:"text-sm text-muted-foreground",children:[a.count," searches"]})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-24 bg-muted rounded-full h-2",children:e.jsx("div",{className:"bg-primary h-2 rounded-full transition-all duration-300",style:{width:`${a.percentage}%`}})}),e.jsxs("span",{className:"text-sm font-medium text-foreground w-12 text-right",children:[a.percentage,"%"]})]})]},a.name))})})]}),e.jsxs(l,{className:"shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:[e.jsxs(c,{className:"pb-4",children:[e.jsxs(d,{className:"flex items-center gap-3 text-xl",children:[e.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:e.jsx(D,{className:"w-5 h-5 text-primary"})}),"Account Overview"]}),e.jsx(x,{className:"text-base mt-1",children:"Your account status and recommendations"})]}),e.jsxs(n,{className:"space-y-6",children:[e.jsxs("div",{className:"bg-muted/30 rounded-lg p-4 border border-border",children:[e.jsxs("h4",{className:"font-semibold text-foreground mb-3 flex items-center gap-2",children:[e.jsx(f,{className:"w-4 h-4 text-green-500"}),"Account Status"]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-muted-foreground",children:"Plan:"}),e.jsx("div",{className:"font-medium text-foreground",children:i.subscription_plan==="premium"?"Premium":"Free Plan"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-muted-foreground",children:"Status:"}),e.jsx("div",{className:"font-medium text-green-600",children:"Active"})]})]})]}),e.jsxs("div",{className:"bg-muted/30 rounded-lg p-4 border border-border",children:[e.jsxs("h4",{className:"font-semibold text-foreground mb-3 flex items-center gap-2",children:[e.jsx(m,{className:"w-4 h-4 text-primary"}),"Usage Summary"]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Searches Today"}),e.jsx("span",{className:"font-medium text-foreground",children:r.searchesToday})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Total Searches"}),e.jsx("span",{className:"font-medium text-foreground",children:r.totalSearches})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Success Rate"}),e.jsxs("span",{className:"font-medium text-green-600",children:[r.successRate,"%"]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Favorites"}),e.jsx("span",{className:"font-medium text-foreground",children:r.favoriteItems})]})]})]}),i.subscription_plan==="free"&&e.jsxs("div",{className:"bg-primary/5 border border-primary/20 rounded-lg p-4",children:[e.jsxs("h4",{className:"font-semibold text-foreground mb-2 flex items-center gap-2",children:[e.jsx(U,{className:"w-4 h-4 text-primary"}),"Upgrade Recommendation"]}),e.jsx("p",{className:"text-sm text-muted-foreground mb-3",children:"Get unlimited searches and advanced features with Premium."}),e.jsx(t,{href:route("subscription.plans"),children:e.jsxs(s,{size:"sm",className:"w-full",children:[e.jsx(u,{className:"w-4 h-4 mr-2"}),"Upgrade to Premium"]})})]})]})]})]})]})})]})}export{xe as default};
