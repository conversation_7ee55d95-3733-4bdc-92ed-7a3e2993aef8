import{j as e,Q as p,t as l}from"./app-J5EqS6dS.js";import{C as m,a as d,b as n,c,d as j}from"./card-9XCADs-4.js";import{B as g}from"./badge-BucYuCBs.js";import{B as o}from"./smartphone-GGiwNneF.js";import{A as N}from"./app-layout-ox1kAwY6.js";import{A as b}from"./arrow-left-D4U9AVF9.js";import{A as x}from"./ImpersonationBanner-CYn5eDk6.js";import{C as v}from"./calendar-B-u_QN2Q.js";import{G as w}from"./globe-zfFlVOSX.js";import{U as f}from"./user-DCnDRzMf.js";import{F as h}from"./file-text-Dx6bYLtE.js";import{S as u}from"./shield-D9nQfigG.js";import{S as A}from"./search-DBK6jUoc.js";import{E as B}from"./eye-D-fsmYB2.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./users-RYmOyic9.js";import"./lock-Tx_yfI4R.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./triangle-alert-BW76NKO9.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";const C=({type:s})=>{const t=r=>r.includes("login")||r.includes("logout")?{className:"bg-blue-100 text-blue-800",icon:u}:r.includes("search")?{className:"bg-green-100 text-green-800",icon:A}:r.includes("favorite")?{className:"bg-purple-100 text-purple-800",icon:B}:r.includes("subscription")?{className:"bg-orange-100 text-orange-800",icon:h}:r.includes("admin")||r.includes("approval")?{className:"bg-red-100 text-red-800",icon:u}:{className:"bg-gray-100 text-gray-800",icon:x},{className:i,icon:a}=t(s);return e.jsxs(g,{className:`flex items-center gap-1 ${i}`,children:[e.jsx(a,{className:"h-3 w-3"}),s.replace(/_/g," ").replace(/\b\w/g,r=>r.toUpperCase())]})};function Y({activity:s}){var t,i,a;return e.jsxs(N,{children:[e.jsx(p,{title:`Activity: ${s.activity_type}`}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(l,{href:"/admin/activities",children:e.jsxs(o,{variant:"outline",size:"sm",children:[e.jsx(b,{className:"h-4 w-4 mr-2"}),"Back to Activities"]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:"Activity Details"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"View activity log information"})]})]})}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[e.jsxs(m,{children:[e.jsx(d,{children:e.jsxs(n,{className:"flex items-center gap-2",children:[e.jsx(x,{className:"h-5 w-5"}),"Activity Information"]})}),e.jsxs(c,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Activity Type"}),e.jsx("div",{className:"mt-1",children:e.jsx(C,{type:s.activity_type})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Description"}),e.jsx("p",{className:"text-sm",children:s.description})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Timestamp"}),e.jsxs("div",{className:"mt-1 flex items-center gap-2",children:[e.jsx(v,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{children:new Date(s.created_at).toLocaleString()})]})]}),s.ip_address&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"IP Address"}),e.jsxs("div",{className:"mt-1 flex items-center gap-2",children:[e.jsx(w,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"font-mono text-sm",children:s.ip_address})]})]})]})]}),e.jsxs(m,{children:[e.jsx(d,{children:e.jsxs(n,{className:"flex items-center gap-2",children:[e.jsx(f,{className:"h-5 w-5"}),"User & Performer"]})}),e.jsxs(c,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"User"}),e.jsxs("div",{className:"mt-1",children:[e.jsx("p",{className:"font-medium",children:((t=s.user)==null?void 0:t.name)||"Unknown User"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:((i=s.user)==null?void 0:i.email)||"No email"})]})]}),s.performedBy&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Performed By"}),e.jsxs("div",{className:"mt-1",children:[e.jsx("p",{className:"font-medium",children:s.performedBy.name}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s.performedBy.email})]})]}),!s.performedBy&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Performed By"}),e.jsx("div",{className:"mt-1",children:e.jsx("p",{className:"text-sm text-muted-foreground",children:"System / User Action"})})]})]})]})]}),s.metadata&&Object.keys(s.metadata).length>0&&e.jsxs(m,{children:[e.jsxs(d,{children:[e.jsxs(n,{className:"flex items-center gap-2",children:[e.jsx(h,{className:"h-5 w-5"}),"Additional Data"]}),e.jsx(j,{children:"Metadata associated with this activity"})]}),e.jsx(c,{children:e.jsx("pre",{className:"bg-muted p-4 rounded-lg text-sm overflow-x-auto",children:JSON.stringify(s.metadata,null,2)})})]}),e.jsxs(m,{children:[e.jsxs(d,{children:[e.jsx(n,{children:"Actions"}),e.jsx(j,{children:"Related actions for this activity"})]}),e.jsx(c,{children:e.jsxs("div",{className:"flex items-center gap-4",children:[((a=s.user)==null?void 0:a.id)&&e.jsxs(e.Fragment,{children:[e.jsx(l,{href:`/admin/users/${s.user.id}`,children:e.jsxs(o,{variant:"outline",children:[e.jsx(f,{className:"h-4 w-4 mr-2"}),"View User Profile"]})}),e.jsx(l,{href:`/admin/activities?user_id=${s.user.id}`,children:e.jsxs(o,{variant:"outline",children:[e.jsx(x,{className:"h-4 w-4 mr-2"}),"User's Activities"]})})]}),e.jsx(l,{href:"/admin/activities",children:e.jsxs(o,{variant:"outline",children:[e.jsx(h,{className:"h-4 w-4 mr-2"}),"All Activities"]})})]})})]})]})})]})}export{Y as default};
