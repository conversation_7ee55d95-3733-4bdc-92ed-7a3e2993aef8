import{r as w,x as $,j as e,Q as D}from"./app-J5EqS6dS.js";import{t as _,U as h,P as O}from"./ImpersonationBanner-CYn5eDk6.js";import{A as V}from"./app-layout-ox1kAwY6.js";import{c as z,B as c}from"./smartphone-GGiwNneF.js";import{C as p,a as u,b as g,d as v,c as f}from"./card-9XCADs-4.js";import{T as Q,a as H,b as j,c as b}from"./tabs-DZAL-HvD.js";import{I as l}from"./input-Bo8dOn9p.js";import{L as r}from"./label-BlOrdc-X.js";import{M as q}from"./MediaPicker-Due2OGB1.js";import{S as Y}from"./save-DfhL0V-C.js";import{S as W}from"./users-RYmOyic9.js";import{T as J}from"./trash-2-B3ZEh4hl.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-BzZWUWqx.js";import"./index-CJpBU2i9.js";import"./badge-BucYuCBs.js";import"./crown-UDSxMtlm.js";import"./shield-D9nQfigG.js";import"./user-DCnDRzMf.js";import"./search-DBK6jUoc.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./eye-D-fsmYB2.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./checkout-helpers-CMrRJez4.js";import"./loader-circle-B1NtNhL1.js";import"./check-C7SdgHPn.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const K=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],X=z("RotateCcw",K);function Pe({settings:k,categories:L,menus:C}){const[M,N]=w.useState(!1),[F,y]=w.useState(""),[m,T]=w.useState(L[0]||"branding"),P=()=>{const s={};return Object.values(k).flat().forEach(i=>{s[i.key]=i.value}),s},{data:a,setData:t,post:S,processing:o}=$(P()),A=s=>{s.preventDefault(),S("/admin/site-settings",{onSuccess:()=>{_.success("Site settings updated successfully")},onError:()=>{_.error("Failed to update site settings")}})},I=s=>{confirm(`Are you sure you want to reset ${s?`${s} settings`:"all settings"} to defaults?`)&&S("/admin/site-settings/reset",{data:{category:s},onSuccess:()=>{_.success(`Settings ${s?`for ${s}`:""} reset to defaults`)},onError:()=>{_.error("Failed to reset settings")}})},R=s=>{s.length>0&&F&&t(F,s[0].url),N(!1),y("")},x=s=>{y(s),N(!0)},E=()=>(k.branding,e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium mb-4",children:"Logo Settings"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:"site_logo_url",children:"Logo URL"}),e.jsxs("div",{className:"flex gap-2 mt-1",children:[e.jsx(l,{id:"site_logo_url",value:a.site_logo_url||"",onChange:s=>t("site_logo_url",s.target.value),placeholder:"https://example.com/logo.png",disabled:o}),e.jsx(c,{type:"button",variant:"outline",onClick:()=>x("site_logo_url"),disabled:o,children:e.jsx(h,{className:"w-4 h-4"})})]}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"Upload or select a logo image for your site"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"site_logo_alt",children:"Logo Alt Text"}),e.jsx(l,{id:"site_logo_alt",value:a.site_logo_alt||"",onChange:s=>t("site_logo_alt",s.target.value),placeholder:"Site Logo",disabled:o})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:"site_logo_width",children:"Width (px)"}),e.jsx(l,{id:"site_logo_width",type:"number",value:a.site_logo_width||40,onChange:s=>t("site_logo_width",parseInt(s.target.value)),disabled:o})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"site_logo_height",children:"Height (px)"}),e.jsx(l,{id:"site_logo_height",type:"number",value:a.site_logo_height||40,onChange:s=>t("site_logo_height",parseInt(s.target.value)),disabled:o})]})]})]}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{children:[e.jsx(r,{children:"Logo Preview"}),e.jsx("div",{className:"border rounded-lg p-4 bg-gray-50 dark:bg-gray-800 flex items-center justify-center min-h-[120px]",children:a.site_logo_url?e.jsx("img",{src:a.site_logo_url,alt:a.site_logo_alt||"Site Logo",style:{width:`${a.site_logo_width||40}px`,height:`${a.site_logo_height||40}px`,objectFit:"contain"},className:"max-w-full max-h-full"}):e.jsxs("div",{className:"text-center text-gray-500",children:[e.jsx(h,{className:"w-8 h-8 mx-auto mb-2"}),e.jsx("p",{className:"text-sm",children:"No logo selected"})]})})]})})]})]})})),B=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium mb-4",children:"Favicon Settings"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:"favicon_ico_url",children:"ICO Favicon URL"}),e.jsxs("div",{className:"flex gap-2 mt-1",children:[e.jsx(l,{id:"favicon_ico_url",value:a.favicon_ico_url||"",onChange:s=>t("favicon_ico_url",s.target.value),placeholder:"/favicon.ico",disabled:o}),e.jsx(c,{type:"button",variant:"outline",onClick:()=>x("favicon_ico_url"),disabled:o,children:e.jsx(h,{className:"w-4 h-4"})})]}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"ICO format favicon (16x16, 32x32, 48x48)"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"favicon_svg_url",children:"SVG Favicon URL"}),e.jsxs("div",{className:"flex gap-2 mt-1",children:[e.jsx(l,{id:"favicon_svg_url",value:a.favicon_svg_url||"",onChange:s=>t("favicon_svg_url",s.target.value),placeholder:"/favicon.svg",disabled:o}),e.jsx(c,{type:"button",variant:"outline",onClick:()=>x("favicon_svg_url"),disabled:o,children:e.jsx(h,{className:"w-4 h-4"})})]}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"SVG format favicon (scalable)"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"favicon_png_url",children:"PNG Favicon URL"}),e.jsxs("div",{className:"flex gap-2 mt-1",children:[e.jsx(l,{id:"favicon_png_url",value:a.favicon_png_url||"",onChange:s=>t("favicon_png_url",s.target.value),placeholder:"/apple-touch-icon.png",disabled:o}),e.jsx(c,{type:"button",variant:"outline",onClick:()=>x("favicon_png_url"),disabled:o,children:e.jsx(h,{className:"w-4 h-4"})})]}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"PNG format favicon (Apple touch icon, 180x180)"})]})]}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{children:[e.jsx(r,{children:"Favicon Preview"}),e.jsx("div",{className:"border rounded-lg p-4 bg-gray-50 dark:bg-gray-800",children:e.jsxs("div",{className:"space-y-3",children:[a.favicon_ico_url&&e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:a.favicon_ico_url,alt:"ICO Favicon",className:"w-4 h-4"}),e.jsx("span",{className:"text-sm",children:"ICO Favicon"})]}),a.favicon_svg_url&&e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:a.favicon_svg_url,alt:"SVG Favicon",className:"w-4 h-4"}),e.jsx("span",{className:"text-sm",children:"SVG Favicon"})]}),a.favicon_png_url&&e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:a.favicon_png_url,alt:"PNG Favicon",className:"w-8 h-8"}),e.jsx("span",{className:"text-sm",children:"PNG Favicon (Apple Touch)"})]}),!a.favicon_ico_url&&!a.favicon_svg_url&&!a.favicon_png_url&&e.jsxs("div",{className:"text-center text-gray-500",children:[e.jsx(W,{className:"w-8 h-8 mx-auto mb-2"}),e.jsx("p",{className:"text-sm",children:"No favicons configured"})]})]})})]})})]})]})}),U=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"footer_enabled",checked:a.footer_enabled||!1,onChange:s=>t("footer_enabled",s.target.checked),disabled:o,className:"rounded border-gray-300"}),e.jsx(r,{htmlFor:"footer_enabled",children:"Enable Footer"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"footer_layout",children:"Footer Layout"}),e.jsxs("select",{id:"footer_layout",value:a.footer_layout||"simple",onChange:s=>t("footer_layout",s.target.value),disabled:o,className:"w-full mt-1 rounded-md border-gray-300 shadow-sm",children:[e.jsx("option",{value:"simple",children:"Simple"}),e.jsx("option",{value:"columns",children:"Columns"}),e.jsx("option",{value:"centered",children:"Centered"})]})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"footer_content",children:"Footer Content"}),e.jsx("textarea",{id:"footer_content",value:a.footer_content||"",onChange:s=>t("footer_content",s.target.value),placeholder:"Main footer description",disabled:o,rows:3,className:"w-full mt-1 rounded-md border-gray-300 shadow-sm"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"footer_copyright",children:"Copyright Text"}),e.jsx(l,{id:"footer_copyright",value:a.footer_copyright||"",onChange:s=>t("footer_copyright",s.target.value),placeholder:"© 2024 Your Company. All rights reserved.",disabled:o})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:"footer_background_color",children:"Background Color"}),e.jsx(l,{id:"footer_background_color",type:"color",value:a.footer_background_color||"#1f2937",onChange:s=>t("footer_background_color",s.target.value),disabled:o})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"footer_text_color",children:"Text Color"}),e.jsx(l,{id:"footer_text_color",type:"color",value:a.footer_text_color||"#ffffff",onChange:s=>t("footer_text_color",s.target.value),disabled:o})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"footer_show_logo",checked:a.footer_show_logo||!1,onChange:s=>t("footer_show_logo",s.target.checked),disabled:o,className:"rounded border-gray-300"}),e.jsx(r,{htmlFor:"footer_show_logo",children:"Show Logo in Footer"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"footer_logo_position",children:"Logo Position"}),e.jsxs("select",{id:"footer_logo_position",value:a.footer_logo_position||"center",onChange:s=>t("footer_logo_position",s.target.value),disabled:o,className:"w-full mt-1 rounded-md border-gray-300 shadow-sm",children:[e.jsx("option",{value:"left",children:"Left"}),e.jsx("option",{value:"center",children:"Center"}),e.jsx("option",{value:"right",children:"Right"})]})]})]})]}),e.jsxs("div",{className:"space-y-6 pt-6 border-t",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Menu & Newsletter Settings"}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{children:[e.jsx(r,{htmlFor:"footer_menu_ids",children:"Footer Menus"}),e.jsx("div",{className:"space-y-2 mt-2",children:C.map(s=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:`footer_menu_${s.id}`,checked:(a.footer_menu_ids||[]).includes(s.id),onChange:i=>{const d=a.footer_menu_ids||[];i.target.checked?t("footer_menu_ids",[...d,s.id]):t("footer_menu_ids",d.filter(n=>n!==s.id))},disabled:o,className:"rounded border-gray-300"}),e.jsxs(r,{htmlFor:`footer_menu_${s.id}`,className:"text-sm",children:[s.name," ",s.location&&`(${s.location})`]})]},s.id))}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"Select menus to display as columns in the footer."})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"footer_newsletter_enabled",checked:a.footer_newsletter_enabled||!1,onChange:s=>t("footer_newsletter_enabled",s.target.checked),disabled:o,className:"rounded border-gray-300"}),e.jsx(r,{htmlFor:"footer_newsletter_enabled",children:"Enable Newsletter Signup"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"footer_newsletter_title",children:"Newsletter Title"}),e.jsx(l,{id:"footer_newsletter_title",value:a.footer_newsletter_title||"",onChange:s=>t("footer_newsletter_title",s.target.value),placeholder:"Newsletter",disabled:o})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"footer_newsletter_description",children:"Newsletter Description"}),e.jsx("textarea",{id:"footer_newsletter_description",value:a.footer_newsletter_description||"",onChange:s=>t("footer_newsletter_description",s.target.value),placeholder:"Subscribe to our newsletter...",disabled:o,rows:3,className:"w-full mt-1 rounded-md border-gray-300 shadow-sm"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"footer_newsletter_placeholder",children:"Email Placeholder Text"}),e.jsx(l,{id:"footer_newsletter_placeholder",value:a.footer_newsletter_placeholder||"",onChange:s=>t("footer_newsletter_placeholder",s.target.value),placeholder:"Your email",disabled:o})]})]}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{children:[e.jsx(r,{children:"Quick Links"}),e.jsxs("div",{className:"space-y-3 mt-2",children:[(a.footer_links||[]).map((s,i)=>e.jsxs("div",{className:"border rounded-lg p-3 space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"text-sm font-medium",children:["Link ",i+1]}),e.jsx(c,{type:"button",variant:"ghost",size:"sm",onClick:()=>{const d=[...a.footer_links||[]];d.splice(i,1),t("footer_links",d)},disabled:o,className:"h-6 w-6 p-0 text-red-500 hover:text-red-700",children:e.jsx(J,{className:"h-3 w-3"})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{placeholder:"Link title",value:s.title||"",onChange:d=>{const n=[...a.footer_links||[]];n[i]={...n[i],title:d.target.value},t("footer_links",n)},disabled:o}),e.jsx(l,{placeholder:"URL (e.g., /privacy)",value:s.url||"",onChange:d=>{const n=[...a.footer_links||[]];n[i]={...n[i],url:d.target.value},t("footer_links",n)},disabled:o}),e.jsxs("select",{value:s.target||"_self",onChange:d=>{const n=[...a.footer_links||[]];n[i]={...n[i],target:d.target.value},t("footer_links",n)},disabled:o,className:"w-full rounded-md border-gray-300 shadow-sm text-sm",children:[e.jsx("option",{value:"_self",children:"Same window"}),e.jsx("option",{value:"_blank",children:"New window"})]})]})]},i)),e.jsxs(c,{type:"button",variant:"outline",size:"sm",onClick:()=>{const s=[...a.footer_links||[]];s.push({title:"",url:"",target:"_self"}),t("footer_links",s)},disabled:o,className:"w-full",children:[e.jsx(O,{className:"h-4 w-4 mr-2"}),"Add Quick Link"]})]}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"Manage quick navigation links displayed in the footer."})]})})]})]}),e.jsxs("div",{className:"space-y-6 pt-6 border-t",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Mobile App Download"}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"footer_mobile_apps_enabled",checked:a.footer_mobile_apps_enabled||!1,onChange:s=>t("footer_mobile_apps_enabled",s.target.checked),disabled:o,className:"rounded border-gray-300"}),e.jsx(r,{htmlFor:"footer_mobile_apps_enabled",children:"Enable Mobile App Download Section"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"footer_mobile_apps_title",children:"Section Title"}),e.jsx(l,{id:"footer_mobile_apps_title",value:a.footer_mobile_apps_title||"",onChange:s=>t("footer_mobile_apps_title",s.target.value),placeholder:"Here is our Mobile Apps",disabled:o||!a.footer_mobile_apps_enabled})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"footer_app_store_enabled",checked:a.footer_app_store_enabled||!1,onChange:s=>t("footer_app_store_enabled",s.target.checked),disabled:o||!a.footer_mobile_apps_enabled,className:"rounded border-gray-300"}),e.jsx(r,{htmlFor:"footer_app_store_enabled",children:"Enable App Store Button"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"footer_app_store_url",children:"App Store URL"}),e.jsx(l,{id:"footer_app_store_url",value:a.footer_app_store_url||"",onChange:s=>t("footer_app_store_url",s.target.value),placeholder:"https://apps.apple.com/app/your-app",disabled:o||!a.footer_mobile_apps_enabled||!a.footer_app_store_enabled})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"footer_play_store_enabled",checked:a.footer_play_store_enabled||!1,onChange:s=>t("footer_play_store_enabled",s.target.checked),disabled:o||!a.footer_mobile_apps_enabled,className:"rounded border-gray-300"}),e.jsx(r,{htmlFor:"footer_play_store_enabled",children:"Enable Google Play Store Button"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"footer_play_store_url",children:"Google Play Store URL"}),e.jsx(l,{id:"footer_play_store_url",value:a.footer_play_store_url||"",onChange:s=>t("footer_play_store_url",s.target.value),placeholder:"https://play.google.com/store/apps/details?id=your.app",disabled:o||!a.footer_mobile_apps_enabled||!a.footer_play_store_enabled})]})]})]})]})]}),G=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"navbar_enabled",checked:a.navbar_enabled||!1,onChange:s=>t("navbar_enabled",s.target.checked),disabled:o,className:"rounded border-gray-300"}),e.jsx(r,{htmlFor:"navbar_enabled",children:"Enable Custom Navbar"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"navbar_style",children:"Navbar Style"}),e.jsxs("select",{id:"navbar_style",value:a.navbar_style||"default",onChange:s=>t("navbar_style",s.target.value),disabled:o,className:"w-full mt-1 rounded-md border-gray-300 shadow-sm",children:[e.jsx("option",{value:"default",children:"Default"}),e.jsx("option",{value:"minimal",children:"Minimal"}),e.jsx("option",{value:"bold",children:"Bold"})]})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"navbar_logo_position",children:"Logo Position"}),e.jsxs("select",{id:"navbar_logo_position",value:a.navbar_logo_position||"left",onChange:s=>t("navbar_logo_position",s.target.value),disabled:o,className:"w-full mt-1 rounded-md border-gray-300 shadow-sm",children:[e.jsx("option",{value:"left",children:"Left"}),e.jsx("option",{value:"center",children:"Center"}),e.jsx("option",{value:"right",children:"Right"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"navbar_show_search",checked:a.navbar_show_search||!1,onChange:s=>t("navbar_show_search",s.target.checked),disabled:o,className:"rounded border-gray-300"}),e.jsx(r,{htmlFor:"navbar_show_search",children:"Show Search Button"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"navbar_sticky",checked:a.navbar_sticky||!1,onChange:s=>t("navbar_sticky",s.target.checked),disabled:o,className:"rounded border-gray-300"}),e.jsx(r,{htmlFor:"navbar_sticky",children:"Sticky Navbar"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:"navbar_background_color",children:"Background Color"}),e.jsx(l,{id:"navbar_background_color",type:"color",value:a.navbar_background_color||"#ffffff",onChange:s=>t("navbar_background_color",s.target.value),disabled:o})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"navbar_text_color",children:"Text Color"}),e.jsx(l,{id:"navbar_text_color",type:"color",value:a.navbar_text_color||"#1f2937",onChange:s=>t("navbar_text_color",s.target.value),disabled:o})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"navbar_menu_id",children:"Navigation Menu"}),e.jsxs("select",{id:"navbar_menu_id",value:a.navbar_menu_id||"",onChange:s=>t("navbar_menu_id",s.target.value?parseInt(s.target.value):null),disabled:o,className:"w-full mt-1 rounded-md border-gray-300 shadow-sm",children:[e.jsx("option",{value:"",children:"No Menu Selected"}),C.map(s=>e.jsxs("option",{value:s.id,children:[s.name," ",s.location&&`(${s.location})`]},s.id))]}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"Select a menu to display in the navbar. Create menus in the Menu Management section."})]})]})]})});return e.jsxs(V,{children:[e.jsx(D,{title:"Site Settings - Admin"}),e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"flex items-center justify-between mb-6",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Site Settings"}),e.jsx("p",{className:"text-muted-foreground",children:"Manage your site's branding, logos, favicon, footer, and navbar settings"})]})}),e.jsxs("form",{onSubmit:A,children:[e.jsxs(Q,{value:m,onValueChange:T,className:"space-y-6",children:[e.jsxs(H,{className:"grid w-full grid-cols-4",children:[e.jsx(j,{value:"branding",children:"Branding"}),e.jsx(j,{value:"favicon",children:"Favicon"}),e.jsx(j,{value:"footer",children:"Footer"}),e.jsx(j,{value:"navbar",children:"Navbar"})]}),e.jsx(b,{value:"branding",children:e.jsxs(p,{children:[e.jsxs(u,{children:[e.jsx(g,{children:"Branding Settings"}),e.jsx(v,{children:"Configure your site's logo and branding elements"})]}),e.jsx(f,{children:E()})]})}),e.jsx(b,{value:"favicon",children:e.jsxs(p,{children:[e.jsxs(u,{children:[e.jsx(g,{children:"Favicon Settings"}),e.jsx(v,{children:"Configure your site's favicon for different browsers and devices"})]}),e.jsx(f,{children:B()})]})}),e.jsx(b,{value:"footer",children:e.jsxs(p,{children:[e.jsxs(u,{children:[e.jsx(g,{children:"Footer Settings"}),e.jsx(v,{children:"Customize your site's footer content, layout, and styling"})]}),e.jsx(f,{children:U()})]})}),e.jsx(b,{value:"navbar",children:e.jsxs(p,{children:[e.jsxs(u,{children:[e.jsx(g,{children:"Navbar Settings"}),e.jsx(v,{children:"Configure your site's navigation bar appearance and menu"})]}),e.jsx(f,{children:G()})]})})]}),e.jsxs("div",{className:"flex items-center justify-between mt-6 pt-6 border-t",children:[e.jsx("div",{className:"flex gap-2",children:e.jsxs(c,{type:"button",variant:"outline",onClick:()=>I(m),disabled:o,children:[e.jsx(X,{className:"w-4 h-4 mr-2"}),"Reset ",m.charAt(0).toUpperCase()+m.slice(1)," Settings"]})}),e.jsxs(c,{type:"submit",disabled:o,children:[e.jsx(Y,{className:"w-4 h-4 mr-2"}),o?"Saving...":"Save Settings"]})]})]}),e.jsx(q,{isOpen:M,onClose:()=>{N(!1),y("")},onSelect:R,multiple:!1,title:"Select Image",acceptedTypes:["image/*"]})]})]})}export{Pe as default};
