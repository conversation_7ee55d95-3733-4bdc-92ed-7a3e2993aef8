import{j as e,t as i}from"./app-J5EqS6dS.js";import{S as l}from"./smartphone-GGiwNneF.js";import{D as d}from"./database-s9JOA0jY.js";import{S as c}from"./shield-D9nQfigG.js";import{Z as n}from"./zap-BcmHRR4K.js";function x({children:s,title:t,description:a}){return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:e.jsxs("div",{className:"flex min-h-screen",children:[e.jsxs("div",{className:"hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 to-indigo-700 p-12 text-white relative overflow-hidden",children:[e.jsxs("div",{className:"absolute inset-0 opacity-10",children:[e.jsx("div",{className:"absolute top-20 left-20 w-32 h-32 border border-white/20 rounded-full"}),e.jsx("div",{className:"absolute top-40 right-32 w-24 h-24 border border-white/20 rounded-full"}),e.jsx("div",{className:"absolute bottom-32 left-32 w-40 h-40 border border-white/20 rounded-full"}),e.jsx("div",{className:"absolute bottom-20 right-20 w-28 h-28 border border-white/20 rounded-full"})]}),e.jsxs("div",{className:"relative z-10 flex flex-col justify-center max-w-md",children:[e.jsxs(i,{href:route("home"),className:"flex items-center space-x-3 mb-12",children:[e.jsx(l,{className:"h-10 w-10 text-white"}),e.jsx("span",{className:"text-2xl font-bold",children:"FixHaat"})]}),e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-3xl font-bold mb-4",children:"Find the Right Mobile Parts"}),e.jsx("p",{className:"text-blue-100 text-lg leading-relaxed",children:"Access our comprehensive database of mobile phone parts with detailed specifications and compatibility information."})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"flex-shrink-0 w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center",children:e.jsx(d,{className:"h-5 w-5 text-white"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-1",children:"Comprehensive Database"}),e.jsx("p",{className:"text-blue-100 text-sm",children:"300+ mobile models with detailed parts information"})]})]}),e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"flex-shrink-0 w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center",children:e.jsx(c,{className:"h-5 w-5 text-white"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-1",children:"Verified Compatibility"}),e.jsx("p",{className:"text-blue-100 text-sm",children:"All part compatibility information is verified and updated"})]})]}),e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"flex-shrink-0 w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center",children:e.jsx(n,{className:"h-5 w-5 text-white"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-1",children:"Lightning Fast Search"}),e.jsx("p",{className:"text-blue-100 text-sm",children:"Advanced search with instant results and filtering"})]})]})]})]})]})]}),e.jsx("div",{className:"flex-1 flex items-center justify-center p-6 lg:p-12",children:e.jsxs("div",{className:"w-full max-w-md",children:[e.jsx("div",{className:"lg:hidden text-center mb-8",children:e.jsxs(i,{href:route("home"),className:"inline-flex items-center space-x-2",children:[e.jsx(l,{className:"h-8 w-8 text-blue-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"FixHaat"})]})}),e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm dark:bg-gray-800/80 rounded-2xl shadow-xl border-0 p-8",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:t}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:a})]}),s]})]})})]})})}function j({children:s,title:t,description:a,...r}){return e.jsx(x,{title:t,description:a,...r,children:s})}export{j as A};
