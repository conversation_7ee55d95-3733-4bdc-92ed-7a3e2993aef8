import{r as g,j as e,Q as j,t as c}from"./app-J5EqS6dS.js";import{B as d}from"./smartphone-GGiwNneF.js";import{C as t,a as l,b as n,c as i}from"./card-9XCADs-4.js";import{B as x}from"./badge-BucYuCBs.js";import{A as u}from"./app-layout-ox1kAwY6.js";import{C as p}from"./circle-check-big-DOFoatRy.js";import{C as f}from"./crown-UDSxMtlm.js";import{Z as o}from"./zap-BcmHRR4K.js";import{F as y}from"./ImpersonationBanner-CYn5eDk6.js";import{C as N}from"./calendar-B-u_QN2Q.js";import{S as w}from"./shield-D9nQfigG.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./users-RYmOyic9.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./search-DBK6jUoc.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./eye-D-fsmYB2.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";function K({user:b,subscription:a,currentPlan:v,transactionId:s,remainingSearches:k}){g.useEffect(()=>{typeof window<"u"&&window.gtag&&s&&window.gtag("event","purchase",{transaction_id:s,value:a?29.99:0,currency:"USD",items:[{item_id:(a==null?void 0:a.plan_name)||"unknown",item_name:`${(a==null?void 0:a.plan_name)||"Unknown"} Subscription`,category:"subscription",quantity:1,price:a?29.99:0}]})},[s,a]);const m=r=>new Date(r).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),h=r=>{switch(r){case"paddle":return"Paddle";case"shurjopay":return"ShurjoPay";case"coinbase_commerce":return"Coinbase Commerce";case"offline":return"Offline Payment";default:return r}};return e.jsxs(u,{children:[e.jsx(j,{title:"Payment Successful"}),e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 py-12",children:e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("div",{className:"inline-flex items-center justify-center w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full mb-6",children:e.jsx(p,{className:"w-10 h-10 text-green-600 dark:text-green-400"})}),e.jsx("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"Payment Successful!"}),e.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto",children:"Thank you for your payment. Your subscription has been activated successfully."})]}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-8 mb-8",children:[e.jsxs(t,{className:"border-green-200 dark:border-green-800",children:[e.jsx(l,{children:e.jsxs(n,{className:"flex items-center gap-2",children:[e.jsx(f,{className:"w-5 h-5 text-yellow-500"}),"Subscription Details"]})}),e.jsx(i,{className:"space-y-4",children:a?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Plan:"}),e.jsx(x,{variant:"secondary",className:"capitalize",children:a.plan_name})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Status:"}),e.jsx(x,{variant:"default",className:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",children:a.status})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Started:"}),e.jsx("span",{className:"font-medium",children:m(a.current_period_start)})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Next Billing:"}),e.jsx("span",{className:"font-medium",children:m(a.current_period_end)})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Payment Method:"}),e.jsx("span",{className:"font-medium",children:h(a.payment_gateway)})]})]}):e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Subscription details are being processed..."})})]}),e.jsxs(t,{children:[e.jsx(l,{children:e.jsxs(n,{className:"flex items-center gap-2",children:[e.jsx(o,{className:"w-5 h-5 text-blue-500"}),"What's Next?"]})}),e.jsxs(i,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Start Searching"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"You now have unlimited access to our mobile parts database"})]})]}),e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Manage Your Subscription"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"View your subscription details and usage statistics"})]})]}),e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Get Support"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Contact our support team if you need any assistance"})]})]})]})]})]}),s&&e.jsxs(t,{className:"mb-8",children:[e.jsx(l,{children:e.jsxs(n,{className:"flex items-center gap-2",children:[e.jsx(y,{className:"w-5 h-5 text-gray-500"}),"Transaction Information"]})}),e.jsx(i,{children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Transaction ID:"}),e.jsx("code",{className:"bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm",children:s})]})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[e.jsx(d,{asChild:!0,size:"lg",className:"bg-blue-600 hover:bg-blue-700",children:e.jsxs(c,{href:route("search.index"),children:[e.jsx(o,{className:"w-4 h-4 mr-2"}),"Start Searching"]})}),e.jsx(d,{asChild:!0,variant:"outline",size:"lg",children:e.jsxs(c,{href:route("subscription.dashboard"),children:[e.jsx(N,{className:"w-4 h-4 mr-2"}),"View Dashboard"]})})]}),e.jsxs("div",{className:"text-center mt-8 p-6 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-center gap-2 mb-2",children:[e.jsx(w,{className:"w-5 h-5 text-green-500"}),e.jsx("span",{className:"font-medium text-gray-900 dark:text-white",children:"Secure Payment"})]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Your payment was processed securely. You will receive a confirmation email shortly."})]})]})})]})}export{K as default};
