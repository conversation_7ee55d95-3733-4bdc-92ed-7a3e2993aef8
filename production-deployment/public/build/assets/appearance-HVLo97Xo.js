import{u as l,j as t,Q as c}from"./app-J5EqS6dS.js";import{a}from"./smartphone-GGiwNneF.js";import{a as u,b as d,A as x}from"./app-layout-ox1kAwY6.js";import{M as g}from"./triangle-alert-BW76NKO9.js";import{S as b,H as h}from"./layout-CBB3cBA6.js";/* empty css            */import"./ImpersonationBanner-CYn5eDk6.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./badge-BucYuCBs.js";import"./crown-UDSxMtlm.js";import"./shield-D9nQfigG.js";import"./user-DCnDRzMf.js";import"./search-DBK6jUoc.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./eye-D-fsmYB2.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./globe-zfFlVOSX.js";function j({className:r="",...s}){const{appearance:i,setAppearance:n}=l(),o=[{value:"light",icon:u,label:"Light"},{value:"dark",icon:d,label:"Dark"},{value:"system",icon:g,label:"System"}];return t.jsx("div",{className:a("inline-flex gap-1 rounded-lg bg-neutral-100 p-1 dark:bg-neutral-800",r),...s,children:o.map(({value:e,icon:p,label:m})=>t.jsxs("button",{onClick:()=>n(e),className:a("flex items-center rounded-md px-3.5 py-1.5 transition-colors",i===e?"bg-white shadow-xs dark:bg-neutral-700 dark:text-neutral-100":"text-neutral-500 hover:bg-neutral-200/60 hover:text-black dark:text-neutral-400 dark:hover:bg-neutral-700/60"),children:[t.jsx(p,{className:"-ml-1 h-4 w-4"}),t.jsx("span",{className:"ml-1.5 text-sm",children:m})]},e))})}const f=[{title:"Appearance settings",href:"/settings/appearance"}];function K(){return t.jsxs(x,{breadcrumbs:f,children:[t.jsx(c,{title:"Appearance settings"}),t.jsx(b,{children:t.jsxs("div",{className:"space-y-6",children:[t.jsx(h,{title:"Appearance settings",description:"Update your account's appearance settings"}),t.jsx(j,{})]})})]})}export{K as default};
