import{r as N,j as e,Q as H,t as m,S as x}from"./app-J5EqS6dS.js";import{B as a}from"./smartphone-GGiwNneF.js";import{C as i,a as n,b as d,c,d as P}from"./card-9XCADs-4.js";import{B as u}from"./badge-BucYuCBs.js";import{A as B,C as L}from"./app-layout-ox1kAwY6.js";import{G as y,A as U,C as q}from"./ImpersonationBanner-CYn5eDk6.js";import{T as v}from"./trash-2-B3ZEh4hl.js";import{S as g}from"./search-DBK6jUoc.js";import{T as O}from"./target-BJCwZ93C.js";import{C as w}from"./circle-check-big-DOFoatRy.js";import{C}from"./calendar-B-u_QN2Q.js";import{T as M}from"./trending-up-BtixJGWw.js";import{F as S}from"./filter-DKJvAZFg.js";import{C as _}from"./chevron-down-C6yPNer6.js";import{Z as Q}from"./zap-BcmHRR4K.js";import{E as Z}from"./eye-D-fsmYB2.js";import{D as G}from"./download-fvx_BKiV.js";import{S as I}from"./users-RYmOyic9.js";import{A as k}from"./arrow-up-right-9Qf6vHK9.js";import{T as W}from"./triangle-alert-BW76NKO9.js";import{C as X}from"./clock-Brl7_5s7.js";import{R as J}from"./refresh-cw-b5UG9YKX.js";import{E as K}from"./ellipsis-Bwr8pvFI.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./globe-zfFlVOSX.js";import"./crown-UDSxMtlm.js";function De({searches:r,stats:o,filters:t,search_types:j,date_ranges:f}){const[h,T]=N.useState(t.type),[p,R]=N.useState(t.range),A=()=>{const s=new URLSearchParams;h!=="all"&&s.set("type",h),p!=="30d"&&s.set("range",p);const l=route("dashboard.history")+(s.toString()?"?"+s.toString():"");x.get(l)},b=(s="all")=>{const l=s==="all"?"Are you sure you want to clear your entire search history? This action cannot be undone.":`Are you sure you want to clear your search history from the ${f[s]}? This action cannot be undone.`;confirm(l)&&x.delete(route("dashboard.clear-history"),{data:{range:s},onSuccess:()=>{x.reload()},onError:$=>{console.error("Error clearing history:",$)}})},z=s=>{const l=new URLSearchParams({q:s.search_query,type:s.search_type});x.get(route("search.results")+"?"+l.toString())},E=()=>o.total_searches>0?Math.round(o.successful_searches/o.total_searches*100):0,F=s=>j[s]||s.charAt(0).toUpperCase()+s.slice(1),D=({search:s})=>e.jsx(i,{className:"group shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm hover:border-primary/30 hover:shadow-xl transition-all duration-300",children:e.jsx(c,{className:"p-2",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2 flex-1 min-w-0",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${s.results_count>0?"bg-green-500/10 border border-green-500/20":"bg-red-500/10 border border-red-500/20"}`,children:s.results_count>0?e.jsx(w,{className:"w-4 h-4 text-green-500"}):e.jsx(L,{className:"w-4 h-4 text-red-500"})})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h3",{className:"font-semibold text-foreground truncate group-hover:text-primary transition-colors",children:s.search_query}),e.jsx(u,{variant:"outline",className:"text-xs px-2 py-0.5 flex-shrink-0 border-primary/20 text-primary bg-primary/5",children:F(s.search_type)})]}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(X,{className:"w-3 h-3"}),e.jsx("span",{children:new Date(s.created_at).toLocaleString()})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(q,{className:"w-3 h-3"}),e.jsx("span",{className:s.results_count>0?"text-green-600 font-medium":"text-red-600",children:s.results_count>0?`${s.results_count} results`:"No results"})]})]})]})]}),e.jsxs("div",{className:"flex items-center gap-2 flex-shrink-0 ml-3",children:[e.jsxs(a,{variant:"outline",size:"sm",onClick:()=>z(s),className:"gap-1 opacity-70 group-hover:opacity-100 transition-opacity h-8 px-3",children:[e.jsx(J,{className:"w-3 h-3"}),"Repeat"]}),e.jsx(a,{variant:"ghost",size:"sm",className:"opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0",children:e.jsx(K,{className:"w-3 h-3"})})]})]})})});return e.jsxs(B,{children:[e.jsx(H,{title:"Search History"}),e.jsx("div",{className:"bg-gradient-to-br from-background via-muted/20 to-background min-h-screen",children:e.jsxs("div",{className:"container mx-auto px-4 py-6 max-w-7xl",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-3xl font-bold text-foreground mb-2 flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:e.jsx(y,{className:"w-6 h-6 text-primary"})}),"Search History"]}),e.jsx("p",{className:"text-muted-foreground text-lg",children:"Comprehensive overview of your search activity and patterns"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(a,{variant:"outline",size:"sm",onClick:()=>b("30d"),className:"gap-2",children:[e.jsx(v,{className:"w-4 h-4"}),"Clear Last 30 Days"]}),e.jsxs(a,{variant:"outline",size:"sm",onClick:()=>b("all"),className:"gap-2 text-destructive hover:text-destructive",children:[e.jsx(v,{className:"w-4 h-4"}),"Clear All"]})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsxs(i,{className:"shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:[e.jsxs(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(d,{className:"text-sm font-medium text-muted-foreground",children:"Total Searches"}),e.jsx("div",{className:"p-2 bg-blue-500/10 rounded-lg",children:e.jsx(g,{className:"h-4 w-4 text-blue-500"})})]}),e.jsxs(c,{children:[e.jsx("div",{className:"text-2xl font-bold text-foreground mb-1",children:o.total_searches}),e.jsx("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:e.jsx("span",{children:"All time searches"})})]})]}),e.jsxs(i,{className:"shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:[e.jsxs(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(d,{className:"text-sm font-medium text-muted-foreground",children:"Success Rate"}),e.jsx("div",{className:"p-2 bg-green-500/10 rounded-lg",children:e.jsx(O,{className:"h-4 w-4 text-green-500"})})]}),e.jsxs(c,{children:[e.jsxs("div",{className:"text-2xl font-bold text-foreground mb-1",children:[E(),"%"]}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[e.jsx(w,{className:"w-3 h-3 text-green-500"}),e.jsxs("span",{children:[o.successful_searches," successful searches"]})]})]})]}),e.jsxs(i,{className:"shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:[e.jsxs(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(d,{className:"text-sm font-medium text-muted-foreground",children:"This Week"}),e.jsx("div",{className:"p-2 bg-purple-500/10 rounded-lg",children:e.jsx(U,{className:"h-4 w-4 text-purple-500"})})]}),e.jsxs(c,{children:[e.jsx("div",{className:"text-2xl font-bold text-foreground mb-1",children:o.recent_searches}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[e.jsx(C,{className:"w-3 h-3"}),e.jsx("span",{children:"Recent searches"})]})]})]}),e.jsxs(i,{className:"shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:[e.jsxs(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(d,{className:"text-sm font-medium text-muted-foreground",children:"Most Searched"}),e.jsx("div",{className:"p-2 bg-orange-500/10 rounded-lg",children:e.jsx(M,{className:"h-4 w-4 text-orange-500"})})]}),e.jsxs(c,{children:[e.jsx("div",{className:"text-lg font-bold text-foreground mb-1 truncate",title:o.most_searched_term,children:o.most_searched_term||"No searches yet"}),e.jsx("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:e.jsx("span",{children:"Popular search term"})})]})]})]}),e.jsxs(i,{className:"mb-8 shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:[e.jsx(n,{className:"pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(d,{className:"flex items-center gap-3 text-xl",children:[e.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:e.jsx(S,{className:"w-5 h-5 text-primary"})}),"Search Filters"]}),e.jsx(P,{className:"text-base mt-1",children:"Filter your search history by type and date range"})]}),e.jsxs(u,{variant:"secondary",className:"text-sm px-3 py-1",children:[r.total," results"]})]})}),e.jsx(c,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"text-sm font-medium text-foreground flex items-center gap-2",children:[e.jsx(g,{className:"w-4 h-4 text-primary"}),"Search Type"]}),e.jsxs("div",{className:"relative",children:[e.jsx("select",{value:h,onChange:s=>T(s.target.value),className:"w-full px-4 py-3 bg-background border-2 border-border rounded-lg text-sm font-medium text-foreground focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all appearance-none cursor-pointer",children:Object.entries(j).map(([s,l])=>e.jsx("option",{value:s,children:l},s))}),e.jsx(_,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"text-sm font-medium text-foreground flex items-center gap-2",children:[e.jsx(C,{className:"w-4 h-4 text-primary"}),"Date Range"]}),e.jsxs("div",{className:"relative",children:[e.jsx("select",{value:p,onChange:s=>R(s.target.value),className:"w-full px-4 py-3 bg-background border-2 border-border rounded-lg text-sm font-medium text-foreground focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all appearance-none cursor-pointer",children:Object.entries(f).map(([s,l])=>e.jsx("option",{value:s,children:l},s))}),e.jsx(_,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-transparent",children:"Apply"}),e.jsxs(a,{onClick:A,className:"w-full h-12 gap-2 font-medium",children:[e.jsx(Q,{className:"w-4 h-4"}),"Apply Filters"]})]})]})})]}),r.data.length>0?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-xl font-semibold text-foreground flex items-center gap-2",children:[e.jsx(Z,{className:"w-5 h-5 text-primary"}),"Search Results"]}),e.jsxs("p",{className:"text-muted-foreground mt-1",children:["Showing ",r.from," to ",r.to," of ",r.total," searches"]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(a,{variant:"outline",size:"sm",className:"gap-2",children:[e.jsx(G,{className:"w-4 h-4"}),"Export"]}),e.jsxs(a,{variant:"outline",size:"sm",className:"gap-2",children:[e.jsx(I,{className:"w-4 h-4"}),"Options"]})]})]})}),e.jsx("div",{className:"space-y-4 mb-8",children:r.data.map(s=>e.jsx(D,{search:s},s.id))}),r.last_page>1&&e.jsx(i,{className:"shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:e.jsx(c,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"text-sm text-muted-foreground",children:[e.jsxs("span",{className:"font-medium text-foreground",children:[r.from,"-",r.to]})," ","of"," ",e.jsx("span",{className:"font-medium text-foreground",children:r.total})," ","searches"]}),e.jsxs("div",{className:"flex items-center gap-3",children:[r.current_page>1&&e.jsx(m,{href:`${route("dashboard.history")}?page=${r.current_page-1}`,children:e.jsxs(a,{variant:"outline",size:"sm",className:"gap-2",children:[e.jsx(k,{className:"w-4 h-4 rotate-180"}),"Previous"]})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Page"}),e.jsxs(u,{variant:"secondary",className:"px-3 py-1",children:[r.current_page," of ",r.last_page]})]}),r.current_page<r.last_page&&e.jsx(m,{href:`${route("dashboard.history")}?page=${r.current_page+1}`,children:e.jsxs(a,{variant:"outline",size:"sm",className:"gap-2",children:["Next",e.jsx(k,{className:"w-4 h-4"})]})})]})]})})})]}):e.jsx(i,{className:"shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:e.jsx(c,{className:"text-center py-16",children:e.jsxs("div",{className:"max-w-md mx-auto",children:[e.jsx("div",{className:"w-24 h-24 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-6",children:t.type!=="all"||t.range!=="30d"?e.jsx(W,{className:"w-10 h-10 text-muted-foreground"}):e.jsx(y,{className:"w-10 h-10 text-muted-foreground"})}),e.jsx("h3",{className:"text-2xl font-bold text-foreground mb-3",children:t.type!=="all"||t.range!=="30d"?"No matching searches found":"No search history yet"}),e.jsx("p",{className:"text-muted-foreground text-lg mb-8 leading-relaxed",children:t.type!=="all"||t.range!=="30d"?"Try adjusting your filters to see more results, or start a new search to add to your history.":"Start exploring our comprehensive mobile parts database to build your search history and track your activity."}),e.jsxs("div",{className:"flex items-center justify-center gap-4",children:[e.jsx(m,{href:route("search.index"),children:e.jsxs(a,{size:"lg",className:"gap-2",children:[e.jsx(g,{className:"w-5 h-5"}),"Start Searching"]})}),(t.type!=="all"||t.range!=="30d")&&e.jsx(m,{href:route("dashboard.history"),children:e.jsxs(a,{variant:"outline",size:"lg",className:"gap-2",children:[e.jsx(S,{className:"w-5 h-5"}),"Clear Filters"]})})]})]})})})]})})]})}export{De as default};
