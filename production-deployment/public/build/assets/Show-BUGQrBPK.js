import{j as e,Q as g,t as i}from"./app-J5EqS6dS.js";import{C as p,a as u,b as j,d as f,c as w}from"./card-9XCADs-4.js";import{B as n,S as N}from"./smartphone-GGiwNneF.js";import{B as y}from"./badge-BucYuCBs.js";import{A as b,G as v}from"./app-layout-ox1kAwY6.js";import{A as c}from"./arrow-left-D4U9AVF9.js";import{C as _}from"./calendar-B-u_QN2Q.js";import{G as S}from"./globe-zfFlVOSX.js";import{U as l}from"./user-DCnDRzMf.js";import{S as m}from"./users-RYmOyic9.js";import{M as C}from"./triangle-alert-BW76NKO9.js";import{A,F as L,ak as B}from"./ImpersonationBanner-CYn5eDk6.js";import{M as D}from"./mail-CDon-vZy.js";import{E as k}from"./eye-D-fsmYB2.js";import{P as U}from"./package-CoyvngX8.js";import{S as O}from"./search-DBK6jUoc.js";import{L as P}from"./log-in-By89FsAx.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";const E=[{title:"Activity Log",href:"/activity"},{title:"Activity Details",href:"#"}],M=s=>{switch(s){case"login":return e.jsx(P,{className:"w-8 h-8 text-green-500"});case"logout":return e.jsx(B,{className:"w-8 h-8 text-gray-500"});case"search":return e.jsx(O,{className:"w-8 h-8 text-blue-500"});case"view_part":return e.jsx(U,{className:"w-8 h-8 text-purple-500"});case"view_category":return e.jsx(v,{className:"w-8 h-8 text-orange-500"});case"view_brand":return e.jsx(N,{className:"w-8 h-8 text-indigo-500"});case"view_model":return e.jsx(k,{className:"w-8 h-8 text-teal-500"});case"subscription_change":return e.jsx(L,{className:"w-8 h-8 text-yellow-500"});case"profile_update":return e.jsx(l,{className:"w-8 h-8 text-pink-500"});case"password_change":return e.jsx(m,{className:"w-8 h-8 text-red-500"});case"email_verification":return e.jsx(D,{className:"w-8 h-8 text-cyan-500"});default:return e.jsx(A,{className:"w-8 h-8 text-gray-500"})}},V=s=>{switch(s){case"login":return"bg-green-100 text-green-800";case"logout":return"bg-gray-100 text-gray-800";case"search":return"bg-blue-100 text-blue-800";case"view_part":return"bg-purple-100 text-purple-800";case"view_category":return"bg-orange-100 text-orange-800";case"view_brand":return"bg-indigo-100 text-indigo-800";case"view_model":return"bg-teal-100 text-teal-800";case"subscription_change":return"bg-yellow-100 text-yellow-800";case"profile_update":return"bg-pink-100 text-pink-800";case"password_change":return"bg-red-100 text-red-800";case"email_verification":return"bg-cyan-100 text-cyan-800";default:return"bg-gray-100 text-gray-800"}},G=s=>{switch(s){case"login":return"Login";case"logout":return"Logout";case"search":return"Search";case"view_part":return"View Part";case"view_category":return"View Category";case"view_brand":return"View Brand";case"view_model":return"View Model";case"subscription_change":return"Subscription Change";case"profile_update":return"Profile Update";case"password_change":return"Password Change";case"email_verification":return"Email Verification";default:return s.replace("_"," ").replace(/\b\w/g,r=>r.toUpperCase())}};function xe({activity:s}){const r=t=>new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit",second:"2-digit"}),d=t=>{const a=t.includes("Chrome")?"Chrome":t.includes("Firefox")?"Firefox":t.includes("Safari")?"Safari":t.includes("Edge")?"Edge":"Unknown",h=t.includes("Windows")?"Windows":t.includes("Mac")?"macOS":t.includes("Linux")?"Linux":t.includes("Android")?"Android":t.includes("iOS")?"iOS":"Unknown";return{browser:a,os:h}},{browser:o,os:x}=d(s.user_agent);return e.jsxs(b,{breadcrumbs:E,children:[e.jsx(g,{title:`Activity: ${s.description}`}),e.jsxs("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(i,{href:route("activity.index"),children:e.jsxs(n,{variant:"outline",size:"sm",children:[e.jsx(c,{className:"w-4 h-4 mr-2"}),"Back to Activity Log"]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Activity Details"}),e.jsx("p",{className:"text-gray-600",children:"Detailed information about this activity"})]})]})}),e.jsxs(p,{className:"max-w-4xl",children:[e.jsx(u,{children:e.jsx("div",{className:"flex items-start justify-between",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[M(s.activity_type),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(j,{className:"text-xl",children:s.description}),e.jsx(y,{className:`${V(s.activity_type)}`,children:G(s.activity_type)})]}),e.jsxs(f,{children:["Activity performed on ",r(s.created_at)]})]})]})})}),e.jsxs(w,{children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{children:[e.jsxs("h4",{className:"text-sm font-medium text-gray-900 mb-3 flex items-center",children:[e.jsx(_,{className:"w-4 h-4 mr-2"}),"Timing"]}),e.jsxs("div",{className:"bg-white border rounded-lg p-3",children:[e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Date & Time:"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:r(s.created_at)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Activity ID:"}),e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["#",s.id]})]})]})]}),e.jsxs("div",{children:[e.jsxs("h4",{className:"text-sm font-medium text-gray-900 mb-3 flex items-center",children:[e.jsx(S,{className:"w-4 h-4 mr-2"}),"Location & Device"]}),e.jsxs("div",{className:"bg-white border rounded-lg p-3 space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"IP Address:"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:s.ip_address})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Browser:"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:o})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Operating System:"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:x})]})]})]})]}),s.performedBy&&e.jsxs("div",{className:"mb-6",children:[e.jsxs("h4",{className:"text-sm font-medium text-gray-900 mb-3 flex items-center",children:[e.jsx(l,{className:"w-4 h-4 mr-2"}),"Performed By"]}),e.jsxs("div",{className:"bg-white border rounded-lg p-3",children:[e.jsx("p",{className:"font-medium text-gray-900",children:s.performedBy.name}),e.jsx("p",{className:"text-sm text-gray-600",children:s.performedBy.email}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"This activity was performed by an administrator"})]})]}),s.metadata&&Object.keys(s.metadata).length>0&&e.jsxs("div",{className:"mb-6",children:[e.jsxs("h4",{className:"text-sm font-medium text-gray-900 mb-3 flex items-center",children:[e.jsx(m,{className:"w-4 h-4 mr-2"}),"Additional Details"]}),e.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Object.entries(s.metadata).map(([t,a])=>e.jsxs("div",{className:"flex justify-between",children:[e.jsxs("span",{className:"text-sm text-gray-600 capitalize",children:[t.replace("_"," "),":"]}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:typeof a=="object"?JSON.stringify(a):String(a)})]},t))})})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("h4",{className:"text-sm font-medium text-gray-900 mb-3 flex items-center",children:[e.jsx(C,{className:"w-4 h-4 mr-2"}),"User Agent"]}),e.jsx("div",{className:"bg-gray-50 rounded-lg p-3",children:e.jsx("p",{className:"text-xs text-gray-600 font-mono break-all",children:s.user_agent})})]}),e.jsx("div",{className:"pt-6 border-t",children:e.jsx("div",{className:"flex justify-between items-center",children:e.jsx(i,{href:route("activity.index"),children:e.jsxs(n,{variant:"outline",children:[e.jsx(c,{className:"w-4 h-4 mr-2"}),"Back to Activity Log"]})})})})]})]})]})]})}export{xe as default};
