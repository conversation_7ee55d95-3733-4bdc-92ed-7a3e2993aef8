import{j as e,Q as u,t as i}from"./app-J5EqS6dS.js";import{C as l,a as m,b as n,c,d as N}from"./card-9XCADs-4.js";import{B as o}from"./badge-BucYuCBs.js";import{B as d}from"./smartphone-GGiwNneF.js";import{A as g,M as w}from"./app-layout-ox1kAwY6.js";import{A as v}from"./arrow-left-D4U9AVF9.js";import{B as j}from"./ImpersonationBanner-CYn5eDk6.js";import{M as b}from"./mail-open-D1_362Am.js";import{M as y}from"./mail-CDon-vZy.js";import{U as p}from"./user-DCnDRzMf.js";import{C as f}from"./calendar-B-u_QN2Q.js";import{C}from"./circle-alert-C6UwDlxH.js";import{C as B}from"./circle-check-big-DOFoatRy.js";import{T as A,I as S}from"./triangle-alert-BW76NKO9.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./users-RYmOyic9.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./search-DBK6jUoc.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./eye-D-fsmYB2.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";const M=({type:s})=>{const t={info:{icon:S,className:"bg-blue-100 text-blue-800"},warning:{icon:A,className:"bg-yellow-100 text-yellow-800"},success:{icon:B,className:"bg-green-100 text-green-800"},error:{icon:C,className:"bg-red-100 text-red-800"},announcement:{icon:w,className:"bg-purple-100 text-purple-800"}},{icon:r,className:a}=t[s]||t.info;return e.jsxs(o,{className:`flex items-center gap-1 ${a}`,children:[e.jsx(r,{className:"h-3 w-3"}),s.charAt(0).toUpperCase()+s.slice(1)]})};function ae({notification:s}){var t,r,a,x,h;return e.jsxs(g,{children:[e.jsx(u,{title:`Notification: ${s.title}`}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(i,{href:"/admin/notifications",children:e.jsxs(d,{variant:"outline",size:"sm",children:[e.jsx(v,{className:"h-4 w-4 mr-2"}),"Back to Notifications"]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:"Notification Details"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"View notification information"})]})]})}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[e.jsxs(l,{children:[e.jsx(m,{children:e.jsxs(n,{className:"flex items-center gap-2",children:[e.jsx(j,{className:"h-5 w-5"}),"Notification Content"]})}),e.jsxs(c,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Title"}),e.jsx("p",{className:"text-lg font-medium",children:s.title})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Message"}),e.jsx("p",{className:"text-sm whitespace-pre-wrap",children:s.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Type"}),e.jsx("div",{className:"mt-1",children:e.jsx(M,{type:s.type})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Status"}),e.jsx("div",{className:"mt-1",children:s.read_at?e.jsxs(o,{className:"bg-gray-100 text-gray-800 flex items-center gap-1 w-fit",children:[e.jsx(b,{className:"h-3 w-3"}),"Read"]}):e.jsxs(o,{className:"bg-blue-100 text-blue-800 flex items-center gap-1 w-fit",children:[e.jsx(y,{className:"h-3 w-3"}),"Unread"]})})]})]})]}),e.jsxs(l,{children:[e.jsx(m,{children:e.jsxs(n,{className:"flex items-center gap-2",children:[e.jsx(p,{className:"h-5 w-5"}),"Recipient & Sender"]})}),e.jsxs(c,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Recipient"}),e.jsxs("div",{className:"mt-1",children:[e.jsx("p",{className:"font-medium",children:((t=s.user)==null?void 0:t.name)||"Unknown User"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:((r=s.user)==null?void 0:r.email)||"No email"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Sent By"}),e.jsxs("div",{className:"mt-1",children:[e.jsx("p",{className:"font-medium",children:((a=s.sentBy)==null?void 0:a.name)||"System"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:((x=s.sentBy)==null?void 0:x.email)||"System notification"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Sent At"}),e.jsxs("div",{className:"mt-1 flex items-center gap-2",children:[e.jsx(f,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{children:new Date(s.created_at).toLocaleString()})]})]}),s.read_at&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Read At"}),e.jsxs("div",{className:"mt-1 flex items-center gap-2",children:[e.jsx(f,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{children:new Date(s.read_at).toLocaleString()})]})]})]})]})]}),e.jsxs(l,{children:[e.jsxs(m,{children:[e.jsx(n,{children:"Actions"}),e.jsx(N,{children:"Manage this notification"})]}),e.jsx(c,{children:e.jsxs("div",{className:"flex items-center gap-4",children:[((h=s.user)==null?void 0:h.id)&&e.jsx(i,{href:`/admin/users/${s.user.id}`,children:e.jsxs(d,{variant:"outline",children:[e.jsx(p,{className:"h-4 w-4 mr-2"}),"View User Profile"]})}),e.jsx(i,{href:"/admin/notifications",children:e.jsxs(d,{variant:"outline",children:[e.jsx(j,{className:"h-4 w-4 mr-2"}),"All Notifications"]})})]})})]})]})})]})}export{ae as default};
