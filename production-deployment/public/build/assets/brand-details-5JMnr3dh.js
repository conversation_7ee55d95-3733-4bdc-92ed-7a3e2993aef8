import{j as e,Q as m,t as r}from"./app-J5EqS6dS.js";import{B as a}from"./smartphone-GGiwNneF.js";import{C as t,c as i,a as l,b as c}from"./card-9XCADs-4.js";import{B as n}from"./badge-BucYuCBs.js";import{A as o}from"./app-layout-ox1kAwY6.js";import{M as x}from"./map-pin-BdPUntxP.js";import{G as h}from"./globe-zfFlVOSX.js";import{E as d}from"./external-link-A4n9PP4e.js";import{B as p}from"./building-Dgyml3QN.js";import{S as j}from"./search-DBK6jUoc.js";import{A as f}from"./arrow-left-D4U9AVF9.js";/* empty css            */import"./ImpersonationBanner-CYn5eDk6.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./crown-UDSxMtlm.js";import"./shield-D9nQfigG.js";import"./user-DCnDRzMf.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./eye-D-fsmYB2.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./triangle-alert-BW76NKO9.js";function q({brand:s}){return e.jsxs(o,{children:[e.jsx(m,{title:s.name}),e.jsx("div",{className:"py-12",children:e.jsxs("div",{className:"max-w-7xl mx-auto sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-6 text-sm text-gray-600",children:[e.jsx(r,{href:route("search.index"),className:"hover:text-gray-900",children:"Search"}),e.jsx("span",{children:"/"}),e.jsx("span",{className:"text-gray-900",children:s.name})]}),e.jsxs("div",{className:"grid lg:grid-cols-3 gap-8",children:[e.jsx("div",{className:"lg:col-span-2 space-y-6",children:e.jsx(t,{children:e.jsx(i,{className:"p-6",children:e.jsxs("div",{className:"flex items-start gap-4 mb-4",children:[s.logo_url&&e.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden",children:e.jsx("img",{src:s.logo_url,alt:`${s.name} logo`,className:"w-full h-full object-contain"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:s.name}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[s.country&&e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(x,{className:"w-4 h-4"}),s.country]}),e.jsx(n,{variant:s.is_active?"default":"secondary",children:s.is_active?"Active":"Inactive"})]})]}),s.website&&e.jsx(r,{href:s.website,target:"_blank",rel:"noopener noreferrer",children:e.jsxs(a,{variant:"outline",size:"sm",children:[e.jsx(h,{className:"w-4 h-4 mr-2"}),"Website",e.jsx(d,{className:"w-3 h-3 ml-1"})]})})]})})})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs(t,{children:[e.jsx(l,{children:e.jsxs(c,{className:"flex items-center gap-2",children:[e.jsx(p,{className:"w-5 h-5"}),"Brand Information"]})}),e.jsx(i,{children:e.jsxs("div",{className:"space-y-3",children:[s.country&&e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Country:"}),e.jsx("span",{className:"ml-2 text-gray-900",children:s.country})]}),s.website&&e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Website:"}),e.jsx("a",{href:s.website,target:"_blank",rel:"noopener noreferrer",className:"ml-2 text-blue-600 hover:text-blue-800",children:"Visit Website"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Status:"}),e.jsx("span",{className:"ml-2 text-gray-900",children:s.is_active?"Active":"Inactive"})]})]})})]}),e.jsxs(t,{children:[e.jsx(l,{children:e.jsx(c,{children:"Actions"})}),e.jsx(i,{children:e.jsxs("div",{className:"space-y-3",children:[e.jsx(r,{href:route("search.brand",s.slug||s.id),children:e.jsxs(a,{className:"w-full",children:[e.jsx(j,{className:"w-4 h-4 mr-2"}),"Search ",s.name," Parts"]})}),e.jsx(r,{href:route("search.index"),children:e.jsxs(a,{className:"w-full",variant:"outline",children:[e.jsx(f,{className:"w-4 h-4 mr-2"}),"Back to Search"]})})]})})]})]})]})]})})]})}export{q as default};
