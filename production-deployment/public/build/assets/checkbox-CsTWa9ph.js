import{r as i,j as o}from"./app-J5EqS6dS.js";import{u as N,a as L}from"./smartphone-GGiwNneF.js";import{c as O,u as H,a as j,e as G}from"./index-D86BnqlV.js";import{u as K}from"./index-Ba8m9N9L.js";import{P as U}from"./index-BzZWUWqx.js";import{P as R}from"./index-CJpBU2i9.js";import{C as X}from"./check-C7SdgHPn.js";var y="Checkbox",[$,ne]=O(y),[J,_]=$(y);function Q(t){const{__scopeCheckbox:n,checked:c,children:l,defaultChecked:s,disabled:e,form:f,name:h,onCheckedChange:d,required:m,value:k="on",internal_do_not_use_render:u}=t,[p,v]=H({prop:c,defaultProp:s??!1,onChange:d,caller:y}),[x,C]=i.useState(null),[g,r]=i.useState(null),a=i.useRef(!1),E=x?!!f||!!x.closest("form"):!0,P={checked:p,disabled:e,setChecked:v,control:x,setControl:C,name:h,form:f,value:k,hasConsumerStoppedPropagationRef:a,required:m,defaultChecked:b(s)?!1:s,isFormControl:E,bubbleInput:g,setBubbleInput:r};return o.jsx(J,{scope:n,...P,children:V(u)?u(P):l})}var S="CheckboxTrigger",w=i.forwardRef(({__scopeCheckbox:t,onKeyDown:n,onClick:c,...l},s)=>{const{control:e,value:f,disabled:h,checked:d,required:m,setControl:k,setChecked:u,hasConsumerStoppedPropagationRef:p,isFormControl:v,bubbleInput:x}=_(S,t),C=N(s,k),g=i.useRef(d);return i.useEffect(()=>{const r=e==null?void 0:e.form;if(r){const a=()=>u(g.current);return r.addEventListener("reset",a),()=>r.removeEventListener("reset",a)}},[e,u]),o.jsx(R.button,{type:"button",role:"checkbox","aria-checked":b(d)?"mixed":d,"aria-required":m,"data-state":z(d),"data-disabled":h?"":void 0,disabled:h,value:f,...l,ref:C,onKeyDown:j(n,r=>{r.key==="Enter"&&r.preventDefault()}),onClick:j(c,r=>{u(a=>b(a)?!0:!a),x&&v&&(p.current=r.isPropagationStopped(),p.current||r.stopPropagation())})})});w.displayName=S;var B=i.forwardRef((t,n)=>{const{__scopeCheckbox:c,name:l,checked:s,defaultChecked:e,required:f,disabled:h,value:d,onCheckedChange:m,form:k,...u}=t;return o.jsx(Q,{__scopeCheckbox:c,checked:s,defaultChecked:e,disabled:h,required:f,onCheckedChange:m,name:l,form:k,value:d,internal_do_not_use_render:({isFormControl:p})=>o.jsxs(o.Fragment,{children:[o.jsx(w,{...u,ref:n,__scopeCheckbox:c}),p&&o.jsx(A,{__scopeCheckbox:c})]})})});B.displayName=y;var M="CheckboxIndicator",T=i.forwardRef((t,n)=>{const{__scopeCheckbox:c,forceMount:l,...s}=t,e=_(M,c);return o.jsx(U,{present:l||b(e.checked)||e.checked===!0,children:o.jsx(R.span,{"data-state":z(e.checked),"data-disabled":e.disabled?"":void 0,...s,ref:n,style:{pointerEvents:"none",...t.style}})})});T.displayName=M;var q="CheckboxBubbleInput",A=i.forwardRef(({__scopeCheckbox:t,...n},c)=>{const{control:l,hasConsumerStoppedPropagationRef:s,checked:e,defaultChecked:f,required:h,disabled:d,name:m,value:k,form:u,bubbleInput:p,setBubbleInput:v}=_(q,t),x=N(c,v),C=K(e),g=G(l);i.useEffect(()=>{const a=p;if(!a)return;const E=window.HTMLInputElement.prototype,I=Object.getOwnPropertyDescriptor(E,"checked").set,D=!s.current;if(C!==e&&I){const F=new Event("click",{bubbles:D});a.indeterminate=b(e),I.call(a,b(e)?!1:e),a.dispatchEvent(F)}},[p,C,e,s]);const r=i.useRef(b(e)?!1:e);return o.jsx(R.input,{type:"checkbox","aria-hidden":!0,defaultChecked:f??r.current,required:h,disabled:d,name:m,value:k,form:u,...n,tabIndex:-1,ref:x,style:{...n.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});A.displayName=q;function V(t){return typeof t=="function"}function b(t){return t==="indeterminate"}function z(t){return b(t)?"indeterminate":t?"checked":"unchecked"}function se({className:t,...n}){return o.jsx(B,{"data-slot":"checkbox",className:L("peer border-input data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...n,children:o.jsx(T,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:o.jsx(X,{className:"size-3.5"})})})}export{se as C};
