import{x as n,j as s,Q as d}from"./app-J5EqS6dS.js";import{I as l}from"./input-error-SDo-ayIc.js";import{B as c}from"./smartphone-GGiwNneF.js";import{I as u}from"./input-Bo8dOn9p.js";import{L as f}from"./label-BlOrdc-X.js";import{A as w}from"./auth-layout-Do0a8FOS.js";import{L as x}from"./loader-circle-B1NtNhL1.js";/* empty css            */import"./index-CJpBU2i9.js";import"./database-s9JOA0jY.js";import"./shield-D9nQfigG.js";import"./zap-BcmHRR4K.js";function A(){const{data:a,setData:e,post:t,processing:o,errors:i,reset:p}=n({password:""}),m=r=>{r.preventDefault(),t(route("password.confirm"),{onFinish:()=>p("password")})};return s.jsxs(w,{title:"Confirm your password",description:"This is a secure area of the application. Please confirm your password before continuing.",children:[s.jsx(d,{title:"Confirm password"}),s.jsx("form",{onSubmit:m,children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"grid gap-2",children:[s.jsx(f,{htmlFor:"password",children:"Password"}),s.jsx(u,{id:"password",type:"password",name:"password",placeholder:"Password",autoComplete:"current-password",value:a.password,autoFocus:!0,onChange:r=>e("password",r.target.value)}),s.jsx(l,{message:i.password})]}),s.jsx("div",{className:"flex items-center",children:s.jsxs(c,{className:"w-full",disabled:o,children:[o&&s.jsx(x,{className:"h-4 w-4 animate-spin"}),"Confirm password"]})})]})})]})}export{A as default};
