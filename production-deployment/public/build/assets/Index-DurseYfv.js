import{r as n,j as e,Q as Ie,t as f,S as v}from"./app-J5EqS6dS.js";import{C as H,c as K,a as Ee,b as Te,d as Ve}from"./card-9XCADs-4.js";import{B as g}from"./badge-BucYuCBs.js";import{B as a,S as me}from"./smartphone-GGiwNneF.js";import{I as $e}from"./input-Bo8dOn9p.js";import{L as y}from"./label-BlOrdc-X.js";import{S as C,a as k,b as A,c as D,d as c}from"./select-CIhY0l9J.js";import{U as Pe,P as he,X as xe,a as Be,D as Re,b as Le,c as Ue,d as Ye,e as Ge,f as Oe,t as m}from"./ImpersonationBanner-CYn5eDk6.js";import{C as pe}from"./checkbox-CsTWa9ph.js";import{A as He,G as Ke}from"./app-layout-ox1kAwY6.js";import{u as Qe}from"./use-delete-confirmation-CFAJok5Z.js";import{F as ue}from"./file-text-Dx6bYLtE.js";import{D as je}from"./download-fvx_BKiV.js";import{S as fe}from"./search-DBK6jUoc.js";import{F as We}from"./filter-DKJvAZFg.js";import{A as E,a as T,b as Q}from"./arrow-up-DSYswbzJ.js";import{T as Xe}from"./table-gSl3ppmW.js";import{L as Je}from"./list-CNjrM85i.js";import{C as W}from"./calendar-B-u_QN2Q.js";import{E as X}from"./external-link-A4n9PP4e.js";import{E as J}from"./eye-D-fsmYB2.js";import{S as Z}from"./square-pen-Bepbg6wc.js";import{T as q}from"./trash-2-B3ZEh4hl.js";import{C as Ze}from"./chevron-left-C6ZNA5qQ.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./index-BzZWUWqx.js";import"./crown-UDSxMtlm.js";import"./shield-D9nQfigG.js";import"./user-DCnDRzMf.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./lock-Tx_yfI4R.js";import"./mail-CDon-vZy.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";function Ls({models:t,filters:ee,queryParams:N}){var ce;const{showDeleteConfirmation:ge}=Qe(),[h,se]=n.useState(N.search||""),[x,ae]=n.useState(N.brand_id||"all"),[p,te]=n.useState(N.status||"all"),[u,re]=n.useState(N.release_year||"all"),[i,V]=n.useState(N.sort_by||"name"),[d,le]=n.useState(N.sort_order||"asc"),[$,ve]=n.useState(!1),[o,Ne]=n.useState(N.view||"table"),[b,P]=n.useState([]),[be,M]=n.useState(!1),[_,we]=n.useState("skip"),[w,B]=n.useState(null),[F,R]=n.useState(!1),S=n.useRef(null),z=h||x!=="all"||p!=="all"||u!=="all",L=s=>{const r={page:s,...h&&{search:h},...x!=="all"&&{brand_id:x},...p!=="all"&&{status:p},...u!=="all"&&{release_year:u},...i!=="name"&&{sort_by:i},...d!=="asc"&&{sort_order:d},...o!=="table"&&{view:o}};v.get("/admin/models",r,{preserveState:!0,preserveScroll:!0})},ie=()=>{const s={...h&&{search:h},...x!=="all"&&{brand_id:x},...p!=="all"&&{status:p},...u!=="all"&&{release_year:u},...i!=="name"&&{sort_by:i},...d!=="asc"&&{sort_order:d},...o!=="table"&&{view:o}};v.get("/admin/models",s,{preserveState:!0,preserveScroll:!1})},ne=()=>{se(""),ae("all"),te("all"),re("all"),V("name"),le("asc"),v.get("/admin/models",{view:o!=="table"?o:void 0},{preserveState:!0,preserveScroll:!1})},I=s=>{const r=i===s&&d==="asc"?"desc":"asc";V(s),le(r);const l={...h&&{search:h},...x!=="all"&&{brand_id:x},...p!=="all"&&{status:p},...u!=="all"&&{release_year:u},sort_by:s,sort_order:r,...o!=="table"&&{view:o}};v.get("/admin/models",l,{preserveState:!0,preserveScroll:!0})},U=s=>{Ne(s);const r={...h&&{search:h},...x!=="all"&&{brand_id:x},...p!=="all"&&{status:p},...u!=="all"&&{release_year:u},...i!=="name"&&{sort_by:i},...d!=="asc"&&{sort_order:d},...s!=="table"&&{view:s}};v.get("/admin/models",r,{preserveState:!0,preserveScroll:!0})},Y=s=>{ge({title:`Delete "${s.brand.name} ${s.name}"?`,description:"This action cannot be undone. All associated parts compatibility will also be removed.",onConfirm:()=>{v.delete(`/admin/models/${s.id}`,{onSuccess:()=>{m.success(`Model "${s.brand.name} ${s.name}" has been deleted successfully.`)},onError:r=>{const l=r.message||"Failed to delete model. It may have associated parts.";m.error(l)}})},onCancel:()=>{m.info("Delete cancelled")}})},Se=()=>{const s=new URLSearchParams;h&&s.append("search",h),x!=="all"&&s.append("brand_id",x),p!=="all"&&s.append("status",p),u!=="all"&&s.append("release_year",u),i!=="name"&&s.append("sort_by",i),d!=="asc"&&s.append("sort_order",d),window.location.href=`/admin/models/export?${s.toString()}`,m.success("Export started. Your download will begin shortly.")},_e=()=>{if(b.length===0){m.error("Please select models to export");return}const s=new URLSearchParams;b.forEach(r=>s.append("ids[]",r.toString())),window.location.href=`/admin/models/export?${s.toString()}`,m.success("Export started. Your download will begin shortly.")},ye=()=>{window.location.href="/admin/models/template/download",m.success("Template download started.")},Ce=()=>{var s;(s=S.current)==null||s.click()},ke=s=>{var l;const r=(l=s.target.files)==null?void 0:l[0];if(r){if(!r.name.toLowerCase().endsWith(".csv")){m.error("Please select a CSV file");return}B(r),M(!0)}},Ae=()=>{var l;if(!w)return;R(!0);const s=new FormData;s.append("file",w),s.append("duplicate_action",_);const r=(l=document.querySelector('meta[name="csrf-token"]'))==null?void 0:l.getAttribute("content");r&&s.append("_token",r),v.post("/admin/bulk-import/models",s,{forceFormData:!0,onSuccess:j=>{var de,oe;const G=((de=j.props)==null?void 0:de.flash)||{},O=((oe=j.props)==null?void 0:oe.import_errors)||G.import_errors;O&&O.length>0&&m.warning(`Import completed with ${O.length} errors. Please check the data and try again.`),M(!1),B(null),R(!1),S.current&&(S.current.value=""),v.reload()},onError:j=>{j.file?m.error(`File error: ${j.file}`):j.message?m.error(j.message):j.import_errors?m.error("Import completed with errors. Please check your data and try again."):m.error("Import failed. Please check your CSV format and try again."),R(!1)}})},De=s=>{P(r=>r.includes(s)?r.filter(l=>l!==s):[...r,s])},Me=()=>{b.length===t.data.length?P([]):P(t.data.map(s=>s.id))},Fe=()=>e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b",children:[e.jsx("th",{className:"text-left p-3 w-12",children:e.jsx(pe,{checked:b.length===t.data.length&&t.data.length>0,onCheckedChange:Me,"aria-label":"Select all models"})}),e.jsx("th",{className:"text-left p-3",children:e.jsxs(a,{variant:"ghost",onClick:()=>I("name"),className:"h-auto p-0 font-semibold hover:bg-transparent",children:["Model",i==="name"&&(d==="asc"?e.jsx(E,{className:"ml-1 h-4 w-4"}):e.jsx(T,{className:"ml-1 h-4 w-4"})),i!=="name"&&e.jsx(Q,{className:"ml-1 h-4 w-4 opacity-50"})]})}),e.jsx("th",{className:"text-left p-3",children:e.jsxs(a,{variant:"ghost",onClick:()=>I("model_number"),className:"h-auto p-0 font-semibold hover:bg-transparent",children:["Model Number",i==="model_number"&&(d==="asc"?e.jsx(E,{className:"ml-1 h-4 w-4"}):e.jsx(T,{className:"ml-1 h-4 w-4"})),i!=="model_number"&&e.jsx(Q,{className:"ml-1 h-4 w-4 opacity-50"})]})}),e.jsx("th",{className:"text-left p-3",children:e.jsxs(a,{variant:"ghost",onClick:()=>I("release_year"),className:"h-auto p-0 font-semibold hover:bg-transparent",children:["Release Year",i==="release_year"&&(d==="asc"?e.jsx(E,{className:"ml-1 h-4 w-4"}):e.jsx(T,{className:"ml-1 h-4 w-4"})),i!=="release_year"&&e.jsx(Q,{className:"ml-1 h-4 w-4 opacity-50"})]})}),e.jsx("th",{className:"text-left p-3",children:"Status"}),e.jsx("th",{className:"text-left p-3",children:"Parts"}),e.jsx("th",{className:"text-right p-3",children:"Actions"})]})}),e.jsx("tbody",{children:t.data.map(s=>e.jsxs("tr",{className:"border-b hover:bg-muted/50",children:[e.jsx("td",{className:"p-3",children:e.jsx(pe,{checked:b.includes(s.id),onCheckedChange:()=>De(s.id),"aria-label":`Select ${s.brand.name} ${s.name}`})}),e.jsx("td",{className:"p-3",children:e.jsxs("div",{className:"flex items-center gap-3",children:[s.brand.logo_url&&e.jsx("img",{src:s.brand.logo_url,alt:s.brand.name,className:"w-6 h-6 object-contain"}),e.jsxs("div",{children:[e.jsxs("div",{className:"font-medium",children:[s.brand.name," ",s.name]}),e.jsxs("div",{className:"text-sm text-muted-foreground",children:["Brand: ",s.brand.name]})]})]})}),e.jsx("td",{className:"p-3",children:e.jsx("span",{className:"text-sm",children:s.model_number||"-"})}),e.jsx("td",{className:"p-3",children:s.release_year?e.jsxs(g,{variant:"outline",className:"flex items-center gap-1 w-fit",children:[e.jsx(W,{className:"h-3 w-3"}),s.release_year]}):e.jsx("span",{className:"text-sm text-muted-foreground",children:"-"})}),e.jsx("td",{className:"p-3",children:e.jsx(g,{variant:s.is_active?"default":"secondary",children:s.is_active?"Active":"Inactive"})}),e.jsx("td",{className:"p-3",children:e.jsxs(g,{variant:"outline",children:[s.parts_count||0," parts"]})}),e.jsx("td",{className:"p-3",children:e.jsxs("div",{className:"flex items-center gap-1 justify-end",children:[e.jsx(f,{href:route("models.show",s.slug||s.id),children:e.jsx(a,{variant:"outline",size:"sm",title:"View Public Page",children:e.jsx(X,{className:"h-3 w-3"})})}),e.jsx(f,{href:`/admin/models/${s.id}`,children:e.jsx(a,{variant:"outline",size:"sm",title:"Admin View",children:e.jsx(J,{className:"h-3 w-3"})})}),e.jsx(f,{href:`/admin/models/${s.id}/edit`,children:e.jsx(a,{variant:"outline",size:"sm",title:"Edit",children:e.jsx(Z,{className:"h-3 w-3"})})}),e.jsx(a,{variant:"outline",size:"sm",className:"text-destructive hover:text-destructive",onClick:()=>Y(s),title:"Delete Model",children:e.jsx(q,{className:"h-3 w-3"})})]})})]},s.id))})]})}),ze=({model:s})=>e.jsx(H,{className:"h-full",children:e.jsx(K,{className:"p-4",children:e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"flex items-start justify-between",children:e.jsxs("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[s.brand.logo_url&&e.jsx("img",{src:s.brand.logo_url,alt:s.brand.name,className:"w-8 h-8 object-contain flex-shrink-0"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsxs("h3",{className:"font-medium truncate",children:[s.brand.name," ",s.name]}),s.model_number&&e.jsxs("p",{className:"text-sm text-muted-foreground truncate",children:["Model: ",s.model_number]})]})]})}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[e.jsx(g,{variant:s.is_active?"default":"secondary",children:s.is_active?"Active":"Inactive"}),s.release_year&&e.jsxs(g,{variant:"outline",className:"flex items-center gap-1",children:[e.jsx(W,{className:"h-3 w-3"}),s.release_year]}),e.jsxs(g,{variant:"outline",children:[s.parts_count||0," parts"]})]}),e.jsxs("div",{className:"flex items-center gap-1 pt-2",children:[e.jsx(f,{href:route("models.show",s.slug||s.id),children:e.jsx(a,{variant:"outline",size:"sm",title:"View Public Page",children:e.jsx(X,{className:"h-3 w-3"})})}),e.jsx(f,{href:`/admin/models/${s.id}`,children:e.jsx(a,{variant:"outline",size:"sm",title:"Admin View",children:e.jsx(J,{className:"h-3 w-3"})})}),e.jsx(f,{href:`/admin/models/${s.id}/edit`,children:e.jsx(a,{variant:"outline",size:"sm",title:"Edit",children:e.jsx(Z,{className:"h-3 w-3"})})}),e.jsx(a,{variant:"outline",size:"sm",className:"text-destructive hover:text-destructive",onClick:()=>Y(s),title:"Delete Model",children:e.jsx(q,{className:"h-3 w-3"})})]})]})})});return e.jsxs(He,{children:[e.jsx(Ie,{title:"Models - Admin"}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Mobile Models"}),e.jsx("p",{className:"text-muted-foreground",children:"Manage mobile device models"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(a,{variant:"outline",onClick:ye,size:"sm",children:[e.jsx(ue,{className:"h-4 w-4 mr-2"}),"Template"]}),e.jsxs(a,{variant:"outline",onClick:Ce,size:"sm",children:[e.jsx(Pe,{className:"h-4 w-4 mr-2"}),"Import"]}),e.jsxs(a,{variant:"outline",onClick:Se,size:"sm",children:[e.jsx(je,{className:"h-4 w-4 mr-2"}),"Export All"]}),b.length>0&&e.jsxs(a,{variant:"outline",onClick:_e,size:"sm",children:[e.jsx(je,{className:"h-4 w-4 mr-2"}),"Export Selected (",b.length,")"]}),e.jsx(f,{href:"/admin/models/create",children:e.jsxs(a,{children:[e.jsx(he,{className:"h-4 w-4 mr-2"}),"Add Model"]})})]})]}),e.jsx(H,{children:e.jsx(K,{className:"pt-6",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(fe,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),e.jsx($e,{placeholder:"Search models, model numbers, or brands...",value:h,onChange:s=>se(s.target.value),className:"pl-10",onKeyDown:s=>{s.key==="Enter"&&ie()}})]}),e.jsxs(a,{onClick:ie,children:[e.jsx(fe,{className:"h-4 w-4 mr-2"}),"Search"]}),e.jsxs(a,{variant:"outline",onClick:()=>ve(!$),className:$?"bg-muted":"",children:[e.jsx(We,{className:"h-4 w-4 mr-2"}),"Filters",z&&e.jsx(g,{variant:"secondary",className:"ml-2 h-5 w-5 p-0 flex items-center justify-center text-xs",children:"!"})]}),z&&e.jsxs(a,{variant:"ghost",onClick:ne,children:[e.jsx(xe,{className:"h-4 w-4 mr-2"}),"Clear"]})]}),$&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-muted/50 rounded-lg",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(y,{htmlFor:"brand-filter",children:"Brand"}),e.jsxs(C,{value:x,onValueChange:ae,children:[e.jsx(k,{id:"brand-filter",children:e.jsx(A,{placeholder:"All brands"})}),e.jsxs(D,{children:[e.jsx(c,{value:"all",children:"All brands"}),ee.brands.map(s=>e.jsx(c,{value:s.id.toString(),children:s.name},s.id))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(y,{htmlFor:"status-filter",children:"Status"}),e.jsxs(C,{value:p,onValueChange:te,children:[e.jsx(k,{id:"status-filter",children:e.jsx(A,{placeholder:"All statuses"})}),e.jsxs(D,{children:[e.jsx(c,{value:"all",children:"All statuses"}),e.jsx(c,{value:"active",children:"Active"}),e.jsx(c,{value:"inactive",children:"Inactive"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(y,{htmlFor:"year-filter",children:"Release Year"}),e.jsxs(C,{value:u,onValueChange:re,children:[e.jsx(k,{id:"year-filter",children:e.jsx(A,{placeholder:"All years"})}),e.jsxs(D,{children:[e.jsx(c,{value:"all",children:"All years"}),(ce=ee.release_years)==null?void 0:ce.filter(s=>s&&s.toString().trim()!=="").map(s=>e.jsx(c,{value:s.toString(),children:s},s))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(y,{htmlFor:"sort-filter",children:"Sort By"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(C,{value:i,onValueChange:V,children:[e.jsx(k,{id:"sort-filter",className:"flex-1",children:e.jsx(A,{})}),e.jsxs(D,{children:[e.jsx(c,{value:"name",children:"Name"}),e.jsx(c,{value:"model_number",children:"Model Number"}),e.jsx(c,{value:"release_year",children:"Release Year"}),e.jsx(c,{value:"created_at",children:"Created Date"})]})]}),e.jsx(a,{variant:"outline",size:"sm",onClick:()=>I(i),className:"px-3",children:d==="asc"?e.jsx(E,{className:"h-4 w-4"}):e.jsx(T,{className:"h-4 w-4"})})]})]})]})]})})}),e.jsxs(H,{children:[e.jsx(Ee,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(Te,{className:"flex items-center gap-2",children:[e.jsx(me,{className:"h-5 w-5"}),"All Models"]}),e.jsxs(Ve,{children:[t.total," models total",t.data.length>0&&e.jsxs("span",{className:"ml-2",children:["(showing ",t.from,"-",t.to,")"]})]})]}),e.jsxs("div",{className:"flex items-center gap-1 border rounded-lg p-1",children:[e.jsx(a,{variant:o==="table"?"default":"ghost",size:"sm",onClick:()=>U("table"),className:"h-8 px-3",children:e.jsx(Xe,{className:"h-4 w-4"})}),e.jsx(a,{variant:o==="list"?"default":"ghost",size:"sm",onClick:()=>U("list"),className:"h-8 px-3",children:e.jsx(Je,{className:"h-4 w-4"})}),e.jsx(a,{variant:o==="grid"?"default":"ghost",size:"sm",onClick:()=>U("grid"),className:"h-8 px-3",children:e.jsx(Ke,{className:"h-4 w-4"})})]})]})}),e.jsxs(K,{children:[t.data.length>0?e.jsx(e.Fragment,{children:o==="table"?e.jsx(Fe,{}):o==="grid"?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:t.data.map(s=>e.jsx(ze,{model:s},s.id))}):e.jsx("div",{className:"space-y-4",children:t.data.map(s=>e.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[s.brand.logo_url&&e.jsx("img",{src:s.brand.logo_url,alt:s.brand.name,className:"w-6 h-6 object-contain"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"font-medium",children:[s.brand.name," ",s.name]}),s.model_number&&e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Model: ",s.model_number]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(g,{variant:s.is_active?"default":"secondary",children:s.is_active?"Active":"Inactive"}),s.release_year&&e.jsxs(g,{variant:"outline",className:"flex items-center gap-1",children:[e.jsx(W,{className:"h-3 w-3"}),s.release_year]}),e.jsxs(g,{variant:"outline",children:[s.parts_count||0," parts"]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(f,{href:route("models.show",s.slug||s.id),children:e.jsx(a,{variant:"outline",size:"sm",title:"View Public Page",children:e.jsx(X,{className:"h-4 w-4"})})}),e.jsx(f,{href:`/admin/models/${s.id}`,children:e.jsx(a,{variant:"outline",size:"sm",title:"Admin View",children:e.jsx(J,{className:"h-4 w-4"})})}),e.jsx(f,{href:`/admin/models/${s.id}/edit`,children:e.jsx(a,{variant:"outline",size:"sm",title:"Edit",children:e.jsx(Z,{className:"h-4 w-4"})})}),e.jsx(a,{variant:"outline",size:"sm",className:"text-destructive hover:text-destructive",onClick:()=>Y(s),title:"Delete",children:e.jsx(q,{className:"h-4 w-4"})})]})]},s.id))})}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(me,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No models found"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:z?"No models match your current filters. Try adjusting your search criteria.":"Get started by adding your first mobile model."}),z?e.jsxs(a,{variant:"outline",onClick:ne,children:[e.jsx(xe,{className:"h-4 w-4 mr-2"}),"Clear Filters"]}):e.jsx(f,{href:"/admin/models/create",children:e.jsxs(a,{children:[e.jsx(he,{className:"h-4 w-4 mr-2"}),"Add Model"]})})]}),t.data.length>0&&t.last_page>1&&e.jsxs("div",{className:"flex items-center justify-between pt-4 border-t",children:[e.jsxs("div",{className:"text-sm text-muted-foreground",children:["Showing ",t.from," to ",t.to," of ",t.total," models"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(a,{variant:"outline",size:"sm",onClick:()=>L(t.current_page-1),disabled:t.current_page<=1,children:[e.jsx(Ze,{className:"h-4 w-4 mr-1"}),"Previous"]}),e.jsx("div",{className:"flex items-center gap-1",children:Array.from({length:Math.min(5,t.last_page)},(s,r)=>{let l;if(t.last_page<=5)l=r+1;else{const j=Math.max(1,t.current_page-2),G=Math.min(t.last_page,j+4);if(l=j+r,l>G)return null}return l>t.last_page||l<1?null:e.jsx(a,{variant:t.current_page===l?"default":"outline",size:"sm",onClick:()=>L(l),className:"w-8 h-8 p-0",children:l},l)})}),e.jsxs(a,{variant:"outline",size:"sm",onClick:()=>L(t.current_page+1),disabled:t.current_page>=t.last_page,children:["Next",e.jsx(Be,{className:"h-4 w-4 ml-1"})]})]})]})]})]})]})}),e.jsx("input",{type:"file",ref:S,onChange:ke,accept:".csv",style:{display:"none"}}),e.jsx(Re,{open:be,onOpenChange:M,children:e.jsxs(Le,{children:[e.jsxs(Ue,{children:[e.jsx(Ye,{children:"Import Models"}),e.jsx(Ge,{children:"Upload a CSV file to import mobile models. Make sure your file follows the template format."})]}),w&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"p-4 bg-muted rounded-lg",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-4 w-4"}),e.jsx("span",{className:"font-medium",children:w.name}),e.jsxs("span",{className:"text-sm text-muted-foreground",children:["(",(w.size/1024).toFixed(1)," KB)"]})]})}),e.jsxs("div",{className:"text-sm text-muted-foreground",children:[e.jsx("p",{children:"• Make sure your CSV file has the correct headers"}),e.jsx("p",{children:"• Brand names must match existing brands exactly"}),e.jsx("p",{children:"• Use the template for the correct format"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(y,{htmlFor:"duplicate-action",children:"How should duplicate models be handled?"}),e.jsxs(C,{value:_,onValueChange:s=>we(s),children:[e.jsx(k,{id:"duplicate-action",children:e.jsx(A,{})}),e.jsxs(D,{children:[e.jsx(c,{value:"skip",children:"Skip duplicates (recommended)"}),e.jsx(c,{value:"update",children:"Update existing models"}),e.jsx(c,{value:"error",children:"Report as errors"})]})]}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:[_==="skip"&&"Duplicate models will be ignored and not imported.",_==="update"&&"Existing models will be updated with new data.",_==="error"&&"Import will report duplicates as errors."]})]}),F&&e.jsx("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-lg",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),e.jsx("span",{className:"text-sm font-medium text-blue-800",children:"Processing import..."})]})})]}),e.jsxs(Oe,{children:[e.jsx(a,{variant:"outline",onClick:()=>{M(!1),B(null),S.current&&(S.current.value="")},disabled:F,children:"Cancel"}),e.jsx(a,{onClick:Ae,disabled:!w||F,children:F?"Importing...":"Import Models"})]})]})})]})}export{Ls as default};
