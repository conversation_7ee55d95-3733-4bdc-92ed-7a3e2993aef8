import{j as e,Q as j,t as d,S as b}from"./app-J5EqS6dS.js";import{B as t}from"./smartphone-GGiwNneF.js";import{C as f,a as g,b as N,d as v,c as C,e as w}from"./card-9XCADs-4.js";import{B as i}from"./badge-BucYuCBs.js";import{A as y}from"./app-layout-ox1kAwY6.js";import{C as _}from"./crown-UDSxMtlm.js";import{Z as P}from"./zap-BcmHRR4K.js";import{C as $}from"./check-C7SdgHPn.js";/* empty css            */import"./ImpersonationBanner-CYn5eDk6.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./user-DCnDRzMf.js";import"./search-DBK6jUoc.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./eye-D-fsmYB2.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";function X({plans:x,currentPlan:r,remainingSearches:l}){const p=a=>{b.get(route("subscription.checkout",{plan:a}))};return e.jsxs(y,{children:[e.jsx(j,{title:"Subscription Plans"}),e.jsx("div",{className:"py-12",children:e.jsxs("div",{className:"max-w-7xl mx-auto sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Choose Your Plan"}),e.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Get unlimited access to our comprehensive mobile parts database"}),r==="free"&&e.jsx("div",{className:"mt-4",children:e.jsx(i,{variant:"outline",className:"text-sm",children:l===-1?"Unlimited searches remaining":`${l} searches remaining today`})})]}),e.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto",children:Object.entries(x).map(([a,s])=>{var o,c,m,n;return e.jsxs(f,{className:`relative ${s.is_popular?"border-blue-500 shadow-lg scale-105":((o=s.metadata)==null?void 0:o.color)==="purple"?"border-purple-300 shadow-md":"border-gray-200"} ${r===a?"ring-2 ring-green-500":""}`,children:[s.is_popular&&e.jsx("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:e.jsxs(i,{className:"bg-blue-500 text-white px-4 py-1",children:[e.jsx(_,{className:"w-4 h-4 mr-1"}),"Most Popular"]})}),((c=s.metadata)==null?void 0:c.color)==="purple"&&!s.is_popular&&e.jsx("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:e.jsx(i,{className:"bg-purple-600 text-white px-4 py-1",children:"Enterprise"})}),r===a&&e.jsx("div",{className:"absolute -top-4 right-4",children:e.jsx(i,{className:"bg-green-500 text-white px-3 py-1",children:"Current Plan"})}),e.jsxs(g,{className:"text-center pb-8",children:[e.jsxs(N,{className:"text-2xl font-bold flex items-center justify-center gap-2",children:[s.is_popular&&e.jsx(P,{className:"w-6 h-6 text-blue-500"}),s.name]}),e.jsxs("div",{className:"mt-4",children:[e.jsx("span",{className:"text-4xl font-bold",children:s.formatted_price||`$${s.price}`}),!s.formatted_price&&e.jsxs("span",{className:"text-gray-600",children:["/",s.interval]})]}),e.jsx(v,{className:"mt-2",children:((m=s.metadata)==null?void 0:m.color)==="purple"?"Custom solutions for large organizations":s.search_limit===-1?"Unlimited access for professionals":`Perfect for occasional searches (${s.search_limit} per day)`})]}),e.jsx(C,{children:e.jsx("ul",{className:"space-y-3",children:s.features.map((h,u)=>e.jsxs("li",{className:"flex items-center gap-3",children:[e.jsx($,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),e.jsx("span",{className:"text-gray-700",children:h})]},u))})}),e.jsx(w,{children:r===a?e.jsx(t,{className:"w-full",variant:"outline",disabled:!0,children:"Current Plan"}):(n=s.metadata)!=null&&n.contact_sales?e.jsx(t,{className:"w-full bg-purple-600 hover:bg-purple-700",onClick:()=>window.open("mailto:<EMAIL>?subject=Enterprise Plan Inquiry","_blank"),children:"Contact Sales"}):s.price>0?e.jsxs(t,{className:"w-full bg-blue-600 hover:bg-blue-700",onClick:()=>p(a),children:["Upgrade to ",s.name]}):e.jsx(t,{className:"w-full",variant:"outline",disabled:!0,children:"Free Plan"})})]},a)})}),e.jsxs("div",{className:"text-center mt-12",children:[e.jsxs("p",{className:"text-gray-600 mb-4",children:["Need help choosing? ",e.jsx(d,{href:"#",className:"text-blue-600 hover:underline",children:"Contact our support team"})]}),e.jsx(d,{href:route("subscription.dashboard"),className:"text-blue-600 hover:underline",children:"Manage your subscription →"})]})]})})]})}export{X as default};
