import{r as p,j as e}from"./app-J5EqS6dS.js";import{B as r}from"./smartphone-GGiwNneF.js";import{I as g}from"./input-Bo8dOn9p.js";import{L as h}from"./label-BlOrdc-X.js";import{M as x}from"./MediaPicker-Due2OGB1.js";import{X as u,I as j}from"./ImpersonationBanner-CYn5eDk6.js";function k({value:s,onChange:t,accept:l="image/*",placeholder:c="Select image"}){const[n,i]=p.useState(!1),o=a=>{a.length>0&&t(a[0].url),i(!1)},m=()=>{t("")},d=a=>{t(a.target.value)};return e.jsxs("div",{className:"space-y-4",children:[s&&e.jsxs("div",{className:"relative inline-block",children:[e.jsx("img",{src:s,alt:"Selected image",className:"w-32 h-32 object-cover rounded-lg border"}),e.jsx(r,{type:"button",variant:"destructive",size:"icon",className:"absolute -top-2 -right-2 h-6 w-6",onClick:m,children:e.jsx(u,{className:"h-3 w-3"})})]}),e.jsx("div",{className:"flex gap-2",children:e.jsxs(r,{type:"button",variant:"outline",onClick:()=>i(!0),className:"flex items-center gap-2",children:[e.jsx(j,{className:"h-4 w-4"}),s?"Change Image":"Select Image"]})}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(h,{htmlFor:"image-url",children:"Or enter image URL"}),e.jsx(g,{id:"image-url",type:"url",value:s,onChange:d,placeholder:c})]}),e.jsx(x,{isOpen:n,onClose:()=>i(!1),onSelect:o,multiple:!1,title:"Select Featured Image",acceptedTypes:[l]})]})}export{k as S};
