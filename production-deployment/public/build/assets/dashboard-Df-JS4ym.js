import{j as e,Q as C,t,S as k}from"./app-J5EqS6dS.js";import{B as a}from"./smartphone-GGiwNneF.js";import{C as d,c as i,a as u,b as j,d as N}from"./card-9XCADs-4.js";import{B as x}from"./badge-BucYuCBs.js";import{A as S,C as f}from"./app-layout-ox1kAwY6.js";import{C as o}from"./crown-UDSxMtlm.js";import{S as g}from"./users-RYmOyic9.js";import{S as A}from"./star-D0YOm-Sd.js";import{C as D}from"./circle-check-big-DOFoatRy.js";import{A as U,C as p,F as P}from"./ImpersonationBanner-CYn5eDk6.js";import{Z as b}from"./zap-BcmHRR4K.js";import{A as _}from"./arrow-up-right-9Qf6vHK9.js";import{T as R}from"./trending-up-BtixJGWw.js";import{C as v}from"./circle-alert-C6UwDlxH.js";import{S as n}from"./search-DBK6jUoc.js";import{C as T}from"./clock-Brl7_5s7.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./eye-D-fsmYB2.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";function ne({subscription:r,currentPlan:c,remainingSearches:s,searchHistory:m}){const y=()=>{confirm("Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your billing period.")&&k.post(route("subscription.cancel"))},h=l=>new Date(l).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return e.jsxs(S,{children:[e.jsx(C,{title:"Subscription Dashboard"}),e.jsx("div",{className:"bg-gradient-to-br from-background via-muted/20 to-background min-h-screen",children:e.jsxs("div",{className:"container mx-auto px-4 py-6 max-w-7xl",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-3xl font-bold text-foreground mb-2 flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:e.jsx(o,{className:"w-6 h-6 text-primary"})}),"Subscription Dashboard"]}),e.jsx("p",{className:"text-muted-foreground text-lg",children:"Manage your subscription and monitor your usage statistics"})]}),e.jsx("div",{className:"flex gap-3",children:e.jsx(t,{href:route("subscription.plans"),children:e.jsxs(a,{variant:"outline",className:"gap-2",children:[e.jsx(g,{className:"w-4 h-4"}),"Manage Plans"]})})})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsx(d,{className:"shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:e.jsx(i,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Current Plan"}),e.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[c==="premium"&&e.jsx(o,{className:"w-4 h-4 text-primary"}),e.jsx("p",{className:"text-2xl font-bold text-foreground",children:c==="premium"?"Premium":"Free"})]})]}),e.jsx("div",{className:"p-3 bg-primary/10 rounded-full",children:e.jsx(A,{className:"w-6 h-6 text-primary"})})]})})}),e.jsx(d,{className:"shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:e.jsx(i,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Status"}),e.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[(r==null?void 0:r.status)==="active"?e.jsx(D,{className:"w-4 h-4 text-green-500"}):e.jsx(f,{className:"w-4 h-4 text-red-500"}),e.jsx("p",{className:"text-2xl font-bold text-foreground capitalize",children:(r==null?void 0:r.status)||"Free"})]})]}),e.jsx("div",{className:"p-3 bg-green-500/10 rounded-full",children:e.jsx(U,{className:"w-6 h-6 text-green-500"})})]})})}),e.jsx(d,{className:"shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:e.jsx(i,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Daily Searches"}),e.jsx("p",{className:"text-2xl font-bold text-foreground mt-1",children:s===-1?"∞":s}),e.jsx("p",{className:"text-xs text-muted-foreground",children:s===-1?"Unlimited":"Remaining today"})]}),e.jsx("div",{className:"p-3 bg-blue-500/10 rounded-full",children:e.jsx(b,{className:"w-6 h-6 text-blue-500"})})]})})}),e.jsx(d,{className:"shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:e.jsx(i,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Searches"}),e.jsx("p",{className:"text-2xl font-bold text-foreground mt-1",children:m.length}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"All time"})]}),e.jsx("div",{className:"p-3 bg-purple-500/10 rounded-full",children:e.jsx(p,{className:"w-6 h-6 text-purple-500"})})]})})})]}),e.jsxs("div",{className:"grid lg:grid-cols-3 gap-6",children:[e.jsxs(d,{className:"lg:col-span-2 shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:[e.jsxs(u,{className:"pb-4",children:[e.jsxs(j,{className:"flex items-center gap-3 text-xl",children:[e.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:e.jsx(o,{className:"w-5 h-5 text-primary"})}),"Subscription Details"]}),e.jsx(N,{className:"text-base",children:"Manage your current subscription and billing information"})]}),e.jsxs(i,{className:"space-y-6",children:[e.jsx("div",{className:"bg-muted/30 rounded-lg p-4 border border-border",children:e.jsxs("div",{className:"grid md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium text-muted-foreground",children:"Plan Type"}),e.jsx(x,{variant:c==="premium"?"default":"secondary",className:"text-sm px-3 py-1",children:c==="premium"?"Premium":"Free Plan"})]}),r&&e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium text-muted-foreground",children:"Status"}),e.jsx(x,{variant:r.status==="active"?"default":"destructive",className:"text-sm px-3 py-1",children:r.status})]})]}),r&&e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium text-muted-foreground",children:"Billing Period"}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"text-sm font-medium text-foreground",children:h(r.current_period_start)}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:["to ",h(r.current_period_end)]})]})]})})]})}),e.jsxs("div",{className:"bg-muted/30 rounded-lg p-4 border border-border",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h4",{className:"font-semibold text-foreground",children:"Search Quota"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(b,{className:"w-4 h-4 text-primary"}),e.jsx("span",{className:"text-sm font-medium text-foreground",children:s===-1?"Unlimited":`${s} left`})]})]}),s!==-1&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"w-full bg-muted rounded-full h-2",children:e.jsx("div",{className:"bg-primary h-2 rounded-full transition-all duration-300",style:{width:`${s>0?s/20*100:0}%`}})}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Daily limit resets at midnight"})]})]}),e.jsxs("div",{className:"flex flex-wrap gap-3 pt-4",children:[c==="free"?e.jsx(t,{href:route("subscription.plans"),children:e.jsxs(a,{className:"gap-2 bg-primary hover:bg-primary/90",children:[e.jsx(_,{className:"w-4 h-4"}),"Upgrade to Premium"]})}):e.jsxs(a,{variant:"destructive",onClick:y,className:"gap-2",children:[e.jsx(f,{className:"w-4 h-4"}),"Cancel Subscription"]}),e.jsx(t,{href:route("subscription.plans"),children:e.jsxs(a,{variant:"outline",className:"gap-2",children:[e.jsx(g,{className:"w-4 h-4"}),"View All Plans"]})}),e.jsx(t,{href:route("payment-history.index"),children:e.jsxs(a,{variant:"outline",className:"gap-2",children:[e.jsx(P,{className:"w-4 h-4"}),"Payment History"]})})]})]})]}),e.jsxs(d,{className:"shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm",children:[e.jsx(u,{className:"pb-4",children:e.jsxs(j,{className:"flex items-center gap-3 text-xl",children:[e.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:e.jsx(R,{className:"w-5 h-5 text-primary"})}),"Quick Actions"]})}),e.jsxs(i,{className:"space-y-4",children:[s===0&&e.jsx("div",{className:"bg-destructive/10 border border-destructive/20 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(v,{className:"w-5 h-5 text-destructive mt-0.5"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-destructive mb-1",children:"Daily Limit Reached"}),e.jsx("p",{className:"text-sm text-destructive/80 mb-3",children:"You've used all your daily searches. Upgrade to Premium for unlimited access."}),e.jsx(t,{href:route("subscription.plans"),children:e.jsx(a,{size:"sm",className:"bg-primary hover:bg-primary/90",children:"Upgrade Now"})})]})]})}),s>0&&s<=5&&s!==-1&&e.jsx("div",{className:"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(v,{className:"w-5 h-5 text-yellow-600 mt-0.5"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-yellow-700 mb-1",children:"Running Low"}),e.jsxs("p",{className:"text-sm text-yellow-600 mb-3",children:["Only ",s," searches remaining today. Consider upgrading for unlimited access."]}),e.jsx(t,{href:route("subscription.plans"),children:e.jsx(a,{size:"sm",variant:"outline",className:"border-yellow-500 text-yellow-700 hover:bg-yellow-50",children:"View Plans"})})]})]})}),e.jsxs("div",{className:"bg-muted/30 rounded-lg p-4 border border-border",children:[e.jsxs("h4",{className:"font-semibold text-foreground mb-3 flex items-center gap-2",children:[e.jsx(p,{className:"w-4 h-4 text-primary"}),"Usage Summary"]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-lg font-bold text-primary",children:m.length}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Total Searches"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-lg font-bold text-green-600",children:s===-1?"∞":s}),e.jsx("div",{className:"text-xs text-muted-foreground",children:s===-1?"Unlimited":"Remaining"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(t,{href:"/search",className:"block",children:e.jsxs(a,{variant:"outline",className:"w-full justify-start gap-2",children:[e.jsx(n,{className:"w-4 h-4"}),"Start New Search"]})}),e.jsx(t,{href:route("subscription.plans"),className:"block",children:e.jsxs(a,{variant:"outline",className:"w-full justify-start gap-2",children:[e.jsx(o,{className:"w-4 h-4"}),"Compare Plans"]})})]})]})]})]}),e.jsxs(d,{className:"shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm mt-6",children:[e.jsx(u,{className:"pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(j,{className:"flex items-center gap-3 text-xl",children:[e.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:e.jsx(n,{className:"w-5 h-5 text-primary"})}),"Recent Search Activity"]}),e.jsx(N,{className:"text-base mt-1",children:"Track your latest searches and their results"})]}),e.jsxs(x,{variant:"secondary",className:"text-sm px-3 py-1",children:[m.length," searches"]})]})}),e.jsx(i,{children:m.length>0?e.jsx("div",{className:"space-y-1",children:m.map((l,w)=>e.jsxs("div",{className:"group flex items-center justify-between p-4 rounded-lg border border-border/50 hover:border-primary/30 hover:bg-muted/30 transition-all duration-200",children:[e.jsxs("div",{className:"flex items-center gap-4 flex-1 min-w-0",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center",children:e.jsx(n,{className:"w-4 h-4 text-primary"})})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h4",{className:"font-semibold text-foreground truncate",children:l.search_query}),e.jsx(x,{variant:"outline",className:"text-xs px-2 py-0.5 flex-shrink-0",children:l.search_type})]}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(p,{className:"w-3 h-3"}),l.results_count," results"]}),e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(T,{className:"w-3 h-3"}),h(l.created_at)]})]})]})]}),e.jsx("div",{className:"flex-shrink-0 ml-4",children:e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-sm font-medium text-foreground",children:["#",w+1]}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Search"})]})})]},l.id))}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"w-20 h-20 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(n,{className:"w-8 h-8 text-muted-foreground"})}),e.jsx("h3",{className:"text-lg font-semibold text-foreground mb-2",children:"No searches yet"}),e.jsx("p",{className:"text-muted-foreground mb-6 max-w-md mx-auto",children:"Start exploring our mobile parts database to see your search history here"}),e.jsx(t,{href:"/search",children:e.jsxs(a,{className:"gap-2",children:[e.jsx(n,{className:"w-4 h-4"}),"Start Your First Search"]})})]})})]})]})})]})}export{ne as default};
