import{j as e,Q as v,t as n}from"./app-J5EqS6dS.js";import{C as t,a,b as i,c as l,d}from"./card-9XCADs-4.js";import{B as m}from"./smartphone-GGiwNneF.js";import{B as c}from"./badge-BucYuCBs.js";import{A as C}from"./app-layout-ox1kAwY6.js";import{C as h}from"./crown-UDSxMtlm.js";import{S}from"./search-DBK6jUoc.js";import{C as _}from"./calendar-B-u_QN2Q.js";import{C as N}from"./circle-check-big-DOFoatRy.js";import{S as A}from"./star-D0YOm-Sd.js";import{A as j}from"./arrow-right-CCfGNWZ9.js";import{T as B}from"./trending-up-BtixJGWw.js";import{C as P}from"./clock-Brl7_5s7.js";/* empty css            */import"./ImpersonationBanner-CYn5eDk6.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./user-DCnDRzMf.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./eye-D-fsmYB2.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";function le({subscription:r,currentPlan:p,remainingSearches:u,plans:b}){const x=p==="premium",g=u===-1,f=s=>{switch(s){case"active":return e.jsx(c,{className:"bg-green-100 text-green-800",children:"Active"});case"cancelled":return e.jsx(c,{className:"bg-red-100 text-red-800",children:"Cancelled"});case"expired":return e.jsx(c,{className:"bg-gray-100 text-gray-800",children:"Expired"});default:return e.jsx(c,{className:"bg-yellow-100 text-yellow-800",children:"Pending"})}},o=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return e.jsxs(C,{children:[e.jsx(v,{title:"Subscription Management"}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:"Subscription Management"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Manage your subscription and view usage statistics"})]}),!x&&e.jsx(n,{href:"/subscription/checkout",children:e.jsxs(m,{className:"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700",children:[e.jsx(h,{className:"h-4 w-4 mr-2"}),"Upgrade to Premium"]})})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-3",children:[e.jsxs(t,{children:[e.jsxs(a,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(i,{className:"text-sm font-medium",children:"Current Plan"}),e.jsx(h,{className:`h-4 w-4 ${x?"text-yellow-600":"text-gray-400"}`})]}),e.jsxs(l,{children:[e.jsx("div",{className:"text-2xl font-bold capitalize",children:p}),r&&e.jsx("div",{className:"mt-2",children:f(r.status)})]})]}),e.jsxs(t,{children:[e.jsxs(a,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(i,{className:"text-sm font-medium",children:"Daily Searches"}),e.jsx(S,{className:"h-4 w-4 text-blue-600"})]}),e.jsxs(l,{children:[e.jsx("div",{className:"text-2xl font-bold",children:g?"Unlimited":u}),e.jsx("p",{className:"text-xs text-muted-foreground",children:g?"Premium benefits":"Remaining today"})]})]}),e.jsxs(t,{children:[e.jsxs(a,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(i,{className:"text-sm font-medium",children:"Next Billing"}),e.jsx(_,{className:"h-4 w-4 text-green-600"})]}),e.jsxs(l,{children:[e.jsx("div",{className:"text-2xl font-bold",children:r?o(r.current_period_end):"N/A"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:r?"Auto-renewal":"No active subscription"})]})]})]}),r&&e.jsxs(t,{children:[e.jsxs(a,{children:[e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(N,{className:"h-5 w-5 text-green-600"}),"Active Subscription"]}),e.jsx(d,{children:"Your current subscription details and billing information"})]}),e.jsx(l,{children:e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-sm text-muted-foreground",children:"Plan Details"}),e.jsxs("div",{className:"mt-2 space-y-1",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Plan:"})," ",r.plan_name]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Status:"})," ",f(r.status)]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Started:"})," ",o(r.current_period_start)]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-sm text-muted-foreground",children:"Billing Information"}),e.jsxs("div",{className:"mt-2 space-y-1",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Next Billing:"})," ",o(r.current_period_end)]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Auto-Renewal:"})," Enabled"]})]})]})]})})]}),!x&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Available Plans"}),e.jsx("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:b.map(s=>e.jsxs(t,{className:`relative ${s.is_popular?"border-purple-200 shadow-lg":""}`,children:[s.is_popular&&e.jsx("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:e.jsxs(c,{className:"bg-purple-600 text-white",children:[e.jsx(A,{className:"h-3 w-3 mr-1"}),"Most Popular"]})}),e.jsxs(a,{children:[e.jsxs(i,{className:"flex items-center justify-between",children:[e.jsx("span",{className:"capitalize",children:s.name}),e.jsx(h,{className:`h-5 w-5 ${s.name==="premium"?"text-yellow-600":"text-gray-400"}`})]}),e.jsxs(d,{children:[e.jsxs("span",{className:"text-3xl font-bold",children:["$",s.price]}),e.jsxs("span",{className:"text-muted-foreground",children:["/",s.billing_cycle]})]})]}),e.jsxs(l,{children:[e.jsx("ul",{className:"space-y-2 mb-4",children:s.features.map((w,y)=>e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx(N,{className:"h-4 w-4 text-green-600"}),e.jsx("span",{className:"text-sm",children:w})]},y))}),e.jsx(n,{href:`/subscription/checkout?plan=${s.name}`,children:e.jsxs(m,{className:"w-full",variant:s.is_popular?"default":"outline",children:["Choose ",s.name,e.jsx(j,{className:"h-4 w-4 ml-2"})]})})]})]},s.id))})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs(t,{children:[e.jsxs(a,{children:[e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(B,{className:"h-5 w-5"}),"Usage Statistics"]}),e.jsx(d,{children:"View detailed analytics of your search activity"})]}),e.jsx(l,{children:e.jsx(n,{href:"/subscription/search-stats",children:e.jsxs(m,{variant:"outline",className:"w-full",children:["View Statistics",e.jsx(j,{className:"h-4 w-4 ml-2"})]})})})]}),e.jsxs(t,{children:[e.jsxs(a,{children:[e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(P,{className:"h-5 w-5"}),"Subscription Dashboard"]}),e.jsx(d,{children:"Manage your subscription settings and billing"})]}),e.jsx(l,{children:e.jsx(n,{href:"/subscription/dashboard",children:e.jsxs(m,{variant:"outline",className:"w-full",children:["Manage Subscription",e.jsx(j,{className:"h-4 w-4 ml-2"})]})})})]})]})]})})]})}export{le as default};
