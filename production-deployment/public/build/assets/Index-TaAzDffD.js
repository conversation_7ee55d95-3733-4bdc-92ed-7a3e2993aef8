import{r as d,j as e,Q as C}from"./app-J5EqS6dS.js";import{C as c,a as l,b as i,c as n}from"./card-9XCADs-4.js";import{B as o}from"./badge-BucYuCBs.js";import{B as N,S as A}from"./smartphone-GGiwNneF.js";import{S as T,a as R,b as D,c as U,d as x}from"./select-CIhY0l9J.js";import{A as E}from"./app-layout-ox1kAwY6.js";import{R as q}from"./refresh-cw-b5UG9YKX.js";import{D as I}from"./download-fvx_BKiV.js";import{S as k}from"./search-DBK6jUoc.js";import{U as G}from"./users-RYmOyic9.js";import{G as L}from"./globe-zfFlVOSX.js";import{T as $}from"./target-BJCwZ93C.js";import{C as v,A as z}from"./ImpersonationBanner-CYn5eDk6.js";import{T as B}from"./trending-up-BtixJGWw.js";import{E as F}from"./eye-D-fsmYB2.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./triangle-alert-BW76NKO9.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";function ge({analytics:a,days:g}){const[h,w]=d.useState(g.toString()),[m,j]=d.useState(a.real_time_stats),[u,f]=d.useState(!1);d.useEffect(()=>{const s=setInterval(async()=>{try{const t=await fetch("/admin/search-analytics/real-time?type=overview");if(t.ok){const S=await t.json();j(S)}}catch(t){console.error("Failed to fetch real-time stats:",t)}},3e4);return()=>clearInterval(s)},[]);const y=s=>{w(s),window.location.href=`/admin/search-analytics?days=${s}`},_=async()=>{f(!0);try{const s=await fetch("/admin/search-analytics/real-time?type=overview");if(s.ok){const t=await s.json();j(t)}}catch(s){console.error("Failed to refresh stats:",s)}finally{f(!1)}},b=s=>{window.location.href=`/admin/search-analytics/export?days=${h}&format=${s}`},r=s=>new Intl.NumberFormat().format(s),p=s=>`${s}%`;return e.jsxs(E,{children:[e.jsx(C,{title:"Search Analytics - Admin Dashboard"}),e.jsx("div",{className:"py-12",children:e.jsxs("div",{className:"max-w-7xl mx-auto sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Search Analytics"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Comprehensive search tracking and performance insights"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(T,{value:h,onValueChange:y,children:[e.jsx(R,{className:"w-32",children:e.jsx(D,{})}),e.jsxs(U,{children:[e.jsx(x,{value:"7",children:"Last 7 days"}),e.jsx(x,{value:"30",children:"Last 30 days"}),e.jsx(x,{value:"90",children:"Last 90 days"}),e.jsx(x,{value:"365",children:"Last year"})]})]}),e.jsxs(N,{variant:"outline",size:"sm",onClick:_,disabled:u,children:[e.jsx(q,{className:`h-4 w-4 mr-2 ${u?"animate-spin":""}`}),"Refresh"]}),e.jsxs(N,{variant:"outline",size:"sm",onClick:()=>b("csv"),children:[e.jsx(I,{className:"h-4 w-4 mr-2"}),"Export CSV"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[e.jsxs(c,{children:[e.jsxs(l,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(i,{className:"text-sm font-medium",children:"Searches Today"}),e.jsx(k,{className:"h-4 w-4 text-blue-600"})]}),e.jsxs(n,{children:[e.jsx("div",{className:"text-2xl font-bold",children:r(m.searches_today)}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:[r(m.searches_last_hour)," in last hour"]})]})]}),e.jsxs(c,{children:[e.jsxs(l,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(i,{className:"text-sm font-medium",children:"Active Searchers"}),e.jsx(G,{className:"h-4 w-4 text-green-600"})]}),e.jsxs(n,{children:[e.jsx("div",{className:"text-2xl font-bold",children:r(m.active_searchers_today)}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Unique users today"})]})]}),e.jsxs(c,{children:[e.jsxs(l,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(i,{className:"text-sm font-medium",children:"Guest Searches"}),e.jsx(L,{className:"h-4 w-4 text-purple-600"})]}),e.jsxs(n,{children:[e.jsx("div",{className:"text-2xl font-bold",children:r(m.guest_searches_today)}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"From guest users today"})]})]}),e.jsxs(c,{children:[e.jsxs(l,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(i,{className:"text-sm font-medium",children:"Success Rate"}),e.jsx($,{className:"h-4 w-4 text-orange-600"})]}),e.jsxs(n,{children:[e.jsx("div",{className:"text-2xl font-bold",children:p(a.overview.search_success_rate)}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Searches with results"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[e.jsxs(c,{children:[e.jsx(l,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(v,{className:"h-5 w-5"}),"Search Overview"]})}),e.jsxs(n,{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Total Searches"}),e.jsx("span",{className:"font-medium",children:r(a.overview.total_searches)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"User Searches"}),e.jsx("span",{className:"font-medium",children:r(a.overview.user_searches)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Guest Searches"}),e.jsx("span",{className:"font-medium",children:r(a.overview.guest_searches)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Unique Searchers"}),e.jsx("span",{className:"font-medium",children:r(a.overview.unique_searchers)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Avg per User"}),e.jsx("span",{className:"font-medium",children:a.overview.avg_searches_per_user})]})]})]}),e.jsxs(c,{children:[e.jsx(l,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(z,{className:"h-5 w-5"}),"Performance Metrics"]})}),e.jsxs(n,{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Success Rate"}),e.jsx(o,{variant:"outline",className:"text-green-600",children:p(a.search_performance.success_rate)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Avg Results"}),e.jsx("span",{className:"font-medium",children:a.search_performance.avg_results_per_search})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Zero Results"}),e.jsx("span",{className:"font-medium",children:r(a.search_performance.zero_result_searches)})]})]})]}),e.jsxs(c,{children:[e.jsx(l,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(A,{className:"h-5 w-5"}),"Guest Analytics"]})}),e.jsxs(n,{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Total Searches"}),e.jsx("span",{className:"font-medium",children:r(a.guest_searches.total_searches)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Unique Devices"}),e.jsx("span",{className:"font-medium",children:r(a.guest_searches.unique_devices)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Avg per Device"}),e.jsx("span",{className:"font-medium",children:a.guest_searches.avg_searches_per_device})]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[e.jsxs(c,{children:[e.jsx(l,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(B,{className:"h-5 w-5"}),"Popular Search Queries"]})}),e.jsx(n,{children:e.jsx("div",{className:"space-y-3",children:a.popular_searches.popular_queries.slice(0,10).map((s,t)=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm truncate flex-1 mr-4",children:s.search_query}),e.jsx(o,{variant:"outline",children:r(s.count)})]},t))})})]}),e.jsxs(c,{children:[e.jsx(l,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(F,{className:"h-5 w-5"}),"Top Searchers"]})}),e.jsx(n,{children:e.jsx("div",{className:"space-y-3",children:a.user_searches.top_searchers.slice(0,10).map((s,t)=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"flex-1 mr-4",children:[e.jsx("div",{className:"text-sm font-medium truncate",children:s.user.name}),e.jsx("div",{className:"text-xs text-muted-foreground truncate",children:s.user.email})]}),e.jsx(o,{variant:"outline",children:r(s.search_count)})]},t))})})]})]}),e.jsxs(c,{children:[e.jsx(l,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(v,{className:"h-5 w-5"}),"Search Types Distribution"]})}),e.jsx(n,{children:e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:a.popular_searches.searches_by_type.map((s,t)=>e.jsxs("div",{className:"text-center p-4 border rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:r(s.count)}),e.jsx("div",{className:"text-sm text-muted-foreground capitalize",children:s.search_type})]},t))})})]})]})})]})}export{ge as default};
