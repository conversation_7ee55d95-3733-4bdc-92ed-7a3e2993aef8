var y0=Object.defineProperty;var g0=(e,t,r)=>t in e?y0(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var Wa=(e,t,r)=>g0(e,typeof t!="symbol"?t+"":t,r);import{g as Lt,r as v,a as Nv,j as b,Q as b0}from"./app-J5EqS6dS.js";import{C as ye,a as ge,b as be,c as xe,d as rt}from"./card-9XCADs-4.js";import{B as gt}from"./badge-BucYuCBs.js";import{T as x0,a as w0,b as Jn,c as ei}from"./tabs-DZAL-HvD.js";import{A as P0}from"./app-layout-ox1kAwY6.js";import{a4 as O0,A as Tc,C as Cc}from"./ImpersonationBanner-CYn5eDk6.js";import{U as Fa}from"./users-RYmOyic9.js";import{P as ln}from"./package-CoyvngX8.js";import{S as Ua}from"./search-DBK6jUoc.js";import{T as A0}from"./trending-up-BtixJGWw.js";import{E as un}from"./eye-D-fsmYB2.js";import{T as S0}from"./target-BJCwZ93C.js";import{c as El,i as W}from"./smartphone-GGiwNneF.js";import{r as Iv}from"./index-CJpBU2i9.js";import{Z as kc}from"./zap-BcmHRR4K.js";import{D as j0}from"./database-s9JOA0jY.js";import{C as Ya}from"./circle-check-big-DOFoatRy.js";import{T as Ha}from"./triangle-alert-BW76NKO9.js";import{H as E0}from"./hard-drive-BTn_ba7c.js";import{C as _0}from"./chart-pie-CL4t8B4f.js";import{C as T0}from"./calendar-B-u_QN2Q.js";import{G as C0}from"./globe-zfFlVOSX.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./user-DCnDRzMf.js";import"./crown-UDSxMtlm.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k0=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]],M0=El("ChartLine",k0);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N0=[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]],I0=El("Server",N0);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D0=[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]],$0=El("ShoppingCart",D0);var Ga={},Va={},Mc;function R0(){return Mc||(Mc=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return r==="__proto__"}e.isUnsafeProperty=t}(Va)),Va}var Xa={},Nc;function Dv(){return Nc||(Nc=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){switch(typeof r){case"number":case"symbol":return!1;case"string":return r.includes(".")||r.includes("[")||r.includes("]")}}e.isDeepKey=t}(Xa)),Xa}var Za={},Ic;function $v(){return Ic||(Ic=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){var n;return typeof r=="string"||typeof r=="symbol"?r:Object.is((n=r==null?void 0:r.valueOf)==null?void 0:n.call(r),-0)?"-0":String(r)}e.toKey=t}(Za)),Za}var Qa={},Dc;function _l(){return Dc||(Dc=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){const n=[],i=r.length;if(i===0)return n;let a=0,o="",s="",l=!1;for(r.charCodeAt(0)===46&&(n.push(""),a++);a<i;){const c=r[a];s?c==="\\"&&a+1<i?(a++,o+=r[a]):c===s?s="":o+=c:l?c==='"'||c==="'"?s=c:c==="]"?(l=!1,n.push(o),o=""):o+=c:c==="["?(l=!0,o&&(n.push(o),o="")):c==="."?o&&(n.push(o),o=""):o+=c,a++}return o&&n.push(o),n}e.toPath=t}(Qa)),Qa}var $c;function Tl(){return $c||($c=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=R0(),r=Dv(),n=$v(),i=_l();function a(s,l,c){if(s==null)return c;switch(typeof l){case"string":{if(t.isUnsafeProperty(l))return c;const u=s[l];return u===void 0?r.isDeepKey(l)?a(s,i.toPath(l),c):c:u}case"number":case"symbol":{typeof l=="number"&&(l=n.toKey(l));const u=s[l];return u===void 0?c:u}default:{if(Array.isArray(l))return o(s,l,c);if(Object.is(l==null?void 0:l.valueOf(),-0)?l="-0":l=String(l),t.isUnsafeProperty(l))return c;const u=s[l];return u===void 0?c:u}}}function o(s,l,c){if(l.length===0)return c;let u=s;for(let f=0;f<l.length;f++){if(u==null||t.isUnsafeProperty(l[f]))return c;u=u[l[f]]}return u===void 0?c:u}e.get=a}(Ga)),Ga}var Ja,Rc;function L0(){return Rc||(Rc=1,Ja=Tl().get),Ja}var B0=L0();const Zt=Lt(B0);var eo={exports:{}},Z={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lc;function K0(){if(Lc)return Z;Lc=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),n=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),a=Symbol.for("react.consumer"),o=Symbol.for("react.context"),s=Symbol.for("react.forward_ref"),l=Symbol.for("react.suspense"),c=Symbol.for("react.suspense_list"),u=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),d=Symbol.for("react.view_transition"),h=Symbol.for("react.client.reference");function p(m){if(typeof m=="object"&&m!==null){var y=m.$$typeof;switch(y){case e:switch(m=m.type,m){case r:case i:case n:case l:case c:case d:return m;default:switch(m=m&&m.$$typeof,m){case o:case s:case f:case u:return m;case a:return m;default:return y}}case t:return y}}}return Z.ContextConsumer=a,Z.ContextProvider=o,Z.Element=e,Z.ForwardRef=s,Z.Fragment=r,Z.Lazy=f,Z.Memo=u,Z.Portal=t,Z.Profiler=i,Z.StrictMode=n,Z.Suspense=l,Z.SuspenseList=c,Z.isContextConsumer=function(m){return p(m)===a},Z.isContextProvider=function(m){return p(m)===o},Z.isElement=function(m){return typeof m=="object"&&m!==null&&m.$$typeof===e},Z.isForwardRef=function(m){return p(m)===s},Z.isFragment=function(m){return p(m)===r},Z.isLazy=function(m){return p(m)===f},Z.isMemo=function(m){return p(m)===u},Z.isPortal=function(m){return p(m)===t},Z.isProfiler=function(m){return p(m)===i},Z.isStrictMode=function(m){return p(m)===n},Z.isSuspense=function(m){return p(m)===l},Z.isSuspenseList=function(m){return p(m)===c},Z.isValidElementType=function(m){return typeof m=="string"||typeof m=="function"||m===r||m===i||m===n||m===l||m===c||typeof m=="object"&&m!==null&&(m.$$typeof===f||m.$$typeof===u||m.$$typeof===o||m.$$typeof===a||m.$$typeof===s||m.$$typeof===h||m.getModuleId!==void 0)},Z.typeOf=p,Z}var Bc;function z0(){return Bc||(Bc=1,eo.exports=K0()),eo.exports}var q0=z0(),Oe=e=>e===0?0:e>0?1:-1,Ke=e=>typeof e=="number"&&e!=+e,fr=e=>typeof e=="string"&&e.indexOf("%")===e.length-1,N=e=>(typeof e=="number"||e instanceof Number)&&!Ke(e),wt=e=>N(e)||typeof e=="string",W0=0,Qt=e=>{var t=++W0;return"".concat(e||"").concat(t)},$e=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!N(t)&&typeof t!="string")return n;var a;if(fr(t)){if(r==null)return n;var o=t.indexOf("%");a=r*parseFloat(t.slice(0,o))/100}else a=+t;return Ke(a)&&(a=n),i&&r!=null&&a>r&&(a=r),a},Rv=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++)if(!r[e[n]])r[e[n]]=!0;else return!0;return!1},He=(e,t)=>N(e)&&N(t)?r=>e+r*(t-e):()=>t;function Mr(e,t,r){return N(e)&&N(t)?e+r*(t-e):t}function Lv(e,t,r){if(!(!e||!e.length))return e.find(n=>n&&(typeof t=="function"?t(n):Zt(n,t))===r)}var V=e=>e===null||typeof e>"u",Rn=e=>V(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1)),F0=["viewBox","children"],U0=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],Kc=["points","pathLength"],to={svg:F0,polygon:Kc,polyline:Kc},Cl=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],kl=(e,t)=>{if(!e||typeof e=="function"||typeof e=="boolean")return null;var r=e;if(v.isValidElement(e)&&(r=e.props),typeof r!="object"&&typeof r!="function")return null;var n={};return Object.keys(r).forEach(i=>{Cl.includes(i)&&(n[i]=a=>r[i](r,a))}),n},Y0=(e,t,r)=>n=>(e(t,r,n),null),Ln=(e,t,r)=>{if(e===null||typeof e!="object"&&typeof e!="function")return null;var n=null;return Object.keys(e).forEach(i=>{var a=e[i];Cl.includes(i)&&typeof a=="function"&&(n||(n={}),n[i]=Y0(a,t,r))}),n},zc=e=>typeof e=="string"?e:e?e.displayName||e.name||"Component":"",qc=null,ro=null,Bv=e=>{if(e===qc&&Array.isArray(ro))return ro;var t=[];return v.Children.forEach(e,r=>{V(r)||(q0.isFragment(r)?t=t.concat(Bv(r.props.children)):t.push(r))}),ro=t,qc=e,t};function Bn(e,t){var r=[],n=[];return Array.isArray(t)?n=t.map(i=>zc(i)):n=[zc(t)],Bv(e).forEach(i=>{var a=Zt(i,"type.displayName")||Zt(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}var na=e=>e&&typeof e=="object"&&"clipDot"in e?!!e.clipDot:!0,H0=(e,t,r,n)=>{var i,a=(i=n&&(to==null?void 0:to[n]))!==null&&i!==void 0?i:[];return t.startsWith("data-")||typeof e!="function"&&(n&&a.includes(t)||U0.includes(t))||r&&Cl.includes(t)},z=(e,t,r)=>{if(!e||typeof e=="function"||typeof e=="boolean")return null;var n=e;if(v.isValidElement(e)&&(n=e.props),typeof n!="object"&&typeof n!="function")return null;var i={};return Object.keys(n).forEach(a=>{var o;H0((o=n)===null||o===void 0?void 0:o[a],a,t,r)&&(i[a]=n[a])}),i},G0=["children","width","height","viewBox","className","style","title","desc"];function Cs(){return Cs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Cs.apply(null,arguments)}function V0(e,t){if(e==null)return{};var r,n,i=X0(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function X0(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var Ml=v.forwardRef((e,t)=>{var{children:r,width:n,height:i,viewBox:a,className:o,style:s,title:l,desc:c}=e,u=V0(e,G0),f=a||{width:n,height:i,x:0,y:0},d=W("recharts-surface",o);return v.createElement("svg",Cs({},z(u,!0,"svg"),{className:d,width:n,height:i,style:s,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height),ref:t}),v.createElement("title",null,l),v.createElement("desc",null,c),r)}),Z0=["children","className"];function ks(){return ks=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ks.apply(null,arguments)}function Q0(e,t){if(e==null)return{};var r,n,i=J0(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function J0(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var ae=v.forwardRef((e,t)=>{var{children:r,className:n}=e,i=Q0(e,Z0),a=W("recharts-layer",n);return v.createElement("g",ks({className:a},z(i,!0),{ref:t}),r)}),Kv=v.createContext(null),eb=()=>v.useContext(Kv);function te(e){return function(){return e}}const zv=Math.cos,gi=Math.sin,yt=Math.sqrt,bi=Math.PI,ia=2*bi,Ms=Math.PI,Ns=2*Ms,lr=1e-6,tb=Ns-lr;function qv(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function rb(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return qv;const r=10**t;return function(n){this._+=n[0];for(let i=1,a=n.length;i<a;++i)this._+=Math.round(arguments[i]*r)/r+n[i]}}class nb{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?qv:rb(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,n,i){this._append`Q${+t},${+r},${this._x1=+n},${this._y1=+i}`}bezierCurveTo(t,r,n,i,a,o){this._append`C${+t},${+r},${+n},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,n,i,a){if(t=+t,r=+r,n=+n,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,s=this._y1,l=n-t,c=i-r,u=o-t,f=s-r,d=u*u+f*f;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(d>lr)if(!(Math.abs(f*l-c*u)>lr)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let h=n-o,p=i-s,m=l*l+c*c,y=h*h+p*p,g=Math.sqrt(m),x=Math.sqrt(d),w=a*Math.tan((Ms-Math.acos((m+d-y)/(2*g*x)))/2),P=w/x,O=w/g;Math.abs(P-1)>lr&&this._append`L${t+P*u},${r+P*f}`,this._append`A${a},${a},0,0,${+(f*h>u*p)},${this._x1=t+O*l},${this._y1=r+O*c}`}}arc(t,r,n,i,a,o){if(t=+t,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let s=n*Math.cos(i),l=n*Math.sin(i),c=t+s,u=r+l,f=1^o,d=o?i-a:a-i;this._x1===null?this._append`M${c},${u}`:(Math.abs(this._x1-c)>lr||Math.abs(this._y1-u)>lr)&&this._append`L${c},${u}`,n&&(d<0&&(d=d%Ns+Ns),d>tb?this._append`A${n},${n},0,1,${f},${t-s},${r-l}A${n},${n},0,1,${f},${this._x1=c},${this._y1=u}`:d>lr&&this._append`A${n},${n},0,${+(d>=Ms)},${f},${this._x1=t+n*Math.cos(a)},${this._y1=r+n*Math.sin(a)}`)}rect(t,r,n,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${n=+n}v${+i}h${-n}Z`}toString(){return this._}}function Nl(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);t=n}return e},()=>new nb(t)}function Il(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function Wv(e){this._context=e}Wv.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function aa(e){return new Wv(e)}function Fv(e){return e[0]}function Uv(e){return e[1]}function Yv(e,t){var r=te(!0),n=null,i=aa,a=null,o=Nl(s);e=typeof e=="function"?e:e===void 0?Fv:te(e),t=typeof t=="function"?t:t===void 0?Uv:te(t);function s(l){var c,u=(l=Il(l)).length,f,d=!1,h;for(n==null&&(a=i(h=o())),c=0;c<=u;++c)!(c<u&&r(f=l[c],c,l))===d&&((d=!d)?a.lineStart():a.lineEnd()),d&&a.point(+e(f,c,l),+t(f,c,l));if(h)return a=null,h+""||null}return s.x=function(l){return arguments.length?(e=typeof l=="function"?l:te(+l),s):e},s.y=function(l){return arguments.length?(t=typeof l=="function"?l:te(+l),s):t},s.defined=function(l){return arguments.length?(r=typeof l=="function"?l:te(!!l),s):r},s.curve=function(l){return arguments.length?(i=l,n!=null&&(a=i(n)),s):i},s.context=function(l){return arguments.length?(l==null?n=a=null:a=i(n=l),s):n},s}function ti(e,t,r){var n=null,i=te(!0),a=null,o=aa,s=null,l=Nl(c);e=typeof e=="function"?e:e===void 0?Fv:te(+e),t=typeof t=="function"?t:te(t===void 0?0:+t),r=typeof r=="function"?r:r===void 0?Uv:te(+r);function c(f){var d,h,p,m=(f=Il(f)).length,y,g=!1,x,w=new Array(m),P=new Array(m);for(a==null&&(s=o(x=l())),d=0;d<=m;++d){if(!(d<m&&i(y=f[d],d,f))===g)if(g=!g)h=d,s.areaStart(),s.lineStart();else{for(s.lineEnd(),s.lineStart(),p=d-1;p>=h;--p)s.point(w[p],P[p]);s.lineEnd(),s.areaEnd()}g&&(w[d]=+e(y,d,f),P[d]=+t(y,d,f),s.point(n?+n(y,d,f):w[d],r?+r(y,d,f):P[d]))}if(x)return s=null,x+""||null}function u(){return Yv().defined(i).curve(o).context(a)}return c.x=function(f){return arguments.length?(e=typeof f=="function"?f:te(+f),n=null,c):e},c.x0=function(f){return arguments.length?(e=typeof f=="function"?f:te(+f),c):e},c.x1=function(f){return arguments.length?(n=f==null?null:typeof f=="function"?f:te(+f),c):n},c.y=function(f){return arguments.length?(t=typeof f=="function"?f:te(+f),r=null,c):t},c.y0=function(f){return arguments.length?(t=typeof f=="function"?f:te(+f),c):t},c.y1=function(f){return arguments.length?(r=f==null?null:typeof f=="function"?f:te(+f),c):r},c.lineX0=c.lineY0=function(){return u().x(e).y(t)},c.lineY1=function(){return u().x(e).y(r)},c.lineX1=function(){return u().x(n).y(t)},c.defined=function(f){return arguments.length?(i=typeof f=="function"?f:te(!!f),c):i},c.curve=function(f){return arguments.length?(o=f,a!=null&&(s=o(a)),c):o},c.context=function(f){return arguments.length?(f==null?a=s=null:s=o(a=f),c):a},c}class Hv{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function ib(e){return new Hv(e,!0)}function ab(e){return new Hv(e,!1)}const Dl={draw(e,t){const r=yt(t/bi);e.moveTo(r,0),e.arc(0,0,r,0,ia)}},ob={draw(e,t){const r=yt(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},Gv=yt(1/3),sb=Gv*2,lb={draw(e,t){const r=yt(t/sb),n=r*Gv;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},ub={draw(e,t){const r=yt(t),n=-r/2;e.rect(n,n,r,r)}},cb=.8908130915292852,Vv=gi(bi/10)/gi(7*bi/10),fb=gi(ia/10)*Vv,db=-zv(ia/10)*Vv,hb={draw(e,t){const r=yt(t*cb),n=fb*r,i=db*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const o=ia*a/5,s=zv(o),l=gi(o);e.lineTo(l*r,-s*r),e.lineTo(s*n-l*i,l*n+s*i)}e.closePath()}},no=yt(3),vb={draw(e,t){const r=-yt(t/(no*3));e.moveTo(0,r*2),e.lineTo(-no*r,-r),e.lineTo(no*r,-r),e.closePath()}},nt=-.5,it=yt(3)/2,Is=1/yt(12),pb=(Is/2+1)*3,mb={draw(e,t){const r=yt(t/pb),n=r/2,i=r*Is,a=n,o=r*Is+r,s=-a,l=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(s,l),e.lineTo(nt*n-it*i,it*n+nt*i),e.lineTo(nt*a-it*o,it*a+nt*o),e.lineTo(nt*s-it*l,it*s+nt*l),e.lineTo(nt*n+it*i,nt*i-it*n),e.lineTo(nt*a+it*o,nt*o-it*a),e.lineTo(nt*s+it*l,nt*l-it*s),e.closePath()}};function yb(e,t){let r=null,n=Nl(i);e=typeof e=="function"?e:te(e||Dl),t=typeof t=="function"?t:te(t===void 0?64:+t);function i(){let a;if(r||(r=a=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),a)return r=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:te(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:te(+a),i):t},i.context=function(a){return arguments.length?(r=a??null,i):r},i}function xi(){}function wi(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function Xv(e){this._context=e}Xv.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:wi(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:wi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function gb(e){return new Xv(e)}function Zv(e){this._context=e}Zv.prototype={areaStart:xi,areaEnd:xi,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:wi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function bb(e){return new Zv(e)}function Qv(e){this._context=e}Qv.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:wi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function xb(e){return new Qv(e)}function Jv(e){this._context=e}Jv.prototype={areaStart:xi,areaEnd:xi,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function wb(e){return new Jv(e)}function Wc(e){return e<0?-1:1}function Fc(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),s=(a*i+o*n)/(n+i);return(Wc(a)+Wc(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(s))||0}function Uc(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function io(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,s=(a-n)/3;e._context.bezierCurveTo(n+s,i+s*t,a-s,o-s*r,a,o)}function Pi(e){this._context=e}Pi.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:io(this,this._t0,Uc(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,io(this,Uc(this,r=Fc(this,e,t)),r);break;default:io(this,this._t0,r=Fc(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function ep(e){this._context=new tp(e)}(ep.prototype=Object.create(Pi.prototype)).point=function(e,t){Pi.prototype.point.call(this,t,e)};function tp(e){this._context=e}tp.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}};function Pb(e){return new Pi(e)}function Ob(e){return new ep(e)}function rp(e){this._context=e}rp.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var n=Yc(e),i=Yc(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function Yc(e){var t,r=e.length-1,n,i=new Array(r),a=new Array(r),o=new Array(r);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=i[t]/a[t-1],a[t]-=n,o[t]-=n*o[t-1];for(i[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[r-1]=(e[r]+i[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function Ab(e){return new rp(e)}function oa(e,t){this._context=e,this._t=t}oa.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function Sb(e){return new oa(e,.5)}function jb(e){return new oa(e,0)}function Eb(e){return new oa(e,1)}function Fr(e,t){if((o=e.length)>1)for(var r=1,n,i,a=e[t[0]],o,s=a.length;r<o;++r)for(i=a,a=e[t[r]],n=0;n<s;++n)a[n][1]+=a[n][0]=isNaN(i[n][1])?i[n][0]:i[n][1]}function Ds(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function _b(e,t){return e[t]}function Tb(e){const t=[];return t.key=e,t}function Cb(){var e=te([]),t=Ds,r=Fr,n=_b;function i(a){var o=Array.from(e.apply(this,arguments),Tb),s,l=o.length,c=-1,u;for(const f of a)for(s=0,++c;s<l;++s)(o[s][c]=[0,+n(f,o[s].key,c,a)]).data=f;for(s=0,u=Il(t(o));s<l;++s)o[u[s]].index=s;return r(o,u),o}return i.keys=function(a){return arguments.length?(e=typeof a=="function"?a:te(Array.from(a)),i):e},i.value=function(a){return arguments.length?(n=typeof a=="function"?a:te(+a),i):n},i.order=function(a){return arguments.length?(t=a==null?Ds:typeof a=="function"?a:te(Array.from(a)),i):t},i.offset=function(a){return arguments.length?(r=a??Fr,i):r},i}function kb(e,t){if((n=e.length)>0){for(var r,n,i=0,a=e[0].length,o;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}Fr(e,t)}}function Mb(e,t){if((i=e.length)>0){for(var r=0,n=e[t[0]],i,a=n.length;r<a;++r){for(var o=0,s=0;o<i;++o)s+=e[o][r][1]||0;n[r][1]+=n[r][0]=-s/2}Fr(e,t)}}function Nb(e,t){if(!(!((o=e.length)>0)||!((a=(i=e[t[0]]).length)>0))){for(var r=0,n=1,i,a,o;n<a;++n){for(var s=0,l=0,c=0;s<o;++s){for(var u=e[t[s]],f=u[n][1]||0,d=u[n-1][1]||0,h=(f-d)/2,p=0;p<s;++p){var m=e[t[p]],y=m[n][1]||0,g=m[n-1][1]||0;h+=y-g}l+=f,c+=h*f}i[n-1][1]+=i[n-1][0]=r,l&&(r-=c/l)}i[n-1][1]+=i[n-1][0]=r,Fr(e,t)}}var Ib=["type","size","sizeType"];function $s(){return $s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$s.apply(null,arguments)}function Hc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Gc(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Hc(Object(r),!0).forEach(function(n){Db(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hc(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Db(e,t,r){return(t=$b(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $b(e){var t=Rb(e,"string");return typeof t=="symbol"?t:t+""}function Rb(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Lb(e,t){if(e==null)return{};var r,n,i=Bb(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function Bb(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var np={symbolCircle:Dl,symbolCross:ob,symbolDiamond:lb,symbolSquare:ub,symbolStar:hb,symbolTriangle:vb,symbolWye:mb},Kb=Math.PI/180,zb=e=>{var t="symbol".concat(Rn(e));return np[t]||Dl},qb=(e,t,r)=>{if(t==="area")return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":{var n=18*Kb;return 1.25*e*e*(Math.tan(n)-Math.tan(n*2)*Math.tan(n)**2)}case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},Wb=(e,t)=>{np["symbol".concat(Rn(e))]=t},$l=e=>{var{type:t="circle",size:r=64,sizeType:n="area"}=e,i=Lb(e,Ib),a=Gc(Gc({},i),{},{type:t,size:r,sizeType:n}),o=()=>{var f=zb(t),d=yb().type(f).size(qb(r,n,t));return d()},{className:s,cx:l,cy:c}=a,u=z(a,!0);return l===+l&&c===+c&&r===+r?v.createElement("path",$s({},u,{className:W("recharts-symbols",s),transform:"translate(".concat(l,", ").concat(c,")"),d:o()})):null};$l.registerSymbol=Wb;function Rs(){return Rs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Rs.apply(null,arguments)}function Vc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Fb(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Vc(Object(r),!0).forEach(function(n){Rl(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Vc(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Rl(e,t,r){return(t=Ub(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ub(e){var t=Yb(e,"string");return typeof t=="symbol"?t:t+""}function Yb(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var at=32;class Ll extends v.PureComponent{renderIcon(t,r){var{inactiveColor:n}=this.props,i=at/2,a=at/6,o=at/3,s=t.inactive?n:t.color,l=r??t.type;if(l==="none")return null;if(l==="plainline")return v.createElement("line",{strokeWidth:4,fill:"none",stroke:s,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:i,x2:at,y2:i,className:"recharts-legend-icon"});if(l==="line")return v.createElement("path",{strokeWidth:4,fill:"none",stroke:s,d:"M0,".concat(i,"h").concat(o,`
            A`).concat(a,",").concat(a,",0,1,1,").concat(2*o,",").concat(i,`
            H`).concat(at,"M").concat(2*o,",").concat(i,`
            A`).concat(a,",").concat(a,",0,1,1,").concat(o,",").concat(i),className:"recharts-legend-icon"});if(l==="rect")return v.createElement("path",{stroke:"none",fill:s,d:"M0,".concat(at/8,"h").concat(at,"v").concat(at*3/4,"h").concat(-at,"z"),className:"recharts-legend-icon"});if(v.isValidElement(t.legendIcon)){var c=Fb({},t);return delete c.legendIcon,v.cloneElement(t.legendIcon,c)}return v.createElement($l,{fill:s,cx:i,cy:i,size:at,sizeType:"diameter",type:l})}renderItems(){var{payload:t,iconSize:r,layout:n,formatter:i,inactiveColor:a,iconType:o}=this.props,s={x:0,y:0,width:at,height:at},l={display:n==="horizontal"?"inline-block":"block",marginRight:10},c={display:"inline-block",verticalAlign:"middle",marginRight:4};return t.map((u,f)=>{var d=u.formatter||i,h=W({"recharts-legend-item":!0,["legend-item-".concat(f)]:!0,inactive:u.inactive});if(u.type==="none")return null;var p=u.inactive?a:u.color,m=d?d(u.value,u,f):u.value;return v.createElement("li",Rs({className:h,style:l,key:"legend-item-".concat(f)},Ln(this.props,u,f)),v.createElement(Ml,{width:r,height:r,viewBox:s,style:c,"aria-label":"".concat(m," legend icon")},this.renderIcon(u,o)),v.createElement("span",{className:"recharts-legend-item-text",style:{color:p}},m))})}render(){var{payload:t,layout:r,align:n}=this.props;if(!t||!t.length)return null;var i={padding:0,margin:0,textAlign:r==="horizontal"?n:"left"};return v.createElement("ul",{className:"recharts-default-legend",style:i},this.renderItems())}}Rl(Ll,"displayName","Legend");Rl(Ll,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",layout:"horizontal",verticalAlign:"middle"});var ao={},oo={},Xc;function Hb(){return Xc||(Xc=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r,n){const i=new Map;for(let a=0;a<r.length;a++){const o=r[a],s=n(o);i.has(s)||i.set(s,o)}return Array.from(i.values())}e.uniqBy=t}(oo)),oo}var so={},Zc;function ip(){return Zc||(Zc=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return r}e.identity=t}(so)),so}var lo={},uo={},co={},Qc;function Gb(){return Qc||(Qc=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return Number.isSafeInteger(r)&&r>=0}e.isLength=t}(co)),co}var Jc;function Bl(){return Jc||(Jc=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Gb();function r(n){return n!=null&&typeof n!="function"&&t.isLength(n.length)}e.isArrayLike=r}(uo)),uo}var fo={},ef;function Vb(){return ef||(ef=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return typeof r=="object"&&r!==null}e.isObjectLike=t}(fo)),fo}var tf;function Xb(){return tf||(tf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Bl(),r=Vb();function n(i){return r.isObjectLike(i)&&t.isArrayLike(i)}e.isArrayLikeObject=n}(lo)),lo}var ho={},vo={},rf;function Zb(){return rf||(rf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Tl();function r(n){return function(i){return t.get(i,n)}}e.property=r}(vo)),vo}var po={},mo={},yo={},go={},nf;function ap(){return nf||(nf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return r!==null&&(typeof r=="object"||typeof r=="function")}e.isObject=t}(go)),go}var bo={},af;function op(){return af||(af=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return r==null||typeof r!="object"&&typeof r!="function"}e.isPrimitive=t}(bo)),bo}var xo={},of;function Kl(){return of||(of=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r,n){return r===n||Number.isNaN(r)&&Number.isNaN(n)}e.eq=t}(xo)),xo}var sf;function Qb(){return sf||(sf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=zl(),r=ap(),n=op(),i=Kl();function a(f,d,h){return typeof h!="function"?t.isMatch(f,d):o(f,d,function p(m,y,g,x,w,P){const O=h(m,y,g,x,w,P);return O!==void 0?!!O:o(m,y,p,P)},new Map)}function o(f,d,h,p){if(d===f)return!0;switch(typeof d){case"object":return s(f,d,h,p);case"function":return Object.keys(d).length>0?o(f,{...d},h,p):i.eq(f,d);default:return r.isObject(f)?typeof d=="string"?d==="":!0:i.eq(f,d)}}function s(f,d,h,p){if(d==null)return!0;if(Array.isArray(d))return c(f,d,h,p);if(d instanceof Map)return l(f,d,h,p);if(d instanceof Set)return u(f,d,h,p);const m=Object.keys(d);if(f==null)return m.length===0;if(m.length===0)return!0;if(p&&p.has(d))return p.get(d)===f;p&&p.set(d,f);try{for(let y=0;y<m.length;y++){const g=m[y];if(!n.isPrimitive(f)&&!(g in f)||d[g]===void 0&&f[g]!==void 0||d[g]===null&&f[g]!==null||!h(f[g],d[g],g,f,d,p))return!1}return!0}finally{p&&p.delete(d)}}function l(f,d,h,p){if(d.size===0)return!0;if(!(f instanceof Map))return!1;for(const[m,y]of d.entries()){const g=f.get(m);if(h(g,y,m,f,d,p)===!1)return!1}return!0}function c(f,d,h,p){if(d.length===0)return!0;if(!Array.isArray(f))return!1;const m=new Set;for(let y=0;y<d.length;y++){const g=d[y];let x=!1;for(let w=0;w<f.length;w++){if(m.has(w))continue;const P=f[w];let O=!1;if(h(P,g,y,f,d,p)&&(O=!0),O){m.add(w),x=!0;break}}if(!x)return!1}return!0}function u(f,d,h,p){return d.size===0?!0:f instanceof Set?c([...f],[...d],h,p):!1}e.isMatchWith=a,e.isSetMatch=u}(yo)),yo}var lf;function zl(){return lf||(lf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Qb();function r(n,i){return t.isMatchWith(n,i,()=>{})}e.isMatch=r}(mo)),mo}var wo={},Po={},Oo={},uf;function sp(){return uf||(uf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return Object.getOwnPropertySymbols(r).filter(n=>Object.prototype.propertyIsEnumerable.call(r,n))}e.getSymbols=t}(Oo)),Oo}var Ao={},cf;function ql(){return cf||(cf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return r==null?r===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(r)}e.getTag=t}(Ao)),Ao}var So={},ff;function Wl(){return ff||(ff=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t="[object RegExp]",r="[object String]",n="[object Number]",i="[object Boolean]",a="[object Arguments]",o="[object Symbol]",s="[object Date]",l="[object Map]",c="[object Set]",u="[object Array]",f="[object Function]",d="[object ArrayBuffer]",h="[object Object]",p="[object Error]",m="[object DataView]",y="[object Uint8Array]",g="[object Uint8ClampedArray]",x="[object Uint16Array]",w="[object Uint32Array]",P="[object BigUint64Array]",O="[object Int8Array]",A="[object Int16Array]",S="[object Int32Array]",E="[object BigInt64Array]",_="[object Float32Array]",M="[object Float64Array]";e.argumentsTag=a,e.arrayBufferTag=d,e.arrayTag=u,e.bigInt64ArrayTag=E,e.bigUint64ArrayTag=P,e.booleanTag=i,e.dataViewTag=m,e.dateTag=s,e.errorTag=p,e.float32ArrayTag=_,e.float64ArrayTag=M,e.functionTag=f,e.int16ArrayTag=A,e.int32ArrayTag=S,e.int8ArrayTag=O,e.mapTag=l,e.numberTag=n,e.objectTag=h,e.regexpTag=t,e.setTag=c,e.stringTag=r,e.symbolTag=o,e.uint16ArrayTag=x,e.uint32ArrayTag=w,e.uint8ArrayTag=y,e.uint8ClampedArrayTag=g}(So)),So}var jo={},df;function Jb(){return df||(df=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return ArrayBuffer.isView(r)&&!(r instanceof DataView)}e.isTypedArray=t}(jo)),jo}var hf;function lp(){return hf||(hf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=sp(),r=ql(),n=Wl(),i=op(),a=Jb();function o(u,f){return s(u,void 0,u,new Map,f)}function s(u,f,d,h=new Map,p=void 0){const m=p==null?void 0:p(u,f,d,h);if(m!=null)return m;if(i.isPrimitive(u))return u;if(h.has(u))return h.get(u);if(Array.isArray(u)){const y=new Array(u.length);h.set(u,y);for(let g=0;g<u.length;g++)y[g]=s(u[g],g,d,h,p);return Object.hasOwn(u,"index")&&(y.index=u.index),Object.hasOwn(u,"input")&&(y.input=u.input),y}if(u instanceof Date)return new Date(u.getTime());if(u instanceof RegExp){const y=new RegExp(u.source,u.flags);return y.lastIndex=u.lastIndex,y}if(u instanceof Map){const y=new Map;h.set(u,y);for(const[g,x]of u)y.set(g,s(x,g,d,h,p));return y}if(u instanceof Set){const y=new Set;h.set(u,y);for(const g of u)y.add(s(g,void 0,d,h,p));return y}if(typeof Buffer<"u"&&Buffer.isBuffer(u))return u.subarray();if(a.isTypedArray(u)){const y=new(Object.getPrototypeOf(u)).constructor(u.length);h.set(u,y);for(let g=0;g<u.length;g++)y[g]=s(u[g],g,d,h,p);return y}if(u instanceof ArrayBuffer||typeof SharedArrayBuffer<"u"&&u instanceof SharedArrayBuffer)return u.slice(0);if(u instanceof DataView){const y=new DataView(u.buffer.slice(0),u.byteOffset,u.byteLength);return h.set(u,y),l(y,u,d,h,p),y}if(typeof File<"u"&&u instanceof File){const y=new File([u],u.name,{type:u.type});return h.set(u,y),l(y,u,d,h,p),y}if(u instanceof Blob){const y=new Blob([u],{type:u.type});return h.set(u,y),l(y,u,d,h,p),y}if(u instanceof Error){const y=new u.constructor;return h.set(u,y),y.message=u.message,y.name=u.name,y.stack=u.stack,y.cause=u.cause,l(y,u,d,h,p),y}if(typeof u=="object"&&c(u)){const y=Object.create(Object.getPrototypeOf(u));return h.set(u,y),l(y,u,d,h,p),y}return u}function l(u,f,d=u,h,p){const m=[...Object.keys(f),...t.getSymbols(f)];for(let y=0;y<m.length;y++){const g=m[y],x=Object.getOwnPropertyDescriptor(u,g);(x==null||x.writable)&&(u[g]=s(f[g],g,d,h,p))}}function c(u){switch(r.getTag(u)){case n.argumentsTag:case n.arrayTag:case n.arrayBufferTag:case n.dataViewTag:case n.booleanTag:case n.dateTag:case n.float32ArrayTag:case n.float64ArrayTag:case n.int8ArrayTag:case n.int16ArrayTag:case n.int32ArrayTag:case n.mapTag:case n.numberTag:case n.objectTag:case n.regexpTag:case n.setTag:case n.stringTag:case n.symbolTag:case n.uint8ArrayTag:case n.uint8ClampedArrayTag:case n.uint16ArrayTag:case n.uint32ArrayTag:return!0;default:return!1}}e.cloneDeepWith=o,e.cloneDeepWithImpl=s,e.copyProperties=l}(Po)),Po}var vf;function ex(){return vf||(vf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=lp();function r(n){return t.cloneDeepWithImpl(n,void 0,n,new Map,void 0)}e.cloneDeep=r}(wo)),wo}var pf;function tx(){return pf||(pf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=zl(),r=ex();function n(i){return i=r.cloneDeep(i),a=>t.isMatch(a,i)}e.matches=n}(po)),po}var Eo={},_o={},To={},mf;function rx(){return mf||(mf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=lp(),r=Wl();function n(i,a){return t.cloneDeepWith(i,(o,s,l,c)=>{const u=a==null?void 0:a(o,s,l,c);if(u!=null)return u;if(typeof i=="object")switch(Object.prototype.toString.call(i)){case r.numberTag:case r.stringTag:case r.booleanTag:{const f=new i.constructor(i==null?void 0:i.valueOf());return t.copyProperties(f,i),f}case r.argumentsTag:{const f={};return t.copyProperties(f,i),f.length=i.length,f[Symbol.iterator]=i[Symbol.iterator],f}default:return}})}e.cloneDeepWith=n}(To)),To}var yf;function nx(){return yf||(yf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=rx();function r(n){return t.cloneDeepWith(n)}e.cloneDeep=r}(_o)),_o}var Co={},ko={},gf;function up(){return gf||(gf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=/^(?:0|[1-9]\d*)$/;function r(n,i=Number.MAX_SAFE_INTEGER){switch(typeof n){case"number":return Number.isInteger(n)&&n>=0&&n<i;case"symbol":return!1;case"string":return t.test(n)}}e.isIndex=r}(ko)),ko}var Mo={},bf;function ix(){return bf||(bf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=ql();function r(n){return n!==null&&typeof n=="object"&&t.getTag(n)==="[object Arguments]"}e.isArguments=r}(Mo)),Mo}var xf;function ax(){return xf||(xf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Dv(),r=up(),n=ix(),i=_l();function a(o,s){let l;if(Array.isArray(s)?l=s:typeof s=="string"&&t.isDeepKey(s)&&(o==null?void 0:o[s])==null?l=i.toPath(s):l=[s],l.length===0)return!1;let c=o;for(let u=0;u<l.length;u++){const f=l[u];if((c==null||!Object.hasOwn(c,f))&&!((Array.isArray(c)||n.isArguments(c))&&r.isIndex(f)&&f<c.length))return!1;c=c[f]}return!0}e.has=a}(Co)),Co}var wf;function ox(){return wf||(wf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=zl(),r=$v(),n=nx(),i=Tl(),a=ax();function o(s,l){switch(typeof s){case"object":{Object.is(s==null?void 0:s.valueOf(),-0)&&(s="-0");break}case"number":{s=r.toKey(s);break}}return l=n.cloneDeep(l),function(c){const u=i.get(c,s);return u===void 0?a.has(c,s):l===void 0?u===void 0:t.isMatch(u,l)}}e.matchesProperty=o}(Eo)),Eo}var Pf;function sx(){return Pf||(Pf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=ip(),r=Zb(),n=tx(),i=ox();function a(o){if(o==null)return t.identity;switch(typeof o){case"function":return o;case"object":return Array.isArray(o)&&o.length===2?i.matchesProperty(o[0],o[1]):n.matches(o);case"string":case"symbol":case"number":return r.property(o)}}e.iteratee=a}(ho)),ho}var Of;function lx(){return Of||(Of=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Hb(),r=ip(),n=Xb(),i=sx();function a(o,s=r.identity){return n.isArrayLikeObject(o)?t.uniqBy(Array.from(o),i.iteratee(s)):[]}e.uniqBy=a}(ao)),ao}var No,Af;function ux(){return Af||(Af=1,No=lx().uniqBy),No}var cx=ux();const Sf=Lt(cx);function cp(e,t,r){return t===!0?Sf(e,r):typeof t=="function"?Sf(e,t):e}var Io={exports:{}},Do={};/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jf;function fx(){if(jf)return Do;jf=1;var e=Nv(),t=O0();function r(c,u){return c===u&&(c!==0||1/c===1/u)||c!==c&&u!==u}var n=typeof Object.is=="function"?Object.is:r,i=t.useSyncExternalStore,a=e.useRef,o=e.useEffect,s=e.useMemo,l=e.useDebugValue;return Do.useSyncExternalStoreWithSelector=function(c,u,f,d,h){var p=a(null);if(p.current===null){var m={hasValue:!1,value:null};p.current=m}else m=p.current;p=s(function(){function g(A){if(!x){if(x=!0,w=A,A=d(A),h!==void 0&&m.hasValue){var S=m.value;if(h(S,A))return P=S}return P=A}if(S=P,n(w,A))return S;var E=d(A);return h!==void 0&&h(S,E)?(w=A,S):(w=A,P=E)}var x=!1,w,P,O=f===void 0?null:f;return[function(){return g(u())},O===null?void 0:function(){return g(O())}]},[u,f,d,h]);var y=i(c,p[0],p[1]);return o(function(){m.hasValue=!0,m.value=y},[y]),l(y),y},Do}var Ef;function dx(){return Ef||(Ef=1,Io.exports=fx()),Io.exports}var hx=dx(),Fl=v.createContext(null),vx=e=>e,ie=()=>{var e=v.useContext(Fl);return e?e.store.dispatch:vx},mi=()=>{},px=()=>mi,mx=(e,t)=>e===t;function D(e){var t=v.useContext(Fl);return hx.useSyncExternalStoreWithSelector(t?t.subscription.addNestedSub:px,t?t.store.getState:mi,t?t.store.getState:mi,t?e:mi,mx)}function yx(e,t=`expected a function, instead received ${typeof e}`){if(typeof e!="function")throw new TypeError(t)}function gx(e,t=`expected an object, instead received ${typeof e}`){if(typeof e!="object")throw new TypeError(t)}function bx(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(r=>typeof r=="function")){const r=e.map(n=>typeof n=="function"?`function ${n.name||"unnamed"}()`:typeof n).join(", ");throw new TypeError(`${t}[${r}]`)}}var _f=e=>Array.isArray(e)?e:[e];function xx(e){const t=Array.isArray(e[0])?e[0]:e;return bx(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}function wx(e,t){const r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}var Px=class{constructor(e){this.value=e}deref(){return this.value}},Ox=typeof WeakRef<"u"?WeakRef:Px,Ax=0,Tf=1;function ri(){return{s:Ax,v:void 0,o:null,p:null}}function fp(e,t={}){let r=ri();const{resultEqualityCheck:n}=t;let i,a=0;function o(){var f;let s=r;const{length:l}=arguments;for(let d=0,h=l;d<h;d++){const p=arguments[d];if(typeof p=="function"||typeof p=="object"&&p!==null){let m=s.o;m===null&&(s.o=m=new WeakMap);const y=m.get(p);y===void 0?(s=ri(),m.set(p,s)):s=y}else{let m=s.p;m===null&&(s.p=m=new Map);const y=m.get(p);y===void 0?(s=ri(),m.set(p,s)):s=y}}const c=s;let u;if(s.s===Tf)u=s.v;else if(u=e.apply(null,arguments),a++,n){const d=((f=i==null?void 0:i.deref)==null?void 0:f.call(i))??i;d!=null&&n(d,u)&&(u=d,a!==0&&a--),i=typeof u=="object"&&u!==null||typeof u=="function"?new Ox(u):u}return c.s=Tf,c.v=u,u}return o.clearCache=()=>{r=ri(),o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}function Sx(e,...t){const r=typeof e=="function"?{memoize:e,memoizeOptions:t}:e,n=(...i)=>{let a=0,o=0,s,l={},c=i.pop();typeof c=="object"&&(l=c,c=i.pop()),yx(c,`createSelector expects an output function after the inputs, but received: [${typeof c}]`);const u={...r,...l},{memoize:f,memoizeOptions:d=[],argsMemoize:h=fp,argsMemoizeOptions:p=[]}=u,m=_f(d),y=_f(p),g=xx(i),x=f(function(){return a++,c.apply(null,arguments)},...m),w=h(function(){o++;const O=wx(g,arguments);return s=x.apply(null,O),s},...y);return Object.assign(w,{resultFunc:c,memoizedResultFunc:x,dependencies:g,dependencyRecomputations:()=>o,resetDependencyRecomputations:()=>{o=0},lastResult:()=>s,recomputations:()=>a,resetRecomputations:()=>{a=0},memoize:f,argsMemoize:h})};return Object.assign(n,{withTypes:()=>n}),n}var j=Sx(fp),jx=Object.assign((e,t=j)=>{gx(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);const r=Object.keys(e),n=r.map(a=>e[a]);return t(n,(...a)=>a.reduce((o,s,l)=>(o[r[l]]=s,o),{}))},{withTypes:()=>jx}),$o={},Ro={},Lo={},Cf;function Ex(){return Cf||(Cf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(n){return typeof n=="symbol"?1:n===null?2:n===void 0?3:n!==n?4:0}const r=(n,i,a)=>{if(n!==i){const o=t(n),s=t(i);if(o===s&&o===0){if(n<i)return a==="desc"?1:-1;if(n>i)return a==="desc"?-1:1}return a==="desc"?s-o:o-s}return 0};e.compareValues=r}(Lo)),Lo}var Bo={},Ko={},kf;function dp(){return kf||(kf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return typeof r=="symbol"||r instanceof Symbol}e.isSymbol=t}(Ko)),Ko}var Mf;function _x(){return Mf||(Mf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=dp(),r=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,n=/^\w*$/;function i(a,o){return Array.isArray(a)?!1:typeof a=="number"||typeof a=="boolean"||a==null||t.isSymbol(a)?!0:typeof a=="string"&&(n.test(a)||!r.test(a))||o!=null&&Object.hasOwn(o,a)}e.isKey=i}(Bo)),Bo}var Nf;function Tx(){return Nf||(Nf=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Ex(),r=_x(),n=_l();function i(a,o,s,l){if(a==null)return[];s=l?void 0:s,Array.isArray(a)||(a=Object.values(a)),Array.isArray(o)||(o=o==null?[null]:[o]),o.length===0&&(o=[null]),Array.isArray(s)||(s=s==null?[]:[s]),s=s.map(h=>String(h));const c=(h,p)=>{let m=h;for(let y=0;y<p.length&&m!=null;++y)m=m[p[y]];return m},u=(h,p)=>p==null||h==null?p:typeof h=="object"&&"key"in h?Object.hasOwn(p,h.key)?p[h.key]:c(p,h.path):typeof h=="function"?h(p):Array.isArray(h)?c(p,h):typeof p=="object"?p[h]:p,f=o.map(h=>(Array.isArray(h)&&h.length===1&&(h=h[0]),h==null||typeof h=="function"||Array.isArray(h)||r.isKey(h)?h:{key:h,path:n.toPath(h)}));return a.map(h=>({original:h,criteria:f.map(p=>u(p,h))})).slice().sort((h,p)=>{for(let m=0;m<f.length;m++){const y=t.compareValues(h.criteria[m],p.criteria[m],s[m]);if(y!==0)return y}return 0}).map(h=>h.original)}e.orderBy=i}(Ro)),Ro}var zo={},If;function Cx(){return If||(If=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r,n=1){const i=[],a=Math.floor(n),o=(s,l)=>{for(let c=0;c<s.length;c++){const u=s[c];Array.isArray(u)&&l<a?o(u,l+1):i.push(u)}};return o(r,0),i}e.flatten=t}(zo)),zo}var qo={},Df;function hp(){return Df||(Df=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=up(),r=Bl(),n=ap(),i=Kl();function a(o,s,l){return n.isObject(l)&&(typeof s=="number"&&r.isArrayLike(l)&&t.isIndex(s)&&s<l.length||typeof s=="string"&&s in l)?i.eq(l[s],o):!1}e.isIterateeCall=a}(qo)),qo}var $f;function kx(){return $f||($f=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Tx(),r=Cx(),n=hp();function i(a,...o){const s=o.length;return s>1&&n.isIterateeCall(a,o[0],o[1])?o=[]:s>2&&n.isIterateeCall(o[0],o[1],o[2])&&(o=[o[0]]),t.orderBy(a,r.flatten(o),["asc"])}e.sortBy=i}($o)),$o}var Wo,Rf;function Mx(){return Rf||(Rf=1,Wo=kx().sortBy),Wo}var Nx=Mx();const sa=Lt(Nx);var vp=e=>e.legend.settings,Ix=e=>e.legend.size,Dx=e=>e.legend.payload,$x=j([Dx,vp],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?sa(n,r):n});function Rx(){return D($x)}var ni=1;function pp(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],[t,r]=v.useState({height:0,left:0,top:0,width:0}),n=v.useCallback(i=>{if(i!=null){var a=i.getBoundingClientRect(),o={height:a.height,left:a.left,top:a.top,width:a.width};(Math.abs(o.height-t.height)>ni||Math.abs(o.left-t.left)>ni||Math.abs(o.top-t.top)>ni||Math.abs(o.width-t.width)>ni)&&r({height:o.height,left:o.left,top:o.top,width:o.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,n]}function Ee(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var Lx=typeof Symbol=="function"&&Symbol.observable||"@@observable",Lf=Lx,Fo=()=>Math.random().toString(36).substring(7).split("").join("."),Bx={INIT:`@@redux/INIT${Fo()}`,REPLACE:`@@redux/REPLACE${Fo()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${Fo()}`},Oi=Bx;function Ul(e){if(typeof e!="object"||e===null)return!1;let t=e;for(;Object.getPrototypeOf(t)!==null;)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||Object.getPrototypeOf(e)===null}function mp(e,t,r){if(typeof e!="function")throw new Error(Ee(2));if(typeof t=="function"&&typeof r=="function"||typeof r=="function"&&typeof arguments[3]=="function")throw new Error(Ee(0));if(typeof t=="function"&&typeof r>"u"&&(r=t,t=void 0),typeof r<"u"){if(typeof r!="function")throw new Error(Ee(1));return r(mp)(e,t)}let n=e,i=t,a=new Map,o=a,s=0,l=!1;function c(){o===a&&(o=new Map,a.forEach((y,g)=>{o.set(g,y)}))}function u(){if(l)throw new Error(Ee(3));return i}function f(y){if(typeof y!="function")throw new Error(Ee(4));if(l)throw new Error(Ee(5));let g=!0;c();const x=s++;return o.set(x,y),function(){if(g){if(l)throw new Error(Ee(6));g=!1,c(),o.delete(x),a=null}}}function d(y){if(!Ul(y))throw new Error(Ee(7));if(typeof y.type>"u")throw new Error(Ee(8));if(typeof y.type!="string")throw new Error(Ee(17));if(l)throw new Error(Ee(9));try{l=!0,i=n(i,y)}finally{l=!1}return(a=o).forEach(x=>{x()}),y}function h(y){if(typeof y!="function")throw new Error(Ee(10));n=y,d({type:Oi.REPLACE})}function p(){const y=f;return{subscribe(g){if(typeof g!="object"||g===null)throw new Error(Ee(11));function x(){const P=g;P.next&&P.next(u())}return x(),{unsubscribe:y(x)}},[Lf](){return this}}}return d({type:Oi.INIT}),{dispatch:d,subscribe:f,getState:u,replaceReducer:h,[Lf]:p}}function Kx(e){Object.keys(e).forEach(t=>{const r=e[t];if(typeof r(void 0,{type:Oi.INIT})>"u")throw new Error(Ee(12));if(typeof r(void 0,{type:Oi.PROBE_UNKNOWN_ACTION()})>"u")throw new Error(Ee(13))})}function yp(e){const t=Object.keys(e),r={};for(let a=0;a<t.length;a++){const o=t[a];typeof e[o]=="function"&&(r[o]=e[o])}const n=Object.keys(r);let i;try{Kx(r)}catch(a){i=a}return function(o={},s){if(i)throw i;let l=!1;const c={};for(let u=0;u<n.length;u++){const f=n[u],d=r[f],h=o[f],p=d(h,s);if(typeof p>"u")throw s&&s.type,new Error(Ee(14));c[f]=p,l=l||p!==h}return l=l||n.length!==Object.keys(o).length,l?c:o}}function Ai(...e){return e.length===0?t=>t:e.length===1?e[0]:e.reduce((t,r)=>(...n)=>t(r(...n)))}function zx(...e){return t=>(r,n)=>{const i=t(r,n);let a=()=>{throw new Error(Ee(15))};const o={getState:i.getState,dispatch:(l,...c)=>a(l,...c)},s=e.map(l=>l(o));return a=Ai(...s)(i.dispatch),{...i,dispatch:a}}}function gp(e){return Ul(e)&&"type"in e&&typeof e.type=="string"}var bp=Symbol.for("immer-nothing"),Bf=Symbol.for("immer-draftable"),Ze=Symbol.for("immer-state");function pt(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var Ur=Object.getPrototypeOf;function Pr(e){return!!e&&!!e[Ze]}function Nt(e){var t;return e?xp(e)||Array.isArray(e)||!!e[Bf]||!!((t=e.constructor)!=null&&t[Bf])||ua(e)||ca(e):!1}var qx=Object.prototype.constructor.toString();function xp(e){if(!e||typeof e!="object")return!1;const t=Ur(e);if(t===null)return!0;const r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object?!0:typeof r=="function"&&Function.toString.call(r)===qx}function Si(e,t){la(e)===0?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function la(e){const t=e[Ze];return t?t.type_:Array.isArray(e)?1:ua(e)?2:ca(e)?3:0}function Ls(e,t){return la(e)===2?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function wp(e,t,r){const n=la(e);n===2?e.set(t,r):n===3?e.add(r):e[t]=r}function Wx(e,t){return e===t?e!==0||1/e===1/t:e!==e&&t!==t}function ua(e){return e instanceof Map}function ca(e){return e instanceof Set}function ur(e){return e.copy_||e.base_}function Bs(e,t){if(ua(e))return new Map(e);if(ca(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const r=xp(e);if(t===!0||t==="class_only"&&!r){const n=Object.getOwnPropertyDescriptors(e);delete n[Ze];let i=Reflect.ownKeys(n);for(let a=0;a<i.length;a++){const o=i[a],s=n[o];s.writable===!1&&(s.writable=!0,s.configurable=!0),(s.get||s.set)&&(n[o]={configurable:!0,writable:!0,enumerable:s.enumerable,value:e[o]})}return Object.create(Ur(e),n)}else{const n=Ur(e);if(n!==null&&r)return{...e};const i=Object.create(n);return Object.assign(i,e)}}function Yl(e,t=!1){return fa(e)||Pr(e)||!Nt(e)||(la(e)>1&&(e.set=e.add=e.clear=e.delete=Fx),Object.freeze(e),t&&Object.entries(e).forEach(([r,n])=>Yl(n,!0))),e}function Fx(){pt(2)}function fa(e){return Object.isFrozen(e)}var Ux={};function Or(e){const t=Ux[e];return t||pt(0,e),t}var Sn;function Pp(){return Sn}function Yx(e,t){return{drafts_:[],parent_:e,immer_:t,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function Kf(e,t){t&&(Or("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function Ks(e){zs(e),e.drafts_.forEach(Hx),e.drafts_=null}function zs(e){e===Sn&&(Sn=e.parent_)}function zf(e){return Sn=Yx(Sn,e)}function Hx(e){const t=e[Ze];t.type_===0||t.type_===1?t.revoke_():t.revoked_=!0}function qf(e,t){t.unfinalizedDrafts_=t.drafts_.length;const r=t.drafts_[0];return e!==void 0&&e!==r?(r[Ze].modified_&&(Ks(t),pt(4)),Nt(e)&&(e=ji(t,e),t.parent_||Ei(t,e)),t.patches_&&Or("Patches").generateReplacementPatches_(r[Ze].base_,e,t.patches_,t.inversePatches_)):e=ji(t,r,[]),Ks(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==bp?e:void 0}function ji(e,t,r){if(fa(t))return t;const n=t[Ze];if(!n)return Si(t,(i,a)=>Wf(e,n,t,i,a,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return Ei(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;const i=n.copy_;let a=i,o=!1;n.type_===3&&(a=new Set(i),i.clear(),o=!0),Si(a,(s,l)=>Wf(e,n,i,s,l,r,o)),Ei(e,i,!1),r&&e.patches_&&Or("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function Wf(e,t,r,n,i,a,o){if(Pr(i)){const s=a&&t&&t.type_!==3&&!Ls(t.assigned_,n)?a.concat(n):void 0,l=ji(e,i,s);if(wp(r,n,l),Pr(l))e.canAutoFreeze_=!1;else return}else o&&r.add(i);if(Nt(i)&&!fa(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;ji(e,i),(!t||!t.scope_.parent_)&&typeof n!="symbol"&&Object.prototype.propertyIsEnumerable.call(r,n)&&Ei(e,i)}}function Ei(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&Yl(t,r)}function Gx(e,t){const r=Array.isArray(e),n={type_:r?1:0,scope_:t?t.scope_:Pp(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let i=n,a=Hl;r&&(i=[n],a=jn);const{revoke:o,proxy:s}=Proxy.revocable(i,a);return n.draft_=s,n.revoke_=o,s}var Hl={get(e,t){if(t===Ze)return e;const r=ur(e);if(!Ls(r,t))return Vx(e,r,t);const n=r[t];return e.finalized_||!Nt(n)?n:n===Uo(e.base_,t)?(Yo(e),e.copy_[t]=Ws(n,e)):n},has(e,t){return t in ur(e)},ownKeys(e){return Reflect.ownKeys(ur(e))},set(e,t,r){const n=Op(ur(e),t);if(n!=null&&n.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){const i=Uo(ur(e),t),a=i==null?void 0:i[Ze];if(a&&a.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if(Wx(r,i)&&(r!==void 0||Ls(e.base_,t)))return!0;Yo(e),qs(e)}return e.copy_[t]===r&&(r!==void 0||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=r,e.assigned_[t]=!0),!0},deleteProperty(e,t){return Uo(e.base_,t)!==void 0||t in e.base_?(e.assigned_[t]=!1,Yo(e),qs(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0},getOwnPropertyDescriptor(e,t){const r=ur(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n&&{writable:!0,configurable:e.type_!==1||t!=="length",enumerable:n.enumerable,value:r[t]}},defineProperty(){pt(11)},getPrototypeOf(e){return Ur(e.base_)},setPrototypeOf(){pt(12)}},jn={};Si(Hl,(e,t)=>{jn[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}});jn.deleteProperty=function(e,t){return jn.set.call(this,e,t,void 0)};jn.set=function(e,t,r){return Hl.set.call(this,e[0],t,r,e[0])};function Uo(e,t){const r=e[Ze];return(r?ur(r):e)[t]}function Vx(e,t,r){var i;const n=Op(t,r);return n?"value"in n?n.value:(i=n.get)==null?void 0:i.call(e.draft_):void 0}function Op(e,t){if(!(t in e))return;let r=Ur(e);for(;r;){const n=Object.getOwnPropertyDescriptor(r,t);if(n)return n;r=Ur(r)}}function qs(e){e.modified_||(e.modified_=!0,e.parent_&&qs(e.parent_))}function Yo(e){e.copy_||(e.copy_=Bs(e.base_,e.scope_.immer_.useStrictShallowCopy_))}var Xx=class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(t,r,n)=>{if(typeof t=="function"&&typeof r!="function"){const a=r;r=t;const o=this;return function(l=a,...c){return o.produce(l,u=>r.call(this,u,...c))}}typeof r!="function"&&pt(6),n!==void 0&&typeof n!="function"&&pt(7);let i;if(Nt(t)){const a=zf(this),o=Ws(t,void 0);let s=!0;try{i=r(o),s=!1}finally{s?Ks(a):zs(a)}return Kf(a,n),qf(i,a)}else if(!t||typeof t!="object"){if(i=r(t),i===void 0&&(i=t),i===bp&&(i=void 0),this.autoFreeze_&&Yl(i,!0),n){const a=[],o=[];Or("Patches").generateReplacementPatches_(t,i,a,o),n(a,o)}return i}else pt(1,t)},this.produceWithPatches=(t,r)=>{if(typeof t=="function")return(o,...s)=>this.produceWithPatches(o,l=>t(l,...s));let n,i;return[this.produce(t,r,(o,s)=>{n=o,i=s}),n,i]},typeof(e==null?void 0:e.autoFreeze)=="boolean"&&this.setAutoFreeze(e.autoFreeze),typeof(e==null?void 0:e.useStrictShallowCopy)=="boolean"&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){Nt(e)||pt(8),Pr(e)&&(e=Ct(e));const t=zf(this),r=Ws(e,void 0);return r[Ze].isManual_=!0,zs(t),r}finishDraft(e,t){const r=e&&e[Ze];(!r||!r.isManual_)&&pt(9);const{scope_:n}=r;return Kf(n,t),qf(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){const i=t[r];if(i.path.length===0&&i.op==="replace"){e=i.value;break}}r>-1&&(t=t.slice(r+1));const n=Or("Patches").applyPatches_;return Pr(e)?n(e,t):this.produce(e,i=>n(i,t))}};function Ws(e,t){const r=ua(e)?Or("MapSet").proxyMap_(e,t):ca(e)?Or("MapSet").proxySet_(e,t):Gx(e,t);return(t?t.scope_:Pp()).drafts_.push(r),r}function Ct(e){return Pr(e)||pt(10,e),Ap(e)}function Ap(e){if(!Nt(e)||fa(e))return e;const t=e[Ze];let r;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,r=Bs(e,t.scope_.immer_.useStrictShallowCopy_)}else r=Bs(e,!0);return Si(r,(n,i)=>{wp(r,n,Ap(i))}),t&&(t.finalized_=!1),r}var Qe=new Xx,Sp=Qe.produce;Qe.produceWithPatches.bind(Qe);Qe.setAutoFreeze.bind(Qe);Qe.setUseStrictShallowCopy.bind(Qe);Qe.applyPatches.bind(Qe);Qe.createDraft.bind(Qe);Qe.finishDraft.bind(Qe);function jp(e){return({dispatch:r,getState:n})=>i=>a=>typeof a=="function"?a(r,n,e):i(a)}var Zx=jp(),Qx=jp,Jx=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(arguments.length!==0)return typeof arguments[0]=="object"?Ai:Ai.apply(null,arguments)};function ut(e,t){function r(...n){if(t){let i=t(...n);if(!i)throw new Error(Xe(0));return{type:e,payload:i.payload,..."meta"in i&&{meta:i.meta},..."error"in i&&{error:i.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=n=>gp(n)&&n.type===e,r}var Ep=class gn extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,gn.prototype)}static get[Symbol.species](){return gn}concat(...t){return super.concat.apply(this,t)}prepend(...t){return t.length===1&&Array.isArray(t[0])?new gn(...t[0].concat(this)):new gn(...t.concat(this))}};function Ff(e){return Nt(e)?Sp(e,()=>{}):e}function ii(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}function ew(e){return typeof e=="boolean"}var tw=()=>function(t){const{thunk:r=!0,immutableCheck:n=!0,serializableCheck:i=!0,actionCreatorCheck:a=!0}=t??{};let o=new Ep;return r&&(ew(r)?o.push(Zx):o.push(Qx(r.extraArgument))),o},rw="RTK_autoBatch",Uf=e=>t=>{setTimeout(t,e)},nw=(e={type:"raf"})=>t=>(...r)=>{const n=t(...r);let i=!0,a=!1,o=!1;const s=new Set,l=e.type==="tick"?queueMicrotask:e.type==="raf"?typeof window<"u"&&window.requestAnimationFrame?window.requestAnimationFrame:Uf(10):e.type==="callback"?e.queueNotification:Uf(e.timeout),c=()=>{o=!1,a&&(a=!1,s.forEach(u=>u()))};return Object.assign({},n,{subscribe(u){const f=()=>i&&u(),d=n.subscribe(f);return s.add(u),()=>{d(),s.delete(u)}},dispatch(u){var f;try{return i=!((f=u==null?void 0:u.meta)!=null&&f[rw]),a=!i,a&&(o||(o=!0,l(c))),n.dispatch(u)}finally{i=!0}}})},iw=e=>function(r){const{autoBatch:n=!0}=r??{};let i=new Ep(e);return n&&i.push(nw(typeof n=="object"?n:void 0)),i};function aw(e){const t=tw(),{reducer:r=void 0,middleware:n,devTools:i=!0,preloadedState:a=void 0,enhancers:o=void 0}=e||{};let s;if(typeof r=="function")s=r;else if(Ul(r))s=yp(r);else throw new Error(Xe(1));let l;typeof n=="function"?l=n(t):l=t();let c=Ai;i&&(c=Jx({trace:!1,...typeof i=="object"&&i}));const u=zx(...l),f=iw(u);let d=typeof o=="function"?o(f):f();const h=c(...d);return mp(s,a,h)}function _p(e){const t={},r=[];let n;const i={addCase(a,o){const s=typeof a=="string"?a:a.type;if(!s)throw new Error(Xe(28));if(s in t)throw new Error(Xe(29));return t[s]=o,i},addMatcher(a,o){return r.push({matcher:a,reducer:o}),i},addDefaultCase(a){return n=a,i}};return e(i),[t,r,n]}function ow(e){return typeof e=="function"}function sw(e,t){let[r,n,i]=_p(t),a;if(ow(e))a=()=>Ff(e());else{const s=Ff(e);a=()=>s}function o(s=a(),l){let c=[r[l.type],...n.filter(({matcher:u})=>u(l)).map(({reducer:u})=>u)];return c.filter(u=>!!u).length===0&&(c=[i]),c.reduce((u,f)=>{if(f)if(Pr(u)){const h=f(u,l);return h===void 0?u:h}else{if(Nt(u))return Sp(u,d=>f(d,l));{const d=f(u,l);if(d===void 0){if(u===null)return u;throw Error("A case reducer on a non-draftable value must not return undefined")}return d}}return u},s)}return o.getInitialState=a,o}var lw="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW",uw=(e=21)=>{let t="",r=e;for(;r--;)t+=lw[Math.random()*64|0];return t},cw=Symbol.for("rtk-slice-createasyncthunk");function fw(e,t){return`${e}/${t}`}function dw({creators:e}={}){var r;const t=(r=e==null?void 0:e.asyncThunk)==null?void 0:r[cw];return function(i){const{name:a,reducerPath:o=a}=i;if(!a)throw new Error(Xe(11));const s=(typeof i.reducers=="function"?i.reducers(vw()):i.reducers)||{},l=Object.keys(s),c={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},u={addCase(P,O){const A=typeof P=="string"?P:P.type;if(!A)throw new Error(Xe(12));if(A in c.sliceCaseReducersByType)throw new Error(Xe(13));return c.sliceCaseReducersByType[A]=O,u},addMatcher(P,O){return c.sliceMatchers.push({matcher:P,reducer:O}),u},exposeAction(P,O){return c.actionCreators[P]=O,u},exposeCaseReducer(P,O){return c.sliceCaseReducersByName[P]=O,u}};l.forEach(P=>{const O=s[P],A={reducerName:P,type:fw(a,P),createNotation:typeof i.reducers=="function"};mw(O)?gw(A,O,u,t):pw(A,O,u)});function f(){const[P={},O=[],A=void 0]=typeof i.extraReducers=="function"?_p(i.extraReducers):[i.extraReducers],S={...P,...c.sliceCaseReducersByType};return sw(i.initialState,E=>{for(let _ in S)E.addCase(_,S[_]);for(let _ of c.sliceMatchers)E.addMatcher(_.matcher,_.reducer);for(let _ of O)E.addMatcher(_.matcher,_.reducer);A&&E.addDefaultCase(A)})}const d=P=>P,h=new Map,p=new WeakMap;let m;function y(P,O){return m||(m=f()),m(P,O)}function g(){return m||(m=f()),m.getInitialState()}function x(P,O=!1){function A(E){let _=E[P];return typeof _>"u"&&O&&(_=ii(p,A,g)),_}function S(E=d){const _=ii(h,O,()=>new WeakMap);return ii(_,E,()=>{const M={};for(const[C,k]of Object.entries(i.selectors??{}))M[C]=hw(k,E,()=>ii(p,E,g),O);return M})}return{reducerPath:P,getSelectors:S,get selectors(){return S(A)},selectSlice:A}}const w={name:a,reducer:y,actions:c.actionCreators,caseReducers:c.sliceCaseReducersByName,getInitialState:g,...x(o),injectInto(P,{reducerPath:O,...A}={}){const S=O??o;return P.inject({reducerPath:S,reducer:y},A),{...w,...x(S,!0)}}};return w}}function hw(e,t,r,n){function i(a,...o){let s=t(a);return typeof s>"u"&&n&&(s=r()),e(s,...o)}return i.unwrapped=e,i}var dt=dw();function vw(){function e(t,r){return{_reducerDefinitionType:"asyncThunk",payloadCreator:t,...r}}return e.withTypes=()=>e,{reducer(t){return Object.assign({[t.name](...r){return t(...r)}}[t.name],{_reducerDefinitionType:"reducer"})},preparedReducer(t,r){return{_reducerDefinitionType:"reducerWithPrepare",prepare:t,reducer:r}},asyncThunk:e}}function pw({type:e,reducerName:t,createNotation:r},n,i){let a,o;if("reducer"in n){if(r&&!yw(n))throw new Error(Xe(17));a=n.reducer,o=n.prepare}else a=n;i.addCase(e,a).exposeCaseReducer(t,a).exposeAction(t,o?ut(e,o):ut(e))}function mw(e){return e._reducerDefinitionType==="asyncThunk"}function yw(e){return e._reducerDefinitionType==="reducerWithPrepare"}function gw({type:e,reducerName:t},r,n,i){if(!i)throw new Error(Xe(18));const{payloadCreator:a,fulfilled:o,pending:s,rejected:l,settled:c,options:u}=r,f=i(e,a,u);n.exposeAction(t,f),o&&n.addCase(f.fulfilled,o),s&&n.addCase(f.pending,s),l&&n.addCase(f.rejected,l),c&&n.addMatcher(f.settled,c),n.exposeCaseReducer(t,{fulfilled:o||ai,pending:s||ai,rejected:l||ai,settled:c||ai})}function ai(){}var bw="task",Tp="listener",Cp="completed",Gl="cancelled",xw=`task-${Gl}`,ww=`task-${Cp}`,Fs=`${Tp}-${Gl}`,Pw=`${Tp}-${Cp}`,da=class{constructor(e){Wa(this,"name","TaskAbortError");Wa(this,"message");this.code=e,this.message=`${bw} ${Gl} (reason: ${e})`}},Vl=(e,t)=>{if(typeof e!="function")throw new TypeError(Xe(32))},_i=()=>{},kp=(e,t=_i)=>(e.catch(t),e),Mp=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),yr=(e,t)=>{const r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},gr=e=>{if(e.aborted){const{reason:t}=e;throw new da(t)}};function Np(e,t){let r=_i;return new Promise((n,i)=>{const a=()=>i(new da(e.reason));if(e.aborted){a();return}r=Mp(e,a),t.finally(()=>r()).then(n,i)}).finally(()=>{r=_i})}var Ow=async(e,t)=>{try{return await Promise.resolve(),{status:"ok",value:await e()}}catch(r){return{status:r instanceof da?"cancelled":"rejected",error:r}}finally{t==null||t()}},Ti=e=>t=>kp(Np(e,t).then(r=>(gr(e),r))),Ip=e=>{const t=Ti(e);return r=>t(new Promise(n=>setTimeout(n,r)))},{assign:zr}=Object,Yf={},ha="listenerMiddleware",Aw=(e,t)=>{const r=n=>Mp(e,()=>yr(n,e.reason));return(n,i)=>{Vl(n);const a=new AbortController;r(a);const o=Ow(async()=>{gr(e),gr(a.signal);const s=await n({pause:Ti(a.signal),delay:Ip(a.signal),signal:a.signal});return gr(a.signal),s},()=>yr(a,ww));return i!=null&&i.autoJoin&&t.push(o.catch(_i)),{result:Ti(e)(o),cancel(){yr(a,xw)}}}},Sw=(e,t)=>{const r=async(n,i)=>{gr(t);let a=()=>{};const s=[new Promise((l,c)=>{let u=e({predicate:n,effect:(f,d)=>{d.unsubscribe(),l([f,d.getState(),d.getOriginalState()])}});a=()=>{u(),c()}})];i!=null&&s.push(new Promise(l=>setTimeout(l,i,null)));try{const l=await Np(t,Promise.race(s));return gr(t),l}finally{a()}};return(n,i)=>kp(r(n,i))},Dp=e=>{let{type:t,actionCreator:r,matcher:n,predicate:i,effect:a}=e;if(t)i=ut(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(!i)throw new Error(Xe(21));return Vl(a),{predicate:i,type:t,effect:a}},$p=zr(e=>{const{type:t,predicate:r,effect:n}=Dp(e);return{id:uw(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw new Error(Xe(22))}}},{withTypes:()=>$p}),Hf=(e,t)=>{const{type:r,effect:n,predicate:i}=Dp(t);return Array.from(e.values()).find(a=>(typeof r=="string"?a.type===r:a.predicate===i)&&a.effect===n)},Us=e=>{e.pending.forEach(t=>{yr(t,Fs)})},jw=e=>()=>{e.forEach(Us),e.clear()},Gf=(e,t,r)=>{try{e(t,r)}catch(n){setTimeout(()=>{throw n},0)}},Rp=zr(ut(`${ha}/add`),{withTypes:()=>Rp}),Ew=ut(`${ha}/removeAll`),Lp=zr(ut(`${ha}/remove`),{withTypes:()=>Lp}),_w=(...e)=>{console.error(`${ha}/error`,...e)},Kn=(e={})=>{const t=new Map,{extra:r,onError:n=_w}=e;Vl(n);const i=u=>(u.unsubscribe=()=>t.delete(u.id),t.set(u.id,u),f=>{u.unsubscribe(),f!=null&&f.cancelActive&&Us(u)}),a=u=>{const f=Hf(t,u)??$p(u);return i(f)};zr(a,{withTypes:()=>a});const o=u=>{const f=Hf(t,u);return f&&(f.unsubscribe(),u.cancelActive&&Us(f)),!!f};zr(o,{withTypes:()=>o});const s=async(u,f,d,h)=>{const p=new AbortController,m=Sw(a,p.signal),y=[];try{u.pending.add(p),await Promise.resolve(u.effect(f,zr({},d,{getOriginalState:h,condition:(g,x)=>m(g,x).then(Boolean),take:m,delay:Ip(p.signal),pause:Ti(p.signal),extra:r,signal:p.signal,fork:Aw(p.signal,y),unsubscribe:u.unsubscribe,subscribe:()=>{t.set(u.id,u)},cancelActiveListeners:()=>{u.pending.forEach((g,x,w)=>{g!==p&&(yr(g,Fs),w.delete(g))})},cancel:()=>{yr(p,Fs),u.pending.delete(p)},throwIfCancelled:()=>{gr(p.signal)}})))}catch(g){g instanceof da||Gf(n,g,{raisedBy:"effect"})}finally{await Promise.all(y),yr(p,Pw),u.pending.delete(p)}},l=jw(t);return{middleware:u=>f=>d=>{if(!gp(d))return f(d);if(Rp.match(d))return a(d.payload);if(Ew.match(d)){l();return}if(Lp.match(d))return o(d.payload);let h=u.getState();const p=()=>{if(h===Yf)throw new Error(Xe(23));return h};let m;try{if(m=f(d),t.size>0){const y=u.getState(),g=Array.from(t.values());for(const x of g){let w=!1;try{w=x.predicate(d,y,h)}catch(P){w=!1,Gf(n,P,{raisedBy:"predicate"})}w&&s(x,d,u,p)}}}finally{h=Yf}return m},startListening:a,stopListening:o,clearListeners:l}};function Xe(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var Tw={layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},Bp=dt({name:"chartLayout",initialState:Tw,reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:Cw,setLayout:kw,setChartSize:Mw,setScale:Nw}=Bp.actions,Iw=Bp.reducer;function Vf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Xf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Vf(Object(r),!0).forEach(function(n){Dw(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Vf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Dw(e,t,r){return(t=$w(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $w(e){var t=Rw(e,"string");return typeof t=="symbol"?t:t+""}function Rw(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Ci=Math.PI/180,Lw=e=>e*180/Math.PI,fe=(e,t,r,n)=>({x:e+Math.cos(-Ci*n)*r,y:t+Math.sin(-Ci*n)*r}),Kp=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(n.left||0)-(n.right||0)),Math.abs(r-(n.top||0)-(n.bottom||0)))/2},Bw=(e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)},Kw=(e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,o=Bw({x:r,y:n},{x:i,y:a});if(o<=0)return{radius:o,angle:0};var s=(r-i)/o,l=Math.acos(s);return n>a&&(l=2*Math.PI-l),{radius:o,angle:Lw(l),angleInRadian:l}},zw=e=>{var{startAngle:t,endAngle:r}=e,n=Math.floor(t/360),i=Math.floor(r/360),a=Math.min(n,i);return{startAngle:t-a*360,endAngle:r-a*360}},qw=(e,t)=>{var{startAngle:r,endAngle:n}=t,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return e+o*360},Ww=(e,t)=>{var{x:r,y:n}=e,{radius:i,angle:a}=Kw({x:r,y:n},t),{innerRadius:o,outerRadius:s}=t;if(i<o||i>s||i===0)return null;var{startAngle:l,endAngle:c}=zw(t),u=a,f;if(l<=c){for(;u>c;)u-=360;for(;u<l;)u+=360;f=u>=l&&u<=c}else{for(;u>l;)u-=360;for(;u<c;)u+=360;f=u>=c&&u<=l}return f?Xf(Xf({},t),{},{radius:i,angle:qw(u,t)}):null};function Zf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function st(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zf(Object(r),!0).forEach(function(n){Fw(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Fw(e,t,r){return(t=Uw(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Uw(e){var t=Yw(e,"string");return typeof t=="symbol"?t:t+""}function Yw(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Q(e,t,r){return V(e)||V(t)?r:wt(t)?Zt(e,t,r):typeof t=="function"?t(e):r}var Hw=(e,t,r,n,i)=>{var a,o=-1,s=(a=t==null?void 0:t.length)!==null&&a!==void 0?a:0;if(s<=1||e==null)return 0;if(n==="angleAxis"&&i!=null&&Math.abs(Math.abs(i[1]-i[0])-360)<=1e-6)for(var l=0;l<s;l++){var c=l>0?r[l-1].coordinate:r[s-1].coordinate,u=r[l].coordinate,f=l>=s-1?r[0].coordinate:r[l+1].coordinate,d=void 0;if(Oe(u-c)!==Oe(f-u)){var h=[];if(Oe(f-u)===Oe(i[1]-i[0])){d=f;var p=u+i[1]-i[0];h[0]=Math.min(p,(p+c)/2),h[1]=Math.max(p,(p+c)/2)}else{d=c;var m=f+i[1]-i[0];h[0]=Math.min(u,(m+u)/2),h[1]=Math.max(u,(m+u)/2)}var y=[Math.min(u,(d+u)/2),Math.max(u,(d+u)/2)];if(e>y[0]&&e<=y[1]||e>=h[0]&&e<=h[1]){({index:o}=r[l]);break}}else{var g=Math.min(c,f),x=Math.max(c,f);if(e>(g+u)/2&&e<=(x+u)/2){({index:o}=r[l]);break}}}else if(t){for(var w=0;w<s;w++)if(w===0&&e<=(t[w].coordinate+t[w+1].coordinate)/2||w>0&&w<s-1&&e>(t[w].coordinate+t[w-1].coordinate)/2&&e<=(t[w].coordinate+t[w+1].coordinate)/2||w===s-1&&e>(t[w].coordinate+t[w-1].coordinate)/2){({index:o}=t[w]);break}}return o},Gw=(e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:s}=t;if((s==="vertical"||s==="horizontal"&&o==="middle")&&a!=="center"&&N(e[a]))return st(st({},e),{},{[a]:e[a]+(n||0)});if((s==="horizontal"||s==="vertical"&&a==="center")&&o!=="middle"&&N(e[o]))return st(st({},e),{},{[o]:e[o]+(i||0)})}return e},At=(e,t)=>e==="horizontal"&&t==="xAxis"||e==="vertical"&&t==="yAxis"||e==="centric"&&t==="angleAxis"||e==="radial"&&t==="radiusAxis",zp=(e,t,r,n)=>{if(n)return e.map(s=>s.coordinate);var i,a,o=e.map(s=>(s.coordinate===t&&(i=!0),s.coordinate===r&&(a=!0),s.coordinate));return i||o.push(t),a||o.push(r),o},qp=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:s,isCategorical:l,categoricalDomain:c,tickCount:u,ticks:f,niceTicks:d,axisType:h}=e;if(!o)return null;var p=s==="scaleBand"&&o.bandwidth?o.bandwidth()/2:2,m=i==="category"&&o.bandwidth?o.bandwidth()/p:0;if(m=h==="angleAxis"&&a&&a.length>=2?Oe(a[0]-a[1])*2*m:m,f||d){var y=(f||d||[]).map((g,x)=>{var w=n?n.indexOf(g):g;return{coordinate:o(w)+m,value:g,offset:m,index:x}});return y.filter(g=>!Ke(g.coordinate))}return l&&c?c.map((g,x)=>({coordinate:o(g)+m,value:g,index:x,offset:m})):o.ticks&&u!=null?o.ticks(u).map((g,x)=>({coordinate:o(g)+m,value:g,offset:m,index:x})):o.domain().map((g,x)=>({coordinate:o(g)+m,value:n?n[g]:g,index:x,offset:m}))},Qf=1e-4,Vw=e=>{var t=e.domain();if(!(!t||t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-Qf,a=Math.max(n[0],n[1])+Qf,o=e(t[0]),s=e(t[r-1]);(o<i||o>a||s<i||s>a)&&e.domain([t[0],t[r-1]])}},Xw=(e,t)=>{if(!t||t.length!==2||!N(t[0])||!N(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!N(e[0])||e[0]<r)&&(i[0]=r),(!N(e[1])||e[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},Zw=e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var s=Ke(e[o][r][1])?e[o][r][0]:e[o][r][1];s>=0?(e[o][r][0]=i,e[o][r][1]=i+s,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+s,a=e[o][r][1])}},Qw=e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=Ke(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}},Jw={sign:Zw,expand:kb,none:Fr,silhouette:Mb,wiggle:Nb,positive:Qw},e1=(e,t,r)=>{var n=Jw[r],i=Cb().keys(t).value((a,o)=>+Q(a,o,0)).order(Ds).offset(n);return i(e)};function Xl(e){return e==null?void 0:String(e)}function ki(e){var{axis:t,ticks:r,bandSize:n,entry:i,index:a,dataKey:o}=e;if(t.type==="category"){if(!t.allowDuplicatedCategory&&t.dataKey&&!V(i[t.dataKey])){var s=Lv(r,"value",i[t.dataKey]);if(s)return s.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var l=Q(i,V(o)?t.dataKey:o);return V(l)?null:t.scale(l)}var Jf=e=>{var{axis:t,ticks:r,offset:n,bandSize:i,entry:a,index:o}=e;if(t.type==="category")return r[o]?r[o].coordinate+n:null;var s=Q(a,t.dataKey,t.scale.domain()[o]);return V(s)?null:t.scale(s)-i/2+n},t1=e=>{var{numericAxis:t}=e,r=t.scale.domain();if(t.type==="number"){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},r1=e=>{var t=e.flat(2).filter(N);return[Math.min(...t),Math.max(...t)]},n1=e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]],i1=(e,t,r)=>{if(e!=null)return n1(Object.keys(e).reduce((n,i)=>{var a=e[i],{stackedData:o}=a,s=o.reduce((l,c)=>{var u=r1(c.slice(t,r+1));return[Math.min(l[0],u[0]),Math.max(l[1],u[1])]},[1/0,-1/0]);return[Math.min(s[0],n[0]),Math.max(s[1],n[1])]},[1/0,-1/0]))},ed=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,td=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Ar=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var i=sa(t,u=>u.coordinate),a=1/0,o=1,s=i.length;o<s;o++){var l=i[o],c=i[o-1];a=Math.min((l.coordinate||0)-(c.coordinate||0),a)}return a===1/0?0:a}return r?void 0:0};function rd(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return st(st({},t),{},{dataKey:r,payload:n,value:i,name:a})}function er(e,t){if(e)return String(e);if(typeof t=="string")return t}function a1(e,t,r,n,i){if(r==="horizontal"||r==="vertical"){var a=e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height;return a?{x:e,y:t}:null}return n?Ww({x:e,y:t},n):null}var o1=(e,t,r,n)=>{var i=t.find(c=>c&&c.index===r);if(i){if(e==="horizontal")return{x:i.coordinate,y:n.y};if(e==="vertical")return{x:n.x,y:i.coordinate};if(e==="centric"){var a=i.coordinate,{radius:o}=n;return st(st(st({},n),fe(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var s=i.coordinate,{angle:l}=n;return st(st(st({},n),fe(n.cx,n.cy,s,l)),{},{angle:l,radius:s})}return{x:0,y:0}},s1=(e,t)=>t==="horizontal"?e.x:t==="vertical"?e.y:t==="centric"?e.angle:e.radius,Bt=e=>e.layout.width,Kt=e=>e.layout.height,l1=e=>e.layout.scale,Wp=e=>e.layout.margin,Zl=j(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),Ql=j(e=>e.cartesianAxis.yAxis,e=>Object.values(e)),Fp="data-recharts-item-index",Up="data-recharts-item-data-key",va=60;function nd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ft(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?nd(Object(r),!0).forEach(function(n){u1(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function u1(e,t,r){return(t=c1(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function c1(e){var t=f1(e,"string");return typeof t=="symbol"?t:t+""}function f1(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var d1=e=>e.brush.height,pe=j([Bt,Kt,Wp,d1,Zl,Ql,vp,Ix],(e,t,r,n,i,a,o,s)=>{var l=a.reduce((p,m)=>{var{orientation:y}=m;if(!m.mirror&&!m.hide){var g=typeof m.width=="number"?m.width:va;return Ft(Ft({},p),{},{[y]:p[y]+g})}return p},{left:r.left||0,right:r.right||0}),c=i.reduce((p,m)=>{var{orientation:y}=m;return!m.mirror&&!m.hide?Ft(Ft({},p),{},{[y]:Zt(p,"".concat(y))+m.height}):p},{top:r.top||0,bottom:r.bottom||0}),u=Ft(Ft({},c),l),f=u.bottom;u.bottom+=n,u=Gw(u,o,s);var d=e-u.left-u.right,h=t-u.top-u.bottom;return Ft(Ft({brushBottom:f},u),{},{width:Math.max(d,0),height:Math.max(h,0)})}),h1=j(pe,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),Yp=j(Bt,Kt,(e,t)=>({x:0,y:0,width:e,height:t})),v1=v.createContext(null),Ie=()=>v.useContext(v1)!=null,pa=e=>e.brush,ma=j([pa,pe,Wp],(e,t,r)=>({height:e.height,x:N(e.x)?e.x:t.left,y:N(e.y)?e.y:t.top+t.height+t.brushBottom-((r==null?void 0:r.bottom)||0),width:N(e.width)?e.width:t.width})),Jl=()=>{var e,t=Ie(),r=D(h1),n=D(ma),i=(e=D(pa))===null||e===void 0?void 0:e.padding;return!t||!n||!i?r:{width:n.width-i.left-i.right,height:n.height-i.top-i.bottom,x:i.left,y:i.top}},p1={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},Hp=()=>{var e;return(e=D(pe))!==null&&e!==void 0?e:p1},eu=()=>D(Bt),tu=()=>D(Kt),m1={top:0,right:0,bottom:0,left:0},y1=()=>{var e;return(e=D(t=>t.layout.margin))!==null&&e!==void 0?e:m1},F=e=>e.layout.layoutType,zn=()=>D(F),g1={settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},Gp=dt({name:"legend",initialState:g1,reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push(t.payload)},removeLegendPayload(e,t){var r=Ct(e).payload.indexOf(t.payload);r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:id,setLegendSettings:b1,addLegendPayload:Vp,removeLegendPayload:Xp}=Gp.actions,x1=Gp.reducer,w1=["contextPayload"];function Ys(){return Ys=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ys.apply(null,arguments)}function ad(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Yr(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ad(Object(r),!0).forEach(function(n){ru(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ad(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ru(e,t,r){return(t=P1(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function P1(e){var t=O1(e,"string");return typeof t=="symbol"?t:t+""}function O1(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function A1(e,t){if(e==null)return{};var r,n,i=S1(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function S1(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function j1(e){return e.value}function E1(e){var{contextPayload:t}=e,r=A1(e,w1),n=cp(t,e.payloadUniqBy,j1),i=Yr(Yr({},r),{},{payload:n});return v.isValidElement(e.content)?v.cloneElement(e.content,i):typeof e.content=="function"?v.createElement(e.content,i):v.createElement(Ll,i)}function _1(e,t,r,n,i,a){var{layout:o,align:s,verticalAlign:l}=t,c,u;return(!e||(e.left===void 0||e.left===null)&&(e.right===void 0||e.right===null))&&(s==="center"&&o==="vertical"?c={left:((n||0)-a.width)/2}:c=s==="right"?{right:r&&r.right||0}:{left:r&&r.left||0}),(!e||(e.top===void 0||e.top===null)&&(e.bottom===void 0||e.bottom===null))&&(l==="middle"?u={top:((i||0)-a.height)/2}:u=l==="bottom"?{bottom:r&&r.bottom||0}:{top:r&&r.top||0}),Yr(Yr({},c),u)}function T1(e){var t=ie();return v.useEffect(()=>{t(b1(e))},[t,e]),null}function C1(e){var t=ie();return v.useEffect(()=>(t(id(e)),()=>{t(id({width:0,height:0}))}),[t,e]),null}function k1(e){var t=Rx(),r=eb(),n=y1(),{width:i,height:a,wrapperStyle:o,portal:s}=e,[l,c]=pp([t]),u=eu(),f=tu(),d=u-(n.left||0)-(n.right||0),h=St.getWidthOrHeight(e.layout,a,i,d),p=s?o:Yr(Yr({position:"absolute",width:(h==null?void 0:h.width)||i||"auto",height:(h==null?void 0:h.height)||a||"auto"},_1(o,e,n,u,f,l)),o),m=s??r;if(m==null)return null;var y=v.createElement("div",{className:"recharts-legend-wrapper",style:p,ref:c},v.createElement(T1,{layout:e.layout,align:e.align,verticalAlign:e.verticalAlign,itemSorter:e.itemSorter}),v.createElement(C1,{width:l.width,height:l.height}),v.createElement(E1,Ys({},e,h,{margin:n,chartWidth:u,chartHeight:f,contextPayload:t})));return Iv.createPortal(y,m)}class St extends v.PureComponent{static getWidthOrHeight(t,r,n,i){return t==="vertical"&&N(r)?{height:r}:t==="horizontal"?{width:n||i}:null}render(){return v.createElement(k1,this.props)}}ru(St,"displayName","Legend");ru(St,"defaultProps",{align:"center",iconSize:14,itemSorter:"value",layout:"horizontal",verticalAlign:"bottom"});function Hs(){return Hs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Hs.apply(null,arguments)}function od(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ho(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?od(Object(r),!0).forEach(function(n){M1(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):od(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function M1(e,t,r){return(t=N1(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function N1(e){var t=I1(e,"string");return typeof t=="symbol"?t:t+""}function I1(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function D1(e){return Array.isArray(e)&&wt(e[0])&&wt(e[1])?e.join(" ~ "):e}var $1=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:n={},labelStyle:i={},payload:a,formatter:o,itemSorter:s,wrapperClassName:l,labelClassName:c,label:u,labelFormatter:f,accessibilityLayer:d=!1}=e,h=()=>{if(a&&a.length){var O={padding:0,margin:0},A=(s?sa(a,s):a).map((S,E)=>{if(S.type==="none")return null;var _=S.formatter||o||D1,{value:M,name:C}=S,k=M,R=C;if(_){var B=_(M,C,S,E,a);if(Array.isArray(B))[k,R]=B;else if(B!=null)k=B;else return null}var U=Ho({display:"block",paddingTop:4,paddingBottom:4,color:S.color||"#000"},n);return v.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(E),style:U},wt(R)?v.createElement("span",{className:"recharts-tooltip-item-name"},R):null,wt(R)?v.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,v.createElement("span",{className:"recharts-tooltip-item-value"},k),v.createElement("span",{className:"recharts-tooltip-item-unit"},S.unit||""))});return v.createElement("ul",{className:"recharts-tooltip-item-list",style:O},A)}return null},p=Ho({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),m=Ho({margin:0},i),y=!V(u),g=y?u:"",x=W("recharts-default-tooltip",l),w=W("recharts-tooltip-label",c);y&&f&&a!==void 0&&a!==null&&(g=f(u,a));var P=d?{role:"status","aria-live":"assertive"}:{};return v.createElement("div",Hs({className:x,style:p},P),v.createElement("p",{className:w,style:m},v.isValidElement(g)?g:"".concat(g)),h())},cn="recharts-tooltip-wrapper",R1={visibility:"hidden"};function L1(e){var{coordinate:t,translateX:r,translateY:n}=e;return W(cn,{["".concat(cn,"-right")]:N(r)&&t&&N(t.x)&&r>=t.x,["".concat(cn,"-left")]:N(r)&&t&&N(t.x)&&r<t.x,["".concat(cn,"-bottom")]:N(n)&&t&&N(t.y)&&n>=t.y,["".concat(cn,"-top")]:N(n)&&t&&N(t.y)&&n<t.y})}function sd(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:s,viewBox:l,viewBoxDimension:c}=e;if(a&&N(a[n]))return a[n];var u=r[n]-s-(i>0?i:0),f=r[n]+i;if(t[n])return o[n]?u:f;var d=l[n];if(d==null)return 0;if(o[n]){var h=u,p=d;return h<p?Math.max(f,d):Math.max(u,d)}if(c==null)return 0;var m=f+s,y=d+c;return m>y?Math.max(u,d):Math.max(f,d)}function B1(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}function K1(e){var{allowEscapeViewBox:t,coordinate:r,offsetTopLeft:n,position:i,reverseDirection:a,tooltipBox:o,useTranslate3d:s,viewBox:l}=e,c,u,f;return o.height>0&&o.width>0&&r?(u=sd({allowEscapeViewBox:t,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:l,viewBoxDimension:l.width}),f=sd({allowEscapeViewBox:t,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:l,viewBoxDimension:l.height}),c=B1({translateX:u,translateY:f,useTranslate3d:s})):c=R1,{cssProperties:c,cssClasses:L1({translateX:u,translateY:f,coordinate:r})}}function ld(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function oi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ld(Object(r),!0).forEach(function(n){Gs(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ld(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Gs(e,t,r){return(t=z1(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function z1(e){var t=q1(e,"string");return typeof t=="symbol"?t:t+""}function q1(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}class W1 extends v.PureComponent{constructor(){super(...arguments),Gs(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),Gs(this,"handleKeyDown",t=>{if(t.key==="Escape"){var r,n,i,a;this.setState({dismissed:!0,dismissedAtCoordinate:{x:(r=(n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==null&&r!==void 0?r:0,y:(i=(a=this.props.coordinate)===null||a===void 0?void 0:a.y)!==null&&i!==void 0?i:0}})}})}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var t,r;this.state.dismissed&&(((t=this.props.coordinate)===null||t===void 0?void 0:t.x)!==this.state.dismissedAtCoordinate.x||((r=this.props.coordinate)===null||r===void 0?void 0:r.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:t,allowEscapeViewBox:r,animationDuration:n,animationEasing:i,children:a,coordinate:o,hasPayload:s,isAnimationActive:l,offset:c,position:u,reverseDirection:f,useTranslate3d:d,viewBox:h,wrapperStyle:p,lastBoundingBox:m,innerRef:y,hasPortalFromProps:g}=this.props,{cssClasses:x,cssProperties:w}=K1({allowEscapeViewBox:r,coordinate:o,offsetTopLeft:c,position:u,reverseDirection:f,tooltipBox:{height:m.height,width:m.width},useTranslate3d:d,viewBox:h}),P=g?{}:oi(oi({transition:l&&t?"transform ".concat(n,"ms ").concat(i):void 0},w),{},{pointerEvents:"none",visibility:!this.state.dismissed&&t&&s?"visible":"hidden",position:"absolute",top:0,left:0}),O=oi(oi({},P),{},{visibility:!this.state.dismissed&&t&&s?"visible":"hidden"},p);return v.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:x,style:O,ref:y},a)}}var F1=()=>!(typeof window<"u"&&window.document&&window.document.createElement&&window.setTimeout),tr={isSsr:F1()},Zp=()=>D(e=>e.rootProps.accessibilityLayer);function Te(e){return Number.isFinite(e)}function Hr(e){return typeof e=="number"&&e>0&&Number.isFinite(e)}function Vs(){return Vs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Vs.apply(null,arguments)}function ud(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function cd(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ud(Object(r),!0).forEach(function(n){U1(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ud(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function U1(e,t,r){return(t=Y1(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Y1(e){var t=H1(e,"string");return typeof t=="symbol"?t:t+""}function H1(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var fd={curveBasisClosed:bb,curveBasisOpen:xb,curveBasis:gb,curveBumpX:ib,curveBumpY:ab,curveLinearClosed:wb,curveLinear:aa,curveMonotoneX:Pb,curveMonotoneY:Ob,curveNatural:Ab,curveStep:Sb,curveStepAfter:Eb,curveStepBefore:jb},si=e=>Te(e.x)&&Te(e.y),fn=e=>e.x,dn=e=>e.y,G1=(e,t)=>{if(typeof e=="function")return e;var r="curve".concat(Rn(e));return(r==="curveMonotone"||r==="curveBump")&&t?fd["".concat(r).concat(t==="vertical"?"Y":"X")]:fd[r]||aa},V1=e=>{var{type:t="linear",points:r=[],baseLine:n,layout:i,connectNulls:a=!1}=e,o=G1(t,i),s=a?r.filter(si):r,l;if(Array.isArray(n)){var c=a?n.filter(f=>si(f)):n,u=s.map((f,d)=>cd(cd({},f),{},{base:c[d]}));return i==="vertical"?l=ti().y(dn).x1(fn).x0(f=>f.base.x):l=ti().x(fn).y1(dn).y0(f=>f.base.y),l.defined(si).curve(o),l(u)}return i==="vertical"&&N(n)?l=ti().y(dn).x1(fn).x0(n):N(n)?l=ti().x(fn).y1(dn).y0(n):l=Yv().x(fn).y(dn),l.defined(si).curve(o),l(s)},qr=e=>{var{className:t,points:r,path:n,pathRef:i}=e;if((!r||!r.length)&&!n)return null;var a=r&&r.length?V1(e):n;return v.createElement("path",Vs({},z(e,!1),kl(e),{className:W("recharts-curve",t),d:a===null?void 0:a,ref:i}))},X1=["x","y","top","left","width","height","className"];function Xs(){return Xs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Xs.apply(null,arguments)}function dd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Z1(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?dd(Object(r),!0).forEach(function(n){Q1(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Q1(e,t,r){return(t=J1(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function J1(e){var t=eP(e,"string");return typeof t=="symbol"?t:t+""}function eP(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function tP(e,t){if(e==null)return{};var r,n,i=rP(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function rP(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var nP=(e,t,r,n,i,a)=>"M".concat(e,",").concat(i,"v").concat(n,"M").concat(a,",").concat(t,"h").concat(r),iP=e=>{var{x:t=0,y:r=0,top:n=0,left:i=0,width:a=0,height:o=0,className:s}=e,l=tP(e,X1),c=Z1({x:t,y:r,top:n,left:i,width:a,height:o},l);return!N(t)||!N(r)||!N(a)||!N(o)||!N(n)||!N(i)?null:v.createElement("path",Xs({},z(c,!0),{className:W("recharts-cross",s),d:nP(t,r,a,o,n,i)}))};function aP(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:e==="horizontal"?t.x-i:r.left+.5,y:e==="horizontal"?r.top+.5:t.y-i,width:e==="horizontal"?n:r.width-1,height:e==="horizontal"?r.height-1:n}}function hd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function oP(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?hd(Object(r),!0).forEach(function(n){sP(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function sP(e,t,r){return(t=lP(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function lP(e){var t=uP(e,"string");return typeof t=="symbol"?t:t+""}function uP(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Je(e,t){var r=oP({},e),n=t,i=Object.keys(t),a=i.reduce((o,s)=>(o[s]===void 0&&n[s]!==void 0&&(o[s]=n[s]),o),r);return a}var Go={},Vo={},Xo={},vd;function cP(){return vd||(vd=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){if(!r||typeof r!="object")return!1;const n=Object.getPrototypeOf(r);return n===null||n===Object.prototype||Object.getPrototypeOf(n)===null?Object.prototype.toString.call(r)==="[object Object]":!1}e.isPlainObject=t}(Xo)),Xo}var pd;function fP(){return pd||(pd=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=cP(),r=sp(),n=ql(),i=Wl(),a=Kl();function o(c,u,f){return s(c,u,void 0,void 0,void 0,void 0,f)}function s(c,u,f,d,h,p,m){const y=m(c,u,f,d,h,p);if(y!==void 0)return y;if(typeof c==typeof u)switch(typeof c){case"bigint":case"string":case"boolean":case"symbol":case"undefined":return c===u;case"number":return c===u||Object.is(c,u);case"function":return c===u;case"object":return l(c,u,p,m)}return l(c,u,p,m)}function l(c,u,f,d){if(Object.is(c,u))return!0;let h=n.getTag(c),p=n.getTag(u);if(h===i.argumentsTag&&(h=i.objectTag),p===i.argumentsTag&&(p=i.objectTag),h!==p)return!1;switch(h){case i.stringTag:return c.toString()===u.toString();case i.numberTag:{const g=c.valueOf(),x=u.valueOf();return a.eq(g,x)}case i.booleanTag:case i.dateTag:case i.symbolTag:return Object.is(c.valueOf(),u.valueOf());case i.regexpTag:return c.source===u.source&&c.flags===u.flags;case i.functionTag:return c===u}f=f??new Map;const m=f.get(c),y=f.get(u);if(m!=null&&y!=null)return m===u;f.set(c,u),f.set(u,c);try{switch(h){case i.mapTag:{if(c.size!==u.size)return!1;for(const[g,x]of c.entries())if(!u.has(g)||!s(x,u.get(g),g,c,u,f,d))return!1;return!0}case i.setTag:{if(c.size!==u.size)return!1;const g=Array.from(c.values()),x=Array.from(u.values());for(let w=0;w<g.length;w++){const P=g[w],O=x.findIndex(A=>s(P,A,void 0,c,u,f,d));if(O===-1)return!1;x.splice(O,1)}return!0}case i.arrayTag:case i.uint8ArrayTag:case i.uint8ClampedArrayTag:case i.uint16ArrayTag:case i.uint32ArrayTag:case i.bigUint64ArrayTag:case i.int8ArrayTag:case i.int16ArrayTag:case i.int32ArrayTag:case i.bigInt64ArrayTag:case i.float32ArrayTag:case i.float64ArrayTag:{if(typeof Buffer<"u"&&Buffer.isBuffer(c)!==Buffer.isBuffer(u)||c.length!==u.length)return!1;for(let g=0;g<c.length;g++)if(!s(c[g],u[g],g,c,u,f,d))return!1;return!0}case i.arrayBufferTag:return c.byteLength!==u.byteLength?!1:l(new Uint8Array(c),new Uint8Array(u),f,d);case i.dataViewTag:return c.byteLength!==u.byteLength||c.byteOffset!==u.byteOffset?!1:l(new Uint8Array(c),new Uint8Array(u),f,d);case i.errorTag:return c.name===u.name&&c.message===u.message;case i.objectTag:{if(!(l(c.constructor,u.constructor,f,d)||t.isPlainObject(c)&&t.isPlainObject(u)))return!1;const x=[...Object.keys(c),...r.getSymbols(c)],w=[...Object.keys(u),...r.getSymbols(u)];if(x.length!==w.length)return!1;for(let P=0;P<x.length;P++){const O=x[P],A=c[O];if(!Object.hasOwn(u,O))return!1;const S=u[O];if(!s(A,S,O,c,u,f,d))return!1}return!0}default:return!1}}finally{f.delete(c),f.delete(u)}}e.isEqualWith=o}(Vo)),Vo}var Zo={},md;function dP(){return md||(md=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(){}e.noop=t}(Zo)),Zo}var yd;function hP(){return yd||(yd=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=fP(),r=dP();function n(i,a){return t.isEqualWith(i,a,r.noop)}e.isEqual=n}(Go)),Go}var Qo,gd;function vP(){return gd||(gd=1,Qo=hP().isEqual),Qo}var pP=vP();const mP=Lt(pP);function yP(e){var t={},r=()=>null,n=!1,i=null,a=o=>{if(!n){if(Array.isArray(o)){if(!o.length)return;var s=o,[l,...c]=s;if(typeof l=="number"){i=e.setTimeout(a.bind(null,c),l);return}a(l),i=e.setTimeout(a.bind(null,c));return}typeof o=="object"&&(t=o,r(t)),typeof o=="function"&&o()}};return{stop:()=>{n=!0},start:o=>{n=!1,i&&(i(),i=null),a(o)},subscribe:o=>(r=o,()=>{r=()=>null}),getTimeoutController:()=>e}}var Mi=1e-4,Qp=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],Jp=(e,t)=>e.map((r,n)=>r*t**n).reduce((r,n)=>r+n),bd=(e,t)=>r=>{var n=Qp(e,t);return Jp(n,r)},gP=(e,t)=>r=>{var n=Qp(e,t),i=[...n.map((a,o)=>a*o).slice(1),0];return Jp(i,r)},xd=function(){for(var t,r,n,i,a=arguments.length,o=new Array(a),s=0;s<a;s++)o[s]=arguments[s];if(o.length===1)switch(o[0]){case"linear":[t,n,r,i]=[0,0,1,1];break;case"ease":[t,n,r,i]=[.25,.1,.25,1];break;case"ease-in":[t,n,r,i]=[.42,0,1,1];break;case"ease-out":[t,n,r,i]=[.42,0,.58,1];break;case"ease-in-out":[t,n,r,i]=[0,0,.58,1];break;default:{var l=o[0].split("(");l[0]==="cubic-bezier"&&l[1].split(")")[0].split(",").length===4&&([t,n,r,i]=l[1].split(")")[0].split(",").map(p=>parseFloat(p)))}}else o.length===4&&([t,n,r,i]=o);var c=bd(t,r),u=bd(n,i),f=gP(t,r),d=p=>p>1?1:p<0?0:p,h=p=>{for(var m=p>1?1:p,y=m,g=0;g<8;++g){var x=c(y)-m,w=f(y);if(Math.abs(x-m)<Mi||w<Mi)return u(y);y=d(y-x/w)}return u(y)};return h.isStepper=!1,h},bP=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},{stiff:r=100,damping:n=8,dt:i=17}=t,a=(o,s,l)=>{var c=-(o-s)*r,u=l*n,f=l+(c-u)*i/1e3,d=l*i/1e3+o;return Math.abs(d-s)<Mi&&Math.abs(f)<Mi?[s,0]:[d,f]};return a.isStepper=!0,a.dt=i,a},xP=e=>{if(typeof e=="string")switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return xd(e);case"spring":return bP();default:if(e.split("(")[0]==="cubic-bezier")return xd(e)}return typeof e=="function"?e:null};function wd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Pd(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?wd(Object(r),!0).forEach(function(n){wP(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function wP(e,t,r){return(t=PP(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function PP(e){var t=OP(e,"string");return typeof t=="symbol"?t:t+""}function OP(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var AP=e=>e.replace(/([A-Z])/g,t=>"-".concat(t.toLowerCase())),SP=(e,t,r)=>e.map(n=>"".concat(AP(n)," ").concat(t,"ms ").concat(r)).join(","),jP=(e,t)=>[Object.keys(e),Object.keys(t)].reduce((r,n)=>r.filter(i=>n.includes(i))),En=(e,t)=>Object.keys(t).reduce((r,n)=>Pd(Pd({},r),{},{[n]:e(n,t[n])}),{});function Od(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function _e(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Od(Object(r),!0).forEach(function(n){EP(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Od(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function EP(e,t,r){return(t=_P(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _P(e){var t=TP(e,"string");return typeof t=="symbol"?t:t+""}function TP(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Ni=(e,t,r)=>e+(t-e)*r,Zs=e=>{var{from:t,to:r}=e;return t!==r},em=(e,t,r)=>{var n=En((i,a)=>{if(Zs(a)){var[o,s]=e(a.from,a.to,a.velocity);return _e(_e({},a),{},{from:o,velocity:s})}return a},t);return r<1?En((i,a)=>Zs(a)?_e(_e({},a),{},{velocity:Ni(a.velocity,n[i].velocity,r),from:Ni(a.from,n[i].from,r)}):a,t):em(e,n,r-1)};function CP(e,t,r,n,i,a){var o,s=n.reduce((d,h)=>_e(_e({},d),{},{[h]:{from:e[h],velocity:0,to:t[h]}}),{}),l=()=>En((d,h)=>h.from,s),c=()=>!Object.values(s).filter(Zs).length,u=null,f=d=>{o||(o=d);var h=d-o,p=h/r.dt;s=em(r,s,p),i(_e(_e(_e({},e),t),l())),o=d,c()||(u=a.setTimeout(f))};return()=>(u=a.setTimeout(f),()=>{u()})}function kP(e,t,r,n,i,a,o){var s=null,l=i.reduce((f,d)=>_e(_e({},f),{},{[d]:[e[d],t[d]]}),{}),c,u=f=>{c||(c=f);var d=(f-c)/n,h=En((m,y)=>Ni(...y,r(d)),l);if(a(_e(_e(_e({},e),t),h)),d<1)s=o.setTimeout(u);else{var p=En((m,y)=>Ni(...y,r(1)),l);a(_e(_e(_e({},e),t),p))}};return()=>(s=o.setTimeout(u),()=>{s()})}const MP=(e,t,r,n,i,a)=>{var o=jP(e,t);return r.isStepper===!0?CP(e,t,r,o,i,a):kP(e,t,r,n,o,i,a)};class NP{setTimeout(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=performance.now(),i=null,a=o=>{o-n>=r?t(o):typeof requestAnimationFrame=="function"&&(i=requestAnimationFrame(a))};return i=requestAnimationFrame(a),()=>{cancelAnimationFrame(i)}}}var IP=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function Qs(){return Qs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Qs.apply(null,arguments)}function DP(e,t){if(e==null)return{};var r,n,i=$P(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function $P(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function Ad(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ut(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ad(Object(r),!0).forEach(function(n){dr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ad(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function dr(e,t,r){return(t=RP(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function RP(e){var t=LP(e,"string");return typeof t=="symbol"?t:t+""}function LP(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function BP(){return yP(new NP)}class nu extends v.PureComponent{constructor(t,r){super(t,r),dr(this,"mounted",!1),dr(this,"manager",null),dr(this,"stopJSAnimation",null),dr(this,"unSubscribe",null);var{isActive:n,attributeName:i,from:a,to:o,children:s,duration:l,animationManager:c}=this.props;if(this.manager=c,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!n||l<=0){this.state={style:{}},typeof s=="function"&&(this.state={style:o});return}if(a){if(typeof s=="function"){this.state={style:a};return}this.state={style:i?{[i]:a}:a}}else this.state={style:{}}}componentDidMount(){var{isActive:t,canBegin:r}=this.props;this.mounted=!0,!(!t||!r)&&this.runAnimation(this.props)}componentDidUpdate(t){var{isActive:r,canBegin:n,attributeName:i,shouldReAnimate:a,to:o,from:s}=this.props,{style:l}=this.state;if(n){if(!r){var c={style:i?{[i]:o}:o};this.state&&l&&(i&&l[i]!==o||!i&&l!==o)&&this.setState(c);return}if(!(mP(t.to,o)&&t.canBegin&&t.isActive)){var u=!t.canBegin||!t.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=u||a?s:t.to;if(this.state&&l){var d={style:i?{[i]:f}:f};(i&&l[i]!==f||!i&&l!==f)&&this.setState(d)}this.runAnimation(Ut(Ut({},this.props),{},{from:f,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:t}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}handleStyleChange(t){this.changeStyle(t)}changeStyle(t){this.mounted&&this.setState({style:t})}runJSAnimation(t){var{from:r,to:n,duration:i,easing:a,begin:o,onAnimationEnd:s,onAnimationStart:l}=t,c=MP(r,n,xP(a),i,this.changeStyle,this.manager.getTimeoutController()),u=()=>{this.stopJSAnimation=c()};this.manager.start([l,o,u,i,s])}runAnimation(t){var{begin:r,duration:n,attributeName:i,to:a,easing:o,onAnimationStart:s,onAnimationEnd:l,children:c}=t;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),typeof o=="function"||typeof c=="function"||o==="spring"){this.runJSAnimation(t);return}var u=i?{[i]:a}:a,f=SP(Object.keys(u),n,o);this.manager.start([s,r,Ut(Ut({},u),{},{transition:f}),n,l])}render(){var t=this.props,{children:r,begin:n,duration:i,attributeName:a,easing:o,isActive:s,from:l,to:c,canBegin:u,onAnimationEnd:f,shouldReAnimate:d,onAnimationReStart:h,animationManager:p}=t,m=DP(t,IP),y=v.Children.count(r),g=this.state.style;if(typeof r=="function")return r(g);if(!s||y===0||i<=0)return r;var x=w=>{var{style:P={},className:O}=w.props,A=v.cloneElement(w,Ut(Ut({},m),{},{style:Ut(Ut({},P),g),className:O}));return A};return y===1?x(v.Children.only(r)):v.createElement("div",null,v.Children.map(r,w=>x(w)))}}dr(nu,"displayName","Animate");dr(nu,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var KP=v.createContext(null);function It(e){var t,r,n=v.useContext(KP);return v.createElement(nu,Qs({},e,{animationManager:(t=(r=e.animationManager)!==null&&r!==void 0?r:n)!==null&&t!==void 0?t:BP()}))}function Ii(){return Ii=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ii.apply(null,arguments)}var Sd=(e,t,r,n,i)=>{var a=Math.min(Math.abs(r)/2,Math.abs(n)/2),o=n>=0?1:-1,s=r>=0?1:-1,l=n>=0&&r>=0||n<0&&r<0?1:0,c;if(a>0&&i instanceof Array){for(var u=[0,0,0,0],f=0,d=4;f<d;f++)u[f]=i[f]>a?a:i[f];c="M".concat(e,",").concat(t+o*u[0]),u[0]>0&&(c+="A ".concat(u[0],",").concat(u[0],",0,0,").concat(l,",").concat(e+s*u[0],",").concat(t)),c+="L ".concat(e+r-s*u[1],",").concat(t),u[1]>0&&(c+="A ".concat(u[1],",").concat(u[1],",0,0,").concat(l,`,
        `).concat(e+r,",").concat(t+o*u[1])),c+="L ".concat(e+r,",").concat(t+n-o*u[2]),u[2]>0&&(c+="A ".concat(u[2],",").concat(u[2],",0,0,").concat(l,`,
        `).concat(e+r-s*u[2],",").concat(t+n)),c+="L ".concat(e+s*u[3],",").concat(t+n),u[3]>0&&(c+="A ".concat(u[3],",").concat(u[3],",0,0,").concat(l,`,
        `).concat(e,",").concat(t+n-o*u[3])),c+="Z"}else if(a>0&&i===+i&&i>0){var h=Math.min(a,i);c="M ".concat(e,",").concat(t+o*h,`
            A `).concat(h,",").concat(h,",0,0,").concat(l,",").concat(e+s*h,",").concat(t,`
            L `).concat(e+r-s*h,",").concat(t,`
            A `).concat(h,",").concat(h,",0,0,").concat(l,",").concat(e+r,",").concat(t+o*h,`
            L `).concat(e+r,",").concat(t+n-o*h,`
            A `).concat(h,",").concat(h,",0,0,").concat(l,",").concat(e+r-s*h,",").concat(t+n,`
            L `).concat(e+s*h,",").concat(t+n,`
            A `).concat(h,",").concat(h,",0,0,").concat(l,",").concat(e,",").concat(t+n-o*h," Z")}else c="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return c},zP={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},tm=e=>{var t=Je(e,zP),r=v.useRef(null),[n,i]=v.useState(-1);v.useEffect(()=>{if(r.current&&r.current.getTotalLength)try{var g=r.current.getTotalLength();g&&i(g)}catch{}},[]);var{x:a,y:o,width:s,height:l,radius:c,className:u}=t,{animationEasing:f,animationDuration:d,animationBegin:h,isAnimationActive:p,isUpdateAnimationActive:m}=t;if(a!==+a||o!==+o||s!==+s||l!==+l||s===0||l===0)return null;var y=W("recharts-rectangle",u);return m?v.createElement(It,{canBegin:n>0,from:{width:s,height:l,x:a,y:o},to:{width:s,height:l,x:a,y:o},duration:d,animationEasing:f,isActive:m},g=>{var{width:x,height:w,x:P,y:O}=g;return v.createElement(It,{canBegin:n>0,from:"0px ".concat(n===-1?1:n,"px"),to:"".concat(n,"px 0px"),attributeName:"strokeDasharray",begin:h,duration:d,isActive:p,easing:f},v.createElement("path",Ii({},z(t,!0),{className:y,d:Sd(P,O,x,w,c),ref:r})))}):v.createElement("path",Ii({},z(t,!0),{className:y,d:Sd(a,o,s,l,c)}))};function rm(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:a}=e,o=fe(t,r,n,i),s=fe(t,r,n,a);return{points:[o,s],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function Js(){return Js=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Js.apply(null,arguments)}var qP=(e,t)=>{var r=Oe(t-e),n=Math.min(Math.abs(t-e),359.999);return r*n},li=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:o,cornerRadius:s,cornerIsExternal:l}=e,c=s*(o?1:-1)+n,u=Math.asin(s/c)/Ci,f=l?i:i+a*u,d=fe(t,r,c,f),h=fe(t,r,n,f),p=l?i-a*u:i,m=fe(t,r,c*Math.cos(u*Ci),p);return{center:d,circleTangency:h,lineTangency:m,theta:u}},nm=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:o}=e,s=qP(a,o),l=a+s,c=fe(t,r,i,a),u=fe(t,r,i,l),f="M ".concat(c.x,",").concat(c.y,`
    A `).concat(i,",").concat(i,`,0,
    `).concat(+(Math.abs(s)>180),",").concat(+(a>l),`,
    `).concat(u.x,",").concat(u.y,`
  `);if(n>0){var d=fe(t,r,n,a),h=fe(t,r,n,l);f+="L ".concat(h.x,",").concat(h.y,`
            A `).concat(n,",").concat(n,`,0,
            `).concat(+(Math.abs(s)>180),",").concat(+(a<=l),`,
            `).concat(d.x,",").concat(d.y," Z")}else f+="L ".concat(t,",").concat(r," Z");return f},WP=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:s,startAngle:l,endAngle:c}=e,u=Oe(c-l),{circleTangency:f,lineTangency:d,theta:h}=li({cx:t,cy:r,radius:i,angle:l,sign:u,cornerRadius:a,cornerIsExternal:s}),{circleTangency:p,lineTangency:m,theta:y}=li({cx:t,cy:r,radius:i,angle:c,sign:-u,cornerRadius:a,cornerIsExternal:s}),g=s?Math.abs(l-c):Math.abs(l-c)-h-y;if(g<0)return o?"M ".concat(d.x,",").concat(d.y,`
        a`).concat(a,",").concat(a,",0,0,1,").concat(a*2,`,0
        a`).concat(a,",").concat(a,",0,0,1,").concat(-a*2,`,0
      `):nm({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:l,endAngle:c});var x="M ".concat(d.x,",").concat(d.y,`
    A`).concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(f.x,",").concat(f.y,`
    A`).concat(i,",").concat(i,",0,").concat(+(g>180),",").concat(+(u<0),",").concat(p.x,",").concat(p.y,`
    A`).concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(m.x,",").concat(m.y,`
  `);if(n>0){var{circleTangency:w,lineTangency:P,theta:O}=li({cx:t,cy:r,radius:n,angle:l,sign:u,isExternal:!0,cornerRadius:a,cornerIsExternal:s}),{circleTangency:A,lineTangency:S,theta:E}=li({cx:t,cy:r,radius:n,angle:c,sign:-u,isExternal:!0,cornerRadius:a,cornerIsExternal:s}),_=s?Math.abs(l-c):Math.abs(l-c)-O-E;if(_<0&&a===0)return"".concat(x,"L").concat(t,",").concat(r,"Z");x+="L".concat(S.x,",").concat(S.y,`
      A`).concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(A.x,",").concat(A.y,`
      A`).concat(n,",").concat(n,",0,").concat(+(_>180),",").concat(+(u>0),",").concat(w.x,",").concat(w.y,`
      A`).concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(P.x,",").concat(P.y,"Z")}else x+="L".concat(t,",").concat(r,"Z");return x},FP={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},im=e=>{var t=Je(e,FP),{cx:r,cy:n,innerRadius:i,outerRadius:a,cornerRadius:o,forceCornerRadius:s,cornerIsExternal:l,startAngle:c,endAngle:u,className:f}=t;if(a<i||c===u)return null;var d=W("recharts-sector",f),h=a-i,p=$e(o,h,0,!0),m;return p>0&&Math.abs(c-u)<360?m=WP({cx:r,cy:n,innerRadius:i,outerRadius:a,cornerRadius:Math.min(p,h/2),forceCornerRadius:s,cornerIsExternal:l,startAngle:c,endAngle:u}):m=nm({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:c,endAngle:u}),v.createElement("path",Js({},z(t,!0),{className:d,d:m}))};function UP(e,t,r){var n,i,a,o;if(e==="horizontal")n=t.x,a=n,i=r.top,o=r.top+r.height;else if(e==="vertical")i=t.y,o=i,n=r.left,a=r.left+r.width;else if(t.cx!=null&&t.cy!=null)if(e==="centric"){var{cx:s,cy:l,innerRadius:c,outerRadius:u,angle:f}=t,d=fe(s,l,c,f),h=fe(s,l,u,f);n=d.x,i=d.y,a=h.x,o=h.y}else return rm(t);return[{x:n,y:i},{x:a,y:o}]}var Jo={},es={},ts={},jd;function YP(){return jd||(jd=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=dp();function r(n){return t.isSymbol(n)?NaN:Number(n)}e.toNumber=r}(ts)),ts}var Ed;function HP(){return Ed||(Ed=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=YP();function r(n){return n?(n=t.toNumber(n),n===1/0||n===-1/0?(n<0?-1:1)*Number.MAX_VALUE:n===n?n:0):n===0?n:0}e.toFinite=r}(es)),es}var _d;function GP(){return _d||(_d=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=hp(),r=HP();function n(i,a,o){o&&typeof o!="number"&&t.isIterateeCall(i,a,o)&&(a=o=void 0),i=r.toFinite(i),a===void 0?(a=i,i=0):a=r.toFinite(a),o=o===void 0?i<a?1:-1:r.toFinite(o);const s=Math.max(Math.ceil((a-i)/(o||1)),0),l=new Array(s);for(let c=0;c<s;c++)l[c]=i,i+=o;return l}e.range=n}(Jo)),Jo}var rs,Td;function VP(){return Td||(Td=1,rs=GP().range),rs}var XP=VP();const am=Lt(XP);function Gt(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function ZP(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function iu(e){let t,r,n;e.length!==2?(t=Gt,r=(s,l)=>Gt(e(s),l),n=(s,l)=>e(s)-l):(t=e===Gt||e===ZP?e:QP,r=e,n=e);function i(s,l,c=0,u=s.length){if(c<u){if(t(l,l)!==0)return u;do{const f=c+u>>>1;r(s[f],l)<0?c=f+1:u=f}while(c<u)}return c}function a(s,l,c=0,u=s.length){if(c<u){if(t(l,l)!==0)return u;do{const f=c+u>>>1;r(s[f],l)<=0?c=f+1:u=f}while(c<u)}return c}function o(s,l,c=0,u=s.length){const f=i(s,l,c,u-1);return f>c&&n(s[f-1],l)>-n(s[f],l)?f-1:f}return{left:i,center:o,right:a}}function QP(){return 0}function om(e){return e===null?NaN:+e}function*JP(e,t){for(let r of e)r!=null&&(r=+r)>=r&&(yield r)}const eO=iu(Gt),qn=eO.right;iu(om).center;class Cd extends Map{constructor(t,r=nO){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[n,i]of t)this.set(n,i)}get(t){return super.get(kd(this,t))}has(t){return super.has(kd(this,t))}set(t,r){return super.set(tO(this,t),r)}delete(t){return super.delete(rO(this,t))}}function kd({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function tO({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function rO({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function nO(e){return e!==null&&typeof e=="object"?e.valueOf():e}function iO(e=Gt){if(e===Gt)return sm;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||n===0?n:(e(r,r)===0)-(e(t,t)===0)}}function sm(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const aO=Math.sqrt(50),oO=Math.sqrt(10),sO=Math.sqrt(2);function Di(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=aO?10:a>=oO?5:a>=sO?2:1;let s,l,c;return i<0?(c=Math.pow(10,-i)/o,s=Math.round(e*c),l=Math.round(t*c),s/c<e&&++s,l/c>t&&--l,c=-c):(c=Math.pow(10,i)*o,s=Math.round(e/c),l=Math.round(t/c),s*c<e&&++s,l*c>t&&--l),l<s&&.5<=r&&r<2?Di(e,t,r*2):[s,l,c]}function el(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const n=t<e,[i,a,o]=n?Di(t,e,r):Di(e,t,r);if(!(a>=i))return[];const s=a-i+1,l=new Array(s);if(n)if(o<0)for(let c=0;c<s;++c)l[c]=(a-c)/-o;else for(let c=0;c<s;++c)l[c]=(a-c)*o;else if(o<0)for(let c=0;c<s;++c)l[c]=(i+c)/-o;else for(let c=0;c<s;++c)l[c]=(i+c)*o;return l}function tl(e,t,r){return t=+t,e=+e,r=+r,Di(e,t,r)[2]}function rl(e,t,r){t=+t,e=+e,r=+r;const n=t<e,i=n?tl(t,e,r):tl(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function Md(e,t){let r;for(const n of e)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);return r}function Nd(e,t){let r;for(const n of e)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);return r}function lm(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=i===void 0?sm:iO(i);n>r;){if(n-r>600){const l=n-r+1,c=t-r+1,u=Math.log(l),f=.5*Math.exp(2*u/3),d=.5*Math.sqrt(u*f*(l-f)/l)*(c-l/2<0?-1:1),h=Math.max(r,Math.floor(t-c*f/l+d)),p=Math.min(n,Math.floor(t+(l-c)*f/l+d));lm(e,t,h,p,i)}const a=e[t];let o=r,s=n;for(hn(e,r,t),i(e[n],a)>0&&hn(e,r,n);o<s;){for(hn(e,o,s),++o,--s;i(e[o],a)<0;)++o;for(;i(e[s],a)>0;)--s}i(e[r],a)===0?hn(e,r,s):(++s,hn(e,s,n)),s<=t&&(r=s+1),t<=s&&(n=s-1)}return e}function hn(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function lO(e,t,r){if(e=Float64Array.from(JP(e)),!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return Nd(e);if(t>=1)return Md(e);var n,i=(n-1)*t,a=Math.floor(i),o=Md(lm(e,a).subarray(0,a+1)),s=Nd(e.subarray(a+1));return o+(s-o)*(i-a)}}function uO(e,t,r=om){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e),s=+r(e[a+1],a+1,e);return o+(s-o)*(i-a)}}function cO(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=Math.max(0,Math.ceil((t-e)/r))|0,a=new Array(i);++n<i;)a[n]=e+n*r;return a}function ht(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function zt(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const nl=Symbol("implicit");function au(){var e=new Cd,t=[],r=[],n=nl;function i(a){let o=e.get(a);if(o===void 0){if(n!==nl)return n;e.set(a,o=t.push(a)-1)}return r[o%r.length]}return i.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new Cd;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return i},i.range=function(a){return arguments.length?(r=Array.from(a),i):r.slice()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return au(t,r).unknown(n)},ht.apply(i,arguments),i}function ou(){var e=au().unknown(void 0),t=e.domain,r=e.range,n=0,i=1,a,o,s=!1,l=0,c=0,u=.5;delete e.unknown;function f(){var d=t().length,h=i<n,p=h?i:n,m=h?n:i;a=(m-p)/Math.max(1,d-l+c*2),s&&(a=Math.floor(a)),p+=(m-p-a*(d-l))*u,o=a*(1-l),s&&(p=Math.round(p),o=Math.round(o));var y=cO(d).map(function(g){return p+a*g});return r(h?y.reverse():y)}return e.domain=function(d){return arguments.length?(t(d),f()):t()},e.range=function(d){return arguments.length?([n,i]=d,n=+n,i=+i,f()):[n,i]},e.rangeRound=function(d){return[n,i]=d,n=+n,i=+i,s=!0,f()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(d){return arguments.length?(s=!!d,f()):s},e.padding=function(d){return arguments.length?(l=Math.min(1,c=+d),f()):l},e.paddingInner=function(d){return arguments.length?(l=Math.min(1,d),f()):l},e.paddingOuter=function(d){return arguments.length?(c=+d,f()):c},e.align=function(d){return arguments.length?(u=Math.max(0,Math.min(1,d)),f()):u},e.copy=function(){return ou(t(),[n,i]).round(s).paddingInner(l).paddingOuter(c).align(u)},ht.apply(f(),arguments)}function um(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return um(t())},e}function fO(){return um(ou.apply(null,arguments).paddingInner(1))}function su(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function cm(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function Wn(){}var _n=.7,$i=1/_n,Wr="\\s*([+-]?\\d+)\\s*",Tn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",xt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",dO=/^#([0-9a-f]{3,8})$/,hO=new RegExp(`^rgb\\(${Wr},${Wr},${Wr}\\)$`),vO=new RegExp(`^rgb\\(${xt},${xt},${xt}\\)$`),pO=new RegExp(`^rgba\\(${Wr},${Wr},${Wr},${Tn}\\)$`),mO=new RegExp(`^rgba\\(${xt},${xt},${xt},${Tn}\\)$`),yO=new RegExp(`^hsl\\(${Tn},${xt},${xt}\\)$`),gO=new RegExp(`^hsla\\(${Tn},${xt},${xt},${Tn}\\)$`),Id={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};su(Wn,Cn,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Dd,formatHex:Dd,formatHex8:bO,formatHsl:xO,formatRgb:$d,toString:$d});function Dd(){return this.rgb().formatHex()}function bO(){return this.rgb().formatHex8()}function xO(){return fm(this).formatHsl()}function $d(){return this.rgb().formatRgb()}function Cn(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=dO.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?Rd(t):r===3?new Be(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?ui(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?ui(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=hO.exec(e))?new Be(t[1],t[2],t[3],1):(t=vO.exec(e))?new Be(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=pO.exec(e))?ui(t[1],t[2],t[3],t[4]):(t=mO.exec(e))?ui(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=yO.exec(e))?Kd(t[1],t[2]/100,t[3]/100,1):(t=gO.exec(e))?Kd(t[1],t[2]/100,t[3]/100,t[4]):Id.hasOwnProperty(e)?Rd(Id[e]):e==="transparent"?new Be(NaN,NaN,NaN,0):null}function Rd(e){return new Be(e>>16&255,e>>8&255,e&255,1)}function ui(e,t,r,n){return n<=0&&(e=t=r=NaN),new Be(e,t,r,n)}function wO(e){return e instanceof Wn||(e=Cn(e)),e?(e=e.rgb(),new Be(e.r,e.g,e.b,e.opacity)):new Be}function il(e,t,r,n){return arguments.length===1?wO(e):new Be(e,t,r,n??1)}function Be(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}su(Be,il,cm(Wn,{brighter(e){return e=e==null?$i:Math.pow($i,e),new Be(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?_n:Math.pow(_n,e),new Be(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Be(br(this.r),br(this.g),br(this.b),Ri(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Ld,formatHex:Ld,formatHex8:PO,formatRgb:Bd,toString:Bd}));function Ld(){return`#${hr(this.r)}${hr(this.g)}${hr(this.b)}`}function PO(){return`#${hr(this.r)}${hr(this.g)}${hr(this.b)}${hr((isNaN(this.opacity)?1:this.opacity)*255)}`}function Bd(){const e=Ri(this.opacity);return`${e===1?"rgb(":"rgba("}${br(this.r)}, ${br(this.g)}, ${br(this.b)}${e===1?")":`, ${e})`}`}function Ri(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function br(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function hr(e){return e=br(e),(e<16?"0":"")+e.toString(16)}function Kd(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new mt(e,t,r,n)}function fm(e){if(e instanceof mt)return new mt(e.h,e.s,e.l,e.opacity);if(e instanceof Wn||(e=Cn(e)),!e)return new mt;if(e instanceof mt)return e;e=e.rgb();var t=e.r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,s=a-i,l=(a+i)/2;return s?(t===a?o=(r-n)/s+(r<n)*6:r===a?o=(n-t)/s+2:o=(t-r)/s+4,s/=l<.5?a+i:2-a-i,o*=60):s=l>0&&l<1?0:o,new mt(o,s,l,e.opacity)}function OO(e,t,r,n){return arguments.length===1?fm(e):new mt(e,t,r,n??1)}function mt(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}su(mt,OO,cm(Wn,{brighter(e){return e=e==null?$i:Math.pow($i,e),new mt(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?_n:Math.pow(_n,e),new mt(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new Be(ns(e>=240?e-240:e+120,i,n),ns(e,i,n),ns(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new mt(zd(this.h),ci(this.s),ci(this.l),Ri(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Ri(this.opacity);return`${e===1?"hsl(":"hsla("}${zd(this.h)}, ${ci(this.s)*100}%, ${ci(this.l)*100}%${e===1?")":`, ${e})`}`}}));function zd(e){return e=(e||0)%360,e<0?e+360:e}function ci(e){return Math.max(0,Math.min(1,e||0))}function ns(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const lu=e=>()=>e;function AO(e,t){return function(r){return e+r*t}}function SO(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}function jO(e){return(e=+e)==1?dm:function(t,r){return r-t?SO(t,r,e):lu(isNaN(t)?r:t)}}function dm(e,t){var r=t-e;return r?AO(e,r):lu(isNaN(e)?t:e)}const qd=function e(t){var r=jO(t);function n(i,a){var o=r((i=il(i)).r,(a=il(a)).r),s=r(i.g,a.g),l=r(i.b,a.b),c=dm(i.opacity,a.opacity);return function(u){return i.r=o(u),i.g=s(u),i.b=l(u),i.opacity=c(u),i+""}}return n.gamma=e,n}(1);function EO(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,n=t.slice(),i;return function(a){for(i=0;i<r;++i)n[i]=e[i]*(1-a)+t[i]*a;return n}}function _O(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function TO(e,t){var r=t?t.length:0,n=e?Math.min(r,e.length):0,i=new Array(n),a=new Array(r),o;for(o=0;o<n;++o)i[o]=Jr(e[o],t[o]);for(;o<r;++o)a[o]=t[o];return function(s){for(o=0;o<n;++o)a[o]=i[o](s);return a}}function CO(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function Li(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function kO(e,t){var r={},n={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?r[i]=Jr(e[i],t[i]):n[i]=t[i];return function(a){for(i in r)n[i]=r[i](a);return n}}var al=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,is=new RegExp(al.source,"g");function MO(e){return function(){return e}}function NO(e){return function(t){return e(t)+""}}function IO(e,t){var r=al.lastIndex=is.lastIndex=0,n,i,a,o=-1,s=[],l=[];for(e=e+"",t=t+"";(n=al.exec(e))&&(i=is.exec(t));)(a=i.index)>r&&(a=t.slice(r,a),s[o]?s[o]+=a:s[++o]=a),(n=n[0])===(i=i[0])?s[o]?s[o]+=i:s[++o]=i:(s[++o]=null,l.push({i:o,x:Li(n,i)})),r=is.lastIndex;return r<t.length&&(a=t.slice(r),s[o]?s[o]+=a:s[++o]=a),s.length<2?l[0]?NO(l[0].x):MO(t):(t=l.length,function(c){for(var u=0,f;u<t;++u)s[(f=l[u]).i]=f.x(c);return s.join("")})}function Jr(e,t){var r=typeof t,n;return t==null||r==="boolean"?lu(t):(r==="number"?Li:r==="string"?(n=Cn(t))?(t=n,qd):IO:t instanceof Cn?qd:t instanceof Date?CO:_O(t)?EO:Array.isArray(t)?TO:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?kO:Li)(e,t)}function uu(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function DO(e,t){t===void 0&&(t=e,e=Jr);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(o){var s=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return a[s](o-s)}}function $O(e){return function(){return e}}function Bi(e){return+e}var Wd=[0,1];function De(e){return e}function ol(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:$O(isNaN(t)?NaN:.5)}function RO(e,t){var r;return e>t&&(r=e,e=t,t=r),function(n){return Math.max(e,Math.min(t,n))}}function LO(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=ol(i,n),a=r(o,a)):(n=ol(n,i),a=r(a,o)),function(s){return a(n(s))}}function BO(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=ol(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(s){var l=qn(e,s,1,n)-1;return a[l](i[l](s))}}function Fn(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function ya(){var e=Wd,t=Wd,r=Jr,n,i,a,o=De,s,l,c;function u(){var d=Math.min(e.length,t.length);return o!==De&&(o=RO(e[0],e[d-1])),s=d>2?BO:LO,l=c=null,f}function f(d){return d==null||isNaN(d=+d)?a:(l||(l=s(e.map(n),t,r)))(n(o(d)))}return f.invert=function(d){return o(i((c||(c=s(t,e.map(n),Li)))(d)))},f.domain=function(d){return arguments.length?(e=Array.from(d,Bi),u()):e.slice()},f.range=function(d){return arguments.length?(t=Array.from(d),u()):t.slice()},f.rangeRound=function(d){return t=Array.from(d),r=uu,u()},f.clamp=function(d){return arguments.length?(o=d?!0:De,u()):o!==De},f.interpolate=function(d){return arguments.length?(r=d,u()):r},f.unknown=function(d){return arguments.length?(a=d,f):a},function(d,h){return n=d,i=h,u()}}function cu(){return ya()(De,De)}function KO(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function Ki(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function Gr(e){return e=Ki(Math.abs(e)),e?e[1]:NaN}function zO(e,t){return function(r,n){for(var i=r.length,a=[],o=0,s=e[0],l=0;i>0&&s>0&&(l+s+1>n&&(s=Math.max(1,n-l)),a.push(r.substring(i-=s,i+s)),!((l+=s+1)>n));)s=e[o=(o+1)%e.length];return a.reverse().join(t)}}function qO(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var WO=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function kn(e){if(!(t=WO.exec(e)))throw new Error("invalid format: "+e);var t;return new fu({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}kn.prototype=fu.prototype;function fu(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}fu.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function FO(e){e:for(var t=e.length,r=1,n=-1,i;r<t;++r)switch(e[r]){case".":n=i=r;break;case"0":n===0&&(n=r),i=r;break;default:if(!+e[r])break e;n>0&&(n=0);break}return n>0?e.slice(0,n)+e.slice(i+1):e}var hm;function UO(e,t){var r=Ki(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(hm=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+Ki(e,Math.max(0,t+a-1))[0]}function Fd(e,t){var r=Ki(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}const Ud={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:KO,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>Fd(e*100,t),r:Fd,s:UO,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function Yd(e){return e}var Hd=Array.prototype.map,Gd=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function YO(e){var t=e.grouping===void 0||e.thousands===void 0?Yd:zO(Hd.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",n=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?Yd:qO(Hd.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",s=e.minus===void 0?"−":e.minus+"",l=e.nan===void 0?"NaN":e.nan+"";function c(f){f=kn(f);var d=f.fill,h=f.align,p=f.sign,m=f.symbol,y=f.zero,g=f.width,x=f.comma,w=f.precision,P=f.trim,O=f.type;O==="n"?(x=!0,O="g"):Ud[O]||(w===void 0&&(w=12),P=!0,O="g"),(y||d==="0"&&h==="=")&&(y=!0,d="0",h="=");var A=m==="$"?r:m==="#"&&/[boxX]/.test(O)?"0"+O.toLowerCase():"",S=m==="$"?n:/[%p]/.test(O)?o:"",E=Ud[O],_=/[defgprs%]/.test(O);w=w===void 0?6:/[gprs]/.test(O)?Math.max(1,Math.min(21,w)):Math.max(0,Math.min(20,w));function M(C){var k=A,R=S,B,U,X;if(O==="c")R=E(C)+R,C="";else{C=+C;var K=C<0||1/C<0;if(C=isNaN(C)?l:E(Math.abs(C),w),P&&(C=FO(C)),K&&+C==0&&p!=="+"&&(K=!1),k=(K?p==="("?p:s:p==="-"||p==="("?"":p)+k,R=(O==="s"?Gd[8+hm/3]:"")+R+(K&&p==="("?")":""),_){for(B=-1,U=C.length;++B<U;)if(X=C.charCodeAt(B),48>X||X>57){R=(X===46?i+C.slice(B+1):C.slice(B))+R,C=C.slice(0,B);break}}}x&&!y&&(C=t(C,1/0));var he=k.length+C.length+R.length,se=he<g?new Array(g-he+1).join(d):"";switch(x&&y&&(C=t(se+C,se.length?g-R.length:1/0),se=""),h){case"<":C=k+C+R+se;break;case"=":C=k+se+C+R;break;case"^":C=se.slice(0,he=se.length>>1)+k+C+R+se.slice(he);break;default:C=se+k+C+R;break}return a(C)}return M.toString=function(){return f+""},M}function u(f,d){var h=c((f=kn(f),f.type="f",f)),p=Math.max(-8,Math.min(8,Math.floor(Gr(d)/3)))*3,m=Math.pow(10,-p),y=Gd[8+p/3];return function(g){return h(m*g)+y}}return{format:c,formatPrefix:u}}var fi,du,vm;HO({thousands:",",grouping:[3],currency:["$",""]});function HO(e){return fi=YO(e),du=fi.format,vm=fi.formatPrefix,fi}function GO(e){return Math.max(0,-Gr(Math.abs(e)))}function VO(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(Gr(t)/3)))*3-Gr(Math.abs(e)))}function XO(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,Gr(t)-Gr(e))+1}function pm(e,t,r,n){var i=rl(e,t,r),a;switch(n=kn(n??",f"),n.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return n.precision==null&&!isNaN(a=VO(i,o))&&(n.precision=a),vm(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(a=XO(i,Math.max(Math.abs(e),Math.abs(t))))&&(n.precision=a-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(a=GO(i))&&(n.precision=a-(n.type==="%")*2);break}}return du(n)}function rr(e){var t=e.domain;return e.ticks=function(r){var n=t();return el(n[0],n[n.length-1],r??10)},e.tickFormat=function(r,n){var i=t();return pm(i[0],i[i.length-1],r??10,n)},e.nice=function(r){r==null&&(r=10);var n=t(),i=0,a=n.length-1,o=n[i],s=n[a],l,c,u=10;for(s<o&&(c=o,o=s,s=c,c=i,i=a,a=c);u-- >0;){if(c=tl(o,s,r),c===l)return n[i]=o,n[a]=s,t(n);if(c>0)o=Math.floor(o/c)*c,s=Math.ceil(s/c)*c;else if(c<0)o=Math.ceil(o*c)/c,s=Math.floor(s*c)/c;else break;l=c}return e},e}function mm(){var e=cu();return e.copy=function(){return Fn(e,mm())},ht.apply(e,arguments),rr(e)}function ym(e){var t;function r(n){return n==null||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(e=Array.from(n,Bi),r):e.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return ym(e).unknown(t)},e=arguments.length?Array.from(e,Bi):[0,1],rr(r)}function gm(e,t){e=e.slice();var r=0,n=e.length-1,i=e[r],a=e[n],o;return a<i&&(o=r,r=n,n=o,o=i,i=a,a=o),e[r]=t.floor(i),e[n]=t.ceil(a),e}function Vd(e){return Math.log(e)}function Xd(e){return Math.exp(e)}function ZO(e){return-Math.log(-e)}function QO(e){return-Math.exp(-e)}function JO(e){return isFinite(e)?+("1e"+e):e<0?0:e}function eA(e){return e===10?JO:e===Math.E?Math.exp:t=>Math.pow(e,t)}function tA(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function Zd(e){return(t,r)=>-e(-t,r)}function hu(e){const t=e(Vd,Xd),r=t.domain;let n=10,i,a;function o(){return i=tA(n),a=eA(n),r()[0]<0?(i=Zd(i),a=Zd(a),e(ZO,QO)):e(Vd,Xd),t}return t.base=function(s){return arguments.length?(n=+s,o()):n},t.domain=function(s){return arguments.length?(r(s),o()):r()},t.ticks=s=>{const l=r();let c=l[0],u=l[l.length-1];const f=u<c;f&&([c,u]=[u,c]);let d=i(c),h=i(u),p,m;const y=s==null?10:+s;let g=[];if(!(n%1)&&h-d<y){if(d=Math.floor(d),h=Math.ceil(h),c>0){for(;d<=h;++d)for(p=1;p<n;++p)if(m=d<0?p/a(-d):p*a(d),!(m<c)){if(m>u)break;g.push(m)}}else for(;d<=h;++d)for(p=n-1;p>=1;--p)if(m=d>0?p/a(-d):p*a(d),!(m<c)){if(m>u)break;g.push(m)}g.length*2<y&&(g=el(c,u,y))}else g=el(d,h,Math.min(h-d,y)).map(a);return f?g.reverse():g},t.tickFormat=(s,l)=>{if(s==null&&(s=10),l==null&&(l=n===10?"s":","),typeof l!="function"&&(!(n%1)&&(l=kn(l)).precision==null&&(l.trim=!0),l=du(l)),s===1/0)return l;const c=Math.max(1,n*s/t.ticks().length);return u=>{let f=u/a(Math.round(i(u)));return f*n<n-.5&&(f*=n),f<=c?l(u):""}},t.nice=()=>r(gm(r(),{floor:s=>a(Math.floor(i(s))),ceil:s=>a(Math.ceil(i(s)))})),t}function bm(){const e=hu(ya()).domain([1,10]);return e.copy=()=>Fn(e,bm()).base(e.base()),ht.apply(e,arguments),e}function Qd(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function Jd(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function vu(e){var t=1,r=e(Qd(t),Jd(t));return r.constant=function(n){return arguments.length?e(Qd(t=+n),Jd(t)):t},rr(r)}function xm(){var e=vu(ya());return e.copy=function(){return Fn(e,xm()).constant(e.constant())},ht.apply(e,arguments)}function eh(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function rA(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function nA(e){return e<0?-e*e:e*e}function pu(e){var t=e(De,De),r=1;function n(){return r===1?e(De,De):r===.5?e(rA,nA):e(eh(r),eh(1/r))}return t.exponent=function(i){return arguments.length?(r=+i,n()):r},rr(t)}function mu(){var e=pu(ya());return e.copy=function(){return Fn(e,mu()).exponent(e.exponent())},ht.apply(e,arguments),e}function iA(){return mu.apply(null,arguments).exponent(.5)}function th(e){return Math.sign(e)*e*e}function aA(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function wm(){var e=cu(),t=[0,1],r=!1,n;function i(a){var o=aA(e(a));return isNaN(o)?n:r?Math.round(o):o}return i.invert=function(a){return e.invert(th(a))},i.domain=function(a){return arguments.length?(e.domain(a),i):e.domain()},i.range=function(a){return arguments.length?(e.range((t=Array.from(a,Bi)).map(th)),i):t.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(r=!!a,i):r},i.clamp=function(a){return arguments.length?(e.clamp(a),i):e.clamp()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return wm(e.domain(),t).round(r).clamp(e.clamp()).unknown(n)},ht.apply(i,arguments),rr(i)}function Pm(){var e=[],t=[],r=[],n;function i(){var o=0,s=Math.max(1,t.length);for(r=new Array(s-1);++o<s;)r[o-1]=uO(e,o/s);return a}function a(o){return o==null||isNaN(o=+o)?n:t[qn(r,o)]}return a.invertExtent=function(o){var s=t.indexOf(o);return s<0?[NaN,NaN]:[s>0?r[s-1]:e[0],s<r.length?r[s]:e[e.length-1]]},a.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let s of o)s!=null&&!isNaN(s=+s)&&e.push(s);return e.sort(Gt),i()},a.range=function(o){return arguments.length?(t=Array.from(o),i()):t.slice()},a.unknown=function(o){return arguments.length?(n=o,a):n},a.quantiles=function(){return r.slice()},a.copy=function(){return Pm().domain(e).range(t).unknown(n)},ht.apply(a,arguments)}function Om(){var e=0,t=1,r=1,n=[.5],i=[0,1],a;function o(l){return l!=null&&l<=l?i[qn(n,l,0,r)]:a}function s(){var l=-1;for(n=new Array(r);++l<r;)n[l]=((l+1)*t-(l-r)*e)/(r+1);return o}return o.domain=function(l){return arguments.length?([e,t]=l,e=+e,t=+t,s()):[e,t]},o.range=function(l){return arguments.length?(r=(i=Array.from(l)).length-1,s()):i.slice()},o.invertExtent=function(l){var c=i.indexOf(l);return c<0?[NaN,NaN]:c<1?[e,n[0]]:c>=r?[n[r-1],t]:[n[c-1],n[c]]},o.unknown=function(l){return arguments.length&&(a=l),o},o.thresholds=function(){return n.slice()},o.copy=function(){return Om().domain([e,t]).range(i).unknown(a)},ht.apply(rr(o),arguments)}function Am(){var e=[.5],t=[0,1],r,n=1;function i(a){return a!=null&&a<=a?t[qn(e,a,0,n)]:r}return i.domain=function(a){return arguments.length?(e=Array.from(a),n=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),n=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var o=t.indexOf(a);return[e[o-1],e[o]]},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return Am().domain(e).range(t).unknown(r)},ht.apply(i,arguments)}const as=new Date,os=new Date;function me(e,t,r,n){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const o=i(a),s=i.ceil(a);return a-o<s-a?o:s},i.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,s)=>{const l=[];if(a=i.ceil(a),s=s==null?1:Math.floor(s),!(a<o)||!(s>0))return l;let c;do l.push(c=new Date(+a)),t(a,s),e(a);while(c<a&&a<o);return l},i.filter=a=>me(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,s)=>{if(o>=o)if(s<0)for(;++s<=0;)for(;t(o,-1),!a(o););else for(;--s>=0;)for(;t(o,1),!a(o););}),r&&(i.count=(a,o)=>(as.setTime(+a),os.setTime(+o),e(as),e(os),Math.floor(r(as,os))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(n?o=>n(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const zi=me(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);zi.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?me(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):zi);zi.range;const _t=1e3,lt=_t*60,Tt=lt*60,Dt=Tt*24,yu=Dt*7,rh=Dt*30,ss=Dt*365,vr=me(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*_t)},(e,t)=>(t-e)/_t,e=>e.getUTCSeconds());vr.range;const gu=me(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*_t)},(e,t)=>{e.setTime(+e+t*lt)},(e,t)=>(t-e)/lt,e=>e.getMinutes());gu.range;const bu=me(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*lt)},(e,t)=>(t-e)/lt,e=>e.getUTCMinutes());bu.range;const xu=me(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*_t-e.getMinutes()*lt)},(e,t)=>{e.setTime(+e+t*Tt)},(e,t)=>(t-e)/Tt,e=>e.getHours());xu.range;const wu=me(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*Tt)},(e,t)=>(t-e)/Tt,e=>e.getUTCHours());wu.range;const Un=me(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*lt)/Dt,e=>e.getDate()-1);Un.range;const ga=me(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/Dt,e=>e.getUTCDate()-1);ga.range;const Sm=me(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/Dt,e=>Math.floor(e/Dt));Sm.range;function Er(e){return me(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*lt)/yu)}const ba=Er(0),qi=Er(1),oA=Er(2),sA=Er(3),Vr=Er(4),lA=Er(5),uA=Er(6);ba.range;qi.range;oA.range;sA.range;Vr.range;lA.range;uA.range;function _r(e){return me(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/yu)}const xa=_r(0),Wi=_r(1),cA=_r(2),fA=_r(3),Xr=_r(4),dA=_r(5),hA=_r(6);xa.range;Wi.range;cA.range;fA.range;Xr.range;dA.range;hA.range;const Pu=me(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());Pu.range;const Ou=me(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());Ou.range;const $t=me(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());$t.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:me(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});$t.range;const Rt=me(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());Rt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:me(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});Rt.range;function jm(e,t,r,n,i,a){const o=[[vr,1,_t],[vr,5,5*_t],[vr,15,15*_t],[vr,30,30*_t],[a,1,lt],[a,5,5*lt],[a,15,15*lt],[a,30,30*lt],[i,1,Tt],[i,3,3*Tt],[i,6,6*Tt],[i,12,12*Tt],[n,1,Dt],[n,2,2*Dt],[r,1,yu],[t,1,rh],[t,3,3*rh],[e,1,ss]];function s(c,u,f){const d=u<c;d&&([c,u]=[u,c]);const h=f&&typeof f.range=="function"?f:l(c,u,f),p=h?h.range(c,+u+1):[];return d?p.reverse():p}function l(c,u,f){const d=Math.abs(u-c)/f,h=iu(([,,y])=>y).right(o,d);if(h===o.length)return e.every(rl(c/ss,u/ss,f));if(h===0)return zi.every(Math.max(rl(c,u,f),1));const[p,m]=o[d/o[h-1][2]<o[h][2]/d?h-1:h];return p.every(m)}return[s,l]}const[vA,pA]=jm(Rt,Ou,xa,Sm,wu,bu),[mA,yA]=jm($t,Pu,ba,Un,xu,gu);function ls(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function us(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function vn(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function gA(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,s=e.months,l=e.shortMonths,c=pn(i),u=mn(i),f=pn(a),d=mn(a),h=pn(o),p=mn(o),m=pn(s),y=mn(s),g=pn(l),x=mn(l),w={a:K,A:he,b:se,B:ze,c:null,d:lh,e:lh,f:zA,g:ZA,G:JA,H:LA,I:BA,j:KA,L:Em,m:qA,M:WA,p:et,q:L,Q:fh,s:dh,S:FA,u:UA,U:YA,V:HA,w:GA,W:VA,x:null,X:null,y:XA,Y:QA,Z:eS,"%":ch},P={a:je,A:or,b:tt,B:v0,c:null,d:uh,e:uh,f:iS,g:vS,G:mS,H:tS,I:rS,j:nS,L:Tm,m:aS,M:oS,p:p0,q:m0,Q:fh,s:dh,S:sS,u:lS,U:uS,V:cS,w:fS,W:dS,x:null,X:null,y:hS,Y:pS,Z:yS,"%":ch},O={a:M,A:C,b:k,B:R,c:B,d:oh,e:oh,f:IA,g:ah,G:ih,H:sh,I:sh,j:CA,L:NA,m:TA,M:kA,p:_,q:_A,Q:$A,s:RA,S:MA,u:OA,U:AA,V:SA,w:PA,W:jA,x:U,X,y:ah,Y:ih,Z:EA,"%":DA};w.x=A(r,w),w.X=A(n,w),w.c=A(t,w),P.x=A(r,P),P.X=A(n,P),P.c=A(t,P);function A($,q){return function(Y){var T=[],Re=-1,ee=0,qe=$.length,We,sr,_c;for(Y instanceof Date||(Y=new Date(+Y));++Re<qe;)$.charCodeAt(Re)===37&&(T.push($.slice(ee,Re)),(sr=nh[We=$.charAt(++Re)])!=null?We=$.charAt(++Re):sr=We==="e"?" ":"0",(_c=q[We])&&(We=_c(Y,sr)),T.push(We),ee=Re+1);return T.push($.slice(ee,Re)),T.join("")}}function S($,q){return function(Y){var T=vn(1900,void 0,1),Re=E(T,$,Y+="",0),ee,qe;if(Re!=Y.length)return null;if("Q"in T)return new Date(T.Q);if("s"in T)return new Date(T.s*1e3+("L"in T?T.L:0));if(q&&!("Z"in T)&&(T.Z=0),"p"in T&&(T.H=T.H%12+T.p*12),T.m===void 0&&(T.m="q"in T?T.q:0),"V"in T){if(T.V<1||T.V>53)return null;"w"in T||(T.w=1),"Z"in T?(ee=us(vn(T.y,0,1)),qe=ee.getUTCDay(),ee=qe>4||qe===0?Wi.ceil(ee):Wi(ee),ee=ga.offset(ee,(T.V-1)*7),T.y=ee.getUTCFullYear(),T.m=ee.getUTCMonth(),T.d=ee.getUTCDate()+(T.w+6)%7):(ee=ls(vn(T.y,0,1)),qe=ee.getDay(),ee=qe>4||qe===0?qi.ceil(ee):qi(ee),ee=Un.offset(ee,(T.V-1)*7),T.y=ee.getFullYear(),T.m=ee.getMonth(),T.d=ee.getDate()+(T.w+6)%7)}else("W"in T||"U"in T)&&("w"in T||(T.w="u"in T?T.u%7:"W"in T?1:0),qe="Z"in T?us(vn(T.y,0,1)).getUTCDay():ls(vn(T.y,0,1)).getDay(),T.m=0,T.d="W"in T?(T.w+6)%7+T.W*7-(qe+5)%7:T.w+T.U*7-(qe+6)%7);return"Z"in T?(T.H+=T.Z/100|0,T.M+=T.Z%100,us(T)):ls(T)}}function E($,q,Y,T){for(var Re=0,ee=q.length,qe=Y.length,We,sr;Re<ee;){if(T>=qe)return-1;if(We=q.charCodeAt(Re++),We===37){if(We=q.charAt(Re++),sr=O[We in nh?q.charAt(Re++):We],!sr||(T=sr($,Y,T))<0)return-1}else if(We!=Y.charCodeAt(T++))return-1}return T}function _($,q,Y){var T=c.exec(q.slice(Y));return T?($.p=u.get(T[0].toLowerCase()),Y+T[0].length):-1}function M($,q,Y){var T=h.exec(q.slice(Y));return T?($.w=p.get(T[0].toLowerCase()),Y+T[0].length):-1}function C($,q,Y){var T=f.exec(q.slice(Y));return T?($.w=d.get(T[0].toLowerCase()),Y+T[0].length):-1}function k($,q,Y){var T=g.exec(q.slice(Y));return T?($.m=x.get(T[0].toLowerCase()),Y+T[0].length):-1}function R($,q,Y){var T=m.exec(q.slice(Y));return T?($.m=y.get(T[0].toLowerCase()),Y+T[0].length):-1}function B($,q,Y){return E($,t,q,Y)}function U($,q,Y){return E($,r,q,Y)}function X($,q,Y){return E($,n,q,Y)}function K($){return o[$.getDay()]}function he($){return a[$.getDay()]}function se($){return l[$.getMonth()]}function ze($){return s[$.getMonth()]}function et($){return i[+($.getHours()>=12)]}function L($){return 1+~~($.getMonth()/3)}function je($){return o[$.getUTCDay()]}function or($){return a[$.getUTCDay()]}function tt($){return l[$.getUTCMonth()]}function v0($){return s[$.getUTCMonth()]}function p0($){return i[+($.getUTCHours()>=12)]}function m0($){return 1+~~($.getUTCMonth()/3)}return{format:function($){var q=A($+="",w);return q.toString=function(){return $},q},parse:function($){var q=S($+="",!1);return q.toString=function(){return $},q},utcFormat:function($){var q=A($+="",P);return q.toString=function(){return $},q},utcParse:function($){var q=S($+="",!0);return q.toString=function(){return $},q}}}var nh={"-":"",_:" ",0:"0"},Ae=/^\s*\d+/,bA=/^%/,xA=/[\\^$*+?|[\]().{}]/g;function H(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function wA(e){return e.replace(xA,"\\$&")}function pn(e){return new RegExp("^(?:"+e.map(wA).join("|")+")","i")}function mn(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function PA(e,t,r){var n=Ae.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function OA(e,t,r){var n=Ae.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function AA(e,t,r){var n=Ae.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function SA(e,t,r){var n=Ae.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function jA(e,t,r){var n=Ae.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function ih(e,t,r){var n=Ae.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function ah(e,t,r){var n=Ae.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function EA(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function _A(e,t,r){var n=Ae.exec(t.slice(r,r+1));return n?(e.q=n[0]*3-3,r+n[0].length):-1}function TA(e,t,r){var n=Ae.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function oh(e,t,r){var n=Ae.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function CA(e,t,r){var n=Ae.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function sh(e,t,r){var n=Ae.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function kA(e,t,r){var n=Ae.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function MA(e,t,r){var n=Ae.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function NA(e,t,r){var n=Ae.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function IA(e,t,r){var n=Ae.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function DA(e,t,r){var n=bA.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function $A(e,t,r){var n=Ae.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function RA(e,t,r){var n=Ae.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function lh(e,t){return H(e.getDate(),t,2)}function LA(e,t){return H(e.getHours(),t,2)}function BA(e,t){return H(e.getHours()%12||12,t,2)}function KA(e,t){return H(1+Un.count($t(e),e),t,3)}function Em(e,t){return H(e.getMilliseconds(),t,3)}function zA(e,t){return Em(e,t)+"000"}function qA(e,t){return H(e.getMonth()+1,t,2)}function WA(e,t){return H(e.getMinutes(),t,2)}function FA(e,t){return H(e.getSeconds(),t,2)}function UA(e){var t=e.getDay();return t===0?7:t}function YA(e,t){return H(ba.count($t(e)-1,e),t,2)}function _m(e){var t=e.getDay();return t>=4||t===0?Vr(e):Vr.ceil(e)}function HA(e,t){return e=_m(e),H(Vr.count($t(e),e)+($t(e).getDay()===4),t,2)}function GA(e){return e.getDay()}function VA(e,t){return H(qi.count($t(e)-1,e),t,2)}function XA(e,t){return H(e.getFullYear()%100,t,2)}function ZA(e,t){return e=_m(e),H(e.getFullYear()%100,t,2)}function QA(e,t){return H(e.getFullYear()%1e4,t,4)}function JA(e,t){var r=e.getDay();return e=r>=4||r===0?Vr(e):Vr.ceil(e),H(e.getFullYear()%1e4,t,4)}function eS(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+H(t/60|0,"0",2)+H(t%60,"0",2)}function uh(e,t){return H(e.getUTCDate(),t,2)}function tS(e,t){return H(e.getUTCHours(),t,2)}function rS(e,t){return H(e.getUTCHours()%12||12,t,2)}function nS(e,t){return H(1+ga.count(Rt(e),e),t,3)}function Tm(e,t){return H(e.getUTCMilliseconds(),t,3)}function iS(e,t){return Tm(e,t)+"000"}function aS(e,t){return H(e.getUTCMonth()+1,t,2)}function oS(e,t){return H(e.getUTCMinutes(),t,2)}function sS(e,t){return H(e.getUTCSeconds(),t,2)}function lS(e){var t=e.getUTCDay();return t===0?7:t}function uS(e,t){return H(xa.count(Rt(e)-1,e),t,2)}function Cm(e){var t=e.getUTCDay();return t>=4||t===0?Xr(e):Xr.ceil(e)}function cS(e,t){return e=Cm(e),H(Xr.count(Rt(e),e)+(Rt(e).getUTCDay()===4),t,2)}function fS(e){return e.getUTCDay()}function dS(e,t){return H(Wi.count(Rt(e)-1,e),t,2)}function hS(e,t){return H(e.getUTCFullYear()%100,t,2)}function vS(e,t){return e=Cm(e),H(e.getUTCFullYear()%100,t,2)}function pS(e,t){return H(e.getUTCFullYear()%1e4,t,4)}function mS(e,t){var r=e.getUTCDay();return e=r>=4||r===0?Xr(e):Xr.ceil(e),H(e.getUTCFullYear()%1e4,t,4)}function yS(){return"+0000"}function ch(){return"%"}function fh(e){return+e}function dh(e){return Math.floor(+e/1e3)}var Nr,km,Mm;gS({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function gS(e){return Nr=gA(e),km=Nr.format,Nr.parse,Mm=Nr.utcFormat,Nr.utcParse,Nr}function bS(e){return new Date(e)}function xS(e){return e instanceof Date?+e:+new Date(+e)}function Au(e,t,r,n,i,a,o,s,l,c){var u=cu(),f=u.invert,d=u.domain,h=c(".%L"),p=c(":%S"),m=c("%I:%M"),y=c("%I %p"),g=c("%a %d"),x=c("%b %d"),w=c("%B"),P=c("%Y");function O(A){return(l(A)<A?h:s(A)<A?p:o(A)<A?m:a(A)<A?y:n(A)<A?i(A)<A?g:x:r(A)<A?w:P)(A)}return u.invert=function(A){return new Date(f(A))},u.domain=function(A){return arguments.length?d(Array.from(A,xS)):d().map(bS)},u.ticks=function(A){var S=d();return e(S[0],S[S.length-1],A??10)},u.tickFormat=function(A,S){return S==null?O:c(S)},u.nice=function(A){var S=d();return(!A||typeof A.range!="function")&&(A=t(S[0],S[S.length-1],A??10)),A?d(gm(S,A)):u},u.copy=function(){return Fn(u,Au(e,t,r,n,i,a,o,s,l,c))},u}function wS(){return ht.apply(Au(mA,yA,$t,Pu,ba,Un,xu,gu,vr,km).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function PS(){return ht.apply(Au(vA,pA,Rt,Ou,xa,ga,wu,bu,vr,Mm).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function wa(){var e=0,t=1,r,n,i,a,o=De,s=!1,l;function c(f){return f==null||isNaN(f=+f)?l:o(i===0?.5:(f=(a(f)-r)*i,s?Math.max(0,Math.min(1,f)):f))}c.domain=function(f){return arguments.length?([e,t]=f,r=a(e=+e),n=a(t=+t),i=r===n?0:1/(n-r),c):[e,t]},c.clamp=function(f){return arguments.length?(s=!!f,c):s},c.interpolator=function(f){return arguments.length?(o=f,c):o};function u(f){return function(d){var h,p;return arguments.length?([h,p]=d,o=f(h,p),c):[o(0),o(1)]}}return c.range=u(Jr),c.rangeRound=u(uu),c.unknown=function(f){return arguments.length?(l=f,c):l},function(f){return a=f,r=f(e),n=f(t),i=r===n?0:1/(n-r),c}}function nr(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function Nm(){var e=rr(wa()(De));return e.copy=function(){return nr(e,Nm())},zt.apply(e,arguments)}function Im(){var e=hu(wa()).domain([1,10]);return e.copy=function(){return nr(e,Im()).base(e.base())},zt.apply(e,arguments)}function Dm(){var e=vu(wa());return e.copy=function(){return nr(e,Dm()).constant(e.constant())},zt.apply(e,arguments)}function Su(){var e=pu(wa());return e.copy=function(){return nr(e,Su()).exponent(e.exponent())},zt.apply(e,arguments)}function OS(){return Su.apply(null,arguments).exponent(.5)}function $m(){var e=[],t=De;function r(n){if(n!=null&&!isNaN(n=+n))return t((qn(e,n,1)-1)/(e.length-1))}return r.domain=function(n){if(!arguments.length)return e.slice();e=[];for(let i of n)i!=null&&!isNaN(i=+i)&&e.push(i);return e.sort(Gt),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return e.map((n,i)=>t(i/(e.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(i,a)=>lO(e,a/n))},r.copy=function(){return $m(t).domain(e)},zt.apply(r,arguments)}function Pa(){var e=0,t=.5,r=1,n=1,i,a,o,s,l,c=De,u,f=!1,d;function h(m){return isNaN(m=+m)?d:(m=.5+((m=+u(m))-a)*(n*m<n*a?s:l),c(f?Math.max(0,Math.min(1,m)):m))}h.domain=function(m){return arguments.length?([e,t,r]=m,i=u(e=+e),a=u(t=+t),o=u(r=+r),s=i===a?0:.5/(a-i),l=a===o?0:.5/(o-a),n=a<i?-1:1,h):[e,t,r]},h.clamp=function(m){return arguments.length?(f=!!m,h):f},h.interpolator=function(m){return arguments.length?(c=m,h):c};function p(m){return function(y){var g,x,w;return arguments.length?([g,x,w]=y,c=DO(m,[g,x,w]),h):[c(0),c(.5),c(1)]}}return h.range=p(Jr),h.rangeRound=p(uu),h.unknown=function(m){return arguments.length?(d=m,h):d},function(m){return u=m,i=m(e),a=m(t),o=m(r),s=i===a?0:.5/(a-i),l=a===o?0:.5/(o-a),n=a<i?-1:1,h}}function Rm(){var e=rr(Pa()(De));return e.copy=function(){return nr(e,Rm())},zt.apply(e,arguments)}function Lm(){var e=hu(Pa()).domain([.1,1,10]);return e.copy=function(){return nr(e,Lm()).base(e.base())},zt.apply(e,arguments)}function Bm(){var e=vu(Pa());return e.copy=function(){return nr(e,Bm()).constant(e.constant())},zt.apply(e,arguments)}function ju(){var e=pu(Pa());return e.copy=function(){return nr(e,ju()).exponent(e.exponent())},zt.apply(e,arguments)}function AS(){return ju.apply(null,arguments).exponent(.5)}const bn=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:ou,scaleDiverging:Rm,scaleDivergingLog:Lm,scaleDivergingPow:ju,scaleDivergingSqrt:AS,scaleDivergingSymlog:Bm,scaleIdentity:ym,scaleImplicit:nl,scaleLinear:mm,scaleLog:bm,scaleOrdinal:au,scalePoint:fO,scalePow:mu,scaleQuantile:Pm,scaleQuantize:Om,scaleRadial:wm,scaleSequential:Nm,scaleSequentialLog:Im,scaleSequentialPow:Su,scaleSequentialQuantile:$m,scaleSequentialSqrt:OS,scaleSequentialSymlog:Dm,scaleSqrt:iA,scaleSymlog:xm,scaleThreshold:Am,scaleTime:wS,scaleUtc:PS,tickFormat:pm},Symbol.toStringTag,{value:"Module"}));var Tr=e=>e.chartData,Eu=j([Tr],e=>{var t=e.chartData!=null?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),Oa=(e,t,r,n)=>n?Eu(e):Tr(e);function Zr(e){if(Array.isArray(e)&&e.length===2){var[t,r]=e;if(Te(t)&&Te(r))return!0}return!1}function hh(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}function SS(e,t){if(t&&typeof e!="function"&&Array.isArray(e)&&e.length===2){var[r,n]=e,i,a;if(Te(r))i=r;else if(typeof r=="function")return;if(Te(n))a=n;else if(typeof n=="function")return;var o=[i,a];if(Zr(o))return o}}function jS(e,t,r){if(!(!r&&t==null)){if(typeof e=="function"&&t!=null)try{var n=e(t,r);if(Zr(n))return hh(n,t,r)}catch{}if(Array.isArray(e)&&e.length===2){var[i,a]=e,o,s;if(i==="auto")t!=null&&(o=Math.min(...t));else if(N(i))o=i;else if(typeof i=="function")try{t!=null&&(o=i(t==null?void 0:t[0]))}catch{}else if(typeof i=="string"&&ed.test(i)){var l=ed.exec(i);if(l==null||t==null)o=void 0;else{var c=+l[1];o=t[0]-c}}else o=t==null?void 0:t[0];if(a==="auto")t!=null&&(s=Math.max(...t));else if(N(a))s=a;else if(typeof a=="function")try{t!=null&&(s=a(t==null?void 0:t[1]))}catch{}else if(typeof a=="string"&&td.test(a)){var u=td.exec(a);if(u==null||t==null)s=void 0;else{var f=+u[1];s=t[1]+f}}else s=t==null?void 0:t[1];var d=[o,s];if(Zr(d))return t==null?d:hh(d,t,r)}}}var en=1e9,ES={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},Tu,ne=!0,ct="[DecimalError] ",xr=ct+"Invalid argument: ",_u=ct+"Exponent out of range: ",tn=Math.floor,cr=Math.pow,_S=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Ve,Pe=1e7,re=7,Km=9007199254740991,Fi=tn(Km/re),I={};I.absoluteValue=I.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};I.comparedTo=I.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(n=a.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};I.decimalPlaces=I.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*re;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};I.dividedBy=I.div=function(e){return kt(this,new this.constructor(e))};I.dividedToIntegerBy=I.idiv=function(e){var t=this,r=t.constructor;return J(kt(t,new r(e),0,1),r.precision)};I.equals=I.eq=function(e){return!this.cmp(e)};I.exponent=function(){return de(this)};I.greaterThan=I.gt=function(e){return this.cmp(e)>0};I.greaterThanOrEqualTo=I.gte=function(e){return this.cmp(e)>=0};I.isInteger=I.isint=function(){return this.e>this.d.length-2};I.isNegative=I.isneg=function(){return this.s<0};I.isPositive=I.ispos=function(){return this.s>0};I.isZero=function(){return this.s===0};I.lessThan=I.lt=function(e){return this.cmp(e)<0};I.lessThanOrEqualTo=I.lte=function(e){return this.cmp(e)<1};I.logarithm=I.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(Ve))throw Error(ct+"NaN");if(r.s<1)throw Error(ct+(r.s?"NaN":"-Infinity"));return r.eq(Ve)?new n(0):(ne=!1,t=kt(Mn(r,a),Mn(e,a),a),ne=!0,J(t,i))};I.minus=I.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Wm(t,e):zm(t,(e.s=-e.s,e))};I.modulo=I.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(ct+"NaN");return r.s?(ne=!1,t=kt(r,e,0,1).times(e),ne=!0,r.minus(t)):J(new n(r),i)};I.naturalExponential=I.exp=function(){return qm(this)};I.naturalLogarithm=I.ln=function(){return Mn(this)};I.negated=I.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};I.plus=I.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?zm(t,e):Wm(t,(e.s=-e.s,e))};I.precision=I.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(xr+e);if(t=de(i)+1,n=i.d.length-1,r=n*re+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};I.squareRoot=I.sqrt=function(){var e,t,r,n,i,a,o,s=this,l=s.constructor;if(s.s<1){if(!s.s)return new l(0);throw Error(ct+"NaN")}for(e=de(s),ne=!1,i=Math.sqrt(+s),i==0||i==1/0?(t=bt(s.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=tn((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new l(t)):n=new l(i.toString()),r=l.precision,i=o=r+3;;)if(a=n,n=a.plus(kt(s,a,o+2)).times(.5),bt(a.d).slice(0,o)===(t=bt(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&t=="4999"){if(J(a,r+1,0),a.times(a).eq(s)){n=a;break}}else if(t!="9999")break;o+=4}return ne=!0,J(n,r)};I.times=I.mul=function(e){var t,r,n,i,a,o,s,l,c,u=this,f=u.constructor,d=u.d,h=(e=new f(e)).d;if(!u.s||!e.s)return new f(0);for(e.s*=u.s,r=u.e+e.e,l=d.length,c=h.length,l<c&&(a=d,d=h,h=a,o=l,l=c,c=o),a=[],o=l+c,n=o;n--;)a.push(0);for(n=c;--n>=0;){for(t=0,i=l+n;i>n;)s=a[i]+h[n]*d[i-n-1]+t,a[i--]=s%Pe|0,t=s/Pe|0;a[i]=(a[i]+t)%Pe|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,ne?J(e,f.precision):e};I.toDecimalPlaces=I.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(Pt(e,0,en),t===void 0?t=n.rounding:Pt(t,0,8),J(r,e+de(r)+1,t))};I.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=Sr(n,!0):(Pt(e,0,en),t===void 0?t=i.rounding:Pt(t,0,8),n=J(new i(n),e+1,t),r=Sr(n,!0,e+1)),r};I.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?Sr(i):(Pt(e,0,en),t===void 0?t=a.rounding:Pt(t,0,8),n=J(new a(i),e+de(i)+1,t),r=Sr(n.abs(),!1,e+de(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};I.toInteger=I.toint=function(){var e=this,t=e.constructor;return J(new t(e),de(e)+1,t.rounding)};I.toNumber=function(){return+this};I.toPower=I.pow=function(e){var t,r,n,i,a,o,s=this,l=s.constructor,c=12,u=+(e=new l(e));if(!e.s)return new l(Ve);if(s=new l(s),!s.s){if(e.s<1)throw Error(ct+"Infinity");return s}if(s.eq(Ve))return s;if(n=l.precision,e.eq(Ve))return J(s,n);if(t=e.e,r=e.d.length-1,o=t>=r,a=s.s,o){if((r=u<0?-u:u)<=Km){for(i=new l(Ve),t=Math.ceil(n/re+4),ne=!1;r%2&&(i=i.times(s),ph(i.d,t)),r=tn(r/2),r!==0;)s=s.times(s),ph(s.d,t);return ne=!0,e.s<0?new l(Ve).div(i):J(i,n)}}else if(a<0)throw Error(ct+"NaN");return a=a<0&&e.d[Math.max(t,r)]&1?-1:1,s.s=1,ne=!1,i=e.times(Mn(s,n+c)),ne=!0,i=qm(i),i.s=a,i};I.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?(r=de(i),n=Sr(i,r<=a.toExpNeg||r>=a.toExpPos)):(Pt(e,1,en),t===void 0?t=a.rounding:Pt(t,0,8),i=J(new a(i),e,t),r=de(i),n=Sr(i,e<=r||r<=a.toExpNeg,e)),n};I.toSignificantDigits=I.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(Pt(e,1,en),t===void 0?t=n.rounding:Pt(t,0,8)),J(new n(r),e,t)};I.toString=I.valueOf=I.val=I.toJSON=I[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=de(e),r=e.constructor;return Sr(e,t<=r.toExpNeg||t>=r.toExpPos)};function zm(e,t){var r,n,i,a,o,s,l,c,u=e.constructor,f=u.precision;if(!e.s||!t.s)return t.s||(t=new u(e)),ne?J(t,f):t;if(l=e.d,c=t.d,o=e.e,i=t.e,l=l.slice(),a=o-i,a){for(a<0?(n=l,a=-a,s=c.length):(n=c,i=o,s=l.length),o=Math.ceil(f/re),s=o>s?o+1:s+1,a>s&&(a=s,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(s=l.length,a=c.length,s-a<0&&(a=s,n=c,c=l,l=n),r=0;a;)r=(l[--a]=l[a]+c[a]+r)/Pe|0,l[a]%=Pe;for(r&&(l.unshift(r),++i),s=l.length;l[--s]==0;)l.pop();return t.d=l,t.e=i,ne?J(t,f):t}function Pt(e,t,r){if(e!==~~e||e<t||e>r)throw Error(xr+e)}function bt(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)n=e[t]+"",r=re-n.length,r&&(a+=Yt(r)),a+=n;o=e[t],n=o+"",r=re-n.length,r&&(a+=Yt(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var kt=function(){function e(n,i){var a,o=0,s=n.length;for(n=n.slice();s--;)a=n[s]*i+o,n[s]=a%Pe|0,o=a/Pe|0;return o&&n.unshift(o),n}function t(n,i,a,o){var s,l;if(a!=o)l=a>o?1:-1;else for(s=l=0;s<a;s++)if(n[s]!=i[s]){l=n[s]>i[s]?1:-1;break}return l}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*Pe+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var s,l,c,u,f,d,h,p,m,y,g,x,w,P,O,A,S,E,_=n.constructor,M=n.s==i.s?1:-1,C=n.d,k=i.d;if(!n.s)return new _(n);if(!i.s)throw Error(ct+"Division by zero");for(l=n.e-i.e,S=k.length,O=C.length,h=new _(M),p=h.d=[],c=0;k[c]==(C[c]||0);)++c;if(k[c]>(C[c]||0)&&--l,a==null?x=a=_.precision:o?x=a+(de(n)-de(i))+1:x=a,x<0)return new _(0);if(x=x/re+2|0,c=0,S==1)for(u=0,k=k[0],x++;(c<O||u)&&x--;c++)w=u*Pe+(C[c]||0),p[c]=w/k|0,u=w%k|0;else{for(u=Pe/(k[0]+1)|0,u>1&&(k=e(k,u),C=e(C,u),S=k.length,O=C.length),P=S,m=C.slice(0,S),y=m.length;y<S;)m[y++]=0;E=k.slice(),E.unshift(0),A=k[0],k[1]>=Pe/2&&++A;do u=0,s=t(k,m,S,y),s<0?(g=m[0],S!=y&&(g=g*Pe+(m[1]||0)),u=g/A|0,u>1?(u>=Pe&&(u=Pe-1),f=e(k,u),d=f.length,y=m.length,s=t(f,m,d,y),s==1&&(u--,r(f,S<d?E:k,d))):(u==0&&(s=u=1),f=k.slice()),d=f.length,d<y&&f.unshift(0),r(m,f,y),s==-1&&(y=m.length,s=t(k,m,S,y),s<1&&(u++,r(m,S<y?E:k,y))),y=m.length):s===0&&(u++,m=[0]),p[c++]=u,s&&m[0]?m[y++]=C[P]||0:(m=[C[P]],y=1);while((P++<O||m[0]!==void 0)&&x--)}return p[0]||p.shift(),h.e=l,J(h,o?a+de(h)+1:a)}}();function qm(e,t){var r,n,i,a,o,s,l=0,c=0,u=e.constructor,f=u.precision;if(de(e)>16)throw Error(_u+de(e));if(!e.s)return new u(Ve);for(ne=!1,s=f,o=new u(.03125);e.abs().gte(.1);)e=e.times(o),c+=5;for(n=Math.log(cr(2,c))/Math.LN10*2+5|0,s+=n,r=i=a=new u(Ve),u.precision=s;;){if(i=J(i.times(e),s),r=r.times(++l),o=a.plus(kt(i,r,s)),bt(o.d).slice(0,s)===bt(a.d).slice(0,s)){for(;c--;)a=J(a.times(a),s);return u.precision=f,t==null?(ne=!0,J(a,f)):a}a=o}}function de(e){for(var t=e.e*re,r=e.d[0];r>=10;r/=10)t++;return t}function cs(e,t,r){if(t>e.LN10.sd())throw ne=!0,r&&(e.precision=r),Error(ct+"LN10 precision limit exceeded");return J(new e(e.LN10),t)}function Yt(e){for(var t="";e--;)t+="0";return t}function Mn(e,t){var r,n,i,a,o,s,l,c,u,f=1,d=10,h=e,p=h.d,m=h.constructor,y=m.precision;if(h.s<1)throw Error(ct+(h.s?"NaN":"-Infinity"));if(h.eq(Ve))return new m(0);if(t==null?(ne=!1,c=y):c=t,h.eq(10))return t==null&&(ne=!0),cs(m,c);if(c+=d,m.precision=c,r=bt(p),n=r.charAt(0),a=de(h),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)h=h.times(e),r=bt(h.d),n=r.charAt(0),f++;a=de(h),n>1?(h=new m("0."+r),a++):h=new m(n+"."+r.slice(1))}else return l=cs(m,c+2,y).times(a+""),h=Mn(new m(n+"."+r.slice(1)),c-d).plus(l),m.precision=y,t==null?(ne=!0,J(h,y)):h;for(s=o=h=kt(h.minus(Ve),h.plus(Ve),c),u=J(h.times(h),c),i=3;;){if(o=J(o.times(u),c),l=s.plus(kt(o,new m(i),c)),bt(l.d).slice(0,c)===bt(s.d).slice(0,c))return s=s.times(2),a!==0&&(s=s.plus(cs(m,c+2,y).times(a+""))),s=kt(s,new m(f),c),m.precision=y,t==null?(ne=!0,J(s,y)):s;s=l,i+=2}}function vh(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=tn(r/re),e.d=[],n=(r+1)%re,r<0&&(n+=re),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=re;n<i;)e.d.push(+t.slice(n,n+=re));t=t.slice(n),n=re-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),ne&&(e.e>Fi||e.e<-Fi))throw Error(_u+r)}else e.s=0,e.e=0,e.d=[0];return e}function J(e,t,r){var n,i,a,o,s,l,c,u,f=e.d;for(o=1,a=f[0];a>=10;a/=10)o++;if(n=t-o,n<0)n+=re,i=t,c=f[u=0];else{if(u=Math.ceil((n+1)/re),a=f.length,u>=a)return e;for(c=a=f[u],o=1;a>=10;a/=10)o++;n%=re,i=n-re+o}if(r!==void 0&&(a=cr(10,o-i-1),s=c/a%10|0,l=t<0||f[u+1]!==void 0||c%a,l=r<4?(s||l)&&(r==0||r==(e.s<0?3:2)):s>5||s==5&&(r==4||l||r==6&&(n>0?i>0?c/cr(10,o-i):0:f[u-1])%10&1||r==(e.s<0?8:7))),t<1||!f[0])return l?(a=de(e),f.length=1,t=t-a-1,f[0]=cr(10,(re-t%re)%re),e.e=tn(-t/re)||0):(f.length=1,f[0]=e.e=e.s=0),e;if(n==0?(f.length=u,a=1,u--):(f.length=u+1,a=cr(10,re-n),f[u]=i>0?(c/cr(10,o-i)%cr(10,i)|0)*a:0),l)for(;;)if(u==0){(f[0]+=a)==Pe&&(f[0]=1,++e.e);break}else{if(f[u]+=a,f[u]!=Pe)break;f[u--]=0,a=1}for(n=f.length;f[--n]===0;)f.pop();if(ne&&(e.e>Fi||e.e<-Fi))throw Error(_u+de(e));return e}function Wm(e,t){var r,n,i,a,o,s,l,c,u,f,d=e.constructor,h=d.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new d(e),ne?J(t,h):t;if(l=e.d,f=t.d,n=t.e,c=e.e,l=l.slice(),o=c-n,o){for(u=o<0,u?(r=l,o=-o,s=f.length):(r=f,n=c,s=l.length),i=Math.max(Math.ceil(h/re),s)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=l.length,s=f.length,u=i<s,u&&(s=i),i=0;i<s;i++)if(l[i]!=f[i]){u=l[i]<f[i];break}o=0}for(u&&(r=l,l=f,f=r,t.s=-t.s),s=l.length,i=f.length-s;i>0;--i)l[s++]=0;for(i=f.length;i>o;){if(l[--i]<f[i]){for(a=i;a&&l[--a]===0;)l[a]=Pe-1;--l[a],l[i]+=Pe}l[i]-=f[i]}for(;l[--s]===0;)l.pop();for(;l[0]===0;l.shift())--n;return l[0]?(t.d=l,t.e=n,ne?J(t,h):t):new d(0)}function Sr(e,t,r){var n,i=de(e),a=bt(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+Yt(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+Yt(-i-1)+a,r&&(n=r-o)>0&&(a+=Yt(n))):i>=o?(a+=Yt(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+Yt(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=Yt(n))),e.s<0?"-"+a:a}function ph(e,t){if(e.length>t)return e.length=t,!0}function Fm(e){var t,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(xr+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return vh(o,a.toString())}else if(typeof a!="string")throw Error(xr+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,_S.test(a))vh(o,a);else throw Error(xr+a)}if(i.prototype=I,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=Fm,i.config=i.set=TS,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function TS(e){if(!e||typeof e!="object")throw Error(ct+"Object expected");var t,r,n,i=["precision",1,en,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(tn(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(xr+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(xr+r+": "+n);return this}var Tu=Fm(ES);Ve=new Tu(1);const G=Tu;var CS=e=>e,Um={},Ym=e=>e===Um,mh=e=>function t(){return arguments.length===0||arguments.length===1&&Ym(arguments.length<=0?void 0:arguments[0])?t:e(...arguments)},Hm=(e,t)=>e===1?t:mh(function(){for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(o=>o!==Um).length;return a>=e?t(...n):Hm(e-a,mh(function(){for(var o=arguments.length,s=new Array(o),l=0;l<o;l++)s[l]=arguments[l];var c=n.map(u=>Ym(u)?s.shift():u);return t(...c,...s)}))}),Aa=e=>Hm(e.length,e),sl=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},kS=Aa((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(r=>t[r]).map(e)),MS=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return CS;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce((s,l)=>l(s),a(...arguments))}},ll=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),Gm=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every((o,s)=>{var l;return o===((l=t)===null||l===void 0?void 0:l[s])})||(t=i,r=e(...i)),r}};function Vm(e){var t;return e===0?t=1:t=Math.floor(new G(e).abs().log(10).toNumber())+1,t}function Xm(e,t,r){for(var n=new G(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}Aa((e,t,r)=>{var n=+e,i=+t;return n+r*(i-n)});Aa((e,t,r)=>{var n=t-+e;return n=n||1/0,(r-e)/n});Aa((e,t,r)=>{var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});var Zm=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},Qm=(e,t,r)=>{if(e.lte(0))return new G(0);var n=Vm(e.toNumber()),i=new G(10).pow(n),a=e.div(i),o=n!==1?.05:.1,s=new G(Math.ceil(a.div(o).toNumber())).add(r).mul(o),l=s.mul(i);return t?new G(l.toNumber()):new G(Math.ceil(l.toNumber()))},NS=(e,t,r)=>{var n=new G(1),i=new G(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new G(10).pow(Vm(e)-1),i=new G(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new G(Math.floor(e)))}else e===0?i=new G(Math.floor((t-1)/2)):r||(i=new G(Math.floor(e)));var o=Math.floor((t-1)/2),s=MS(kS(l=>i.add(new G(l-o).mul(n)).toNumber()),sl);return s(0,t)},Jm=function(t,r,n,i){var a=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((r-t)/(n-1)))return{step:new G(0),tickMin:new G(0),tickMax:new G(0)};var o=Qm(new G(r).sub(t).div(n-1),i,a),s;t<=0&&r>=0?s=new G(0):(s=new G(t).add(r).div(2),s=s.sub(new G(s).mod(o)));var l=Math.ceil(s.sub(t).div(o).toNumber()),c=Math.ceil(new G(r).sub(s).div(o).toNumber()),u=l+c+1;return u>n?Jm(t,r,n,i,a+1):(u<n&&(c=r>0?c+(n-u):c,l=r>0?l:l+(n-u)),{step:o,tickMin:s.sub(new G(l).mul(o)),tickMax:s.add(new G(c).mul(o))})};function IS(e){var[t,r]=e,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,a=Math.max(n,2),[o,s]=Zm([t,r]);if(o===-1/0||s===1/0){var l=s===1/0?[o,...sl(0,n-1).map(()=>1/0)]:[...sl(0,n-1).map(()=>-1/0),s];return t>r?ll(l):l}if(o===s)return NS(o,n,i);var{step:c,tickMin:u,tickMax:f}=Jm(o,s,a,i,0),d=Xm(u,f.add(new G(.1).mul(c)),c);return t>r?ll(d):d}function DS(e,t){var[r,n]=e,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,[a,o]=Zm([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var s=Math.max(t,2),l=Qm(new G(o).sub(a).div(s-1),i,0),c=[...Xm(new G(a),new G(o),l),o];return i===!1&&(c=c.map(u=>Math.round(u))),r>n?ll(c):c}var $S=Gm(IS),RS=Gm(DS),ey=e=>e.rootProps.maxBarSize,LS=e=>e.rootProps.barGap,ty=e=>e.rootProps.barCategoryGap,BS=e=>e.rootProps.barSize,Yn=e=>e.rootProps.stackOffset,Cu=e=>e.options.chartName,ku=e=>e.rootProps.syncId,ry=e=>e.rootProps.syncMethod,Mu=e=>e.options.eventEmitter,jt={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},Ye={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},Sa=(e,t)=>{if(!(!e||!t))return e!=null&&e.reversed?[t[1],t[0]]:t},KS={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:jt.angleAxisId,includeHidden:!1,name:void 0,reversed:jt.reversed,scale:jt.scale,tick:jt.tick,tickCount:void 0,ticks:void 0,type:jt.type,unit:void 0},zS={allowDataOverflow:Ye.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:Ye.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:Ye.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:Ye.scale,tick:Ye.tick,tickCount:Ye.tickCount,ticks:void 0,type:Ye.type,unit:void 0},qS={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:jt.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:jt.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:jt.scale,tick:jt.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},WS={allowDataOverflow:Ye.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:Ye.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:Ye.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:Ye.scale,tick:Ye.tick,tickCount:Ye.tickCount,ticks:void 0,type:"category",unit:void 0},Nu=(e,t)=>e.polarAxis.angleAxis[t]!=null?e.polarAxis.angleAxis[t]:e.layout.layoutType==="radial"?qS:KS,Iu=(e,t)=>e.polarAxis.radiusAxis[t]!=null?e.polarAxis.radiusAxis[t]:e.layout.layoutType==="radial"?WS:zS,ja=e=>e.polarOptions,Du=j([Bt,Kt,pe],Kp),ny=j([ja,Du],(e,t)=>{if(e!=null)return $e(e.innerRadius,t,0)}),iy=j([ja,Du],(e,t)=>{if(e!=null)return $e(e.outerRadius,t,t*.8)}),FS=e=>{if(e==null)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]},ay=j([ja],FS);j([Nu,ay],Sa);var oy=j([Du,ny,iy],(e,t,r)=>{if(!(e==null||t==null||r==null))return[t,r]});j([Iu,oy],Sa);var US=j([F,ja,ny,iy,Bt,Kt],(e,t,r,n,i,a)=>{if(!(e!=="centric"&&e!=="radial"||t==null||r==null||n==null)){var{cx:o,cy:s,startAngle:l,endAngle:c}=t;return{cx:$e(o,i,i/2),cy:$e(s,a,a/2),innerRadius:r,outerRadius:n,startAngle:l,endAngle:c,clockWise:!1}}}),le=(e,t)=>t,Hn=(e,t,r)=>r;function yh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ui(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?yh(Object(r),!0).forEach(function(n){YS(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function YS(e,t,r){return(t=HS(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function HS(e){var t=GS(e,"string");return typeof t=="symbol"?t:t+""}function GS(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var ul=[0,"auto"],Fe={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},qt=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return r??Fe},Ue={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:ul,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:va},ir=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return r??Ue},VS={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},$u=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return r??VS},ue=(e,t,r)=>{switch(t){case"xAxis":return qt(e,r);case"yAxis":return ir(e,r);case"zAxis":return $u(e,r);case"angleAxis":return Nu(e,r);case"radiusAxis":return Iu(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},XS=(e,t,r)=>{switch(t){case"xAxis":return qt(e,r);case"yAxis":return ir(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},Gn=(e,t,r)=>{switch(t){case"xAxis":return qt(e,r);case"yAxis":return ir(e,r);case"angleAxis":return Nu(e,r);case"radiusAxis":return Iu(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},sy=e=>e.graphicalItems.countOfBars>0;function Ru(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var Vn=e=>e.graphicalItems.cartesianItems,ZS=j([le,Hn],Ru),Lu=(e,t,r)=>e.filter(r).filter(n=>(t==null?void 0:t.includeHidden)===!0?!0:!n.hide),Xn=j([Vn,ue,ZS],Lu),ly=e=>e.filter(t=>t.stackId===void 0),QS=j([Xn],ly),Bu=e=>e.map(t=>t.data).filter(Boolean).flat(1),JS=j([Xn],Bu),Ku=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},Ea=j([JS,Oa],Ku),zu=(e,t,r)=>(t==null?void 0:t.dataKey)!=null?e.map(n=>({value:Q(n,t.dataKey)})):r.length>0?r.map(n=>n.dataKey).flatMap(n=>e.map(i=>({value:Q(i,n)}))):e.map(n=>({value:n})),_a=j([Ea,ue,Xn],zu);function uy(e,t){switch(e){case"xAxis":return t.direction==="x";case"yAxis":return t.direction==="y";default:return!1}}function Cr(e){return e.filter(t=>wt(t)||t instanceof Date).map(Number).filter(t=>Ke(t)===!1)}function ej(e,t,r){return!r||typeof t!="number"||Ke(t)?[]:r.length?Cr(r.flatMap(n=>{var i=Q(e,n.dataKey),a,o;if(Array.isArray(i)?[a,o]=i:a=o=i,!(!Te(a)||!Te(o)))return[t-a,t+o]})):[]}var cy=(e,t,r)=>{var n={},i=t.reduce((a,o)=>(o.stackId==null||(a[o.stackId]==null&&(a[o.stackId]=[]),a[o.stackId].push(o)),a),n);return Object.fromEntries(Object.entries(i).map(a=>{var[o,s]=a,l=s.map(c=>c.dataKey);return[o,{stackedData:e1(e,l,r),graphicalItems:s}]}))},Nn=j([Ea,Xn,Yn],cy),fy=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if(r!=="zAxis"){var a=i1(e,n,i);if(!(a!=null&&a[0]===0&&a[1]===0))return a}},tj=j([Nn,Tr,le],fy),dy=(e,t,r,n)=>r.length>0?e.flatMap(i=>r.flatMap(a=>{var o,s,l=(o=a.errorBars)===null||o===void 0?void 0:o.filter(u=>uy(n,u)),c=Q(i,(s=t.dataKey)!==null&&s!==void 0?s:a.dataKey);return{value:c,errorDomain:ej(i,c,l)}})).filter(Boolean):(t==null?void 0:t.dataKey)!=null?e.map(i=>({value:Q(i,t.dataKey),errorDomain:[]})):e.map(i=>({value:i,errorDomain:[]})),rj=j(Ea,ue,QS,le,dy);function nj(e){var{value:t}=e;if(wt(t)||t instanceof Date)return t}var ij=e=>{var t=e.flatMap(n=>[n.value,n.errorDomain]).flat(1),r=Cr(t);if(r.length!==0)return[Math.min(...r),Math.max(...r)]},aj=(e,t,r)=>{var n=e.map(nj).filter(i=>i!=null);return r&&(t.dataKey==null||t.allowDuplicatedCategory&&Rv(n))?am(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))},qu=e=>{var t;if(e==null||!("domain"in e))return ul;if(e.domain!=null)return e.domain;if(e.ticks!=null){if(e.type==="number"){var r=Cr(e.ticks);return[Math.min(...r),Math.max(...r)]}if(e.type==="category")return e.ticks.map(String)}return(t=e==null?void 0:e.domain)!==null&&t!==void 0?t:ul},Wu=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r.filter(Boolean);if(i.length!==0){var a=i.flat(),o=Math.min(...a),s=Math.max(...a);return[o,s]}},hy=e=>e.referenceElements.dots,rn=(e,t,r)=>e.filter(n=>n.ifOverflow==="extendDomain").filter(n=>t==="xAxis"?n.xAxisId===r:n.yAxisId===r),oj=j([hy,le,Hn],rn),vy=e=>e.referenceElements.areas,sj=j([vy,le,Hn],rn),py=e=>e.referenceElements.lines,lj=j([py,le,Hn],rn),my=(e,t)=>{var r=Cr(e.map(n=>t==="xAxis"?n.x:n.y));if(r.length!==0)return[Math.min(...r),Math.max(...r)]},uj=j(oj,le,my),yy=(e,t)=>{var r=Cr(e.flatMap(n=>[t==="xAxis"?n.x1:n.y1,t==="xAxis"?n.x2:n.y2]));if(r.length!==0)return[Math.min(...r),Math.max(...r)]},cj=j([sj,le],yy),gy=(e,t)=>{var r=Cr(e.map(n=>t==="xAxis"?n.x:n.y));if(r.length!==0)return[Math.min(...r),Math.max(...r)]},fj=j(lj,le,gy),dj=j(uj,fj,cj,(e,t,r)=>Wu(e,r,t)),by=j([ue],qu),Fu=(e,t,r,n,i)=>{var a=SS(t,e.allowDataOverflow);return a??jS(t,Wu(r,i,ij(n)),e.allowDataOverflow)},hj=j([ue,by,tj,rj,dj],Fu),vj=[0,1],Uu=(e,t,r,n,i,a,o)=>{if(!(e==null||r==null||r.length===0)){var{dataKey:s,type:l}=e,c=At(t,a);return c&&s==null?am(0,r.length):l==="category"?aj(n,e,c):i==="expand"?vj:o}},Yu=j([ue,F,Ea,_a,Yn,le,hj],Uu),xy=(e,t,r,n,i)=>{if(e!=null){var{scale:a,type:o}=e;if(a==="auto")return t==="radial"&&i==="radiusAxis"?"band":t==="radial"&&i==="angleAxis"?"linear":o==="category"&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?"point":o==="category"?"band":"linear";if(typeof a=="string"){var s="scale".concat(Rn(a));return s in bn?s:"point"}}},nn=j([ue,F,sy,Cu,le],xy);function pj(e){if(e!=null){if(e in bn)return bn[e]();var t="scale".concat(Rn(e));if(t in bn)return bn[t]()}}function Hu(e,t,r,n){if(!(r==null||n==null)){if(typeof e.scale=="function")return e.scale.copy().domain(r).range(n);var i=pj(t);if(i!=null){var a=i.domain(r).range(n);return Vw(a),a}}}var Gu=(e,t,r)=>{var n=qu(t);if(!(r!=="auto"&&r!=="linear")){if(t!=null&&t.tickCount&&Array.isArray(n)&&(n[0]==="auto"||n[1]==="auto")&&Zr(e))return $S(e,t.tickCount,t.allowDecimals);if(t!=null&&t.tickCount&&t.type==="number"&&Zr(e))return RS(e,t.tickCount,t.allowDecimals)}},Vu=j([Yu,Gn,nn],Gu),Xu=(e,t,r,n)=>{if(n!=="angleAxis"&&(e==null?void 0:e.type)==="number"&&Zr(t)&&Array.isArray(r)&&r.length>0){var i=t[0],a=r[0],o=t[1],s=r[r.length-1];return[Math.min(i,a),Math.max(o,s)]}return t},mj=j([ue,Yu,Vu,le],Xu),yj=j(_a,ue,(e,t)=>{if(!(!t||t.type!=="number")){var r=1/0,n=Array.from(Cr(e.map(s=>s.value))).sort((s,l)=>s-l);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(i===0)return 1/0;for(var a=0;a<n.length-1;a++){var o=n[a+1]-n[a];r=Math.min(r,o)}return r/i}}),wy=j(yj,F,ty,pe,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!Te(e))return 0;var a=t==="vertical"?n.height:n.width;if(i==="gap")return e*a/2;if(i==="no-gap"){var o=$e(r,e*a),s=e*a/2;return s-o-(s-o)/a*o}return 0}),gj=(e,t)=>{var r=qt(e,t);return r==null||typeof r.padding!="string"?0:wy(e,"xAxis",t,r.padding)},bj=(e,t)=>{var r=ir(e,t);return r==null||typeof r.padding!="string"?0:wy(e,"yAxis",t,r.padding)},xj=j(qt,gj,(e,t)=>{var r,n;if(e==null)return{left:0,right:0};var{padding:i}=e;return typeof i=="string"?{left:t,right:t}:{left:((r=i.left)!==null&&r!==void 0?r:0)+t,right:((n=i.right)!==null&&n!==void 0?n:0)+t}}),wj=j(ir,bj,(e,t)=>{var r,n;if(e==null)return{top:0,bottom:0};var{padding:i}=e;return typeof i=="string"?{top:t,bottom:t}:{top:((r=i.top)!==null&&r!==void 0?r:0)+t,bottom:((n=i.bottom)!==null&&n!==void 0?n:0)+t}}),Pj=j([pe,xj,ma,pa,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]}),Oj=j([pe,F,wj,ma,pa,(e,t,r)=>r],(e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:t==="horizontal"?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),Zn=(e,t,r,n)=>{var i;switch(t){case"xAxis":return Pj(e,r,n);case"yAxis":return Oj(e,r,n);case"zAxis":return(i=$u(e,r))===null||i===void 0?void 0:i.range;case"angleAxis":return ay(e);case"radiusAxis":return oy(e,r);default:return}},Py=j([ue,Zn],Sa),an=j([ue,nn,mj,Py],Hu);j(Xn,le,(e,t)=>e.flatMap(r=>{var n;return(n=r.errorBars)!==null&&n!==void 0?n:[]}).filter(r=>uy(t,r)));function Oy(e,t){return e.id<t.id?-1:e.id>t.id?1:0}var Ta=(e,t)=>t,Ca=(e,t,r)=>r,Aj=j(Zl,Ta,Ca,(e,t,r)=>e.filter(n=>n.orientation===t).filter(n=>n.mirror===r).sort(Oy)),Sj=j(Ql,Ta,Ca,(e,t,r)=>e.filter(n=>n.orientation===t).filter(n=>n.mirror===r).sort(Oy)),Ay=(e,t)=>({width:e.width,height:t.height}),jj=(e,t)=>{var r=typeof t.width=="number"?t.width:va;return{width:r,height:e.height}},Sy=j(pe,qt,Ay),Ej=(e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}},_j=(e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}},Tj=j(Kt,pe,Aj,Ta,Ca,(e,t,r,n,i)=>{var a={},o;return r.forEach(s=>{var l=Ay(t,s);o==null&&(o=Ej(t,n,e));var c=n==="top"&&!i||n==="bottom"&&i;a[s.id]=o-Number(c)*l.height,o+=(c?-1:1)*l.height}),a}),Cj=j(Bt,pe,Sj,Ta,Ca,(e,t,r,n,i)=>{var a={},o;return r.forEach(s=>{var l=jj(t,s);o==null&&(o=_j(t,n,e));var c=n==="left"&&!i||n==="right"&&i;a[s.id]=o-Number(c)*l.width,o+=(c?-1:1)*l.width}),a}),kj=(e,t)=>{var r=pe(e),n=qt(e,t);if(n!=null){var i=Tj(e,n.orientation,n.mirror),a=i[t];return a==null?{x:r.left,y:0}:{x:r.left,y:a}}},Mj=(e,t)=>{var r=pe(e),n=ir(e,t);if(n!=null){var i=Cj(e,n.orientation,n.mirror),a=i[t];return a==null?{x:0,y:r.top}:{x:a,y:r.top}}},jy=j(pe,ir,(e,t)=>{var r=typeof t.width=="number"?t.width:va;return{width:r,height:e.height}}),gh=(e,t,r)=>{switch(t){case"xAxis":return Sy(e,r).width;case"yAxis":return jy(e,r).height;default:return}},Ey=(e,t,r,n)=>{if(r!=null){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,s=At(e,n),l=t.map(c=>c.value);if(o&&s&&a==="category"&&i&&Rv(l))return l}},Zu=j([F,_a,ue,le],Ey),_y=(e,t,r,n)=>{if(!(r==null||r.dataKey==null)){var{type:i,scale:a}=r,o=At(e,n);if(o&&(i==="number"||a!=="auto"))return t.map(s=>s.value)}},Qu=j([F,_a,Gn,le],_y),bh=j([F,XS,nn,an,Zu,Qu,Zn,Vu,le],(e,t,r,n,i,a,o,s,l)=>{if(t==null)return null;var c=At(e,l);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:l,categoricalDomain:a,duplicateDomain:i,isCategorical:c,niceTicks:s,range:o,realScaleType:r,scale:n}}),Nj=(e,t,r,n,i,a,o,s,l)=>{if(!(t==null||n==null)){var c=At(e,l),{type:u,ticks:f,tickCount:d}=t,h=r==="scaleBand"&&typeof n.bandwidth=="function"?n.bandwidth()/2:2,p=u==="category"&&n.bandwidth?n.bandwidth()/h:0;p=l==="angleAxis"&&a!=null&&a.length>=2?Oe(a[0]-a[1])*2*p:p;var m=f||i;if(m){var y=m.map((g,x)=>{var w=o?o.indexOf(g):g;return{index:x,coordinate:n(w)+p,value:g,offset:p}});return y.filter(g=>!Ke(g.coordinate))}return c&&s?s.map((g,x)=>({coordinate:n(g)+p,value:g,index:x,offset:p})):n.ticks?n.ticks(d).map(g=>({coordinate:n(g)+p,value:g,offset:p})):n.domain().map((g,x)=>({coordinate:n(g)+p,value:o?o[g]:g,index:x,offset:p}))}},Ty=j([F,Gn,nn,an,Vu,Zn,Zu,Qu,le],Nj),Ij=(e,t,r,n,i,a,o)=>{if(!(t==null||r==null||n==null||n[0]===n[1])){var s=At(e,o),{tickCount:l}=t,c=0;return c=o==="angleAxis"&&(n==null?void 0:n.length)>=2?Oe(n[0]-n[1])*2*c:c,s&&a?a.map((u,f)=>({coordinate:r(u)+c,value:u,index:f,offset:c})):r.ticks?r.ticks(l).map(u=>({coordinate:r(u)+c,value:u,offset:c})):r.domain().map((u,f)=>({coordinate:r(u)+c,value:i?i[u]:u,index:f,offset:c}))}},Ot=j([F,Gn,an,Zn,Zu,Qu,le],Ij),ft=j(ue,an,(e,t)=>{if(!(e==null||t==null))return Ui(Ui({},e),{},{scale:t})}),Dj=j([ue,nn,Yu,Py],Hu);j((e,t,r)=>$u(e,r),Dj,(e,t)=>{if(!(e==null||t==null))return Ui(Ui({},e),{},{scale:t})});var $j=j([F,Zl,Ql],(e,t,r)=>{switch(e){case"horizontal":return t.some(n=>n.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(n=>n.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}}),Cy=e=>e.options.defaultTooltipEventType,ky=e=>e.options.validateTooltipEventTypes;function My(e,t,r){if(e==null)return t;var n=e?"axis":"item";return r==null?t:r.includes(n)?n:t}function Ju(e,t){var r=Cy(e),n=ky(e);return My(t,r,n)}function Rj(e){return D(t=>Ju(t,e))}var Ny=(e,t)=>{var r,n=Number(t);if(!(Ke(n)||t==null))return n>=0?e==null||(r=e[n])===null||r===void 0?void 0:r.value:void 0},Lj=e=>e.tooltip.settings,Ht={active:!1,index:null,dataKey:void 0,coordinate:void 0},Bj={itemInteraction:{click:Ht,hover:Ht},axisInteraction:{click:Ht,hover:Ht},keyboardInteraction:Ht,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},Iy=dt({name:"tooltip",initialState:Bj,reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push(t.payload)},removeTooltipEntrySettings(e,t){var r=Ct(e).tooltipItemPayloads.indexOf(t.payload);r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:Kj,removeTooltipEntrySettings:zj,setTooltipSettingsState:qj,setActiveMouseOverItemIndex:Dy,mouseLeaveItem:Wj,mouseLeaveChart:$y,setActiveClickItemIndex:Fj,setMouseOverAxisIndex:Ry,setMouseClickAxisIndex:Uj,setSyncInteraction:cl,setKeyboardInteraction:fl}=Iy.actions,Yj=Iy.reducer;function xh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function di(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?xh(Object(r),!0).forEach(function(n){Hj(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Hj(e,t,r){return(t=Gj(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Gj(e){var t=Vj(e,"string");return typeof t=="symbol"?t:t+""}function Vj(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Xj(e,t,r){return t==="axis"?r==="click"?e.axisInteraction.click:e.axisInteraction.hover:r==="click"?e.itemInteraction.click:e.itemInteraction.hover}function Zj(e){return e.index!=null}var Ly=(e,t,r,n)=>{if(t==null)return Ht;var i=Xj(e,t,r);if(i==null)return Ht;if(i.active)return i;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&e.syncInteraction.index!=null)return e.syncInteraction;var a=e.settings.active===!0;if(Zj(i)){if(a)return di(di({},i),{},{active:!0})}else if(n!=null)return{active:!0,coordinate:void 0,dataKey:void 0,index:n};return di(di({},Ht),{},{coordinate:i.coordinate})},ec=(e,t)=>{var r=e==null?void 0:e.index;if(r==null)return null;var n=Number(r);if(!Te(n))return r;var i=0,a=1/0;return t.length>0&&(a=t.length-1),String(Math.max(i,Math.min(n,a)))},By=(e,t,r,n,i,a,o,s)=>{if(!(a==null||s==null)){var l=o[0],c=l==null?void 0:s(l.positions,a);if(c!=null)return c;var u=i==null?void 0:i[Number(a)];if(u)switch(r){case"horizontal":return{x:u.coordinate,y:(n.top+t)/2};default:return{x:(n.left+e)/2,y:u.coordinate}}}},Ky=(e,t,r,n)=>{if(t==="axis")return e.tooltipItemPayloads;if(e.tooltipItemPayloads.length===0)return[];var i;return r==="hover"?i=e.itemInteraction.hover.dataKey:i=e.itemInteraction.click.dataKey,i==null&&n!=null?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(a=>{var o;return((o=a.settings)===null||o===void 0?void 0:o.dataKey)===i})},Qn=e=>e.options.tooltipPayloadSearcher,on=e=>e.tooltip;function wh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ph(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?wh(Object(r),!0).forEach(function(n){Qj(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Qj(e,t,r){return(t=Jj(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Jj(e){var t=eE(e,"string");return typeof t=="symbol"?t:t+""}function eE(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function tE(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}function rE(e,t){return e??t}var zy=(e,t,r,n,i,a,o)=>{if(!(t==null||a==null)){var{chartData:s,computedData:l,dataStartIndex:c,dataEndIndex:u}=r,f=[];return e.reduce((d,h)=>{var p,{dataDefinedOnItem:m,settings:y}=h,g=rE(m,s),x=tE(g,c,u),w=(p=y==null?void 0:y.dataKey)!==null&&p!==void 0?p:n==null?void 0:n.dataKey,P=y==null?void 0:y.nameKey,O;if(n!=null&&n.dataKey&&Array.isArray(x)&&!Array.isArray(x[0])&&o==="axis"?O=Lv(x,n.dataKey,i):O=a(x,t,l,P),Array.isArray(O))O.forEach(S=>{var E=Ph(Ph({},y),{},{name:S.name,unit:S.unit,color:void 0,fill:void 0});d.push(rd({tooltipEntrySettings:E,dataKey:S.dataKey,payload:S.payload,value:Q(S.payload,S.dataKey),name:S.name}))});else{var A;d.push(rd({tooltipEntrySettings:y,dataKey:w,payload:O,value:Q(O,w),name:(A=Q(O,P))!==null&&A!==void 0?A:y==null?void 0:y.name}))}return d},f)}},Se=e=>{var t=F(e);return t==="horizontal"?"xAxis":t==="vertical"?"yAxis":t==="centric"?"angleAxis":"radiusAxis"},sn=e=>e.tooltip.settings.axisId,Ce=e=>{var t=Se(e),r=sn(e);return Gn(e,t,r)},tc=j([Ce,F,sy,Cu,Se],xy),nE=j([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),iE=j([Se,sn],Ru),ka=j([nE,Ce,iE],Lu),aE=j([ka],Bu),kr=j([aE,Tr],Ku),rc=j([kr,Ce,ka],zu),oE=j([Ce],qu),sE=j([kr,ka,Yn],cy),lE=j([sE,Tr,Se],fy),uE=j([ka],ly),cE=j([kr,Ce,uE,Se],dy),fE=j([hy,Se,sn],rn),dE=j([fE,Se],my),hE=j([vy,Se,sn],rn),vE=j([hE,Se],yy),pE=j([py,Se,sn],rn),mE=j([pE,Se],gy),yE=j([dE,mE,vE],Wu),gE=j([Ce,oE,lE,cE,yE],Fu),qy=j([Ce,F,kr,rc,Yn,Se,gE],Uu),bE=j([qy,Ce,tc],Gu),xE=j([Ce,qy,bE,Se],Xu),Wy=e=>{var t=Se(e),r=sn(e),n=!1;return Zn(e,t,r,n)},Fy=j([Ce,Wy],Sa),Uy=j([Ce,tc,xE,Fy],Hu),wE=j([F,rc,Ce,Se],Ey),PE=j([F,rc,Ce,Se],_y),OE=(e,t,r,n,i,a,o,s)=>{if(t){var{type:l}=t,c=At(e,s);if(n){var u=r==="scaleBand"&&n.bandwidth?n.bandwidth()/2:2,f=l==="category"&&n.bandwidth?n.bandwidth()/u:0;return f=s==="angleAxis"&&i!=null&&(i==null?void 0:i.length)>=2?Oe(i[0]-i[1])*2*f:f,c&&o?o.map((d,h)=>({coordinate:n(d)+f,value:d,index:h,offset:f})):n.domain().map((d,h)=>({coordinate:n(d)+f,value:a?a[d]:d,index:h,offset:f}))}}},Wt=j([F,Ce,tc,Uy,Wy,wE,PE,Se],OE),nc=j([Cy,ky,Lj],(e,t,r)=>My(r.shared,e,t)),Yy=e=>e.tooltip.settings.trigger,ic=e=>e.tooltip.settings.defaultIndex,Ma=j([on,nc,Yy,ic],Ly),Jt=j([Ma,kr],ec),Hy=j([Wt,Jt],Ny),Gy=j([Ma],e=>{if(e)return e.dataKey}),Vy=j([on,nc,Yy,ic],Ky),AE=j([Bt,Kt,F,pe,Wt,ic,Vy,Qn],By),SE=j([Ma,AE],(e,t)=>e!=null&&e.coordinate?e.coordinate:t),jE=j([Ma],e=>e.active),EE=j([Vy,Jt,Tr,Ce,Hy,Qn,nc],zy),_E=j([EE],e=>{if(e!=null){var t=e.map(r=>r.payload).filter(r=>r!=null);return Array.from(new Set(t))}});function Oh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ah(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Oh(Object(r),!0).forEach(function(n){TE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Oh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function TE(e,t,r){return(t=CE(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function CE(e){var t=kE(e,"string");return typeof t=="symbol"?t:t+""}function kE(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var ME=()=>D(Ce),NE=()=>{var e=ME(),t=D(Wt),r=D(Uy);return Ar(Ah(Ah({},e),{},{scale:r}),t)},Xy=()=>D(Cu),ac=(e,t)=>t,Zy=(e,t,r)=>r,oc=(e,t,r,n)=>n,IE=j(Wt,e=>sa(e,t=>t.coordinate)),sc=j([on,ac,Zy,oc],Ly),Qy=j([sc,kr],ec),DE=(e,t,r)=>{if(t!=null){var n=on(e);return t==="axis"?r==="hover"?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:r==="hover"?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},Jy=j([on,ac,Zy,oc],Ky),Yi=j([Bt,Kt,F,pe,Wt,oc,Jy,Qn],By),$E=j([sc,Yi],(e,t)=>{var r;return(r=e.coordinate)!==null&&r!==void 0?r:t}),eg=j(Wt,Qy,Ny),RE=j([Jy,Qy,Tr,Ce,eg,Qn,ac],zy),LE=j([sc],e=>({isActive:e.active,activeIndex:e.index})),BE=(e,t,r,n,i,a,o,s)=>{if(!(!e||!t||!n||!i||!a)){var l=a1(e.chartX,e.chartY,t,r,s);if(l){var c=s1(l,t),u=Hw(c,o,a,n,i),f=o1(t,a,u,l);return{activeIndex:String(u),activeCoordinate:f}}}};function dl(){return dl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},dl.apply(null,arguments)}function Sh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function hi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Sh(Object(r),!0).forEach(function(n){KE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function KE(e,t,r){return(t=zE(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zE(e){var t=qE(e,"string");return typeof t=="symbol"?t:t+""}function qE(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function WE(e){var{coordinate:t,payload:r,index:n,offset:i,tooltipAxisBandSize:a,layout:o,cursor:s,tooltipEventType:l,chartName:c}=e,u=t,f=r,d=n;if(!s||!u||c!=="ScatterChart"&&l!=="axis")return null;var h,p;if(c==="ScatterChart")h=u,p=iP;else if(c==="BarChart")h=aP(o,u,i,a),p=tm;else if(o==="radial"){var{cx:m,cy:y,radius:g,startAngle:x,endAngle:w}=rm(u);h={cx:m,cy:y,startAngle:x,endAngle:w,innerRadius:g,outerRadius:g},p=im}else h={points:UP(o,u,i)},p=qr;var P=typeof s=="object"&&"className"in s?s.className:void 0,O=hi(hi(hi(hi({stroke:"#ccc",pointerEvents:"none"},i),h),z(s,!1)),{},{payload:f,payloadIndex:d,className:W("recharts-tooltip-cursor",P)});return v.isValidElement(s)?v.cloneElement(s,O):v.createElement(p,O)}function FE(e){var t=NE(),r=Hp(),n=zn(),i=Xy();return v.createElement(WE,dl({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:r,layout:n,tooltipAxisBandSize:t,chartName:i}))}var tg=v.createContext(null),UE=()=>v.useContext(tg),fs={exports:{}},jh;function YE(){return jh||(jh=1,function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(l,c,u){this.fn=l,this.context=c,this.once=u||!1}function a(l,c,u,f,d){if(typeof u!="function")throw new TypeError("The listener must be a function");var h=new i(u,f||l,d),p=r?r+c:c;return l._events[p]?l._events[p].fn?l._events[p]=[l._events[p],h]:l._events[p].push(h):(l._events[p]=h,l._eventsCount++),l}function o(l,c){--l._eventsCount===0?l._events=new n:delete l._events[c]}function s(){this._events=new n,this._eventsCount=0}s.prototype.eventNames=function(){var c=[],u,f;if(this._eventsCount===0)return c;for(f in u=this._events)t.call(u,f)&&c.push(r?f.slice(1):f);return Object.getOwnPropertySymbols?c.concat(Object.getOwnPropertySymbols(u)):c},s.prototype.listeners=function(c){var u=r?r+c:c,f=this._events[u];if(!f)return[];if(f.fn)return[f.fn];for(var d=0,h=f.length,p=new Array(h);d<h;d++)p[d]=f[d].fn;return p},s.prototype.listenerCount=function(c){var u=r?r+c:c,f=this._events[u];return f?f.fn?1:f.length:0},s.prototype.emit=function(c,u,f,d,h,p){var m=r?r+c:c;if(!this._events[m])return!1;var y=this._events[m],g=arguments.length,x,w;if(y.fn){switch(y.once&&this.removeListener(c,y.fn,void 0,!0),g){case 1:return y.fn.call(y.context),!0;case 2:return y.fn.call(y.context,u),!0;case 3:return y.fn.call(y.context,u,f),!0;case 4:return y.fn.call(y.context,u,f,d),!0;case 5:return y.fn.call(y.context,u,f,d,h),!0;case 6:return y.fn.call(y.context,u,f,d,h,p),!0}for(w=1,x=new Array(g-1);w<g;w++)x[w-1]=arguments[w];y.fn.apply(y.context,x)}else{var P=y.length,O;for(w=0;w<P;w++)switch(y[w].once&&this.removeListener(c,y[w].fn,void 0,!0),g){case 1:y[w].fn.call(y[w].context);break;case 2:y[w].fn.call(y[w].context,u);break;case 3:y[w].fn.call(y[w].context,u,f);break;case 4:y[w].fn.call(y[w].context,u,f,d);break;default:if(!x)for(O=1,x=new Array(g-1);O<g;O++)x[O-1]=arguments[O];y[w].fn.apply(y[w].context,x)}}return!0},s.prototype.on=function(c,u,f){return a(this,c,u,f,!1)},s.prototype.once=function(c,u,f){return a(this,c,u,f,!0)},s.prototype.removeListener=function(c,u,f,d){var h=r?r+c:c;if(!this._events[h])return this;if(!u)return o(this,h),this;var p=this._events[h];if(p.fn)p.fn===u&&(!d||p.once)&&(!f||p.context===f)&&o(this,h);else{for(var m=0,y=[],g=p.length;m<g;m++)(p[m].fn!==u||d&&!p[m].once||f&&p[m].context!==f)&&y.push(p[m]);y.length?this._events[h]=y.length===1?y[0]:y:o(this,h)}return this},s.prototype.removeAllListeners=function(c){var u;return c?(u=r?r+c:c,this._events[u]&&o(this,u)):(this._events=new n,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=r,s.EventEmitter=s,e.exports=s}(fs)),fs.exports}var HE=YE();const GE=Lt(HE);var In=new GE,hl="recharts.syncEvent.tooltip",Eh="recharts.syncEvent.brush";function Na(e,t){if(t){var r=Number.parseInt(t,10);if(!Ke(r))return e==null?void 0:e[r]}}var VE={chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},rg=dt({name:"options",initialState:VE,reducers:{createEventEmitter:e=>{e.eventEmitter==null&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),XE=rg.reducer,{createEventEmitter:ZE}=rg.actions;function QE(e){return e.tooltip.syncInteraction}var JE={chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},ng=dt({name:"chartData",initialState:JE,reducers:{setChartData(e,t){if(e.chartData=t.payload,t.payload==null){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;r!=null&&(e.dataStartIndex=r),n!=null&&(e.dataEndIndex=n)}}}),{setChartData:_h,setDataStartEndIndexes:e_,setComputedData:LD}=ng.actions,t_=ng.reducer,ig=()=>{};function r_(){var e=D(ku),t=D(Mu),r=ie(),n=D(ry),i=D(Wt),a=zn(),o=Jl(),s=D(l=>l.rootProps.className);v.useEffect(()=>{if(e==null)return ig;var l=(c,u,f)=>{if(t!==f&&e===c){if(n==="index"){r(u);return}if(i!=null){var d;if(typeof n=="function"){var h={activeTooltipIndex:u.payload.index==null?void 0:Number(u.payload.index),isTooltipActive:u.payload.active,activeIndex:u.payload.index==null?void 0:Number(u.payload.index),activeLabel:u.payload.label,activeDataKey:u.payload.dataKey,activeCoordinate:u.payload.coordinate},p=n(i,h);d=i[p]}else n==="value"&&(d=i.find(A=>String(A.value)===u.payload.label));var{coordinate:m}=u.payload;if(d==null||u.payload.active===!1||m==null||o==null){r(cl({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}));return}var{x:y,y:g}=m,x=Math.min(y,o.x+o.width),w=Math.min(g,o.y+o.height),P={x:a==="horizontal"?d.coordinate:x,y:a==="horizontal"?w:d.coordinate},O=cl({active:u.payload.active,coordinate:P,dataKey:u.payload.dataKey,index:String(d.index),label:u.payload.label});r(O)}}};return In.on(hl,l),()=>{In.off(hl,l)}},[s,r,t,e,n,i,a,o])}function n_(){var e=D(ku),t=D(Mu),r=ie();v.useEffect(()=>{if(e==null)return ig;var n=(i,a,o)=>{t!==o&&e===i&&r(e_(a))};return In.on(Eh,n),()=>{In.off(Eh,n)}},[r,t,e])}function i_(){var e=ie();v.useEffect(()=>{e(ZE())},[e]),r_(),n_()}function a_(e,t,r,n,i,a){var o=D(d=>DE(d,e,t)),s=D(Mu),l=D(ku),c=D(ry),u=D(QE),f=u==null?void 0:u.active;v.useEffect(()=>{if(!f&&l!=null&&s!=null){var d=cl({active:a,coordinate:r,dataKey:o,index:i,label:typeof n=="number"?String(n):n});In.emit(hl,l,d,s)}},[f,r,o,i,n,s,l,c,a])}function Th(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ch(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Th(Object(r),!0).forEach(function(n){o_(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Th(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function o_(e,t,r){return(t=s_(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s_(e){var t=l_(e,"string");return typeof t=="symbol"?t:t+""}function l_(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function u_(e){return e.dataKey}function c_(e,t){return v.isValidElement(e)?v.cloneElement(e,t):typeof e=="function"?v.createElement(e,t):v.createElement($1,t)}var kh=[],f_={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!tr.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function Ir(e){var t=Je(e,f_),{active:r,allowEscapeViewBox:n,animationDuration:i,animationEasing:a,content:o,filterNull:s,isAnimationActive:l,offset:c,payloadUniqBy:u,position:f,reverseDirection:d,useTranslate3d:h,wrapperStyle:p,cursor:m,shared:y,trigger:g,defaultIndex:x,portal:w,axisId:P}=t,O=ie(),A=typeof x=="number"?String(x):x;v.useEffect(()=>{O(qj({shared:y,trigger:g,axisId:P,active:r,defaultIndex:A}))},[O,y,g,P,r,A]);var S=Jl(),E=Zp(),_=Rj(y),{activeIndex:M,isActive:C}=D(tt=>LE(tt,_,g,A)),k=D(tt=>RE(tt,_,g,A)),R=D(tt=>eg(tt,_,g,A)),B=D(tt=>$E(tt,_,g,A)),U=k,X=UE(),K=r??C,[he,se]=pp([U,K]),ze=_==="axis"?R:void 0;a_(_,g,B,ze,M,K);var et=w??X;if(et==null)return null;var L=U??kh;K||(L=kh),s&&L.length&&(L=cp(U.filter(tt=>tt.value!=null&&(tt.hide!==!0||t.includeHidden)),u,u_));var je=L.length>0,or=v.createElement(W1,{allowEscapeViewBox:n,animationDuration:i,animationEasing:a,isAnimationActive:l,active:K,coordinate:B,hasPayload:je,offset:c,position:f,reverseDirection:d,useTranslate3d:h,viewBox:S,wrapperStyle:p,lastBoundingBox:he,innerRef:se,hasPortalFromProps:!!w},c_(o,Ch(Ch({},t),{},{payload:L,label:ze,active:K,coordinate:B,accessibilityLayer:E})));return v.createElement(v.Fragment,null,Iv.createPortal(or,et),K&&v.createElement(FE,{cursor:m,tooltipEventType:_,coordinate:B,payload:U,index:M}))}var ds={},hs={},Mh;function d_(){return Mh||(Mh=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r,n=0,i={}){typeof i!="object"&&(i={});let a=null,o=null,s=null,l=0,c=null,u;const{leading:f=!1,trailing:d=!0,maxWait:h}=i,p="maxWait"in i,m=p?Math.max(Number(h)||0,n):0,y=S=>(a!==null&&(u=r.apply(o,a)),a=o=null,l=S,u),g=S=>(l=S,c=setTimeout(O,n),f&&a!==null?y(S):u),x=S=>(c=null,d&&a!==null?y(S):u),w=S=>{if(s===null)return!0;const E=S-s,_=E>=n||E<0,M=p&&S-l>=m;return _||M},P=S=>{const E=s===null?0:S-s,_=n-E,M=m-(S-l);return p?Math.min(_,M):_},O=()=>{const S=Date.now();if(w(S))return x(S);c=setTimeout(O,P(S))},A=function(...S){const E=Date.now(),_=w(E);if(a=S,o=this,s=E,_){if(c===null)return g(E);if(p)return clearTimeout(c),c=setTimeout(O,n),y(E)}return c===null&&(c=setTimeout(O,n)),u};return A.cancel=()=>{c!==null&&clearTimeout(c),l=0,s=a=o=c=null},A.flush=()=>c===null?u:x(Date.now()),A}e.debounce=t}(hs)),hs}var Nh;function h_(){return Nh||(Nh=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=d_();function r(n,i=0,a={}){const{leading:o=!0,trailing:s=!0}=a;return t.debounce(n,i,{leading:o,maxWait:i,trailing:s})}e.throttle=r}(ds)),ds}var vs,Ih;function v_(){return Ih||(Ih=1,vs=h_().throttle),vs}var p_=v_();const m_=Lt(p_);var xn=function(t,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a]};function Dh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ps(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Dh(Object(r),!0).forEach(function(n){y_(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Dh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function y_(e,t,r){return(t=g_(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function g_(e){var t=b_(e,"string");return typeof t=="symbol"?t:t+""}function b_(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Dr=v.forwardRef((e,t)=>{var{aspect:r,initialDimension:n={width:-1,height:-1},width:i="100%",height:a="100%",minWidth:o=0,minHeight:s,maxHeight:l,children:c,debounce:u=0,id:f,className:d,onResize:h,style:p={}}=e,m=v.useRef(null),y=v.useRef();y.current=h,v.useImperativeHandle(t,()=>m.current);var[g,x]=v.useState({containerWidth:n.width,containerHeight:n.height}),w=v.useCallback((O,A)=>{x(S=>{var E=Math.round(O),_=Math.round(A);return S.containerWidth===E&&S.containerHeight===_?S:{containerWidth:E,containerHeight:_}})},[]);v.useEffect(()=>{var O=_=>{var M,{width:C,height:k}=_[0].contentRect;w(C,k),(M=y.current)===null||M===void 0||M.call(y,C,k)};u>0&&(O=m_(O,u,{trailing:!0,leading:!1}));var A=new ResizeObserver(O),{width:S,height:E}=m.current.getBoundingClientRect();return w(S,E),A.observe(m.current),()=>{A.disconnect()}},[w,u]);var P=v.useMemo(()=>{var{containerWidth:O,containerHeight:A}=g;if(O<0||A<0)return null;xn(fr(i)||fr(a),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,i,a),xn(!r||r>0,"The aspect(%s) must be greater than zero.",r);var S=fr(i)?O:i,E=fr(a)?A:a;return r&&r>0&&(S?E=S/r:E&&(S=E*r),l&&E>l&&(E=l)),xn(S>0||E>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,S,E,i,a,o,s,r),v.Children.map(c,_=>v.cloneElement(_,{width:S,height:E,style:ps({width:S,height:E},_.props.style)}))},[r,c,a,l,s,o,g,i]);return v.createElement("div",{id:f?"".concat(f):void 0,className:W("recharts-responsive-container",d),style:ps(ps({},p),{},{width:i,height:a,minWidth:o,minHeight:s,maxHeight:l}),ref:m},v.createElement("div",{style:{width:0,height:0,overflow:"visible"}},P))}),wr=e=>null;wr.displayName="Cell";function $h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function vl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?$h(Object(r),!0).forEach(function(n){x_(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$h(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function x_(e,t,r){return(t=w_(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function w_(e){var t=P_(e,"string");return typeof t=="symbol"?t:t+""}function P_(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var $r={widthCache:{},cacheCount:0},O_=2e3,A_={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},Rh="recharts_measurement_span";function S_(e){var t=vl({},e);return Object.keys(t).forEach(r=>{t[r]||delete t[r]}),t}var wn=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t==null||tr.isSsr)return{width:0,height:0};var n=S_(r),i=JSON.stringify({text:t,copyStyle:n});if($r.widthCache[i])return $r.widthCache[i];try{var a=document.getElementById(Rh);a||(a=document.createElement("span"),a.setAttribute("id",Rh),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=vl(vl({},A_),n);Object.assign(a.style,o),a.textContent="".concat(t);var s=a.getBoundingClientRect(),l={width:s.width,height:s.height};return $r.widthCache[i]=l,++$r.cacheCount>O_&&($r.cacheCount=0,$r.widthCache={}),l}catch{return{width:0,height:0}}},Lh=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Bh=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,j_=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,E_=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,ag={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},__=Object.keys(ag),Br="NaN";function T_(e,t){return e*ag[t]}class Le{static parse(t){var r,[,n,i]=(r=E_.exec(t))!==null&&r!==void 0?r:[];return new Le(parseFloat(n),i??"")}constructor(t,r){this.num=t,this.unit=r,this.num=t,this.unit=r,Ke(t)&&(this.unit=""),r!==""&&!j_.test(r)&&(this.num=NaN,this.unit=""),__.includes(r)&&(this.num=T_(t,r),this.unit="px")}add(t){return this.unit!==t.unit?new Le(NaN,""):new Le(this.num+t.num,this.unit)}subtract(t){return this.unit!==t.unit?new Le(NaN,""):new Le(this.num-t.num,this.unit)}multiply(t){return this.unit!==""&&t.unit!==""&&this.unit!==t.unit?new Le(NaN,""):new Le(this.num*t.num,this.unit||t.unit)}divide(t){return this.unit!==""&&t.unit!==""&&this.unit!==t.unit?new Le(NaN,""):new Le(this.num/t.num,this.unit||t.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return Ke(this.num)}}function og(e){if(e.includes(Br))return Br;for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=(r=Lh.exec(t))!==null&&r!==void 0?r:[],o=Le.parse(n??""),s=Le.parse(a??""),l=i==="*"?o.multiply(s):o.divide(s);if(l.isNaN())return Br;t=t.replace(Lh,l.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var c,[,u,f,d]=(c=Bh.exec(t))!==null&&c!==void 0?c:[],h=Le.parse(u??""),p=Le.parse(d??""),m=f==="+"?h.add(p):h.subtract(p);if(m.isNaN())return Br;t=t.replace(Bh,m.toString())}return t}var Kh=/\(([^()]*)\)/;function C_(e){for(var t=e,r;(r=Kh.exec(t))!=null;){var[,n]=r;t=t.replace(Kh,og(n))}return t}function k_(e){var t=e.replace(/\s+/g,"");return t=C_(t),t=og(t),t}function M_(e){try{return k_(e)}catch{return Br}}function ms(e){var t=M_(e.slice(5,-1));return t===Br?"":t}var N_=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],I_=["dx","dy","angle","className","breakAll"];function pl(){return pl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},pl.apply(null,arguments)}function zh(e,t){if(e==null)return{};var r,n,i=D_(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function D_(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var sg=/[ \f\n\r\t\v\u2028\u2029]+/,lg=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];V(t)||(r?i=t.toString().split(""):i=t.toString().split(sg));var a=i.map(s=>({word:s,width:wn(s,n).width})),o=r?0:wn(" ",n).width;return{wordsWithComputedWidth:a,spaceWidth:o}}catch{return null}},$_=(e,t,r,n,i)=>{var{maxLines:a,children:o,style:s,breakAll:l}=e,c=N(a),u=o,f=function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return C.reduce((k,R)=>{var{word:B,width:U}=R,X=k[k.length-1];if(X&&(n==null||i||X.width+U+r<Number(n)))X.words.push(B),X.width+=U+r;else{var K={words:[B],width:U};k.push(K)}return k},[])},d=f(t),h=M=>M.reduce((C,k)=>C.width>k.width?C:k);if(!c||i)return d;var p=d.length>a||h(d).width>Number(n);if(!p)return d;for(var m="…",y=M=>{var C=u.slice(0,M),k=lg({breakAll:l,style:s,children:C+m}).wordsWithComputedWidth,R=f(k),B=R.length>a||h(R).width>Number(n);return[B,R]},g=0,x=u.length-1,w=0,P;g<=x&&w<=u.length-1;){var O=Math.floor((g+x)/2),A=O-1,[S,E]=y(A),[_]=y(O);if(!S&&!_&&(g=O+1),S&&_&&(x=O-1),!S&&_){P=E;break}w++}return P||d},qh=e=>{var t=V(e)?[]:e.toString().split(sg);return[{words:t}]},R_=e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:o}=e;if((t||r)&&!tr.isSsr){var s,l,c=lg({breakAll:a,children:n,style:i});if(c){var{wordsWithComputedWidth:u,spaceWidth:f}=c;s=u,l=f}else return qh(n);return $_({breakAll:a,children:n,maxLines:o,style:i},s,l,t,r)}return qh(n)},Wh="#808080",Ia=v.forwardRef((e,t)=>{var{x:r=0,y:n=0,lineHeight:i="1em",capHeight:a="0.71em",scaleToFit:o=!1,textAnchor:s="start",verticalAnchor:l="end",fill:c=Wh}=e,u=zh(e,N_),f=v.useMemo(()=>R_({breakAll:u.breakAll,children:u.children,maxLines:u.maxLines,scaleToFit:o,style:u.style,width:u.width}),[u.breakAll,u.children,u.maxLines,o,u.style,u.width]),{dx:d,dy:h,angle:p,className:m,breakAll:y}=u,g=zh(u,I_);if(!wt(r)||!wt(n))return null;var x=r+(N(d)?d:0),w=n+(N(h)?h:0),P;switch(l){case"start":P=ms("calc(".concat(a,")"));break;case"middle":P=ms("calc(".concat((f.length-1)/2," * -").concat(i," + (").concat(a," / 2))"));break;default:P=ms("calc(".concat(f.length-1," * -").concat(i,")"));break}var O=[];if(o){var A=f[0].width,{width:S}=u;O.push("scale(".concat(N(S)?S/A:1,")"))}return p&&O.push("rotate(".concat(p,", ").concat(x,", ").concat(w,")")),O.length&&(g.transform=O.join(" ")),v.createElement("text",pl({},z(g,!0),{ref:t,x,y:w,className:W("recharts-text",m),textAnchor:s,fill:c.includes("url")?Wh:c}),f.map((E,_)=>{var M=E.words.join(y?"":" ");return v.createElement("tspan",{x,dy:_===0?P:i,key:"".concat(M,"-").concat(_)},M)}))});Ia.displayName="Text";var L_=["offset"],B_=["labelRef"];function Fh(e,t){if(e==null)return{};var r,n,i=K_(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function K_(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function Uh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ce(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Uh(Object(r),!0).forEach(function(n){z_(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Uh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function z_(e,t,r){return(t=q_(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function q_(e){var t=W_(e,"string");return typeof t=="symbol"?t:t+""}function W_(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Et(){return Et=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Et.apply(null,arguments)}var F_=e=>{var{value:t,formatter:r}=e,n=V(e.children)?t:e.children;return typeof r=="function"?r(n):n},lc=e=>e!=null&&typeof e=="function",U_=(e,t)=>{var r=Oe(t-e),n=Math.min(Math.abs(t-e),360);return r*n},Y_=(e,t,r)=>{var{position:n,viewBox:i,offset:a,className:o}=e,{cx:s,cy:l,innerRadius:c,outerRadius:u,startAngle:f,endAngle:d,clockWise:h}=i,p=(c+u)/2,m=U_(f,d),y=m>=0?1:-1,g,x;n==="insideStart"?(g=f+y*a,x=h):n==="insideEnd"?(g=d-y*a,x=!h):n==="end"&&(g=d+y*a,x=h),x=m<=0?x:!x;var w=fe(s,l,p,g),P=fe(s,l,p,g+(x?1:-1)*359),O="M".concat(w.x,",").concat(w.y,`
    A`).concat(p,",").concat(p,",0,1,").concat(x?0:1,`,
    `).concat(P.x,",").concat(P.y),A=V(e.id)?Qt("recharts-radial-line-"):e.id;return v.createElement("text",Et({},r,{dominantBaseline:"central",className:W("recharts-radial-bar-label",o)}),v.createElement("defs",null,v.createElement("path",{id:A,d:O})),v.createElement("textPath",{xlinkHref:"#".concat(A)},t))},H_=e=>{var{viewBox:t,offset:r,position:n}=e,{cx:i,cy:a,innerRadius:o,outerRadius:s,startAngle:l,endAngle:c}=t,u=(l+c)/2;if(n==="outside"){var{x:f,y:d}=fe(i,a,s+r,u);return{x:f,y:d,textAnchor:f>=i?"start":"end",verticalAnchor:"middle"}}if(n==="center")return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if(n==="centerTop")return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if(n==="centerBottom")return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var h=(o+s)/2,{x:p,y:m}=fe(i,a,h,u);return{x:p,y:m,textAnchor:"middle",verticalAnchor:"middle"}},G_=(e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:a,y:o,width:s,height:l}=t,c=l>=0?1:-1,u=c*n,f=c>0?"end":"start",d=c>0?"start":"end",h=s>=0?1:-1,p=h*n,m=h>0?"end":"start",y=h>0?"start":"end";if(i==="top"){var g={x:a+s/2,y:o-c*n,textAnchor:"middle",verticalAnchor:f};return ce(ce({},g),r?{height:Math.max(o-r.y,0),width:s}:{})}if(i==="bottom"){var x={x:a+s/2,y:o+l+u,textAnchor:"middle",verticalAnchor:d};return ce(ce({},x),r?{height:Math.max(r.y+r.height-(o+l),0),width:s}:{})}if(i==="left"){var w={x:a-p,y:o+l/2,textAnchor:m,verticalAnchor:"middle"};return ce(ce({},w),r?{width:Math.max(w.x-r.x,0),height:l}:{})}if(i==="right"){var P={x:a+s+p,y:o+l/2,textAnchor:y,verticalAnchor:"middle"};return ce(ce({},P),r?{width:Math.max(r.x+r.width-P.x,0),height:l}:{})}var O=r?{width:s,height:l}:{};return i==="insideLeft"?ce({x:a+p,y:o+l/2,textAnchor:y,verticalAnchor:"middle"},O):i==="insideRight"?ce({x:a+s-p,y:o+l/2,textAnchor:m,verticalAnchor:"middle"},O):i==="insideTop"?ce({x:a+s/2,y:o+u,textAnchor:"middle",verticalAnchor:d},O):i==="insideBottom"?ce({x:a+s/2,y:o+l-u,textAnchor:"middle",verticalAnchor:f},O):i==="insideTopLeft"?ce({x:a+p,y:o+u,textAnchor:y,verticalAnchor:d},O):i==="insideTopRight"?ce({x:a+s-p,y:o+u,textAnchor:m,verticalAnchor:d},O):i==="insideBottomLeft"?ce({x:a+p,y:o+l-u,textAnchor:y,verticalAnchor:f},O):i==="insideBottomRight"?ce({x:a+s-p,y:o+l-u,textAnchor:m,verticalAnchor:f},O):i&&typeof i=="object"&&(N(i.x)||fr(i.x))&&(N(i.y)||fr(i.y))?ce({x:a+$e(i.x,s),y:o+$e(i.y,l),textAnchor:"end",verticalAnchor:"end"},O):ce({x:a+s/2,y:o+l/2,textAnchor:"middle",verticalAnchor:"middle"},O)},V_=e=>"cx"in e&&N(e.cx);function Ge(e){var{offset:t=5}=e,r=Fh(e,L_),n=ce({offset:t},r),{viewBox:i,position:a,value:o,children:s,content:l,className:c="",textBreakAll:u,labelRef:f}=n,d=Jl(),h=i||d;if(!h||V(o)&&V(s)&&!v.isValidElement(l)&&typeof l!="function")return null;if(v.isValidElement(l)){var{labelRef:p}=n,m=Fh(n,B_);return v.cloneElement(l,m)}var y;if(typeof l=="function"){if(y=v.createElement(l,n),v.isValidElement(y))return y}else y=F_(n);var g=V_(h),x=z(n,!0);if(g&&(a==="insideStart"||a==="insideEnd"||a==="end"))return Y_(n,y,x);var w=g?H_(n):G_(n,h);return v.createElement(Ia,Et({ref:f,className:W("recharts-label",c)},x,w,{breakAll:u}),y)}Ge.displayName="Label";var ug=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:s,innerRadius:l,outerRadius:c,x:u,y:f,top:d,left:h,width:p,height:m,clockWise:y,labelViewBox:g}=e;if(g)return g;if(N(p)&&N(m)){if(N(u)&&N(f))return{x:u,y:f,width:p,height:m};if(N(d)&&N(h))return{x:d,y:h,width:p,height:m}}if(N(u)&&N(f))return{x:u,y:f,width:0,height:0};if(N(t)&&N(r))return{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:l||0,outerRadius:c||s||o||0,clockWise:y};if(e.viewBox)return e.viewBox},X_=(e,t,r)=>{if(!e)return null;var n={viewBox:t,labelRef:r};return e===!0?v.createElement(Ge,Et({key:"label-implicit"},n)):wt(e)?v.createElement(Ge,Et({key:"label-implicit",value:e},n)):v.isValidElement(e)?e.type===Ge?v.cloneElement(e,ce({key:"label-implicit"},n)):v.createElement(Ge,Et({key:"label-implicit",content:e},n)):lc(e)?v.createElement(Ge,Et({key:"label-implicit",content:e},n)):e&&typeof e=="object"?v.createElement(Ge,Et({},e,{key:"label-implicit"},n)):null},Z_=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var{children:i,labelRef:a}=t,o=ug(t),s=Bn(i,Ge).map((c,u)=>v.cloneElement(c,{viewBox:r||o,key:"label-".concat(u)}));if(!n)return s;var l=X_(t.label,r||o,a);return[l,...s]};Ge.parseViewBox=ug;Ge.renderCallByParent=Z_;var ys={},gs={},Yh;function Q_(){return Yh||(Yh=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return r[r.length-1]}e.last=t}(gs)),gs}var bs={},Hh;function J_(){return Hh||(Hh=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return Array.isArray(r)?r:Array.from(r)}e.toArray=t}(bs)),bs}var Gh;function eT(){return Gh||(Gh=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Q_(),r=J_(),n=Bl();function i(a){if(n.isArrayLike(a))return t.last(r.toArray(a))}e.last=i}(ys)),ys}var xs,Vh;function tT(){return Vh||(Vh=1,xs=eT().last),xs}var rT=tT();const nT=Lt(rT);var iT=["valueAccessor"],aT=["data","dataKey","clockWise","id","textBreakAll"];function Hi(){return Hi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Hi.apply(null,arguments)}function Xh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Zh(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xh(Object(r),!0).forEach(function(n){oT(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function oT(e,t,r){return(t=sT(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sT(e){var t=lT(e,"string");return typeof t=="symbol"?t:t+""}function lT(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Qh(e,t){if(e==null)return{};var r,n,i=uT(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function uT(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var cT=e=>Array.isArray(e.value)?nT(e.value):e.value;function Mt(e){var{valueAccessor:t=cT}=e,r=Qh(e,iT),{data:n,dataKey:i,clockWise:a,id:o,textBreakAll:s}=r,l=Qh(r,aT);return!n||!n.length?null:v.createElement(ae,{className:"recharts-label-list"},n.map((c,u)=>{var f=V(i)?t(c,u):Q(c&&c.payload,i),d=V(o)?{}:{id:"".concat(o,"-").concat(u)};return v.createElement(Ge,Hi({},z(c,!0),l,d,{parentViewBox:c.parentViewBox,value:f,textBreakAll:s,viewBox:Ge.parseViewBox(V(a)?c:Zh(Zh({},c),{},{clockWise:a})),key:"label-".concat(u),index:u}))}))}Mt.displayName="LabelList";function fT(e,t){return e?e===!0?v.createElement(Mt,{key:"labelList-implicit",data:t}):v.isValidElement(e)||lc(e)?v.createElement(Mt,{key:"labelList-implicit",data:t,content:e}):typeof e=="object"?v.createElement(Mt,Hi({data:t},e,{key:"labelList-implicit"})):null:null}function dT(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var{children:n}=e,i=Bn(n,Mt).map((o,s)=>v.cloneElement(o,{data:t,key:"labelList-".concat(s)}));if(!r)return i;var a=fT(e.label,t);return[a,...i]}Mt.renderCallByParent=dT;function ml(){return ml=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ml.apply(null,arguments)}var uc=e=>{var{cx:t,cy:r,r:n,className:i}=e,a=W("recharts-dot",i);return t===+t&&r===+r&&n===+n?v.createElement("circle",ml({},z(e,!1),kl(e),{className:a,cx:t,cy:r,r:n})):null},cg=e=>e.graphicalItems.polarItems,hT=j([le,Hn],Ru),cc=j([cg,ue,hT],Lu),vT=j([cc],Bu),fc=j([vT,Eu],Ku),pT=j([fc,ue,cc],zu),mT=j([fc,ue,cc],(e,t,r)=>r.length>0?e.flatMap(n=>r.flatMap(i=>{var a,o=Q(n,(a=t.dataKey)!==null&&a!==void 0?a:i.dataKey);return{value:o,errorDomain:[]}})).filter(Boolean):(t==null?void 0:t.dataKey)!=null?e.map(n=>({value:Q(n,t.dataKey),errorDomain:[]})):e.map(n=>({value:n,errorDomain:[]}))),Jh=()=>{},yT=j([ue,by,Jh,mT,Jh],Fu),fg=j([ue,F,fc,pT,Yn,le,yT],Uu),gT=j([fg,ue,nn],Gu);j([ue,fg,gT,le],Xu);var bT={radiusAxis:{},angleAxis:{}},dg=dt({name:"polarAxis",initialState:bT,reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=t.payload},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=t.payload},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:BD,removeRadiusAxis:KD,addAngleAxis:zD,removeAngleAxis:qD}=dg.actions,xT=dg.reducer;function ev(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function tv(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ev(Object(r),!0).forEach(function(n){wT(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ev(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function wT(e,t,r){return(t=PT(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function PT(e){var t=OT(e,"string");return typeof t=="symbol"?t:t+""}function OT(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var dc=(e,t)=>t,AT=[],hc=(e,t,r)=>(r==null?void 0:r.length)===0?AT:r,hg=j([Eu,dc,hc],(e,t,r)=>{var{chartData:n}=e,i;if((t==null?void 0:t.data)!=null&&t.data.length>0?i=t.data:i=n,(!i||!i.length)&&r!=null&&(i=r.map(a=>tv(tv({},t.presentationProps),a.props))),i!=null)return i}),ST=j([hg,dc,hc],(e,t,r)=>{if(e!=null)return e.map((n,i)=>{var a,o=Q(n,t.nameKey,t.name),s;return r!=null&&(a=r[i])!==null&&a!==void 0&&(a=a.props)!==null&&a!==void 0&&a.fill?s=r[i].props.fill:typeof n=="object"&&n!=null&&"fill"in n?s=n.fill:s=t.fill,{value:er(o,t.dataKey),color:s,payload:n,type:t.legendType}})}),jT=j([cg,dc],(e,t)=>{if(e.some(r=>r.type==="pie"&&t.dataKey===r.dataKey&&t.data===r.data))return t}),ET=j([hg,jT,hc,pe],(e,t,r,n)=>{if(!(t==null||e==null))return gC({offset:n,pieSettings:t,displayedData:e,cells:r})}),_T={countOfBars:0,cartesianItems:[],polarItems:[]},vg=dt({name:"graphicalItems",initialState:_T,reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push(t.payload)},replaceCartesianGraphicalItem(e,t){var{prev:r,next:n}=t.payload,i=Ct(e).cartesianItems.indexOf(r);i>-1&&(e.cartesianItems[i]=n)},removeCartesianGraphicalItem(e,t){var r=Ct(e).cartesianItems.indexOf(t.payload);r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push(t.payload)},removePolarGraphicalItem(e,t){var r=Ct(e).polarItems.indexOf(t.payload);r>-1&&e.polarItems.splice(r,1)}}}),{addBar:TT,removeBar:CT,addCartesianGraphicalItem:kT,replaceCartesianGraphicalItem:MT,removeCartesianGraphicalItem:NT,addPolarGraphicalItem:IT,removePolarGraphicalItem:DT}=vg.actions,$T=vg.reducer;function rv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function nv(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?rv(Object(r),!0).forEach(function(n){RT(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function RT(e,t,r){return(t=LT(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function LT(e){var t=BT(e,"string");return typeof t=="symbol"?t:t+""}function BT(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function KT(e){var t=ie(),r=v.useRef(null);return v.useEffect(()=>{var n=nv(nv({},e),{},{stackId:Xl(e.stackId)});r.current===null?t(kT(n)):r.current!==n&&t(MT({prev:r.current,next:n})),r.current=n},[t,e]),v.useEffect(()=>()=>{r.current&&(t(NT(r.current)),r.current=null)},[t]),null}function zT(e){var t=ie();return v.useEffect(()=>(t(IT(e)),()=>{t(DT(e))}),[t,e]),null}var ws={},iv;function qT(){return iv||(iv=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){var i;if(typeof r!="object"||r==null)return!1;if(Object.getPrototypeOf(r)===null)return!0;if(Object.prototype.toString.call(r)!=="[object Object]"){const a=r[Symbol.toStringTag];return a==null||!((i=Object.getOwnPropertyDescriptor(r,Symbol.toStringTag))!=null&&i.writable)?!1:r.toString()===`[object ${a}]`}let n=r;for(;Object.getPrototypeOf(n)!==null;)n=Object.getPrototypeOf(n);return Object.getPrototypeOf(r)===n}e.isPlainObject=t}(ws)),ws}var Ps,av;function WT(){return av||(av=1,Ps=qT().isPlainObject),Ps}var FT=WT();const UT=Lt(FT);function Gi(){return Gi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Gi.apply(null,arguments)}var ov=(e,t,r,n,i)=>{var a=r-n,o;return o="M ".concat(e,",").concat(t),o+="L ".concat(e+r,",").concat(t),o+="L ".concat(e+r-a/2,",").concat(t+i),o+="L ".concat(e+r-a/2-n,",").concat(t+i),o+="L ".concat(e,",").concat(t," Z"),o},YT={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},HT=e=>{var t=Je(e,YT),r=v.useRef(),[n,i]=v.useState(-1);v.useEffect(()=>{if(r.current&&r.current.getTotalLength)try{var y=r.current.getTotalLength();y&&i(y)}catch{}},[]);var{x:a,y:o,upperWidth:s,lowerWidth:l,height:c,className:u}=t,{animationEasing:f,animationDuration:d,animationBegin:h,isUpdateAnimationActive:p}=t;if(a!==+a||o!==+o||s!==+s||l!==+l||c!==+c||s===0&&l===0||c===0)return null;var m=W("recharts-trapezoid",u);return p?v.createElement(It,{canBegin:n>0,from:{upperWidth:0,lowerWidth:0,height:c,x:a,y:o},to:{upperWidth:s,lowerWidth:l,height:c,x:a,y:o},duration:d,animationEasing:f,isActive:p},y=>{var{upperWidth:g,lowerWidth:x,height:w,x:P,y:O}=y;return v.createElement(It,{canBegin:n>0,from:"0px ".concat(n===-1?1:n,"px"),to:"".concat(n,"px 0px"),attributeName:"strokeDasharray",begin:h,duration:d,easing:f},v.createElement("path",Gi({},z(t,!0),{className:m,d:ov(P,O,g,x,w),ref:r})))}):v.createElement("g",null,v.createElement("path",Gi({},z(t,!0),{className:m,d:ov(a,o,s,l,c)})))},GT=["option","shapeType","propTransformer","activeClassName","isActive"];function VT(e,t){if(e==null)return{};var r,n,i=XT(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function XT(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function sv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Vi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?sv(Object(r),!0).forEach(function(n){ZT(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ZT(e,t,r){return(t=QT(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function QT(e){var t=JT(e,"string");return typeof t=="symbol"?t:t+""}function JT(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function eC(e,t){return Vi(Vi({},t),e)}function tC(e,t){return e==="symbols"}function lv(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return v.createElement(tm,r);case"trapezoid":return v.createElement(HT,r);case"sector":return v.createElement(im,r);case"symbols":if(tC(t))return v.createElement($l,r);break;default:return null}}function rC(e){return v.isValidElement(e)?e.props:e}function pg(e){var{option:t,shapeType:r,propTransformer:n=eC,activeClassName:i="recharts-active-shape",isActive:a}=e,o=VT(e,GT),s;if(v.isValidElement(t))s=v.cloneElement(t,Vi(Vi({},o),rC(t)));else if(typeof t=="function")s=t(o);else if(UT(t)&&typeof t!="boolean"){var l=n(t,o);s=v.createElement(lv,{shapeType:r,elementProps:l})}else{var c=o;s=v.createElement(lv,{shapeType:r,elementProps:c})}return a?v.createElement(ae,{className:i},s):s}var vc=(e,t)=>{var r=ie();return(n,i)=>a=>{e==null||e(n,i,a),r(Dy({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},pc=e=>{var t=ie();return(r,n)=>i=>{e==null||e(r,n,i),t(Wj())}},mc=(e,t)=>{var r=ie();return(n,i)=>a=>{e==null||e(n,i,a),r(Fj({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}};function Da(e){var{fn:t,args:r}=e,n=ie(),i=Ie();return v.useEffect(()=>{if(!i){var a=t(r);return n(Kj(a)),()=>{n(zj(a))}}},[t,r,n,i]),null}var mg=()=>{};function yc(e){var{legendPayload:t}=e,r=ie(),n=Ie();return v.useEffect(()=>n?mg:(r(Vp(t)),()=>{r(Xp(t))}),[r,n,t]),null}function nC(e){var{legendPayload:t}=e,r=ie(),n=D(F);return v.useEffect(()=>n!=="centric"&&n!=="radial"?mg:(r(Vp(t)),()=>{r(Xp(t))}),[r,n,t]),null}function $a(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"animation-",r=v.useRef(Qt(t)),n=v.useRef(e);return n.current!==e&&(r.current=Qt(t),n.current=e),r.current}var iC=["onMouseEnter","onClick","onMouseLeave"];function aC(e,t){if(e==null)return{};var r,n,i=oC(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function oC(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function uv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function oe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?uv(Object(r),!0).forEach(function(n){Ra(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):uv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ra(e,t,r){return(t=sC(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sC(e){var t=lC(e,"string");return typeof t=="symbol"?t:t+""}function lC(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function jr(){return jr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},jr.apply(null,arguments)}function uC(e){var t=v.useMemo(()=>z(e,!1),[e]),r=v.useMemo(()=>Bn(e.children,wr),[e.children]),n=v.useMemo(()=>({name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,minAngle:e.minAngle,paddingAngle:e.paddingAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,legendType:e.legendType,fill:e.fill,presentationProps:t}),[e.cornerRadius,e.cx,e.cy,e.data,e.dataKey,e.endAngle,e.innerRadius,e.minAngle,e.name,e.nameKey,e.outerRadius,e.paddingAngle,e.startAngle,e.tooltipType,e.legendType,e.fill,t]),i=D(a=>ST(a,n,r));return v.createElement(nC,{legendPayload:i})}function cC(e){var{dataKey:t,nameKey:r,sectors:n,stroke:i,strokeWidth:a,fill:o,name:s,hide:l,tooltipType:c}=e;return{dataDefinedOnItem:n==null?void 0:n.map(u=>u.tooltipPayload),positions:n==null?void 0:n.map(u=>u.tooltipPosition),settings:{stroke:i,strokeWidth:a,fill:o,dataKey:t,nameKey:r,name:er(s,t),hide:l,type:c,color:o,unit:""}}}var fC=(e,t)=>e>t?"start":e<t?"end":"middle",dC=(e,t,r)=>typeof t=="function"?t(e):$e(t,r,r*.8),hC=(e,t,r)=>{var{top:n,left:i,width:a,height:o}=t,s=Kp(a,o),l=i+$e(e.cx,a,a/2),c=n+$e(e.cy,o,o/2),u=$e(e.innerRadius,s,0),f=dC(r,e.outerRadius,s),d=e.maxRadius||Math.sqrt(a*a+o*o)/2;return{cx:l,cy:c,innerRadius:u,outerRadius:f,maxRadius:d}},vC=(e,t)=>{var r=Oe(t-e),n=Math.min(Math.abs(t-e),360);return r*n},pC=(e,t)=>{if(v.isValidElement(e))return v.cloneElement(e,t);if(typeof e=="function")return e(t);var r=W("recharts-pie-label-line",typeof e!="boolean"?e.className:"");return v.createElement(qr,jr({},t,{type:"linear",className:r}))},mC=(e,t,r)=>{if(v.isValidElement(e))return v.cloneElement(e,t);var n=r;if(typeof e=="function"&&(n=e(t),v.isValidElement(n)))return n;var i=W("recharts-pie-label-text",typeof e!="boolean"&&typeof e!="function"?e.className:"");return v.createElement(Ia,jr({},t,{alignmentBaseline:"middle",className:i}),n)};function yC(e){var{sectors:t,props:r,showLabels:n}=e,{label:i,labelLine:a,dataKey:o}=r;if(!n||!i||!t)return null;var s=z(r,!1),l=z(i,!1),c=z(a,!1),u=typeof i=="object"&&"offsetRadius"in i&&i.offsetRadius||20,f=t.map((d,h)=>{var p=(d.startAngle+d.endAngle)/2,m=fe(d.cx,d.cy,d.outerRadius+u,p),y=oe(oe(oe(oe({},s),d),{},{stroke:"none"},l),{},{index:h,textAnchor:fC(m.x,d.cx)},m),g=oe(oe(oe(oe({},s),d),{},{fill:"none",stroke:d.fill},c),{},{index:h,points:[fe(d.cx,d.cy,d.outerRadius,p),m],key:"line"});return v.createElement(ae,{key:"label-".concat(d.startAngle,"-").concat(d.endAngle,"-").concat(d.midAngle,"-").concat(h)},a&&pC(a,g),mC(i,y,Q(d,o)))});return v.createElement(ae,{className:"recharts-pie-labels"},f)}function yg(e){var{sectors:t,activeShape:r,inactiveShape:n,allOtherPieProps:i,showLabels:a}=e,o=D(Jt),{onMouseEnter:s,onClick:l,onMouseLeave:c}=i,u=aC(i,iC),f=vc(s,i.dataKey),d=pc(c),h=mc(l,i.dataKey);return t==null?null:v.createElement(v.Fragment,null,t.map((p,m)=>{if((p==null?void 0:p.startAngle)===0&&(p==null?void 0:p.endAngle)===0&&t.length!==1)return null;var y=r&&String(m)===o,g=o?n:null,x=y?r:g,w=oe(oe({},p),{},{stroke:p.stroke,tabIndex:-1,[Fp]:m,[Up]:i.dataKey});return v.createElement(ae,jr({tabIndex:-1,className:"recharts-pie-sector"},Ln(u,p,m),{onMouseEnter:f(p,m),onMouseLeave:d(p,m),onClick:h(p,m),key:"sector-".concat(p==null?void 0:p.startAngle,"-").concat(p==null?void 0:p.endAngle,"-").concat(p.midAngle,"-").concat(m)}),v.createElement(pg,jr({option:x,isActive:y,shapeType:"sector"},w)))}),v.createElement(yC,{sectors:t,props:i,showLabels:a}))}function gC(e){var t,{pieSettings:r,displayedData:n,cells:i,offset:a}=e,{cornerRadius:o,startAngle:s,endAngle:l,dataKey:c,nameKey:u,tooltipType:f}=r,d=Math.abs(r.minAngle),h=vC(s,l),p=Math.abs(h),m=n.length<=1?0:(t=r.paddingAngle)!==null&&t!==void 0?t:0,y=n.filter(A=>Q(A,c,0)!==0).length,g=(p>=360?y:y-1)*m,x=p-y*d-g,w=n.reduce((A,S)=>{var E=Q(S,c,0);return A+(N(E)?E:0)},0),P;if(w>0){var O;P=n.map((A,S)=>{var E=Q(A,c,0),_=Q(A,u,S),M=hC(r,a,A),C=(N(E)?E:0)/w,k,R=oe(oe({},A),i&&i[S]&&i[S].props);S?k=O.endAngle+Oe(h)*m*(E!==0?1:0):k=s;var B=k+Oe(h)*((E!==0?d:0)+C*x),U=(k+B)/2,X=(M.innerRadius+M.outerRadius)/2,K=[{name:_,value:E,payload:R,dataKey:c,type:f}],he=fe(M.cx,M.cy,X,U);return O=oe(oe(oe(oe({},r.presentationProps),{},{percent:C,cornerRadius:o,name:_,tooltipPayload:K,midAngle:U,middleRadius:X,tooltipPosition:he},R),M),{},{value:Q(A,c),startAngle:k,endAngle:B,payload:R,paddingAngle:Oe(h)*m}),O})}return P}function bC(e){var{props:t,previousSectorsRef:r}=e,{sectors:n,isAnimationActive:i,animationBegin:a,animationDuration:o,animationEasing:s,activeShape:l,inactiveShape:c,onAnimationStart:u,onAnimationEnd:f}=t,d=$a(t,"recharts-pie-"),h=r.current,[p,m]=v.useState(!0),y=v.useCallback(()=>{typeof f=="function"&&f(),m(!1)},[f]),g=v.useCallback(()=>{typeof u=="function"&&u(),m(!0)},[u]);return v.createElement(It,{begin:a,duration:o,isActive:i,easing:s,from:{t:0},to:{t:1},onAnimationStart:g,onAnimationEnd:y,key:d},x=>{var{t:w}=x,P=[],O=n&&n[0],A=O.startAngle;return n.forEach((S,E)=>{var _=h&&h[E],M=E>0?Zt(S,"paddingAngle",0):0;if(_){var C=He(_.endAngle-_.startAngle,S.endAngle-S.startAngle),k=oe(oe({},S),{},{startAngle:A+M,endAngle:A+C(w)+M});P.push(k),A=k.endAngle}else{var{endAngle:R,startAngle:B}=S,U=He(0,R-B),X=U(w),K=oe(oe({},S),{},{startAngle:A+M,endAngle:A+X+M});P.push(K),A=K.endAngle}}),r.current=P,v.createElement(ae,null,v.createElement(yg,{sectors:P,activeShape:l,inactiveShape:c,allOtherPieProps:t,showLabels:!p}))})}function xC(e){var{sectors:t,isAnimationActive:r,activeShape:n,inactiveShape:i}=e,a=v.useRef(null),o=a.current;return r&&t&&t.length&&(!o||o!==t)?v.createElement(bC,{props:e,previousSectorsRef:a}):v.createElement(yg,{sectors:t,activeShape:n,inactiveShape:i,allOtherPieProps:e,showLabels:!0})}function wC(e){var{hide:t,className:r,rootTabIndex:n}=e,i=W("recharts-pie",r);return t?null:v.createElement(ae,{tabIndex:n,className:i},v.createElement(xC,e))}var gg={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!tr.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function PC(e){var t=Je(e,gg),r=v.useMemo(()=>Bn(e.children,wr),[e.children]),n=z(t,!1),i=v.useMemo(()=>({name:t.name,nameKey:t.nameKey,tooltipType:t.tooltipType,data:t.data,dataKey:t.dataKey,cx:t.cx,cy:t.cy,startAngle:t.startAngle,endAngle:t.endAngle,minAngle:t.minAngle,paddingAngle:t.paddingAngle,innerRadius:t.innerRadius,outerRadius:t.outerRadius,cornerRadius:t.cornerRadius,legendType:t.legendType,fill:t.fill,presentationProps:n}),[t.cornerRadius,t.cx,t.cy,t.data,t.dataKey,t.endAngle,t.innerRadius,t.minAngle,t.name,t.nameKey,t.outerRadius,t.paddingAngle,t.startAngle,t.tooltipType,t.legendType,t.fill,n]),a=D(o=>ET(o,i,r));return v.createElement(v.Fragment,null,v.createElement(Da,{fn:cC,args:oe(oe({},t),{},{sectors:a})}),v.createElement(wC,jr({},t,{sectors:a})))}class Pn extends v.PureComponent{constructor(){super(...arguments),Ra(this,"id",Qt("recharts-pie-"))}render(){return v.createElement(v.Fragment,null,v.createElement(zT,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),v.createElement(uC,this.props),v.createElement(PC,this.props),this.props.children)}}Ra(Pn,"displayName","Pie");Ra(Pn,"defaultProps",gg);var OC=j([pe],e=>{if(e)return{top:e.top,bottom:e.bottom,left:e.left,right:e.right}}),AC=j([OC,Bt,Kt],(e,t,r)=>{if(!(!e||t==null||r==null))return{x:e.left,y:e.top,width:Math.max(0,t-e.left-e.right),height:Math.max(0,r-e.top-e.bottom)}}),SC=e=>{var t=Ie();return D(r=>ft(r,"xAxis",e,t))},jC=e=>{var t=Ie();return D(r=>ft(r,"yAxis",e,t))},La=()=>D(AC),EC=()=>D(_E);function cv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function fv(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?cv(Object(r),!0).forEach(function(n){_C(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):cv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function _C(e,t,r){return(t=TC(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function TC(e){var t=CC(e,"string");return typeof t=="symbol"?t:t+""}function CC(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var kC=e=>{var{point:t,childIndex:r,mainColor:n,activeDot:i,dataKey:a}=e;if(i===!1||t.x==null||t.y==null)return null;var o=fv(fv({index:r,dataKey:a,cx:t.x,cy:t.y,r:4,fill:n??"none",strokeWidth:2,stroke:"#fff",payload:t.payload,value:t.value},z(i,!1)),kl(i)),s;return v.isValidElement(i)?s=v.cloneElement(i,o):typeof i=="function"?s=i(o):s=v.createElement(uc,o),v.createElement(ae,{className:"recharts-active-dot"},s)};function yl(e){var{points:t,mainColor:r,activeDot:n,itemDataKey:i}=e,a=D(Jt),o=EC();if(t==null||o==null)return null;var s=t.find(l=>o.includes(l.payload));return V(s)?null:kC({point:s,childIndex:Number(a),mainColor:r,dataKey:i,activeDot:n})}var MC=()=>{var e=ie();return v.useEffect(()=>(e(TT()),()=>{e(CT())})),null},NC=["children"];function IC(e,t){if(e==null)return{};var r,n,i=DC(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function DC(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var dv=()=>{},bg=v.createContext({addErrorBar:dv,removeErrorBar:dv}),$C={data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0},xg=v.createContext($C);function wg(e){var{children:t}=e,r=IC(e,NC);return v.createElement(xg.Provider,{value:r},t)}var RC=()=>v.useContext(xg),gc=e=>{var{children:t,xAxisId:r,yAxisId:n,zAxisId:i,dataKey:a,data:o,stackId:s,hide:l,type:c,barSize:u}=e,[f,d]=v.useState([]),h=v.useCallback(y=>{d(g=>[...g,y])},[d]),p=v.useCallback(y=>{d(g=>g.filter(x=>x!==y))},[d]),m=Ie();return v.createElement(bg.Provider,{value:{addErrorBar:h,removeErrorBar:p}},v.createElement(KT,{type:c,data:o,xAxisId:r,yAxisId:n,zAxisId:i,dataKey:a,errorBars:f,stackId:s,hide:l,barSize:u,isPanorama:m}),t)};function LC(e){var{addErrorBar:t,removeErrorBar:r}=v.useContext(bg);return v.useEffect(()=>(t(e),()=>{r(e)}),[t,r,e]),null}var BC=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function Pg(e,t,r){return(t=KC(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function KC(e){var t=zC(e,"string");return typeof t=="symbol"?t:t+""}function zC(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Dn(){return Dn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Dn.apply(null,arguments)}function qC(e,t){if(e==null)return{};var r,n,i=WC(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function WC(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function FC(e){var{direction:t,width:r,dataKey:n,isAnimationActive:i,animationBegin:a,animationDuration:o,animationEasing:s}=e,l=qC(e,BC),c=z(l,!1),{data:u,dataPointFormatter:f,xAxisId:d,yAxisId:h,errorBarOffset:p}=RC(),m=SC(d),y=jC(h);if((m==null?void 0:m.scale)==null||(y==null?void 0:y.scale)==null||u==null||t==="x"&&m.type!=="number")return null;var g=u.map(x=>{var{x:w,y:P,value:O,errorVal:A}=f(x,n,t);if(!A)return null;var S=[],E,_;if(Array.isArray(A)?[E,_]=A:E=_=A,t==="x"){var{scale:M}=m,C=P+p,k=C+r,R=C-r,B=M(O-E),U=M(O+_);S.push({x1:U,y1:k,x2:U,y2:R}),S.push({x1:B,y1:C,x2:U,y2:C}),S.push({x1:B,y1:k,x2:B,y2:R})}else if(t==="y"){var{scale:X}=y,K=w+p,he=K-r,se=K+r,ze=X(O-E),et=X(O+_);S.push({x1:he,y1:et,x2:se,y2:et}),S.push({x1:K,y1:ze,x2:K,y2:et}),S.push({x1:he,y1:ze,x2:se,y2:ze})}var L="".concat(w+p,"px ").concat(P+p,"px");return v.createElement(ae,Dn({className:"recharts-errorBar",key:"bar-".concat(S.map(je=>"".concat(je.x1,"-").concat(je.x2,"-").concat(je.y1,"-").concat(je.y2)))},c),S.map(je=>{var or=i?{transformOrigin:"".concat(je.x1-5,"px")}:void 0;return v.createElement(It,{from:{transform:"scaleY(0)",transformOrigin:L},to:{transform:"scaleY(1)",transformOrigin:L},begin:a,easing:s,isActive:i,duration:o,key:"line-".concat(je.x1,"-").concat(je.x2,"-").concat(je.y1,"-").concat(je.y2),style:{transformOrigin:L}},v.createElement("line",Dn({},je,{style:or})))}))});return v.createElement(ae,{className:"recharts-errorBars"},g)}var Og=v.createContext(void 0);function UC(e){var t=v.useContext(Og);return e??t??"x"}function Ag(e){var{direction:t,children:r}=e;return v.createElement(Og.Provider,{value:t},r)}var Sg={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function YC(e){var t=UC(e.direction),{width:r,isAnimationActive:n,animationBegin:i,animationDuration:a,animationEasing:o}=Je(e,Sg);return v.createElement(v.Fragment,null,v.createElement(LC,{dataKey:e.dataKey,direction:t}),v.createElement(FC,Dn({},e,{direction:t,width:r,isAnimationActive:n,animationBegin:i,animationDuration:a,animationEasing:o})))}class jg extends v.Component{render(){return v.createElement(YC,this.props)}}Pg(jg,"defaultProps",Sg);Pg(jg,"displayName","ErrorBar");var HC="Invariant failed";function GC(e,t){throw new Error(HC)}var VC=["x","y"];function gl(){return gl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},gl.apply(null,arguments)}function hv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function yn(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?hv(Object(r),!0).forEach(function(n){XC(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function XC(e,t,r){return(t=ZC(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ZC(e){var t=QC(e,"string");return typeof t=="symbol"?t:t+""}function QC(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function JC(e,t){if(e==null)return{};var r,n,i=ek(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function ek(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function tk(e,t){var{x:r,y:n}=e,i=JC(e,VC),a="".concat(r),o=parseInt(a,10),s="".concat(n),l=parseInt(s,10),c="".concat(t.height||i.height),u=parseInt(c,10),f="".concat(t.width||i.width),d=parseInt(f,10);return yn(yn(yn(yn(yn({},t),i),o?{x:o}:{}),l?{y:l}:{}),{},{height:u,width:d,name:t.name,radius:t.radius})}function Eg(e){return v.createElement(pg,gl({shapeType:"rectangle",propTransformer:tk,activeClassName:"recharts-active-bar"},e))}var rk=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return(n,i)=>{if(N(t))return t;var a=N(n)||V(n);return a?t(n,i):(a||GC(),r)}};function Ba(e,t){var r,n,i=D(c=>qt(c,e)),a=D(c=>ir(c,t)),o=(r=i==null?void 0:i.allowDataOverflow)!==null&&r!==void 0?r:Fe.allowDataOverflow,s=(n=a==null?void 0:a.allowDataOverflow)!==null&&n!==void 0?n:Ue.allowDataOverflow,l=o||s;return{needClip:l,needClipX:o,needClipY:s}}function bc(e){var{xAxisId:t,yAxisId:r,clipPathId:n}=e,i=La(),{needClipX:a,needClipY:o,needClip:s}=Ba(t,r);if(!s)return null;var{x:l,y:c,width:u,height:f}=i;return v.createElement("clipPath",{id:"clipPath-".concat(n)},v.createElement("rect",{x:a?l:l-u/2,y:o?c:c-f/2,width:a?u:u*2,height:o?f:f*2}))}var nk=["onMouseEnter","onMouseLeave","onClick"],ik=["value","background","tooltipPosition"],ak=["onMouseEnter","onClick","onMouseLeave"];function $n(){return $n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$n.apply(null,arguments)}function vv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Me(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?vv(Object(r),!0).forEach(function(n){Ka(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ka(e,t,r){return(t=ok(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ok(e){var t=sk(e,"string");return typeof t=="symbol"?t:t+""}function sk(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function bl(e,t){if(e==null)return{};var r,n,i=lk(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function lk(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var uk=e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:er(r,t),payload:e}]};function ck(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,unit:s}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,dataKey:t,nameKey:void 0,name:er(a,t),hide:o,type:e.tooltipType,color:e.fill,unit:s}}}function fk(e){var t=D(Jt),{data:r,dataKey:n,background:i,allOtherBarProps:a}=e,{onMouseEnter:o,onMouseLeave:s,onClick:l}=a,c=bl(a,nk),u=vc(o,n),f=pc(s),d=mc(l,n);if(!i||r==null)return null;var h=z(i,!1);return v.createElement(v.Fragment,null,r.map((p,m)=>{var{value:y,background:g,tooltipPosition:x}=p,w=bl(p,ik);if(!g)return null;var P=u(p,m),O=f(p,m),A=d(p,m),S=Me(Me(Me(Me(Me({option:i,isActive:String(m)===t},w),{},{fill:"#eee"},g),h),Ln(c,p,m)),{},{onMouseEnter:P,onMouseLeave:O,onClick:A,dataKey:n,index:m,className:"recharts-bar-background-rectangle"});return v.createElement(Eg,$n({key:"background-bar-".concat(m)},S))}))}function _g(e){var{data:t,props:r,showLabels:n}=e,i=z(r,!1),{shape:a,dataKey:o,activeBar:s}=r,l=D(Jt),c=D(Gy),{onMouseEnter:u,onClick:f,onMouseLeave:d}=r,h=bl(r,ak),p=vc(u,o),m=pc(d),y=mc(f,o);return t?v.createElement(v.Fragment,null,t.map((g,x)=>{var w=s&&String(x)===l&&(c==null||o===c),P=w?s:a,O=Me(Me(Me({},i),g),{},{isActive:w,option:P,index:x,dataKey:o});return v.createElement(ae,$n({className:"recharts-bar-rectangle"},Ln(h,g,x),{onMouseEnter:p(g,x),onMouseLeave:m(g,x),onClick:y(g,x),key:"rectangle-".concat(g==null?void 0:g.x,"-").concat(g==null?void 0:g.y,"-").concat(g==null?void 0:g.value,"-").concat(x)}),v.createElement(Eg,O))}),n&&Mt.renderCallByParent(r,t)):null}function dk(e){var{props:t,previousRectanglesRef:r}=e,{data:n,layout:i,isAnimationActive:a,animationBegin:o,animationDuration:s,animationEasing:l,onAnimationEnd:c,onAnimationStart:u}=t,f=r.current,d=$a(t,"recharts-bar-"),[h,p]=v.useState(!1),m=v.useCallback(()=>{typeof c=="function"&&c(),p(!1)},[c]),y=v.useCallback(()=>{typeof u=="function"&&u(),p(!0)},[u]);return v.createElement(It,{begin:o,duration:s,isActive:a,easing:l,from:{t:0},to:{t:1},onAnimationEnd:m,onAnimationStart:y,key:d},g=>{var{t:x}=g,w=x===1?n:n.map((P,O)=>{var A=f&&f[O];if(A){var S=He(A.x,P.x),E=He(A.y,P.y),_=He(A.width,P.width),M=He(A.height,P.height);return Me(Me({},P),{},{x:S(x),y:E(x),width:_(x),height:M(x)})}if(i==="horizontal"){var C=He(0,P.height),k=C(x);return Me(Me({},P),{},{y:P.y+P.height-k,height:k})}var R=He(0,P.width),B=R(x);return Me(Me({},P),{},{width:B})});return x>0&&(r.current=w),v.createElement(ae,null,v.createElement(_g,{props:t,data:w,showLabels:!h}))})}function hk(e){var{data:t,isAnimationActive:r}=e,n=v.useRef(null);return r&&t&&t.length&&(n.current==null||n.current!==t)?v.createElement(dk,{previousRectanglesRef:n,props:e}):v.createElement(_g,{props:e,data:t,showLabels:!0})}var Tg=0,vk=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:Q(e,t)}};class pk extends v.PureComponent{constructor(){super(...arguments),Ka(this,"id",Qt("recharts-bar-"))}render(){var{hide:t,data:r,dataKey:n,className:i,xAxisId:a,yAxisId:o,needClip:s,background:l,id:c,layout:u}=this.props;if(t)return null;var f=W("recharts-bar",i),d=V(c)?this.id:c;return v.createElement(ae,{className:f},s&&v.createElement("defs",null,v.createElement(bc,{clipPathId:d,xAxisId:a,yAxisId:o})),v.createElement(ae,{className:"recharts-bar-rectangles",clipPath:s?"url(#clipPath-".concat(d,")"):null},v.createElement(fk,{data:r,dataKey:n,background:l,allOtherBarProps:this.props}),v.createElement(hk,this.props)),v.createElement(Ag,{direction:u==="horizontal"?"y":"x"},this.props.children))}}var Cg={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!tr.isSsr,legendType:"rect",minPointSize:Tg,xAxisId:0,yAxisId:0};function mk(e){var{xAxisId:t,yAxisId:r,hide:n,legendType:i,minPointSize:a,activeBar:o,animationBegin:s,animationDuration:l,animationEasing:c,isAnimationActive:u}=Je(e,Cg),{needClip:f}=Ba(t,r),d=zn(),h=Ie(),p=v.useMemo(()=>({barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:a,stackId:Xl(e.stackId)}),[e.barSize,e.dataKey,e.maxBarSize,a,e.stackId]),m=Bn(e.children,wr),y=D(w=>Fk(w,t,r,h,p,m));if(d!=="vertical"&&d!=="horizontal")return null;var g,x=y==null?void 0:y[0];return x==null||x.height==null||x.width==null?g=0:g=d==="vertical"?x.height/2:x.width/2,v.createElement(wg,{xAxisId:t,yAxisId:r,data:y,dataPointFormatter:vk,errorBarOffset:g},v.createElement(pk,$n({},e,{layout:d,needClip:f,data:y,xAxisId:t,yAxisId:r,hide:n,legendType:i,minPointSize:a,activeBar:o,animationBegin:s,animationDuration:l,animationEasing:c,isAnimationActive:u})))}function yk(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:i,bandSize:a,xAxis:o,yAxis:s,xAxisTicks:l,yAxisTicks:c,stackedData:u,displayedData:f,offset:d,cells:h}=e,p=t==="horizontal"?s:o,m=u?p.scale.domain():null,y=t1({numericAxis:p});return f.map((g,x)=>{var w,P,O,A,S,E;u?w=Xw(u[x],m):(w=Q(g,r),Array.isArray(w)||(w=[y,w]));var _=rk(n,Tg)(w[1],x);if(t==="horizontal"){var M,[C,k]=[s.scale(w[0]),s.scale(w[1])];P=Jf({axis:o,ticks:l,bandSize:a,offset:i.offset,entry:g,index:x}),O=(M=k??C)!==null&&M!==void 0?M:void 0,A=i.size;var R=C-k;if(S=Ke(R)?0:R,E={x:P,y:d.top,width:A,height:d.height},Math.abs(_)>0&&Math.abs(S)<Math.abs(_)){var B=Oe(S||_)*(Math.abs(_)-Math.abs(S));O-=B,S+=B}}else{var[U,X]=[o.scale(w[0]),o.scale(w[1])];if(P=U,O=Jf({axis:s,ticks:c,bandSize:a,offset:i.offset,entry:g,index:x}),A=X-U,S=i.size,E={x:d.left,y:O,width:d.width,height:S},Math.abs(_)>0&&Math.abs(A)<Math.abs(_)){var K=Oe(A||_)*(Math.abs(_)-Math.abs(A));A+=K}}var he=Me(Me({},g),{},{x:P,y:O,width:A,height:S,value:u?w:w[1],payload:g,background:E,tooltipPosition:{x:P+A/2,y:O+S/2}},h&&h[x]&&h[x].props);return he})}class Xi extends v.PureComponent{render(){return v.createElement(gc,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},v.createElement(MC,null),v.createElement(yc,{legendPayload:uk(this.props)}),v.createElement(Da,{fn:ck,args:this.props}),v.createElement(mk,this.props))}}Ka(Xi,"displayName","Bar");Ka(Xi,"defaultProps",Cg);function pv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function vi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?pv(Object(r),!0).forEach(function(n){gk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function gk(e,t,r){return(t=bk(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bk(e){var t=xk(e,"string");return typeof t=="symbol"?t:t+""}function xk(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var wk=(e,t)=>t,Pk=(e,t,r)=>r,Ok=(e,t,r,n)=>n,xc=(e,t,r,n,i)=>i,Ak=(e,t,r,n,i)=>i.maxBarSize,Sk=(e,t,r,n,i,a)=>a,mv=(e,t,r)=>{var n=r??e;if(!V(n))return $e(n,t,0)},jk=j([F,Vn,wk,Pk,Ok],(e,t,r,n,i)=>t.filter(a=>e==="horizontal"?a.xAxisId===r:a.yAxisId===n).filter(a=>a.isPanorama===i).filter(a=>a.hide===!1).filter(a=>a.type==="bar")),Ek=(e,t,r,n)=>{var i=F(e);return i==="horizontal"?Nn(e,"yAxis",r,n):Nn(e,"xAxis",t,n)},_k=(e,t,r)=>{var n=F(e);return n==="horizontal"?gh(e,"xAxis",t):gh(e,"yAxis",r)};function Tk(e){return e.stackId!=null&&e.dataKey!=null}var Ck=(e,t,r)=>{var n={},i=e.filter(Tk),a=e.filter(c=>c.stackId==null),o=i.reduce((c,u)=>(c[u.stackId]||(c[u.stackId]=[]),c[u.stackId].push(u),c),n),s=Object.entries(o).map(c=>{var[u,f]=c,d=f.map(p=>p.dataKey),h=mv(t,r,f[0].barSize);return{stackId:u,dataKeys:d,barSize:h}}),l=a.map(c=>{var u=[c.dataKey].filter(d=>d!=null),f=mv(t,r,c.barSize);return{stackId:void 0,dataKeys:u,barSize:f}});return[...s,...l]},kk=j([jk,BS,_k],Ck),Mk=(e,t,r,n,i)=>{var a,o,s=F(e),l=ey(e),{maxBarSize:c}=i,u=V(c)?l:c,f,d;return s==="horizontal"?(f=ft(e,"xAxis",t,n),d=Ot(e,"xAxis",t,n)):(f=ft(e,"yAxis",r,n),d=Ot(e,"yAxis",r,n)),(a=(o=Ar(f,d,!0))!==null&&o!==void 0?o:u)!==null&&a!==void 0?a:0},kg=(e,t,r,n)=>{var i=F(e),a,o;return i==="horizontal"?(a=ft(e,"xAxis",t,n),o=Ot(e,"xAxis",t,n)):(a=ft(e,"yAxis",r,n),o=Ot(e,"yAxis",r,n)),Ar(a,o)};function Nk(e,t,r,n,i){var a=n.length;if(!(a<1)){var o=$e(e,r,0,!0),s,l=[];if(Te(n[0].barSize)){var c=!1,u=r/a,f=n.reduce((g,x)=>g+(x.barSize||0),0);f+=(a-1)*o,f>=r&&(f-=(a-1)*o,o=0),f>=r&&u>0&&(c=!0,u*=.9,f=a*u);var d=(r-f)/2>>0,h={offset:d-o,size:0};s=n.reduce((g,x)=>{var w,P={stackId:x.stackId,dataKeys:x.dataKeys,position:{offset:h.offset+h.size+o,size:c?u:(w=x.barSize)!==null&&w!==void 0?w:0}},O=[...g,P];return h=O[O.length-1].position,O},l)}else{var p=$e(t,r,0,!0);r-2*p-(a-1)*o<=0&&(o=0);var m=(r-2*p-(a-1)*o)/a;m>1&&(m>>=0);var y=Te(i)?Math.min(m,i):m;s=n.reduce((g,x,w)=>[...g,{stackId:x.stackId,dataKeys:x.dataKeys,position:{offset:p+(m+o)*w+(m-y)/2,size:y}}],l)}return s}}var Ik=(e,t,r,n,i,a,o)=>{var s=V(o)?t:o,l=Nk(r,n,i!==a?i:a,e,s);return i!==a&&l!=null&&(l=l.map(c=>vi(vi({},c),{},{position:vi(vi({},c.position),{},{offset:c.position.offset-i/2})}))),l},Dk=j([kk,ey,LS,ty,Mk,kg,Ak],Ik),$k=(e,t,r,n)=>ft(e,"xAxis",t,n),Rk=(e,t,r,n)=>ft(e,"yAxis",r,n),Lk=(e,t,r,n)=>Ot(e,"xAxis",t,n),Bk=(e,t,r,n)=>Ot(e,"yAxis",r,n),Kk=j([Dk,xc],(e,t)=>{if(e!=null){var r=e.find(n=>n.stackId===t.stackId&&n.dataKeys.includes(t.dataKey));if(r!=null)return r.position}}),zk=(e,t)=>{if(!(!e||(t==null?void 0:t.dataKey)==null)){var{stackId:r}=t;if(r!=null){var n=e[r];if(n){var{stackedData:i}=n;if(i){var a=i.find(o=>o.key===t.dataKey);return a}}}}},qk=j([Vn,xc],(e,t)=>{if(e.some(r=>r.type==="bar"&&t.dataKey===r.dataKey&&t.stackId===r.stackId&&t.stackId===r.stackId))return t}),Wk=j([Ek,xc],zk),Fk=j([pe,$k,Rk,Lk,Bk,Kk,F,Oa,kg,Wk,qk,Sk],(e,t,r,n,i,a,o,s,l,c,u,f)=>{var{chartData:d,dataStartIndex:h,dataEndIndex:p}=s;if(!(u==null||a==null||o!=="horizontal"&&o!=="vertical"||t==null||r==null||n==null||i==null||l==null)){var{data:m}=u,y;if(m!=null&&m.length>0?y=m:y=d==null?void 0:d.slice(h,p+1),y!=null)return yk({layout:o,barSettings:u,pos:a,bandSize:l,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,stackedData:c,displayedData:y,offset:e,cells:f})}}),Mg=e=>{var{chartData:t}=e,r=ie(),n=Ie();return v.useEffect(()=>n?()=>{}:(r(_h(t)),()=>{r(_h(void 0))}),[t,r,n]),null},yv={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},Ng=dt({name:"brush",initialState:yv,reducers:{setBrushSettings(e,t){return t.payload==null?yv:t.payload}}}),{setBrushSettings:WD}=Ng.actions,Uk=Ng.reducer;function Yk(e,t,r){return(t=Hk(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Hk(e){var t=Gk(e,"string");return typeof t=="symbol"?t:t+""}function Gk(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}class wc{static create(t){return new wc(t)}constructor(t){this.scale=t}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(t){var{bandAware:r,position:n}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t!==void 0){if(n)switch(n){case"start":return this.scale(t);case"middle":{var i=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+i}case"end":{var a=this.bandwidth?this.bandwidth():0;return this.scale(t)+a}default:return this.scale(t)}if(r){var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o}return this.scale(t)}}isInRange(t){var r=this.range(),n=r[0],i=r[r.length-1];return n<=i?t>=n&&t<=i:t>=i&&t<=n}}Yk(wc,"EPS",1e-4);function Vk(e){return(e%180+180)%180}var Xk=function(t){var{width:r,height:n}=t,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=Vk(i),o=a*Math.PI/180,s=Math.atan(n/r),l=o>s&&o<Math.PI-s?n/Math.sin(o):r/Math.cos(o);return Math.abs(l)},Zk={dots:[],areas:[],lines:[]},Ig=dt({name:"referenceElements",initialState:Zk,reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=Ct(e).dots.findIndex(n=>n===t.payload);r!==-1&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=Ct(e).areas.findIndex(n=>n===t.payload);r!==-1&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=Ct(e).lines.findIndex(n=>n===t.payload);r!==-1&&e.lines.splice(r,1)}}}),{addDot:FD,removeDot:UD,addArea:YD,removeArea:HD,addLine:GD,removeLine:VD}=Ig.actions,Qk=Ig.reducer,Jk=v.createContext(void 0),eM=e=>{var{children:t}=e,[r]=v.useState("".concat(Qt("recharts"),"-clip")),n=La();if(n==null)return null;var{x:i,y:a,width:o,height:s}=n;return v.createElement(Jk.Provider,{value:r},v.createElement("defs",null,v.createElement("clipPath",{id:r},v.createElement("rect",{x:i,y:a,height:s,width:o}))),t)};function Os(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function Dg(e,t,r){if(t<1)return[];if(t===1&&r===void 0)return e;for(var n=[],i=0;i<e.length;i+=t)n.push(e[i]);return n}function tM(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return Xk(n,r)}function rM(e,t,r){var n=r==="width",{x:i,y:a,width:o,height:s}=e;return t===1?{start:n?i:a,end:n?i+o:a+s}:{start:n?i+o:a+s,end:n?i:a}}function Zi(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function nM(e,t){return Dg(e,t+1)}function iM(e,t,r,n,i){for(var a=(n||[]).slice(),{start:o,end:s}=t,l=0,c=1,u=o,f=function(){var p=n==null?void 0:n[l];if(p===void 0)return{v:Dg(n,c)};var m=l,y,g=()=>(y===void 0&&(y=r(p,m)),y),x=p.coordinate,w=l===0||Zi(e,x,g,u,s);w||(l=0,u=o,c+=1),w&&(u=x+e*(g()/2+i),l+=c)},d;c<=a.length;)if(d=f(),d)return d.v;return[]}function gv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ke(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?gv(Object(r),!0).forEach(function(n){aM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function aM(e,t,r){return(t=oM(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function oM(e){var t=sM(e,"string");return typeof t=="symbol"?t:t+""}function sM(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function lM(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:s}=t,{end:l}=t,c=function(d){var h=a[d],p,m=()=>(p===void 0&&(p=r(h,d)),p);if(d===o-1){var y=e*(h.coordinate+e*m()/2-l);a[d]=h=ke(ke({},h),{},{tickCoord:y>0?h.coordinate-y*e:h.coordinate})}else a[d]=h=ke(ke({},h),{},{tickCoord:h.coordinate});var g=Zi(e,h.tickCoord,m,s,l);g&&(l=h.tickCoord-e*(m()/2+i),a[d]=ke(ke({},h),{},{isShow:!0}))},u=o-1;u>=0;u--)c(u);return a}function uM(e,t,r,n,i,a){var o=(n||[]).slice(),s=o.length,{start:l,end:c}=t;if(a){var u=n[s-1],f=r(u,s-1),d=e*(u.coordinate+e*f/2-c);o[s-1]=u=ke(ke({},u),{},{tickCoord:d>0?u.coordinate-d*e:u.coordinate});var h=Zi(e,u.tickCoord,()=>f,l,c);h&&(c=u.tickCoord-e*(f/2+i),o[s-1]=ke(ke({},u),{},{isShow:!0}))}for(var p=a?s-1:s,m=function(x){var w=o[x],P,O=()=>(P===void 0&&(P=r(w,x)),P);if(x===0){var A=e*(w.coordinate-e*O()/2-l);o[x]=w=ke(ke({},w),{},{tickCoord:A<0?w.coordinate-A*e:w.coordinate})}else o[x]=w=ke(ke({},w),{},{tickCoord:w.coordinate});var S=Zi(e,w.tickCoord,O,l,c);S&&(l=w.tickCoord+e*(O()/2+i),o[x]=ke(ke({},w),{},{isShow:!0}))},y=0;y<p;y++)m(y);return o}function Pc(e,t,r){var{tick:n,ticks:i,viewBox:a,minTickGap:o,orientation:s,interval:l,tickFormatter:c,unit:u,angle:f}=e;if(!i||!i.length||!n)return[];if(N(l)||tr.isSsr){var d;return(d=nM(i,N(l)?l:0))!==null&&d!==void 0?d:[]}var h=[],p=s==="top"||s==="bottom"?"width":"height",m=u&&p==="width"?wn(u,{fontSize:t,letterSpacing:r}):{width:0,height:0},y=(w,P)=>{var O=typeof c=="function"?c(w.value,P):w.value;return p==="width"?tM(wn(O,{fontSize:t,letterSpacing:r}),m,f):wn(O,{fontSize:t,letterSpacing:r})[p]},g=i.length>=2?Oe(i[1].coordinate-i[0].coordinate):1,x=rM(a,g,p);return l==="equidistantPreserveStart"?iM(g,x,y,i,o):(l==="preserveStart"||l==="preserveStartEnd"?h=uM(g,x,y,i,o,l==="preserveStartEnd"):h=lM(g,x,y,i,o),h.filter(w=>w.isShow))}var cM=["viewBox"],fM=["viewBox"];function Kr(){return Kr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Kr.apply(null,arguments)}function bv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ve(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?bv(Object(r),!0).forEach(function(n){Oc(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function xv(e,t){if(e==null)return{};var r,n,i=dM(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function dM(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function Oc(e,t,r){return(t=hM(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hM(e){var t=vM(e,"string");return typeof t=="symbol"?t:t+""}function vM(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}class ar extends v.Component{constructor(t){super(t),this.tickRefs=v.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(t,r){var{viewBox:n}=t,i=xv(t,cM),a=this.props,{viewBox:o}=a,s=xv(a,fM);return!Os(n,o)||!Os(i,s)||!Os(r,this.state)}getTickLineCoord(t){var{x:r,y:n,width:i,height:a,orientation:o,tickSize:s,mirror:l,tickMargin:c}=this.props,u,f,d,h,p,m,y=l?-1:1,g=t.tickSize||s,x=N(t.tickCoord)?t.tickCoord:t.coordinate;switch(o){case"top":u=f=t.coordinate,h=n+ +!l*a,d=h-y*g,m=d-y*c,p=x;break;case"left":d=h=t.coordinate,f=r+ +!l*i,u=f-y*g,p=u-y*c,m=x;break;case"right":d=h=t.coordinate,f=r+ +l*i,u=f+y*g,p=u+y*c,m=x;break;default:u=f=t.coordinate,h=n+ +l*a,d=h+y*g,m=d+y*c,p=x;break}return{line:{x1:u,y1:d,x2:f,y2:h},tick:{x:p,y:m}}}getTickTextAnchor(){var{orientation:t,mirror:r}=this.props,n;switch(t){case"left":n=r?"start":"end";break;case"right":n=r?"end":"start";break;default:n="middle";break}return n}getTickVerticalAnchor(){var{orientation:t,mirror:r}=this.props;switch(t){case"left":case"right":return"middle";case"top":return r?"start":"end";default:return r?"end":"start"}}renderAxisLine(){var{x:t,y:r,width:n,height:i,orientation:a,mirror:o,axisLine:s}=this.props,l=ve(ve(ve({},z(this.props,!1)),z(s,!1)),{},{fill:"none"});if(a==="top"||a==="bottom"){var c=+(a==="top"&&!o||a==="bottom"&&o);l=ve(ve({},l),{},{x1:t,y1:r+c*i,x2:t+n,y2:r+c*i})}else{var u=+(a==="left"&&!o||a==="right"&&o);l=ve(ve({},l),{},{x1:t+u*n,y1:r,x2:t+u*n,y2:r+i})}return v.createElement("line",Kr({},l,{className:W("recharts-cartesian-axis-line",Zt(s,"className"))}))}static renderTickItem(t,r,n){var i,a=W(r.className,"recharts-cartesian-axis-tick-value");if(v.isValidElement(t))i=v.cloneElement(t,ve(ve({},r),{},{className:a}));else if(typeof t=="function")i=t(ve(ve({},r),{},{className:a}));else{var o="recharts-cartesian-axis-tick-value";typeof t!="boolean"&&(o=W(o,t.className)),i=v.createElement(Ia,Kr({},r,{className:o}),n)}return i}renderTicks(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[],{tickLine:i,stroke:a,tick:o,tickFormatter:s,unit:l}=this.props,c=Pc(ve(ve({},this.props),{},{ticks:n}),t,r),u=this.getTickTextAnchor(),f=this.getTickVerticalAnchor(),d=z(this.props,!1),h=z(o,!1),p=ve(ve({},d),{},{fill:"none"},z(i,!1)),m=c.map((y,g)=>{var{line:x,tick:w}=this.getTickLineCoord(y),P=ve(ve(ve(ve({textAnchor:u,verticalAnchor:f},d),{},{stroke:"none",fill:a},h),w),{},{index:g,payload:y,visibleTicksCount:c.length,tickFormatter:s});return v.createElement(ae,Kr({className:"recharts-cartesian-axis-tick",key:"tick-".concat(y.value,"-").concat(y.coordinate,"-").concat(y.tickCoord)},Ln(this.props,y,g)),i&&v.createElement("line",Kr({},p,x,{className:W("recharts-cartesian-axis-tick-line",Zt(i,"className"))})),o&&ar.renderTickItem(o,P,"".concat(typeof s=="function"?s(y.value,g):y.value).concat(l||"")))});return m.length>0?v.createElement("g",{className:"recharts-cartesian-axis-ticks"},m):null}render(){var{axisLine:t,width:r,height:n,className:i,hide:a}=this.props;if(a)return null;var{ticks:o}=this.props;return r!=null&&r<=0||n!=null&&n<=0?null:v.createElement(ae,{className:W("recharts-cartesian-axis",i),ref:s=>{if(s){var l=s.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(l);var c=l[0];if(c){var u=window.getComputedStyle(c).fontSize,f=window.getComputedStyle(c).letterSpacing;(u!==this.state.fontSize||f!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(c).fontSize,letterSpacing:window.getComputedStyle(c).letterSpacing})}}}},t&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,o),Ge.renderCallByParent(this.props))}}Oc(ar,"displayName","CartesianAxis");Oc(ar,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var pM=["x1","y1","x2","y2","key"],mM=["offset"],yM=["xAxisId","yAxisId"],gM=["xAxisId","yAxisId"];function wv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ne(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?wv(Object(r),!0).forEach(function(n){bM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function bM(e,t,r){return(t=xM(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xM(e){var t=wM(e,"string");return typeof t=="symbol"?t:t+""}function wM(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function pr(){return pr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},pr.apply(null,arguments)}function Qi(e,t){if(e==null)return{};var r,n,i=PM(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function PM(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var OM=e=>{var{fill:t}=e;if(!t||t==="none")return null;var{fillOpacity:r,x:n,y:i,width:a,height:o,ry:s}=e;return v.createElement("rect",{x:n,y:i,ry:s,width:a,height:o,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function $g(e,t){var r;if(v.isValidElement(e))r=v.cloneElement(e,t);else if(typeof e=="function")r=e(t);else{var{x1:n,y1:i,x2:a,y2:o,key:s}=t,l=Qi(t,pM),c=z(l,!1),{offset:u}=c,f=Qi(c,mM);r=v.createElement("line",pr({},f,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:s}))}return r}function AM(e){var{x:t,width:r,horizontal:n=!0,horizontalPoints:i}=e;if(!n||!i||!i.length)return null;var{xAxisId:a,yAxisId:o}=e,s=Qi(e,yM),l=i.map((c,u)=>{var f=Ne(Ne({},s),{},{x1:t,y1:c,x2:t+r,y2:c,key:"line-".concat(u),index:u});return $g(n,f)});return v.createElement("g",{className:"recharts-cartesian-grid-horizontal"},l)}function SM(e){var{y:t,height:r,vertical:n=!0,verticalPoints:i}=e;if(!n||!i||!i.length)return null;var{xAxisId:a,yAxisId:o}=e,s=Qi(e,gM),l=i.map((c,u)=>{var f=Ne(Ne({},s),{},{x1:c,y1:t,x2:c,y2:t+r,key:"line-".concat(u),index:u});return $g(n,f)});return v.createElement("g",{className:"recharts-cartesian-grid-vertical"},l)}function jM(e){var{horizontalFill:t,fillOpacity:r,x:n,y:i,width:a,height:o,horizontalPoints:s,horizontal:l=!0}=e;if(!l||!t||!t.length)return null;var c=s.map(f=>Math.round(f+i-i)).sort((f,d)=>f-d);i!==c[0]&&c.unshift(0);var u=c.map((f,d)=>{var h=!c[d+1],p=h?i+o-f:c[d+1]-f;if(p<=0)return null;var m=d%t.length;return v.createElement("rect",{key:"react-".concat(d),y:f,x:n,height:p,width:a,stroke:"none",fill:t[m],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return v.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},u)}function EM(e){var{vertical:t=!0,verticalFill:r,fillOpacity:n,x:i,y:a,width:o,height:s,verticalPoints:l}=e;if(!t||!r||!r.length)return null;var c=l.map(f=>Math.round(f+i-i)).sort((f,d)=>f-d);i!==c[0]&&c.unshift(0);var u=c.map((f,d)=>{var h=!c[d+1],p=h?i+o-f:c[d+1]-f;if(p<=0)return null;var m=d%r.length;return v.createElement("rect",{key:"react-".concat(d),x:f,y:a,width:p,height:s,stroke:"none",fill:r[m],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return v.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},u)}var _M=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return zp(Pc(Ne(Ne(Ne({},ar.defaultProps),r),{},{ticks:qp(r),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},TM=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return zp(Pc(Ne(Ne(Ne({},ar.defaultProps),r),{},{ticks:qp(r),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},CM={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function yi(e){var t=eu(),r=tu(),n=Hp(),i=Ne(Ne({},Je(e,CM)),{},{x:N(e.x)?e.x:n.left,y:N(e.y)?e.y:n.top,width:N(e.width)?e.width:n.width,height:N(e.height)?e.height:n.height}),{xAxisId:a,yAxisId:o,x:s,y:l,width:c,height:u,syncWithTicks:f,horizontalValues:d,verticalValues:h}=i,p=Ie(),m=D(_=>bh(_,"xAxis",a,p)),y=D(_=>bh(_,"yAxis",o,p));if(!N(c)||c<=0||!N(u)||u<=0||!N(s)||s!==+s||!N(l)||l!==+l)return null;var g=i.verticalCoordinatesGenerator||_M,x=i.horizontalCoordinatesGenerator||TM,{horizontalPoints:w,verticalPoints:P}=i;if((!w||!w.length)&&typeof x=="function"){var O=d&&d.length,A=x({yAxis:y?Ne(Ne({},y),{},{ticks:O?d:y.ticks}):void 0,width:t,height:r,offset:n},O?!0:f);xn(Array.isArray(A),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof A,"]")),Array.isArray(A)&&(w=A)}if((!P||!P.length)&&typeof g=="function"){var S=h&&h.length,E=g({xAxis:m?Ne(Ne({},m),{},{ticks:S?h:m.ticks}):void 0,width:t,height:r,offset:n},S?!0:f);xn(Array.isArray(E),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof E,"]")),Array.isArray(E)&&(P=E)}return v.createElement("g",{className:"recharts-cartesian-grid"},v.createElement(OM,{fill:i.fill,fillOpacity:i.fillOpacity,x:i.x,y:i.y,width:i.width,height:i.height,ry:i.ry}),v.createElement(jM,pr({},i,{horizontalPoints:w})),v.createElement(EM,pr({},i,{verticalPoints:P})),v.createElement(AM,pr({},i,{offset:n,horizontalPoints:w,xAxis:m,yAxis:y})),v.createElement(SM,pr({},i,{offset:n,verticalPoints:P,xAxis:m,yAxis:y})))}yi.displayName="CartesianGrid";var Rg=(e,t,r,n)=>ft(e,"xAxis",t,n),Lg=(e,t,r,n)=>Ot(e,"xAxis",t,n),Bg=(e,t,r,n)=>ft(e,"yAxis",r,n),Kg=(e,t,r,n)=>Ot(e,"yAxis",r,n),kM=j([F,Rg,Bg,Lg,Kg],(e,t,r,n,i)=>At(e,"xAxis")?Ar(t,n,!1):Ar(r,i,!1)),MM=(e,t,r,n,i)=>i,NM=j([Vn,MM],(e,t)=>{if(e.some(r=>r.type==="line"&&t.dataKey===r.dataKey&&t.data===r.data))return t}),IM=j([F,Rg,Bg,Lg,Kg,NM,kM,Oa],(e,t,r,n,i,a,o,s)=>{var{chartData:l,dataStartIndex:c,dataEndIndex:u}=s;if(!(a==null||t==null||r==null||n==null||i==null||n.length===0||i.length===0||o==null)){var{dataKey:f,data:d}=a,h;if(d!=null&&d.length>0?h=d:h=l==null?void 0:l.slice(c,u+1),h!=null)return JM({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataKey:f,bandSize:o,displayedData:h})}}),DM=["type","layout","connectNulls","needClip"],$M=["activeDot","animateNewValues","animationBegin","animationDuration","animationEasing","connectNulls","dot","hide","isAnimationActive","label","legendType","xAxisId","yAxisId"];function zg(e,t){if(e==null)return{};var r,n,i=RM(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function RM(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function Pv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function vt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Pv(Object(r),!0).forEach(function(n){za(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Pv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function za(e,t,r){return(t=LM(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function LM(e){var t=BM(e,"string");return typeof t=="symbol"?t:t+""}function BM(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Qr(){return Qr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Qr.apply(null,arguments)}var KM=e=>{var{dataKey:t,name:r,stroke:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:er(r,t),payload:e}]};function zM(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:s,unit:l}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,nameKey:void 0,name:er(o,t),hide:s,type:e.tooltipType,color:e.stroke,unit:l}}}var qg=(e,t)=>"".concat(t,"px ").concat(e-t,"px");function qM(e,t){for(var r=e.length%2!==0?[...e,0]:e,n=[],i=0;i<t;++i)n=[...n,...r];return n}var WM=(e,t,r)=>{var n=r.reduce((f,d)=>f+d);if(!n)return qg(t,e);for(var i=Math.floor(e/n),a=e%n,o=t-e,s=[],l=0,c=0;l<r.length;c+=r[l],++l)if(c+r[l]>a){s=[...r.slice(0,l),a-c];break}var u=s.length%2===0?[0,o]:[o];return[...qM(r,i),...s,...u].map(f=>"".concat(f,"px")).join(", ")};function FM(e,t){var r;if(v.isValidElement(e))r=v.cloneElement(e,t);else if(typeof e=="function")r=e(t);else{var n=W("recharts-line-dot",typeof e!="boolean"?e.className:"");r=v.createElement(uc,Qr({},t,{className:n}))}return r}function UM(e,t){return e==null?!1:t?!0:e.length===1}function YM(e){var{clipPathId:t,points:r,props:n}=e,{dot:i,dataKey:a,needClip:o}=n;if(!UM(r,i))return null;var s=na(i),l=z(n,!1),c=z(i,!0),u=r.map((d,h)=>{var p=vt(vt(vt({key:"dot-".concat(h),r:3},l),c),{},{index:h,cx:d.x,cy:d.y,dataKey:a,value:d.value,payload:d.payload,points:r});return FM(i,p)}),f={clipPath:o?"url(#clipPath-".concat(s?"":"dots-").concat(t,")"):null};return v.createElement(ae,Qr({className:"recharts-line-dots",key:"dots"},f),u)}function xl(e){var{clipPathId:t,pathRef:r,points:n,strokeDasharray:i,props:a,showLabels:o}=e,{type:s,layout:l,connectNulls:c,needClip:u}=a,f=zg(a,DM),d=vt(vt({},z(f,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:u?"url(#clipPath-".concat(t,")"):null,points:n,type:s,layout:l,connectNulls:c,strokeDasharray:i??a.strokeDasharray});return v.createElement(v.Fragment,null,(n==null?void 0:n.length)>1&&v.createElement(qr,Qr({},d,{pathRef:r})),v.createElement(YM,{points:n,clipPathId:t,props:a}),o&&Mt.renderCallByParent(a,n))}function HM(e){try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch{return 0}}function GM(e){var{clipPathId:t,props:r,pathRef:n,previousPointsRef:i,longestAnimatedLengthRef:a}=e,{points:o,strokeDasharray:s,isAnimationActive:l,animationBegin:c,animationDuration:u,animationEasing:f,animateNewValues:d,width:h,height:p,onAnimationEnd:m,onAnimationStart:y}=r,g=i.current,x=$a(r,"recharts-line-"),[w,P]=v.useState(!1),O=v.useCallback(()=>{typeof m=="function"&&m(),P(!1)},[m]),A=v.useCallback(()=>{typeof y=="function"&&y(),P(!0)},[y]),S=HM(n.current),E=a.current;return v.createElement(It,{begin:c,duration:u,isActive:l,easing:f,from:{t:0},to:{t:1},onAnimationEnd:O,onAnimationStart:A,key:x},_=>{var{t:M}=_,C=He(E,S+E),k=Math.min(C(M),S),R;if(s){var B="".concat(s).split(/[,\s]+/gim).map(K=>parseFloat(K));R=WM(k,S,B)}else R=qg(S,k);if(g){var U=g.length/o.length,X=M===1?o:o.map((K,he)=>{var se=Math.floor(he*U);if(g[se]){var ze=g[se],et=He(ze.x,K.x),L=He(ze.y,K.y);return vt(vt({},K),{},{x:et(M),y:L(M)})}if(d){var je=He(h*2,K.x),or=He(p/2,K.y);return vt(vt({},K),{},{x:je(M),y:or(M)})}return vt(vt({},K),{},{x:K.x,y:K.y})});return i.current=X,v.createElement(xl,{props:r,points:X,clipPathId:t,pathRef:n,showLabels:!w,strokeDasharray:R})}return M>0&&S>0&&(i.current=o,a.current=k),v.createElement(xl,{props:r,points:o,clipPathId:t,pathRef:n,showLabels:!w,strokeDasharray:R})})}function VM(e){var{clipPathId:t,props:r}=e,{points:n,isAnimationActive:i}=r,a=v.useRef(null),o=v.useRef(0),s=v.useRef(null),l=a.current;return i&&n&&n.length&&l!==n?v.createElement(GM,{props:r,clipPathId:t,previousPointsRef:a,longestAnimatedLengthRef:o,pathRef:s}):v.createElement(xl,{props:r,points:n,clipPathId:t,pathRef:s,showLabels:!0})}var XM=(e,t)=>({x:e.x,y:e.y,value:e.value,errorVal:Q(e.payload,t)});class ZM extends v.Component{constructor(){super(...arguments),za(this,"id",Qt("recharts-line-"))}render(){var t,{hide:r,dot:n,points:i,className:a,xAxisId:o,yAxisId:s,top:l,left:c,width:u,height:f,id:d,needClip:h,layout:p}=this.props;if(r)return null;var m=W("recharts-line",a),y=V(d)?this.id:d,{r:g=3,strokeWidth:x=2}=(t=z(n,!1))!==null&&t!==void 0?t:{r:3,strokeWidth:2},w=na(n),P=g*2+x;return v.createElement(v.Fragment,null,v.createElement(ae,{className:m},h&&v.createElement("defs",null,v.createElement(bc,{clipPathId:y,xAxisId:o,yAxisId:s}),!w&&v.createElement("clipPath",{id:"clipPath-dots-".concat(y)},v.createElement("rect",{x:c-P/2,y:l-P/2,width:u+P,height:f+P}))),v.createElement(VM,{props:this.props,clipPathId:y}),v.createElement(Ag,{direction:p==="horizontal"?"y":"x"},v.createElement(wg,{xAxisId:o,yAxisId:s,data:i,dataPointFormatter:XM,errorBarOffset:0},this.props.children))),v.createElement(yl,{activeDot:this.props.activeDot,points:i,mainColor:this.props.stroke,itemDataKey:this.props.dataKey}))}}var Wg={activeDot:!0,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!0,fill:"#fff",hide:!1,isAnimationActive:!tr.isSsr,label:!1,legendType:"line",stroke:"#3182bd",strokeWidth:1,xAxisId:0,yAxisId:0};function QM(e){var t=Je(e,Wg),{activeDot:r,animateNewValues:n,animationBegin:i,animationDuration:a,animationEasing:o,connectNulls:s,dot:l,hide:c,isAnimationActive:u,label:f,legendType:d,xAxisId:h,yAxisId:p}=t,m=zg(t,$M),{needClip:y}=Ba(h,p),{height:g,width:x,x:w,y:P}=La(),O=zn(),A=Ie(),S=v.useMemo(()=>({dataKey:e.dataKey,data:e.data}),[e.dataKey,e.data]),E=D(_=>IM(_,h,p,A,S));return O!=="horizontal"&&O!=="vertical"?null:v.createElement(ZM,Qr({},m,{connectNulls:s,dot:l,activeDot:r,animateNewValues:n,animationBegin:i,animationDuration:a,animationEasing:o,isAnimationActive:u,hide:c,label:f,legendType:d,xAxisId:h,yAxisId:p,points:E,layout:O,height:g,width:x,left:w,top:P,needClip:y}))}function JM(e){var{layout:t,xAxis:r,yAxis:n,xAxisTicks:i,yAxisTicks:a,dataKey:o,bandSize:s,displayedData:l}=e;return l.map((c,u)=>{var f=Q(c,o);return t==="horizontal"?{x:ki({axis:r,ticks:i,bandSize:s,entry:c,index:u}),y:V(f)?null:n.scale(f),value:f,payload:c}:{x:V(f)?null:r.scale(f),y:ki({axis:n,ticks:a,bandSize:s,entry:c,index:u}),value:f,payload:c}})}class Ji extends v.PureComponent{render(){return v.createElement(gc,{type:"line",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},v.createElement(yc,{legendPayload:KM(this.props)}),v.createElement(Da,{fn:zM,args:this.props}),v.createElement(QM,this.props))}}za(Ji,"displayName","Line");za(Ji,"defaultProps",Wg);var Fg=(e,t,r,n)=>ft(e,"xAxis",t,n),Ug=(e,t,r,n)=>Ot(e,"xAxis",t,n),Yg=(e,t,r,n)=>ft(e,"yAxis",r,n),Hg=(e,t,r,n)=>Ot(e,"yAxis",r,n),eN=j([F,Fg,Yg,Ug,Hg],(e,t,r,n,i)=>At(e,"xAxis")?Ar(t,n,!1):Ar(r,i,!1)),tN=(e,t,r,n,i)=>{var a,o=F(e),s=At(o,"xAxis"),l;if(s?l=Nn(e,"yAxis",r,n):l=Nn(e,"xAxis",t,n),l!=null){var{dataKey:c,stackId:u}=i;if(u!=null){var f=(a=l[u])===null||a===void 0?void 0:a.stackedData;return f==null?void 0:f.find(d=>d.key===c)}}},rN=(e,t,r,n,i)=>i,nN=j([Vn,rN],(e,t)=>{if(e.some(r=>r.type==="area"&&t.dataKey===r.dataKey&&Xl(t.stackId)===r.stackId&&t.data===r.data))return t}),iN=j([F,Fg,Yg,Ug,Hg,tN,Oa,eN,nN],(e,t,r,n,i,a,o,s,l)=>{var{chartData:c,dataStartIndex:u,dataEndIndex:f}=o;if(!(l==null||e!=="horizontal"&&e!=="vertical"||t==null||r==null||n==null||i==null||n.length===0||i.length===0||s==null)){var{data:d}=l,h;if(d&&d.length>0?h=d:h=c==null?void 0:c.slice(u,f+1),h!=null){var p=void 0;return ON({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataStartIndex:u,areaSettings:l,stackedData:a,displayedData:h,chartBaseValue:p,bandSize:s})}}}),aN=["layout","type","stroke","connectNulls","isRange"],oN=["activeDot","animationBegin","animationDuration","animationEasing","connectNulls","dot","fill","fillOpacity","hide","isAnimationActive","legendType","stroke","xAxisId","yAxisId"];function Gg(e,t){if(e==null)return{};var r,n,i=sN(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function sN(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function Ov(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function mr(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ov(Object(r),!0).forEach(function(n){qa(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ov(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function qa(e,t,r){return(t=lN(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function lN(e){var t=uN(e,"string");return typeof t=="symbol"?t:t+""}function uN(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Vt(){return Vt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Vt.apply(null,arguments)}function ea(e,t){return e&&e!=="none"?e:t}var cN=e=>{var{dataKey:t,name:r,stroke:n,fill:i,legendType:a,hide:o}=e;return[{inactive:o,dataKey:t,type:a,color:ea(n,i),value:er(r,t),payload:e}]};function fN(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:s,unit:l}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,nameKey:void 0,name:er(o,t),hide:s,type:e.tooltipType,color:ea(n,a),unit:l}}}var dN=(e,t)=>{var r;if(v.isValidElement(e))r=v.cloneElement(e,t);else if(typeof e=="function")r=e(t);else{var n=W("recharts-area-dot",typeof e!="boolean"?e.className:"");r=v.createElement(uc,Vt({},t,{className:n}))}return r};function hN(e,t){return e==null?!1:t?!0:e.length===1}function vN(e){var{clipPathId:t,points:r,props:n}=e,{needClip:i,dot:a,dataKey:o}=n;if(!hN(r,a))return null;var s=na(a),l=z(n,!1),c=z(a,!0),u=r.map((d,h)=>{var p=mr(mr(mr({key:"dot-".concat(h),r:3},l),c),{},{index:h,cx:d.x,cy:d.y,dataKey:o,value:d.value,payload:d.payload,points:r});return dN(a,p)}),f={clipPath:i?"url(#clipPath-".concat(s?"":"dots-").concat(t,")"):void 0};return v.createElement(ae,Vt({className:"recharts-area-dots"},f),u)}function wl(e){var{points:t,baseLine:r,needClip:n,clipPathId:i,props:a,showLabels:o}=e,{layout:s,type:l,stroke:c,connectNulls:u,isRange:f}=a,d=Gg(a,aN);return v.createElement(v.Fragment,null,(t==null?void 0:t.length)>1&&v.createElement(ae,{clipPath:n?"url(#clipPath-".concat(i,")"):void 0},v.createElement(qr,Vt({},z(d,!0),{points:t,connectNulls:u,type:l,baseLine:r,layout:s,stroke:"none",className:"recharts-area-area"})),c!=="none"&&v.createElement(qr,Vt({},z(a,!1),{className:"recharts-area-curve",layout:s,type:l,connectNulls:u,fill:"none",points:t})),c!=="none"&&f&&v.createElement(qr,Vt({},z(a,!1),{className:"recharts-area-curve",layout:s,type:l,connectNulls:u,fill:"none",points:r}))),v.createElement(vN,{points:t,props:a,clipPathId:i}),o&&Mt.renderCallByParent(a,t))}function pN(e){var{alpha:t,baseLine:r,points:n,strokeWidth:i}=e,a=n[0].y,o=n[n.length-1].y;if(!Te(a)||!Te(o))return null;var s=t*Math.abs(a-o),l=Math.max(...n.map(c=>c.x||0));return N(r)?l=Math.max(r,l):r&&Array.isArray(r)&&r.length&&(l=Math.max(...r.map(c=>c.x||0),l)),N(l)?v.createElement("rect",{x:0,y:a<o?a:a-s,width:l+(i?parseInt("".concat(i),10):1),height:Math.floor(s)}):null}function mN(e){var{alpha:t,baseLine:r,points:n,strokeWidth:i}=e,a=n[0].x,o=n[n.length-1].x;if(!Te(a)||!Te(o))return null;var s=t*Math.abs(a-o),l=Math.max(...n.map(c=>c.y||0));return N(r)?l=Math.max(r,l):r&&Array.isArray(r)&&r.length&&(l=Math.max(...r.map(c=>c.y||0),l)),N(l)?v.createElement("rect",{x:a<o?a:a-s,y:0,width:s,height:Math.floor(l+(i?parseInt("".concat(i),10):1))}):null}function yN(e){var{alpha:t,layout:r,points:n,baseLine:i,strokeWidth:a}=e;return r==="vertical"?v.createElement(pN,{alpha:t,points:n,baseLine:i,strokeWidth:a}):v.createElement(mN,{alpha:t,points:n,baseLine:i,strokeWidth:a})}function gN(e){var{needClip:t,clipPathId:r,props:n,previousPointsRef:i,previousBaselineRef:a}=e,{points:o,baseLine:s,isAnimationActive:l,animationBegin:c,animationDuration:u,animationEasing:f,onAnimationStart:d,onAnimationEnd:h}=n,p=$a(n,"recharts-area-"),[m,y]=v.useState(!0),g=v.useCallback(()=>{typeof h=="function"&&h(),y(!1)},[h]),x=v.useCallback(()=>{typeof d=="function"&&d(),y(!0)},[d]),w=i.current,P=a.current;return v.createElement(It,{begin:c,duration:u,isActive:l,easing:f,from:{t:0},to:{t:1},onAnimationEnd:g,onAnimationStart:x,key:p},O=>{var{t:A}=O;if(w){var S=w.length/o.length,E=A===1?o:o.map((M,C)=>{var k=Math.floor(C*S);if(w[k]){var R=w[k];return mr(mr({},M),{},{x:Mr(R.x,M.x,A),y:Mr(R.y,M.y,A)})}return M}),_;return N(s)?_=Mr(P,s,A):V(s)||Ke(s)?_=Mr(P,0,A):_=s.map((M,C)=>{var k=Math.floor(C*S);if(Array.isArray(P)&&P[k]){var R=P[k];return mr(mr({},M),{},{x:Mr(R.x,M.x,A),y:Mr(R.y,M.y,A)})}return M}),A>0&&(i.current=E,a.current=_),v.createElement(wl,{points:E,baseLine:_,needClip:t,clipPathId:r,props:n,showLabels:!m})}return A>0&&(i.current=o,a.current=s),v.createElement(ae,null,v.createElement("defs",null,v.createElement("clipPath",{id:"animationClipPath-".concat(r)},v.createElement(yN,{alpha:A,points:o,baseLine:s,layout:n.layout,strokeWidth:n.strokeWidth}))),v.createElement(ae,{clipPath:"url(#animationClipPath-".concat(r,")")},v.createElement(wl,{points:o,baseLine:s,needClip:t,clipPathId:r,props:n,showLabels:!0})))})}function bN(e){var{needClip:t,clipPathId:r,props:n}=e,{points:i,baseLine:a,isAnimationActive:o}=n,s=v.useRef(null),l=v.useRef(),c=s.current,u=l.current;return o&&i&&i.length&&(c!==i||u!==a)?v.createElement(gN,{needClip:t,clipPathId:r,props:n,previousPointsRef:s,previousBaselineRef:l}):v.createElement(wl,{points:i,baseLine:a,needClip:t,clipPathId:r,props:n,showLabels:!0})}class xN extends v.PureComponent{constructor(){super(...arguments),qa(this,"id",Qt("recharts-area-"))}render(){var t,{hide:r,dot:n,points:i,className:a,top:o,left:s,needClip:l,xAxisId:c,yAxisId:u,width:f,height:d,id:h,baseLine:p}=this.props;if(r)return null;var m=W("recharts-area",a),y=V(h)?this.id:h,{r:g=3,strokeWidth:x=2}=(t=z(n,!1))!==null&&t!==void 0?t:{r:3,strokeWidth:2},w=na(n),P=g*2+x;return v.createElement(v.Fragment,null,v.createElement(ae,{className:m},l&&v.createElement("defs",null,v.createElement(bc,{clipPathId:y,xAxisId:c,yAxisId:u}),!w&&v.createElement("clipPath",{id:"clipPath-dots-".concat(y)},v.createElement("rect",{x:s-P/2,y:o-P/2,width:f+P,height:d+P}))),v.createElement(bN,{needClip:l,clipPathId:y,props:this.props})),v.createElement(yl,{points:i,mainColor:ea(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}),this.props.isRange&&Array.isArray(p)&&v.createElement(yl,{points:p,mainColor:ea(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}))}}var Vg={activeDot:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!1,fill:"#3182bd",fillOpacity:.6,hide:!1,isAnimationActive:!tr.isSsr,legendType:"line",stroke:"#3182bd",xAxisId:0,yAxisId:0};function wN(e){var t,r=Je(e,Vg),{activeDot:n,animationBegin:i,animationDuration:a,animationEasing:o,connectNulls:s,dot:l,fill:c,fillOpacity:u,hide:f,isAnimationActive:d,legendType:h,stroke:p,xAxisId:m,yAxisId:y}=r,g=Gg(r,oN),x=zn(),w=Xy(),{needClip:P}=Ba(m,y),O=Ie(),A=v.useMemo(()=>({baseValue:e.baseValue,stackId:e.stackId,connectNulls:s,data:e.data,dataKey:e.dataKey}),[e.baseValue,e.stackId,s,e.data,e.dataKey]),{points:S,isRange:E,baseLine:_}=(t=D(B=>iN(B,m,y,O,A)))!==null&&t!==void 0?t:{},{height:M,width:C,x:k,y:R}=La();return x!=="horizontal"&&x!=="vertical"||w!=="AreaChart"&&w!=="ComposedChart"?null:v.createElement(xN,Vt({},g,{activeDot:n,animationBegin:i,animationDuration:a,animationEasing:o,baseLine:_,connectNulls:s,dot:l,fill:c,fillOpacity:u,height:M,hide:f,layout:x,isAnimationActive:d,isRange:E,legendType:h,needClip:P,points:S,stroke:p,width:C,left:k,top:R,xAxisId:m,yAxisId:y}))}var PN=(e,t,r,n,i)=>{var a=r??t;if(N(a))return a;var o=e==="horizontal"?i:n,s=o.scale.domain();if(o.type==="number"){var l=Math.max(s[0],s[1]),c=Math.min(s[0],s[1]);return a==="dataMin"?c:a==="dataMax"||l<0?l:Math.max(Math.min(s[0],s[1]),0)}return a==="dataMin"?s[0]:a==="dataMax"?s[1]:s[0]};function ON(e){var{areaSettings:{connectNulls:t,baseValue:r,dataKey:n},stackedData:i,layout:a,chartBaseValue:o,xAxis:s,yAxis:l,displayedData:c,dataStartIndex:u,xAxisTicks:f,yAxisTicks:d,bandSize:h}=e,p=i&&i.length,m=PN(a,o,r,s,l),y=a==="horizontal",g=!1,x=c.map((P,O)=>{var A;p?A=i[u+O]:(A=Q(P,n),Array.isArray(A)?g=!0:A=[m,A]);var S=A[1]==null||p&&!t&&Q(P,n)==null;return y?{x:ki({axis:s,ticks:f,bandSize:h,entry:P,index:O}),y:S?null:l.scale(A[1]),value:A,payload:P}:{x:S?null:s.scale(A[1]),y:ki({axis:l,ticks:d,bandSize:h,entry:P,index:O}),value:A,payload:P}}),w;return p||g?w=x.map(P=>{var O=Array.isArray(P.value)?P.value[0]:null;return y?{x:P.x,y:O!=null&&P.y!=null?l.scale(O):null}:{x:O!=null?s.scale(O):null,y:P.y}}):w=y?l.scale(m):s.scale(m),{points:x,baseLine:w,isRange:g}}class ta extends v.PureComponent{render(){return v.createElement(gc,{type:"area",data:this.props.data,dataKey:this.props.dataKey,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,stackId:this.props.stackId,hide:this.props.hide,barSize:void 0},v.createElement(yc,{legendPayload:cN(this.props)}),v.createElement(Da,{fn:fN,args:this.props}),v.createElement(wN,this.props))}}qa(ta,"displayName","Area");qa(ta,"defaultProps",Vg);function Av(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Sv(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Av(Object(r),!0).forEach(function(n){AN(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Av(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function AN(e,t,r){return(t=SN(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function SN(e){var t=jN(e,"string");return typeof t=="symbol"?t:t+""}function jN(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var EN={xAxis:{},yAxis:{},zAxis:{}},Xg=dt({name:"cartesianAxis",initialState:EN,reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=t.payload},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=t.payload},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=t.payload},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=Sv(Sv({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:_N,removeXAxis:TN,addYAxis:CN,removeYAxis:kN,addZAxis:XD,removeZAxis:ZD,updateYAxisWidth:MN}=Xg.actions,NN=Xg.reducer,IN=["children"],DN=["dangerouslySetInnerHTML","ticks"];function Zg(e,t,r){return(t=$N(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $N(e){var t=RN(e,"string");return typeof t=="symbol"?t:t+""}function RN(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Pl(){return Pl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Pl.apply(null,arguments)}function Qg(e,t){if(e==null)return{};var r,n,i=LN(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function LN(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function BN(e){var t=ie(),r=v.useMemo(()=>{var{children:a}=e,o=Qg(e,IN);return o},[e]),n=D(a=>qt(a,r.id)),i=r===n;return v.useEffect(()=>(t(_N(r)),()=>{t(TN(r))}),[r,t]),i?e.children:null}var KN=e=>{var{xAxisId:t,className:r}=e,n=D(Yp),i=Ie(),a="xAxis",o=D(h=>an(h,a,t,i)),s=D(h=>Ty(h,a,t,i)),l=D(h=>Sy(h,t)),c=D(h=>kj(h,t));if(l==null||c==null)return null;var{dangerouslySetInnerHTML:u,ticks:f}=e,d=Qg(e,DN);return v.createElement(ar,Pl({},d,{scale:o,x:c.x,y:c.y,width:l.width,height:l.height,className:W("recharts-".concat(a," ").concat(a),r),viewBox:n,ticks:s}))},zN=e=>{var t,r,n,i,a;return v.createElement(BN,{interval:(t=e.interval)!==null&&t!==void 0?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:(r=e.includeHidden)!==null&&r!==void 0?r:!1,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:(n=e.angle)!==null&&n!==void 0?n:0,minTickGap:(i=e.minTickGap)!==null&&i!==void 0?i:5,tick:(a=e.tick)!==null&&a!==void 0?a:!0,tickFormatter:e.tickFormatter},v.createElement(KN,e))};class On extends v.Component{render(){return v.createElement(zN,this.props)}}Zg(On,"displayName","XAxis");Zg(On,"defaultProps",{allowDataOverflow:Fe.allowDataOverflow,allowDecimals:Fe.allowDecimals,allowDuplicatedCategory:Fe.allowDuplicatedCategory,height:Fe.height,hide:!1,mirror:Fe.mirror,orientation:Fe.orientation,padding:Fe.padding,reversed:Fe.reversed,scale:Fe.scale,tickCount:Fe.tickCount,type:Fe.type,xAxisId:0});var qN=e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=e,o=0;if(t){t.forEach(u=>{if(u){var f=u.getBoundingClientRect();f.width>o&&(o=f.width)}});var s=r?r.getBoundingClientRect().width:0,l=i+a,c=o+l+s+(r?n:0);return Math.round(c)}return 0},WN=["dangerouslySetInnerHTML","ticks"];function Jg(e,t,r){return(t=FN(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function FN(e){var t=UN(e,"string");return typeof t=="symbol"?t:t+""}function UN(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Ol(){return Ol=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ol.apply(null,arguments)}function YN(e,t){if(e==null)return{};var r,n,i=HN(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function HN(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function GN(e){var t=ie();return v.useEffect(()=>(t(CN(e)),()=>{t(kN(e))}),[e,t]),null}var VN=e=>{var t,{yAxisId:r,className:n,width:i,label:a}=e,o=v.useRef(null),s=v.useRef(null),l=D(Yp),c=Ie(),u=ie(),f="yAxis",d=D(w=>an(w,f,r,c)),h=D(w=>jy(w,r)),p=D(w=>Mj(w,r)),m=D(w=>Ty(w,f,r,c));if(v.useLayoutEffect(()=>{var w;if(!(i!=="auto"||!h||lc(a)||v.isValidElement(a))){var P=o.current,O=P==null||(w=P.tickRefs)===null||w===void 0?void 0:w.current,{tickSize:A,tickMargin:S}=P.props,E=qN({ticks:O,label:s.current,labelGapWithTick:5,tickSize:A,tickMargin:S});Math.round(h.width)!==Math.round(E)&&u(MN({id:r,width:E}))}},[o,o==null||(t=o.current)===null||t===void 0||(t=t.tickRefs)===null||t===void 0?void 0:t.current,h==null?void 0:h.width,h,u,a,r,i]),h==null||p==null)return null;var{dangerouslySetInnerHTML:y,ticks:g}=e,x=YN(e,WN);return v.createElement(ar,Ol({},x,{ref:o,labelRef:s,scale:d,x:p.x,y:p.y,width:h.width,height:h.height,className:W("recharts-".concat(f," ").concat(f),n),viewBox:l,ticks:m}))},XN=e=>{var t,r,n,i,a;return v.createElement(v.Fragment,null,v.createElement(GN,{interval:(t=e.interval)!==null&&t!==void 0?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:(r=e.includeHidden)!==null&&r!==void 0?r:!1,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:(n=e.angle)!==null&&n!==void 0?n:0,minTickGap:(i=e.minTickGap)!==null&&i!==void 0?i:5,tick:(a=e.tick)!==null&&a!==void 0?a:!0,tickFormatter:e.tickFormatter}),v.createElement(VN,e))},ZN={allowDataOverflow:Ue.allowDataOverflow,allowDecimals:Ue.allowDecimals,allowDuplicatedCategory:Ue.allowDuplicatedCategory,hide:!1,mirror:Ue.mirror,orientation:Ue.orientation,padding:Ue.padding,reversed:Ue.reversed,scale:Ue.scale,tickCount:Ue.tickCount,type:Ue.type,width:Ue.width,yAxisId:0};class An extends v.Component{render(){return v.createElement(XN,this.props)}}Jg(An,"displayName","YAxis");Jg(An,"defaultProps",ZN);var As={exports:{}},Ss={};/**
 * @license React
 * use-sync-external-store-with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jv;function QN(){if(jv)return Ss;jv=1;var e=Nv();function t(l,c){return l===c&&(l!==0||1/l===1/c)||l!==l&&c!==c}var r=typeof Object.is=="function"?Object.is:t,n=e.useSyncExternalStore,i=e.useRef,a=e.useEffect,o=e.useMemo,s=e.useDebugValue;return Ss.useSyncExternalStoreWithSelector=function(l,c,u,f,d){var h=i(null);if(h.current===null){var p={hasValue:!1,value:null};h.current=p}else p=h.current;h=o(function(){function y(O){if(!g){if(g=!0,x=O,O=f(O),d!==void 0&&p.hasValue){var A=p.value;if(d(A,O))return w=A}return w=O}if(A=w,r(x,O))return A;var S=f(O);return d!==void 0&&d(A,S)?(x=O,A):(x=O,w=S)}var g=!1,x,w,P=u===void 0?null:u;return[function(){return y(c())},P===null?void 0:function(){return y(P())}]},[c,u,f,d]);var m=n(l,h[0],h[1]);return a(function(){p.hasValue=!0,p.value=m},[m]),s(m),m},Ss}var Ev;function JN(){return Ev||(Ev=1,As.exports=QN()),As.exports}JN();function eI(e){e()}function tI(){let e=null,t=null;return{clear(){e=null,t=null},notify(){eI(()=>{let r=e;for(;r;)r.callback(),r=r.next})},get(){const r=[];let n=e;for(;n;)r.push(n),n=n.next;return r},subscribe(r){let n=!0;const i=t={callback:r,next:null,prev:t};return i.prev?i.prev.next=i:e=i,function(){!n||e===null||(n=!1,i.next?i.next.prev=i.prev:t=i.prev,i.prev?i.prev.next=i.next:e=i.next)}}}}var _v={notify(){},get:()=>[]};function rI(e,t){let r,n=_v,i=0,a=!1;function o(m){u();const y=n.subscribe(m);let g=!1;return()=>{g||(g=!0,y(),f())}}function s(){n.notify()}function l(){p.onStateChange&&p.onStateChange()}function c(){return a}function u(){i++,r||(r=e.subscribe(l),n=tI())}function f(){i--,r&&i===0&&(r(),r=void 0,n.clear(),n=_v)}function d(){a||(a=!0,u())}function h(){a&&(a=!1,f())}const p={addNestedSub:o,notifyNestedSubs:s,handleChangeWrapper:l,isSubscribed:c,trySubscribe:d,tryUnsubscribe:h,getListeners:()=>n};return p}var nI=()=>typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",iI=nI(),aI=()=>typeof navigator<"u"&&navigator.product==="ReactNative",oI=aI(),sI=()=>iI||oI?v.useLayoutEffect:v.useEffect,lI=sI(),js=Symbol.for("react-redux-context"),Es=typeof globalThis<"u"?globalThis:{};function uI(){if(!v.createContext)return{};const e=Es[js]??(Es[js]=new Map);let t=e.get(v.createContext);return t||(t=v.createContext(null),e.set(v.createContext,t)),t}var cI=uI();function fI(e){const{children:t,context:r,serverState:n,store:i}=e,a=v.useMemo(()=>{const l=rI(i);return{store:i,subscription:l,getServerState:n?()=>n:void 0}},[i,n]),o=v.useMemo(()=>i.getState(),[i]);lI(()=>{const{subscription:l}=a;return l.onStateChange=l.notifyNestedSubs,l.trySubscribe(),o!==i.getState()&&l.notifyNestedSubs(),()=>{l.tryUnsubscribe(),l.onStateChange=void 0}},[a,o]);const s=r||cI;return v.createElement(s.Provider,{value:a},t)}var dI=fI,hI=(e,t)=>t,Ac=j([hI,F,US,Se,Fy,Wt,IE,pe],BE),Sc=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}},e0=ut("mouseClick"),t0=Kn();t0.startListening({actionCreator:e0,effect:(e,t)=>{var r=e.payload,n=Ac(t.getState(),Sc(r));(n==null?void 0:n.activeIndex)!=null&&t.dispatch(Uj({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var Al=ut("mouseMove"),r0=Kn();r0.startListening({actionCreator:Al,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=Ju(n,n.tooltip.settings.shared),a=Ac(n,Sc(r));i==="axis"&&((a==null?void 0:a.activeIndex)!=null?t.dispatch(Ry({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate})):t.dispatch($y()))}});function vI(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}var Tv={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},n0=dt({name:"rootProps",initialState:Tv,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=(r=t.payload.barGap)!==null&&r!==void 0?r:Tv.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),pI=n0.reducer,{updateOptions:mI}=n0.actions,i0=dt({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:yI}=i0.actions,gI=i0.reducer,a0=ut("keyDown"),o0=ut("focus"),jc=Kn();jc.startListening({actionCreator:a0,effect:(e,t)=>{var r=t.getState(),n=r.rootProps.accessibilityLayer!==!1;if(n){var{keyboardInteraction:i}=r.tooltip,a=e.payload;if(!(a!=="ArrowRight"&&a!=="ArrowLeft"&&a!=="Enter")){var o=Number(ec(i,kr(r))),s=Wt(r);if(a==="Enter"){var l=Yi(r,"axis","hover",String(i.index));t.dispatch(fl({active:!i.active,activeIndex:i.index,activeDataKey:i.dataKey,activeCoordinate:l}));return}var c=$j(r),u=c==="left-to-right"?1:-1,f=a==="ArrowRight"?1:-1,d=o+f*u;if(!(s==null||d>=s.length||d<0)){var h=Yi(r,"axis","hover",String(d));t.dispatch(fl({active:!0,activeIndex:d.toString(),activeDataKey:void 0,activeCoordinate:h}))}}}}});jc.startListening({actionCreator:o0,effect:(e,t)=>{var r=t.getState(),n=r.rootProps.accessibilityLayer!==!1;if(n){var{keyboardInteraction:i}=r.tooltip;if(!i.active&&i.index==null){var a="0",o=Yi(r,"axis","hover",String(a));t.dispatch(fl({activeDataKey:void 0,active:!0,activeIndex:a,activeCoordinate:o}))}}}});var ot=ut("externalEvent"),s0=Kn();s0.startListening({actionCreator:ot,effect:(e,t)=>{if(e.payload.handler!=null){var r=t.getState(),n={activeCoordinate:SE(r),activeDataKey:Gy(r),activeIndex:Jt(r),activeLabel:Hy(r),activeTooltipIndex:Jt(r),isTooltipActive:jE(r)};e.payload.handler(n,e.payload.reactEvent)}}});var bI=j([on],e=>e.tooltipItemPayloads),xI=j([bI,Qn,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(s=>s.settings.dataKey===n);if(i!=null){var{positions:a}=i;if(a!=null){var o=t(a,r);return o}}}),l0=ut("touchMove"),u0=Kn();u0.startListening({actionCreator:l0,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=Ju(n,n.tooltip.settings.shared);if(i==="axis"){var a=Ac(n,Sc({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(a==null?void 0:a.activeIndex)!=null&&t.dispatch(Ry({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate}))}else if(i==="item"){var o,s=r.touches[0],l=document.elementFromPoint(s.clientX,s.clientY);if(!l||!l.getAttribute)return;var c=l.getAttribute(Fp),u=(o=l.getAttribute(Up))!==null&&o!==void 0?o:void 0,f=xI(t.getState(),c,u);t.dispatch(Dy({activeDataKey:u,activeIndex:c,activeCoordinate:f}))}}});var wI=yp({brush:Uk,cartesianAxis:NN,chartData:t_,graphicalItems:$T,layout:Iw,legend:x1,options:XE,polarAxis:xT,polarOptions:gI,referenceElements:Qk,rootProps:pI,tooltip:Yj}),PI=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"Chart";return aw({reducer:wI,preloadedState:t,middleware:n=>n({serializableCheck:!1}).concat([t0.middleware,r0.middleware,jc.middleware,s0.middleware,u0.middleware]),devTools:{serialize:{replacer:vI},name:"recharts-".concat(r)}})};function c0(e){var{preloadedState:t,children:r,reduxStoreName:n}=e,i=Ie(),a=v.useRef(null);if(i)return r;a.current==null&&(a.current=PI(t,n));var o=Fl;return v.createElement(dI,{context:o,store:a.current},r)}function f0(e){var{layout:t,width:r,height:n,margin:i}=e,a=ie(),o=Ie();return v.useEffect(()=>{o||(a(kw(t)),a(Mw({width:r,height:n})),a(Cw(i)))},[a,o,t,r,n,i]),null}function d0(e){var t=ie();return v.useEffect(()=>{t(mI(e))},[t,e]),null}var OI=["children"];function AI(e,t){if(e==null)return{};var r,n,i=SI(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function SI(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function ra(){return ra=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ra.apply(null,arguments)}var jI={width:"100%",height:"100%"},EI=v.forwardRef((e,t)=>{var r=eu(),n=tu(),i=Zp();if(!Hr(r)||!Hr(n))return null;var{children:a,otherAttributes:o,title:s,desc:l}=e,c,u;return typeof o.tabIndex=="number"?c=o.tabIndex:c=i?0:void 0,typeof o.role=="string"?u=o.role:u=i?"application":void 0,v.createElement(Ml,ra({},o,{title:s,desc:l,role:u,tabIndex:c,width:r,height:n,style:jI,ref:t}),a)}),_I=e=>{var{children:t}=e,r=D(ma);if(!r)return null;var{width:n,height:i,y:a,x:o}=r;return v.createElement(Ml,{width:n,height:i,x:o,y:a},t)},Cv=v.forwardRef((e,t)=>{var{children:r}=e,n=AI(e,OI),i=Ie();return i?v.createElement(_I,null,r):v.createElement(EI,ra({ref:t},n),r)});function TI(){var e=ie(),[t,r]=v.useState(null),n=D(l1);return v.useEffect(()=>{if(t!=null){var i=t.getBoundingClientRect(),a=i.width/t.offsetWidth;Te(a)&&a!==n&&e(Nw(a))}},[t,e,n]),r}function kv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function CI(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?kv(Object(r),!0).forEach(function(n){kI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):kv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function kI(e,t,r){return(t=MI(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function MI(e){var t=NI(e,"string");return typeof t=="symbol"?t:t+""}function NI(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var II=v.forwardRef((e,t)=>{var{children:r,className:n,height:i,onClick:a,onContextMenu:o,onDoubleClick:s,onMouseDown:l,onMouseEnter:c,onMouseLeave:u,onMouseMove:f,onMouseUp:d,onTouchEnd:h,onTouchMove:p,onTouchStart:m,style:y,width:g}=e,x=ie(),[w,P]=v.useState(null),[O,A]=v.useState(null);i_();var S=TI(),E=v.useCallback(L=>{S(L),typeof t=="function"&&t(L),P(L),A(L)},[S,t,P,A]),_=v.useCallback(L=>{x(e0(L)),x(ot({handler:a,reactEvent:L}))},[x,a]),M=v.useCallback(L=>{x(Al(L)),x(ot({handler:c,reactEvent:L}))},[x,c]),C=v.useCallback(L=>{x($y()),x(ot({handler:u,reactEvent:L}))},[x,u]),k=v.useCallback(L=>{x(Al(L)),x(ot({handler:f,reactEvent:L}))},[x,f]),R=v.useCallback(()=>{x(o0())},[x]),B=v.useCallback(L=>{x(a0(L.key))},[x]),U=v.useCallback(L=>{x(ot({handler:o,reactEvent:L}))},[x,o]),X=v.useCallback(L=>{x(ot({handler:s,reactEvent:L}))},[x,s]),K=v.useCallback(L=>{x(ot({handler:l,reactEvent:L}))},[x,l]),he=v.useCallback(L=>{x(ot({handler:d,reactEvent:L}))},[x,d]),se=v.useCallback(L=>{x(ot({handler:m,reactEvent:L}))},[x,m]),ze=v.useCallback(L=>{x(l0(L)),x(ot({handler:p,reactEvent:L}))},[x,p]),et=v.useCallback(L=>{x(ot({handler:h,reactEvent:L}))},[x,h]);return v.createElement(tg.Provider,{value:w},v.createElement(Kv.Provider,{value:O},v.createElement("div",{className:W("recharts-wrapper",n),style:CI({position:"relative",cursor:"default",width:g,height:i},y),onClick:_,onContextMenu:U,onDoubleClick:X,onFocus:R,onKeyDown:B,onMouseDown:K,onMouseEnter:M,onMouseLeave:C,onMouseMove:k,onMouseUp:he,onTouchEnd:et,onTouchMove:ze,onTouchStart:se,ref:E},r)))}),DI=["children","className","width","height","style","compact","title","desc"];function $I(e,t){if(e==null)return{};var r,n,i=RI(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function RI(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var h0=v.forwardRef((e,t)=>{var{children:r,className:n,width:i,height:a,style:o,compact:s,title:l,desc:c}=e,u=$I(e,DI),f=z(u,!1);return s?v.createElement(Cv,{otherAttributes:f,title:l,desc:c},r):v.createElement(II,{className:n,style:o,width:i,height:a,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},v.createElement(Cv,{otherAttributes:f,title:l,desc:c,ref:t},v.createElement(eM,null,r)))}),LI=["width","height"];function Sl(){return Sl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Sl.apply(null,arguments)}function BI(e,t){if(e==null)return{};var r,n,i=KI(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function KI(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var zI={top:5,right:5,bottom:5,left:5},qI={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:zI,reverseStackOrder:!1,syncMethod:"index"},Ec=v.forwardRef(function(t,r){var n,i=Je(t.categoricalChartProps,qI),{width:a,height:o}=i,s=BI(i,LI);if(!Hr(a)||!Hr(o))return null;var{chartName:l,defaultTooltipEventType:c,validateTooltipEventTypes:u,tooltipPayloadSearcher:f,categoricalChartProps:d}=t,h={chartName:l,defaultTooltipEventType:c,validateTooltipEventTypes:u,tooltipPayloadSearcher:f,eventEmitter:void 0};return v.createElement(c0,{preloadedState:{options:h},reduxStoreName:(n=d.id)!==null&&n!==void 0?n:l},v.createElement(Mg,{chartData:d.data}),v.createElement(f0,{width:a,height:o,layout:i.layout,margin:i.margin}),v.createElement(d0,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),v.createElement(h0,Sl({},s,{width:a,height:o,ref:r})))}),WI=["axis"],FI=v.forwardRef((e,t)=>v.createElement(Ec,{chartName:"LineChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:WI,tooltipPayloadSearcher:Na,categoricalChartProps:e,ref:t})),UI=["axis","item"],YI=v.forwardRef((e,t)=>v.createElement(Ec,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:UI,tooltipPayloadSearcher:Na,categoricalChartProps:e,ref:t}));function HI(e){var t=ie();return v.useEffect(()=>{t(yI(e))},[t,e]),null}var GI=["width","height","layout"];function jl(){return jl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},jl.apply(null,arguments)}function VI(e,t){if(e==null)return{};var r,n,i=XI(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function XI(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var ZI={top:5,right:5,bottom:5,left:5},QI={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:ZI,reverseStackOrder:!1,syncMethod:"index",layout:"radial"},JI=v.forwardRef(function(t,r){var n,i=Je(t.categoricalChartProps,QI),{width:a,height:o,layout:s}=i,l=VI(i,GI);if(!Hr(a)||!Hr(o))return null;var{chartName:c,defaultTooltipEventType:u,validateTooltipEventTypes:f,tooltipPayloadSearcher:d}=t,h={chartName:c,defaultTooltipEventType:u,validateTooltipEventTypes:f,tooltipPayloadSearcher:d,eventEmitter:void 0};return v.createElement(c0,{preloadedState:{options:h},reduxStoreName:(n=i.id)!==null&&n!==void 0?n:c},v.createElement(Mg,{chartData:i.data}),v.createElement(f0,{width:a,height:o,layout:s,margin:i.margin}),v.createElement(d0,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),v.createElement(HI,{cx:i.cx,cy:i.cy,startAngle:i.startAngle,endAngle:i.endAngle,innerRadius:i.innerRadius,outerRadius:i.outerRadius}),v.createElement(h0,jl({width:a,height:o},l,{ref:r})))}),eD=["item"],tD={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},_s=v.forwardRef((e,t)=>{var r=Je(e,tD);return v.createElement(JI,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:eD,tooltipPayloadSearcher:Na,categoricalChartProps:r,ref:t})}),rD=["axis"],nD=v.forwardRef((e,t)=>v.createElement(Ec,{chartName:"AreaChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:rD,tooltipPayloadSearcher:Na,categoricalChartProps:e,ref:t}));const Mv={light:{primary:"oklch(0.623 0.214 259.815)",chart1:"oklch(0.646 0.222 41.116)",chart2:"oklch(0.6 0.118 184.704)",chart3:"oklch(0.398 0.07 227.392)",chart4:"oklch(0.828 0.189 84.429)",chart5:"oklch(0.769 0.188 70.08)",background:"oklch(1 0 0)",foreground:"oklch(0.141 0.005 285.823)",muted:"oklch(0.552 0.016 285.938)",border:"oklch(0.92 0.004 286.32)"},dark:{primary:"oklch(0.546 0.245 262.881)",chart1:"oklch(0.488 0.243 264.376)",chart2:"oklch(0.696 0.17 162.48)",chart3:"oklch(0.769 0.188 70.08)",chart4:"oklch(0.627 0.265 303.9)",chart5:"oklch(0.645 0.246 16.439)",background:"oklch(0.141 0.005 285.823)",foreground:"oklch(0.985 0 0)",muted:"oklch(0.705 0.015 286.067)",border:"oklch(1 0 0 / 10%)"}},Xt=(e=!1)=>e?Mv.dark:Mv.light,pi=(e=!1)=>{const t=Xt(e);return[t.chart1,t.chart2,t.chart3,t.chart4,t.chart5,t.primary]},we={common:(e=!1)=>{const t=Xt(e);return{backgroundColor:t.background,textColor:t.foreground,gridColor:t.border,tooltipBackground:t.background,tooltipBorder:t.border,tooltipText:t.foreground}},bar:(e=!1)=>({...we.common(e),colors:pi(e)}),pie:(e=!1)=>({...we.common(e),colors:pi(e)}),line:(e=!1)=>{const t=Xt(e);return{...we.common(e),strokeColor:t.primary,fillColor:`${t.primary}20`,colors:pi(e)}},area:(e=!1)=>{const t=Xt(e);return{...we.common(e),fillColor:`${t.primary}30`,strokeColor:t.primary,colors:pi(e)}}},iD=()=>typeof window<"u"?document.documentElement.classList.contains("dark"):!1,aD=(e=!1)=>{const t=Xt(e);return{backgroundColor:t.background,border:`1px solid ${t.border}`,borderRadius:"8px",color:t.foreground,fontSize:"14px",padding:"12px",boxShadow:e?"0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)":"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"}},Ts=(e=!1)=>({stroke:Xt(e).border,strokeDasharray:"3 3",strokeOpacity:e?.3:.5}),Rr=(e=!1)=>{const t=Xt(e);return{stroke:t.border,fontSize:"12px",fill:t.muted}},Lr=(e=!1)=>({fontSize:"12px",fill:Xt(e).foreground});function QD({stats:e,charts:t,recentActivity:r,topPerformers:n,systemHealth:i}){const[a,o]=v.useState(!1);v.useEffect(()=>{const l=()=>{const u=iD();o(u)};l();const c=new MutationObserver(l);return c.observe(document.documentElement,{attributes:!0,attributeFilter:["class"]}),()=>c.disconnect()},[]);const s=({active:l,payload:c,label:u})=>l&&c&&c.length?b.jsxs("div",{style:aD(a),children:[b.jsx("p",{className:"font-medium",children:u}),c.map((f,d)=>b.jsxs("p",{style:{color:f.color},children:[f.name,": ",f.value]},d))]}):null;return b.jsxs(P0,{children:[b.jsx(b0,{title:"Admin Dashboard"}),b.jsxs("div",{className:"flex h-full flex-1 flex-col gap-6 rounded-xl p-6 overflow-x-auto",children:[b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsxs("div",{children:[b.jsx("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:"Admin Dashboard"}),b.jsx("p",{className:"text-muted-foreground mt-2",children:"Comprehensive overview of your mobile parts database with advanced analytics"})]}),b.jsxs("div",{className:"hidden md:flex items-center gap-2",children:[b.jsxs(gt,{variant:"outline",className:"text-xs",children:[b.jsx(Tc,{className:"h-3 w-3 mr-1"}),"Live Data"]}),b.jsxs(gt,{variant:"secondary",className:"text-xs",children:[b.jsx(Cc,{className:"h-3 w-3 mr-1"}),"Advanced Analytics"]})]})]}),b.jsxs("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6",children:[b.jsxs(ye,{className:"border-l-4 border-l-blue-500",children:[b.jsxs(ge,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[b.jsx(be,{className:"text-sm font-medium",children:"Total Users"}),b.jsx(Fa,{className:"h-4 w-4 text-blue-600"})]}),b.jsxs(xe,{children:[b.jsx("div",{className:"text-2xl font-bold text-foreground",children:e.total_users.toLocaleString()}),b.jsxs("p",{className:"text-xs text-muted-foreground",children:[b.jsx("span",{className:"text-blue-600 font-medium",children:e.premium_users})," premium,",b.jsx("span",{className:"text-gray-600 font-medium ml-1",children:e.free_users})," free"]})]})]}),b.jsxs(ye,{className:"border-l-4 border-l-green-500",children:[b.jsxs(ge,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[b.jsx(be,{className:"text-sm font-medium",children:"Total Parts"}),b.jsx(ln,{className:"h-4 w-4 text-green-600"})]}),b.jsxs(xe,{children:[b.jsx("div",{className:"text-2xl font-bold text-foreground",children:e.total_parts.toLocaleString()}),b.jsxs("p",{className:"text-xs text-muted-foreground",children:["In ",e.total_categories," categories"]})]})]}),b.jsxs(ye,{className:"border-l-4 border-l-purple-500",children:[b.jsxs(ge,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[b.jsx(be,{className:"text-sm font-medium",children:"Searches Today"}),b.jsx(Ua,{className:"h-4 w-4 text-purple-600"})]}),b.jsxs(xe,{children:[b.jsx("div",{className:"text-2xl font-bold text-foreground",children:e.total_searches_today.toLocaleString()}),b.jsxs("p",{className:"text-xs text-muted-foreground",children:[b.jsx("span",{className:"text-purple-600 font-medium",children:e.total_searches_week})," this week"]})]})]}),b.jsxs(ye,{className:"border-l-4 border-l-orange-500",children:[b.jsxs(ge,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[b.jsx(be,{className:"text-sm font-medium",children:"Active Subscriptions"}),b.jsx(A0,{className:"h-4 w-4 text-orange-600"})]}),b.jsxs(xe,{children:[b.jsx("div",{className:"text-2xl font-bold text-foreground",children:e.active_subscriptions.toLocaleString()}),b.jsxs("p",{className:"text-xs text-muted-foreground",children:[b.jsx("span",{className:"text-orange-600 font-medium",children:e.recent_registrations})," new this week"]})]})]}),b.jsxs(ye,{className:"border-l-4 border-l-teal-500",children:[b.jsxs(ge,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[b.jsx(be,{className:"text-sm font-medium",children:"Brands & Models"}),b.jsx(un,{className:"h-4 w-4 text-teal-600"})]}),b.jsxs(xe,{children:[b.jsx("div",{className:"text-2xl font-bold text-foreground",children:e.total_brands.toLocaleString()}),b.jsxs("p",{className:"text-xs text-muted-foreground",children:[b.jsx("span",{className:"text-teal-600 font-medium",children:e.total_models})," models"]})]})]}),b.jsxs(ye,{className:"border-l-4 border-l-indigo-500",children:[b.jsxs(ge,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[b.jsx(be,{className:"text-sm font-medium",children:"Avg Searches"}),b.jsx(S0,{className:"h-4 w-4 text-indigo-600"})]}),b.jsxs(xe,{children:[b.jsx("div",{className:"text-2xl font-bold text-foreground",children:e.avg_searches_per_user}),b.jsx("p",{className:"text-xs text-muted-foreground",children:"Per user"})]})]})]}),b.jsxs(x0,{defaultValue:"analytics",className:"space-y-6",children:[b.jsxs(w0,{className:"grid w-full grid-cols-4",children:[b.jsx(Jn,{value:"analytics",children:"Analytics"}),b.jsx(Jn,{value:"performance",children:"Performance"}),b.jsx(Jn,{value:"distribution",children:"Distribution"}),b.jsx(Jn,{value:"activity",children:"Activity"})]}),b.jsx(ei,{value:"analytics",className:"space-y-6",children:b.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[b.jsxs(ye,{children:[b.jsxs(ge,{children:[b.jsxs(be,{className:"flex items-center gap-2",children:[b.jsx(M0,{className:"h-5 w-5 text-blue-600"}),"User Registration Trends"]}),b.jsx(rt,{children:"Daily user registrations over the last 30 days"})]}),b.jsx(xe,{children:b.jsx(Dr,{width:"100%",height:300,children:b.jsxs(FI,{data:t.userRegistrationTrends,children:[b.jsx(yi,{...Ts(a)}),b.jsx(On,{dataKey:"date",...Rr(a),tick:{fontSize:12}}),b.jsx(An,{...Rr(a),tick:{fontSize:12}}),b.jsx(Ir,{content:b.jsx(s,{})}),b.jsx(St,{...Lr(a)}),b.jsx(Ji,{type:"monotone",dataKey:"total",stroke:we.line(a).colors[0],strokeWidth:2,name:"Total Users"}),b.jsx(Ji,{type:"monotone",dataKey:"premium",stroke:we.line(a).colors[1],strokeWidth:2,name:"Premium Users"})]})})})]}),b.jsxs(ye,{children:[b.jsxs(ge,{children:[b.jsxs(be,{className:"flex items-center gap-2",children:[b.jsx(Cc,{className:"h-5 w-5 text-green-600"}),"Search Activity Trends"]}),b.jsx(rt,{children:"Daily search activity and user engagement"})]}),b.jsx(xe,{children:b.jsx(Dr,{width:"100%",height:300,children:b.jsxs(nD,{data:t.searchActivityTrends,children:[b.jsx(yi,{...Ts(a)}),b.jsx(On,{dataKey:"date",...Rr(a),tick:{fontSize:12}}),b.jsx(An,{...Rr(a),tick:{fontSize:12}}),b.jsx(Ir,{content:b.jsx(s,{})}),b.jsx(St,{...Lr(a)}),b.jsx(ta,{type:"monotone",dataKey:"searches",stackId:"1",stroke:we.area(a).colors[0],fill:we.area(a).colors[0],fillOpacity:.6,name:"Total Searches"}),b.jsx(ta,{type:"monotone",dataKey:"users",stackId:"2",stroke:we.area(a).colors[1],fill:we.area(a).colors[1],fillOpacity:.6,name:"Active Users"})]})})})]})]})}),b.jsx(ei,{value:"performance",className:"space-y-6",children:b.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[b.jsxs(ye,{children:[b.jsxs(ge,{children:[b.jsxs(be,{className:"flex items-center gap-2",children:[b.jsx(kc,{className:"h-5 w-5 text-yellow-600"}),"Search Performance"]}),b.jsx(rt,{children:"Search success rates and average results"})]}),b.jsx(xe,{children:b.jsx(Dr,{width:"100%",height:300,children:b.jsxs(YI,{data:t.performanceMetrics,children:[b.jsx(yi,{...Ts(a)}),b.jsx(On,{dataKey:"date",...Rr(a),tick:{fontSize:12}}),b.jsx(An,{...Rr(a),tick:{fontSize:12}}),b.jsx(Ir,{content:b.jsx(s,{})}),b.jsx(St,{...Lr(a)}),b.jsx(Xi,{dataKey:"successRate",fill:we.bar(a).colors[0],name:"Success Rate (%)"}),b.jsx(Xi,{dataKey:"avgResults",fill:we.bar(a).colors[1],name:"Avg Results"})]})})})]}),b.jsxs(ye,{children:[b.jsxs(ge,{children:[b.jsxs(be,{className:"flex items-center gap-2",children:[b.jsx(I0,{className:"h-5 w-5 text-blue-600"}),"System Health"]}),b.jsx(rt,{children:"Real-time system status and performance"})]}),b.jsxs(xe,{className:"space-y-4",children:[b.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[b.jsxs("div",{className:"flex items-center gap-3",children:[b.jsx(j0,{className:"h-5 w-5 text-blue-600"}),b.jsxs("div",{children:[b.jsx("p",{className:"text-sm font-medium",children:"Database"}),b.jsxs("p",{className:"text-xs text-muted-foreground",children:[i.database.response_time,"ms response time"]})]})]}),b.jsxs("div",{className:"flex items-center gap-2",children:[i.database.status==="healthy"?b.jsx(Ya,{className:"h-4 w-4 text-green-600"}):b.jsx(Ha,{className:"h-4 w-4 text-red-600"}),b.jsx(gt,{variant:i.database.status==="healthy"?"default":"destructive",children:i.database.status})]})]}),b.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[b.jsxs("div",{className:"flex items-center gap-3",children:[b.jsx(kc,{className:"h-5 w-5 text-yellow-600"}),b.jsxs("div",{children:[b.jsx("p",{className:"text-sm font-medium",children:"Cache"}),b.jsx("p",{className:"text-xs text-muted-foreground",children:i.cache.message})]})]}),b.jsxs("div",{className:"flex items-center gap-2",children:[i.cache.status==="healthy"?b.jsx(Ya,{className:"h-4 w-4 text-green-600"}):b.jsx(Ha,{className:"h-4 w-4 text-red-600"}),b.jsx(gt,{variant:i.cache.status==="healthy"?"default":"destructive",children:i.cache.status})]})]}),b.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[b.jsxs("div",{className:"flex items-center gap-3",children:[b.jsx(E0,{className:"h-5 w-5 text-purple-600"}),b.jsxs("div",{children:[b.jsx("p",{className:"text-sm font-medium",children:"Storage"}),b.jsxs("p",{className:"text-xs text-muted-foreground",children:[i.storage.usage_percent,"% used"]})]})]}),b.jsxs("div",{className:"flex items-center gap-2",children:[i.storage.status==="healthy"?b.jsx(Ya,{className:"h-4 w-4 text-green-600"}):b.jsx(Ha,{className:"h-4 w-4 text-yellow-600"}),b.jsx(gt,{variant:i.storage.status==="healthy"?"default":"secondary",children:i.storage.status})]})]})]})]})]})}),b.jsx(ei,{value:"distribution",className:"space-y-6",children:b.jsxs("div",{className:"grid gap-6 md:grid-cols-3",children:[b.jsxs(ye,{children:[b.jsxs(ge,{children:[b.jsxs(be,{className:"flex items-center gap-2",children:[b.jsx(_0,{className:"h-5 w-5 text-blue-600"}),"User Distribution"]}),b.jsx(rt,{children:"Free vs Premium users"})]}),b.jsx(xe,{children:b.jsx(Dr,{width:"100%",height:250,children:b.jsxs(_s,{children:[b.jsx(Pn,{data:t.userDistribution,cx:"50%",cy:"50%",innerRadius:40,outerRadius:80,paddingAngle:5,dataKey:"value",children:t.userDistribution.map((l,c)=>b.jsx(wr,{fill:we.pie(a).colors[c%we.pie(a).colors.length]},`cell-${c}`))}),b.jsx(Ir,{content:b.jsx(s,{})}),b.jsx(St,{...Lr(a)})]})})})]}),b.jsxs(ye,{children:[b.jsxs(ge,{children:[b.jsxs(be,{className:"flex items-center gap-2",children:[b.jsx(ln,{className:"h-5 w-5 text-green-600"}),"Category Distribution"]}),b.jsx(rt,{children:"Parts by category"})]}),b.jsx(xe,{children:b.jsx(Dr,{width:"100%",height:250,children:b.jsxs(_s,{children:[b.jsx(Pn,{data:t.categoryDistribution,cx:"50%",cy:"50%",innerRadius:40,outerRadius:80,paddingAngle:5,dataKey:"value",children:t.categoryDistribution.map((l,c)=>b.jsx(wr,{fill:we.pie(a).colors[c%we.pie(a).colors.length]},`cell-${c}`))}),b.jsx(Ir,{content:b.jsx(s,{})}),b.jsx(St,{...Lr(a)})]})})})]}),b.jsxs(ye,{children:[b.jsxs(ge,{children:[b.jsxs(be,{className:"flex items-center gap-2",children:[b.jsx(un,{className:"h-5 w-5 text-purple-600"}),"Brand Distribution"]}),b.jsx(rt,{children:"Models by brand"})]}),b.jsx(xe,{children:b.jsx(Dr,{width:"100%",height:250,children:b.jsxs(_s,{children:[b.jsx(Pn,{data:t.brandDistribution,cx:"50%",cy:"50%",innerRadius:40,outerRadius:80,paddingAngle:5,dataKey:"value",children:t.brandDistribution.map((l,c)=>b.jsx(wr,{fill:we.pie(a).colors[c%we.pie(a).colors.length]},`cell-${c}`))}),b.jsx(Ir,{content:b.jsx(s,{})}),b.jsx(St,{...Lr(a)})]})})})]})]})}),b.jsxs(ei,{value:"activity",className:"space-y-6",children:[b.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[b.jsxs(ye,{children:[b.jsxs(ge,{children:[b.jsxs(be,{className:"flex items-center gap-2",children:[b.jsx(Ua,{className:"h-5 w-5 text-purple-600"}),"Recent Searches"]}),b.jsx(rt,{children:"Latest search activity from users"})]}),b.jsx(xe,{children:b.jsx("div",{className:"space-y-3",children:r.searches.length>0?r.searches.map(l=>{var c,u,f;return b.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 transition-colors",children:[b.jsxs("div",{className:"space-y-1",children:[b.jsx("p",{className:"text-sm font-medium leading-none text-foreground",children:l.query}),b.jsxs("p",{className:"text-xs text-muted-foreground",children:["by ",b.jsx("span",{className:"text-purple-600 font-medium",children:((c=l.user)==null?void 0:c.name)||"Anonymous"})," •",b.jsx("span",{className:"text-green-600 font-medium ml-1",children:l.results})," results"]})]}),b.jsxs("div",{className:"flex items-center gap-2",children:[b.jsx(gt,{variant:"secondary",className:"text-xs",children:l.type}),b.jsx(gt,{variant:((u=l.user)==null?void 0:u.plan)==="premium"?"default":"outline",className:"text-xs",children:((f=l.user)==null?void 0:f.plan)||"free"})]})]},l.id)}):b.jsxs("div",{className:"text-center py-8",children:[b.jsx(Ua,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),b.jsx("p",{className:"text-sm text-muted-foreground",children:"No recent searches"})]})})})]}),b.jsxs(ye,{children:[b.jsxs(ge,{children:[b.jsxs(be,{className:"flex items-center gap-2",children:[b.jsx(Fa,{className:"h-5 w-5 text-blue-600"}),"Recent Users"]}),b.jsx(rt,{children:"Latest user registrations"})]}),b.jsx(xe,{children:b.jsx("div",{className:"space-y-3",children:r.users.length>0?r.users.map(l=>b.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 transition-colors",children:[b.jsxs("div",{className:"space-y-1",children:[b.jsx("p",{className:"text-sm font-medium leading-none text-foreground",children:l.name}),b.jsxs("p",{className:"text-xs text-muted-foreground",children:[l.email," • ",b.jsx("span",{className:"text-blue-600 font-medium",children:l.searches_count})," searches"]})]}),b.jsxs("div",{className:"flex items-center gap-2",children:[b.jsx(gt,{variant:l.plan==="premium"?"default":"outline",className:"text-xs",children:l.plan}),b.jsx("span",{className:"text-xs text-muted-foreground",children:new Date(l.created_at).toLocaleDateString()})]})]},l.id)):b.jsxs("div",{className:"text-center py-8",children:[b.jsx(Fa,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),b.jsx("p",{className:"text-sm text-muted-foreground",children:"No recent users"})]})})})]})]}),b.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[b.jsxs(ye,{children:[b.jsxs(ge,{children:[b.jsxs(be,{className:"flex items-center gap-2",children:[b.jsx(ln,{className:"h-5 w-5 text-green-600"}),"Top Categories"]}),b.jsx(rt,{children:"Categories with the most parts"})]}),b.jsx(xe,{children:b.jsx("div",{className:"space-y-3",children:n.categories.length>0?n.categories.map((l,c)=>b.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 transition-colors",children:[b.jsxs("div",{className:"space-y-1",children:[b.jsx("p",{className:"text-sm font-medium leading-none text-foreground",children:l.name}),b.jsxs("p",{className:"text-xs text-muted-foreground",children:[b.jsx("span",{className:"text-green-600 font-medium",children:l.parts_count})," parts available"]})]}),b.jsx("div",{className:"flex items-center gap-2",children:b.jsxs(gt,{variant:c<3?"default":"outline",className:"text-xs",children:["#",c+1]})})]},l.id)):b.jsxs("div",{className:"text-center py-8",children:[b.jsx(ln,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),b.jsx("p",{className:"text-sm text-muted-foreground",children:"No categories with parts"})]})})})]}),b.jsxs(ye,{children:[b.jsxs(ge,{children:[b.jsxs(be,{className:"flex items-center gap-2",children:[b.jsx(un,{className:"h-5 w-5 text-blue-600"}),"Top Brands"]}),b.jsx(rt,{children:"Brands with the most mobile models"})]}),b.jsx(xe,{children:b.jsx("div",{className:"space-y-3",children:n.brands.length>0?n.brands.map((l,c)=>b.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 transition-colors",children:[b.jsxs("div",{className:"space-y-1",children:[b.jsx("p",{className:"text-sm font-medium leading-none text-foreground",children:l.name}),b.jsxs("p",{className:"text-xs text-muted-foreground",children:[b.jsx("span",{className:"text-blue-600 font-medium",children:l.models_count})," models"]})]}),b.jsx("div",{className:"flex items-center gap-2",children:b.jsxs(gt,{variant:c<3?"default":"outline",className:"text-xs",children:["#",c+1]})})]},l.id)):b.jsxs("div",{className:"text-center py-8",children:[b.jsx(un,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),b.jsx("p",{className:"text-sm text-muted-foreground",children:"No brands with models"})]})})})]})]})]})]}),b.jsxs(ye,{children:[b.jsxs(ge,{children:[b.jsxs(be,{className:"flex items-center gap-2",children:[b.jsx(T0,{className:"h-5 w-5 text-orange-600"}),"Quick Actions"]}),b.jsx(rt,{children:"Common administrative tasks"})]}),b.jsx(xe,{children:b.jsxs("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-5",children:[b.jsx("a",{href:"/",target:"_blank",rel:"noopener noreferrer",className:"group flex items-center justify-center p-6 border rounded-lg hover:bg-accent/50 hover:border-teal-200 transition-all duration-200 hover:shadow-md",children:b.jsxs("div",{className:"text-center",children:[b.jsx(C0,{className:"h-8 w-8 mx-auto mb-3 text-teal-600 group-hover:scale-110 transition-transform"}),b.jsx("p",{className:"text-sm font-medium text-foreground group-hover:text-teal-700",children:"View Website"}),b.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"Public site"})]})}),b.jsx("a",{href:"/admin/categories",className:"group flex items-center justify-center p-6 border rounded-lg hover:bg-accent/50 hover:border-green-200 transition-all duration-200 hover:shadow-md",children:b.jsxs("div",{className:"text-center",children:[b.jsx(ln,{className:"h-8 w-8 mx-auto mb-3 text-green-600 group-hover:scale-110 transition-transform"}),b.jsx("p",{className:"text-sm font-medium text-foreground group-hover:text-green-700",children:"Manage Categories"}),b.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"Organize parts"})]})}),b.jsx("a",{href:"/admin/brands",className:"group flex items-center justify-center p-6 border rounded-lg hover:bg-accent/50 hover:border-blue-200 transition-all duration-200 hover:shadow-md",children:b.jsxs("div",{className:"text-center",children:[b.jsx($0,{className:"h-8 w-8 mx-auto mb-3 text-blue-600 group-hover:scale-110 transition-transform"}),b.jsx("p",{className:"text-sm font-medium text-foreground group-hover:text-blue-700",children:"Manage Brands"}),b.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"Brand database"})]})}),b.jsx("a",{href:"/admin/models",className:"group flex items-center justify-center p-6 border rounded-lg hover:bg-accent/50 hover:border-purple-200 transition-all duration-200 hover:shadow-md",children:b.jsxs("div",{className:"text-center",children:[b.jsx(un,{className:"h-8 w-8 mx-auto mb-3 text-purple-600 group-hover:scale-110 transition-transform"}),b.jsx("p",{className:"text-sm font-medium text-foreground group-hover:text-purple-700",children:"Manage Models"}),b.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"Device models"})]})}),b.jsx("a",{href:"/admin/parts",className:"group flex items-center justify-center p-6 border rounded-lg hover:bg-accent/50 hover:border-orange-200 transition-all duration-200 hover:shadow-md",children:b.jsxs("div",{className:"text-center",children:[b.jsx(Tc,{className:"h-8 w-8 mx-auto mb-3 text-orange-600 group-hover:scale-110 transition-transform"}),b.jsx("p",{className:"text-sm font-medium text-foreground group-hover:text-orange-700",children:"Manage Parts"}),b.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"Parts inventory"})]})})]})})]})]})]})}export{QD as default};
