import{r as y,x as V,j as e,Q as F,S as _}from"./app-J5EqS6dS.js";import{C as a,a as i,b as r,c as l,d as x}from"./card-9XCADs-4.js";import{B as w}from"./smartphone-GGiwNneF.js";import{I as j}from"./input-Bo8dOn9p.js";import{L as o}from"./label-BlOrdc-X.js";import{S as U}from"./switch-yFNfZ5X-.js";import{T as M,a as z,b as p,c as u}from"./tabs-DZAL-HvD.js";import{B as d}from"./badge-BucYuCBs.js";import{g as Q,h as W,A as H}from"./ImpersonationBanner-CYn5eDk6.js";import{A as K,C}from"./app-layout-ox1kAwY6.js";import{T as f}from"./triangle-alert-BW76NKO9.js";import{S as P}from"./shield-D9nQfigG.js";import{C as S}from"./circle-check-big-DOFoatRy.js";import{U as X,S as Y}from"./users-RYmOyic9.js";import{R as q}from"./refresh-cw-b5UG9YKX.js";import{C as G}from"./clock-Brl7_5s7.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./index-Ba8m9N9L.js";import"./index-BzZWUWqx.js";import"./crown-UDSxMtlm.js";import"./user-DCnDRzMf.js";import"./search-DBK6jUoc.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./eye-D-fsmYB2.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./globe-zfFlVOSX.js";function De({is_enabled:L,configs:g,user_status:R,statistics:n}){const[c,T]=y.useState(L),[D,N]=y.useState(!1),{data:A,setData:h,post:$,processing:v,reset:k}=V(Object.fromEntries(Object.entries(g).map(([s,t])=>[s,{max_attempts:t.max_attempts,decay_seconds:t.decay_seconds,description:t.description}]))),I=()=>{N(!0);const s=!c;_.post("/admin/rate-limit/toggle",{enabled:s},{onSuccess:()=>{T(s)},onFinish:()=>N(!1)})},O=s=>{s.preventDefault(),$("/admin/rate-limit/configs")},B=()=>{_.post("/admin/rate-limit/reset",{},{onSuccess:()=>{k()}})},b=s=>s<60?`${s}s`:s<3600?`${Math.floor(s/60)}m`:`${Math.floor(s/3600)}h`,E=s=>s.enabled?s.is_limited?e.jsxs(d,{variant:"destructive",children:[e.jsx(C,{className:"h-3 w-3 mr-1"}),"Limited"]}):s.remaining_attempts<=5?e.jsxs(d,{variant:"outline",className:"border-yellow-500 text-yellow-700",children:[e.jsx(f,{className:"h-3 w-3 mr-1"}),"Warning"]}):e.jsxs(d,{variant:"default",className:"bg-green-100 text-green-800",children:[e.jsx(S,{className:"h-3 w-3 mr-1"}),"OK"]}):e.jsx(d,{variant:"secondary",children:"Disabled"});return e.jsxs(K,{children:[e.jsx(F,{title:"Rate Limiting"}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:"Rate Limiting"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Manage rate limiting settings to prevent abuse of admin actions"})]}),e.jsx("div",{className:"flex items-center gap-4",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(o,{htmlFor:"rate-limit-toggle",children:"Rate Limiting"}),e.jsx(U,{id:"rate-limit-toggle",checked:c,onCheckedChange:I,disabled:D})]})})]}),!c&&e.jsxs(Q,{variant:"destructive",children:[e.jsx(f,{className:"h-4 w-4"}),e.jsx(W,{children:"Rate limiting is currently disabled. Admin actions are not being rate limited."})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-4",children:[e.jsxs(a,{children:[e.jsxs(i,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(r,{className:"text-sm font-medium",children:"Status"}),e.jsx(P,{className:"h-4 w-4 text-blue-600"})]}),e.jsxs(l,{children:[e.jsx("div",{className:"text-2xl font-bold",children:c?"Enabled":"Disabled"}),e.jsx("div",{className:"mt-2",children:c?e.jsxs(d,{variant:"default",className:"bg-green-100 text-green-800",children:[e.jsx(S,{className:"h-3 w-3 mr-1"}),"Active"]}):e.jsxs(d,{variant:"destructive",children:[e.jsx(C,{className:"h-3 w-3 mr-1"}),"Inactive"]})})]})]}),e.jsxs(a,{children:[e.jsxs(i,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(r,{className:"text-sm font-medium",children:"Violations (7 days)"}),e.jsx(f,{className:"h-4 w-4 text-red-600"})]}),e.jsxs(l,{children:[e.jsx("div",{className:"text-2xl font-bold",children:n.total_violations_7_days}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Rate limit exceeded events"})]})]}),e.jsxs(a,{children:[e.jsxs(i,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(r,{className:"text-sm font-medium",children:"Protected Actions"}),e.jsx(H,{className:"h-4 w-4 text-purple-600"})]}),e.jsxs(l,{children:[e.jsx("div",{className:"text-2xl font-bold",children:Object.keys(g).length}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Different action types"})]})]}),e.jsxs(a,{children:[e.jsxs(i,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(r,{className:"text-sm font-medium",children:"Top Violators"}),e.jsx(X,{className:"h-4 w-4 text-orange-600"})]}),e.jsxs(l,{children:[e.jsx("div",{className:"text-2xl font-bold",children:n.top_violating_users.length}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Users with violations"})]})]})]}),e.jsxs(M,{defaultValue:"settings",className:"space-y-4",children:[e.jsxs(z,{className:"grid w-full grid-cols-3",children:[e.jsx(p,{value:"settings",children:"Settings"}),e.jsx(p,{value:"status",children:"Current Status"}),e.jsx(p,{value:"statistics",children:"Statistics"})]}),e.jsx(u,{value:"settings",className:"space-y-4",children:e.jsxs(a,{children:[e.jsxs(i,{children:[e.jsxs(r,{className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-5 w-5"}),"Rate Limit Configuration"]}),e.jsx(x,{children:"Configure rate limits for different types of admin actions"})]}),e.jsx(l,{children:e.jsxs("form",{onSubmit:O,className:"space-y-6",children:[e.jsx("div",{className:"grid gap-6",children:Object.entries(A).map(([s,t])=>e.jsxs("div",{className:"border rounded-lg p-4",children:[e.jsx("h3",{className:"font-medium mb-3 capitalize",children:s.replace("_"," ")}),e.jsx("p",{className:"text-sm text-muted-foreground mb-4",children:t.description}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:`${s}_max_attempts`,children:"Max Attempts"}),e.jsx(j,{id:`${s}_max_attempts`,type:"number",min:"1",max:"1000",value:t.max_attempts,onChange:m=>h(s,{...t,max_attempts:parseInt(m.target.value)})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:`${s}_decay_seconds`,children:"Window (seconds)"}),e.jsx(j,{id:`${s}_decay_seconds`,type:"number",min:"60",max:"3600",value:t.decay_seconds,onChange:m=>h(s,{...t,decay_seconds:parseInt(m.target.value)})}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:[b(t.decay_seconds)," window"]})]}),e.jsxs("div",{className:"space-y-2 md:col-span-2",children:[e.jsx(o,{htmlFor:`${s}_description`,children:"Description"}),e.jsx(j,{id:`${s}_description`,value:t.description,onChange:m=>h(s,{...t,description:m.target.value})})]})]})]},s))}),e.jsxs("div",{className:"flex justify-between",children:[e.jsxs(w,{type:"button",variant:"outline",onClick:B,children:[e.jsx(q,{className:"h-4 w-4 mr-2"}),"Reset to Defaults"]}),e.jsx(w,{type:"submit",disabled:v,children:v?"Saving...":"Save Configuration"})]})]})})]})}),e.jsx(u,{value:"status",className:"space-y-4",children:e.jsxs(a,{children:[e.jsxs(i,{children:[e.jsxs(r,{className:"flex items-center gap-2",children:[e.jsx(G,{className:"h-5 w-5"}),"Your Current Rate Limit Status"]}),e.jsx(x,{children:"Current rate limit status for your admin account"})]}),e.jsx(l,{children:e.jsx("div",{className:"space-y-4",children:Object.entries(R).map(([s,t])=>e.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium capitalize",children:s.replace("_"," ")}),e.jsxs("p",{className:"text-sm text-muted-foreground",children:[t.remaining_attempts," of ",t.max_attempts," attempts remaining"]})]}),e.jsxs("div",{className:"text-right",children:[E(t),t.is_limited&&e.jsxs("p",{className:"text-xs text-red-600 mt-1",children:["Retry in ",b(t.retry_after_seconds)]})]})]},s))})})]})}),e.jsxs(u,{value:"statistics",className:"space-y-4",children:[e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs(a,{children:[e.jsxs(i,{children:[e.jsx(r,{children:"Violations by Action"}),e.jsx(x,{children:"Last 7 days"})]}),e.jsx(l,{children:e.jsxs("div",{className:"space-y-3",children:[Object.entries(n.violations_by_action).map(([s,t])=>e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm capitalize",children:s.replace("_"," ")}),e.jsx("span",{className:"font-bold",children:t})]},s)),Object.keys(n.violations_by_action).length===0&&e.jsx("p",{className:"text-sm text-muted-foreground",children:"No violations recorded"})]})})]}),e.jsxs(a,{children:[e.jsxs(i,{children:[e.jsx(r,{children:"Top Violating Users"}),e.jsx(x,{children:"Users with most violations"})]}),e.jsx(l,{children:e.jsxs("div",{className:"space-y-3",children:[n.top_violating_users.map(s=>e.jsxs("div",{className:"flex justify-between",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium",children:s.user.name}),e.jsx("p",{className:"text-xs text-muted-foreground",children:s.user.email})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("span",{className:"font-bold",children:s.count}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"violations"})]})]},s.user.id)),n.top_violating_users.length===0&&e.jsx("p",{className:"text-sm text-muted-foreground",children:"No violations recorded"})]})})]})]}),e.jsxs(a,{children:[e.jsxs(i,{children:[e.jsx(r,{children:"Recent Violations"}),e.jsx(x,{children:"Latest rate limit violations"})]}),e.jsx(l,{children:e.jsxs("div",{className:"space-y-3",children:[n.recent_violations.map(s=>e.jsxs("div",{className:"flex justify-between items-center p-3 border rounded-lg",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:s.user.name}),e.jsxs("p",{className:"text-sm text-muted-foreground",children:[s.action.replace("_"," ")," • ",s.ip_address]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"text-sm",children:new Date(s.created_at).toLocaleDateString()}),e.jsx("p",{className:"text-xs text-muted-foreground",children:new Date(s.created_at).toLocaleTimeString()})]})]},s.id)),n.recent_violations.length===0&&e.jsx("p",{className:"text-sm text-muted-foreground",children:"No recent violations"})]})})]})]})]})]})})]})}export{De as default};
