import{x as T,r as m,j as e,Q as V,t as w}from"./app-J5EqS6dS.js";import{B as l}from"./smartphone-GGiwNneF.js";import{C as z,a as B,b as O,d as U,c as $}from"./card-9XCADs-4.js";import{I as d}from"./input-Bo8dOn9p.js";import{L as n}from"./label-BlOrdc-X.js";import{S as G,a as K,b as Q,c as q,d as H}from"./select-CIhY0l9J.js";import{S as R}from"./switch-yFNfZ5X-.js";import{T as X}from"./textarea-BDEiXlPH.js";import{A as J}from"./app-layout-ox1kAwY6.js";import{M as W}from"./MediaPicker-Due2OGB1.js";import{X as f,P as Y,I as v,t as _}from"./ImpersonationBanner-CYn5eDk6.js";import{A as Z}from"./arrow-left-D4U9AVF9.js";import{S as ee}from"./save-DfhL0V-C.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./search-DBK6jUoc.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./badge-BucYuCBs.js";import"./eye-D-fsmYB2.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./tabs-DZAL-HvD.js";import"./checkout-helpers-CMrRJez4.js";import"./loader-circle-B1NtNhL1.js";import"./crown-UDSxMtlm.js";function Be({part:i,categories:P}){const{data:t,setData:c,put:I,processing:N,errors:a}=T({category_id:i.category_id.toString(),name:i.name,part_number:i.part_number||"",manufacturer:i.manufacturer||"",description:i.description||"",specifications:i.specifications||{},images:i.images||[],is_active:i.is_active}),[p,b]=m.useState(""),[x,y]=m.useState(""),[k,h]=m.useState(!1),[u,S]=m.useState([]),[C,g]=m.useState(null),A=s=>{s.preventDefault(),I(`/admin/parts/${i.id}`,{onSuccess:()=>{_.success(`Part "${t.name}" has been updated successfully.`)},onError:()=>{_.error("Failed to update part. Please check the form and try again.")}})},D=()=>{p&&x&&(c("specifications",{...t.specifications,[p]:x}),b(""),y(""))},M=s=>{const r={...t.specifications};delete r[s],c("specifications",r)},F=s=>{const r=s.map(o=>o.url);c("images",[...t.images,...r]),S([...u,...s]),h(!1)},E=s=>{const r=t.images.filter((L,j)=>j!==s),o=u.filter((L,j)=>j!==s);c("images",r),S(o)};return e.jsxs(J,{children:[e.jsx(V,{title:`Edit ${i.name} - Parts - Admin`}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(w,{href:"/admin/parts",children:e.jsxs(l,{variant:"outline",size:"sm",children:[e.jsx(Z,{className:"w-4 h-4 mr-2"}),"Back to Parts"]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Edit Part"}),e.jsxs("p",{className:"text-muted-foreground",children:['Update the information for "',i.name,'"']})]})]}),e.jsxs(z,{children:[e.jsxs(B,{children:[e.jsx(O,{children:"Part Details"}),e.jsx(U,{children:"Update the information for this part"})]}),e.jsx($,{children:e.jsxs("form",{onSubmit:A,className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"category_id",children:"Category *"}),e.jsxs(G,{value:t.category_id,onValueChange:s=>c("category_id",s),children:[e.jsx(K,{className:a.category_id?"border-red-500":"",children:e.jsx(Q,{placeholder:"Select a category"})}),e.jsx(q,{children:P.map(s=>e.jsx(H,{value:s.id.toString(),children:s.name},s.id))})]}),a.category_id&&e.jsx("p",{className:"text-sm text-red-600",children:a.category_id})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"name",children:"Part Name *"}),e.jsx(d,{id:"name",type:"text",value:t.name,onChange:s=>c("name",s.target.value),placeholder:"e.g., LCD Display Assembly, Battery, Camera Module",className:a.name?"border-red-500":""}),a.name&&e.jsx("p",{className:"text-sm text-red-600",children:a.name})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"part_number",children:"Part Number"}),e.jsx(d,{id:"part_number",type:"text",value:t.part_number,onChange:s=>c("part_number",s.target.value),placeholder:"e.g., LCD-001, BAT-123, CAM-456",className:a.part_number?"border-red-500":""}),a.part_number&&e.jsx("p",{className:"text-sm text-red-600",children:a.part_number})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"manufacturer",children:"Manufacturer"}),e.jsx(d,{id:"manufacturer",type:"text",value:t.manufacturer,onChange:s=>c("manufacturer",s.target.value),placeholder:"e.g., Samsung, LG, Sony, Foxconn",className:a.manufacturer?"border-red-500":""}),a.manufacturer&&e.jsx("p",{className:"text-sm text-red-600",children:a.manufacturer})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"description",children:"Description"}),e.jsx(X,{id:"description",value:t.description,onChange:s=>c("description",s.target.value),placeholder:"Detailed description of the part, its features, and compatibility notes...",className:a.description?"border-red-500":"",rows:4}),a.description&&e.jsx("p",{className:"text-sm text-red-600",children:a.description})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(n,{children:"Specifications"}),e.jsxs("div",{className:"space-y-3",children:[Object.entries(t.specifications).map(([s,r])=>e.jsxs("div",{className:"flex items-center gap-2 p-3 border rounded-lg",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("span",{className:"font-medium text-sm",children:[s,":"]}),e.jsx("span",{className:"ml-2 text-sm",children:r})]}),e.jsx(l,{type:"button",variant:"outline",size:"sm",onClick:()=>M(s),className:"text-destructive hover:text-destructive",children:e.jsx(f,{className:"h-4 w-4"})})]},s)),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(d,{placeholder:"Specification name (e.g., Material, Voltage)",value:p,onChange:s=>b(s.target.value),className:"flex-1"}),e.jsx(d,{placeholder:"Value (e.g., Glass, 3.7V)",value:x,onChange:s=>y(s.target.value),className:"flex-1"}),e.jsx(l,{type:"button",variant:"outline",onClick:D,disabled:!p||!x,children:e.jsx(Y,{className:"h-4 w-4"})})]})]}),a.specifications&&e.jsx("p",{className:"text-sm text-red-600",children:a.specifications})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(n,{children:"Images"}),e.jsxs(l,{type:"button",variant:"outline",onClick:()=>h(!0),children:[e.jsx(v,{className:"h-4 w-4 mr-2"}),"Add Images"]})]}),t.images.length>0?e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:t.images.map((s,r)=>e.jsxs("div",{className:"relative group",children:[e.jsx("div",{className:"aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 border-transparent group-hover:border-blue-200 transition-colors",children:e.jsx("img",{src:s,alt:`Part image ${r+1}`,className:"w-full h-full object-cover cursor-pointer",onClick:()=>g(s),onError:o=>{o.currentTarget.style.display="none"}})}),e.jsx(l,{type:"button",variant:"destructive",size:"sm",onClick:()=>E(r),className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity",children:e.jsx(f,{className:"h-3 w-3"})}),u[r]&&e.jsx("div",{className:"absolute bottom-2 left-2 right-2",children:e.jsx("p",{className:"text-xs text-white bg-black bg-opacity-50 rounded px-2 py-1 truncate",children:u[r].original_filename})})]},r))}):e.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center",children:[e.jsx(v,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No images added"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Add images to showcase this part"}),e.jsxs(l,{type:"button",variant:"outline",onClick:()=>h(!0),children:[e.jsx(v,{className:"h-4 w-4 mr-2"}),"Choose Images"]})]}),a.images&&e.jsx("p",{className:"text-sm text-red-600",children:a.images})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(R,{id:"is_active",checked:t.is_active,onCheckedChange:s=>c("is_active",s)}),e.jsx(n,{htmlFor:"is_active",children:"Active"})]}),e.jsxs("div",{className:"flex justify-end gap-4 pt-6 border-t",children:[e.jsx(w,{href:"/admin/parts",children:e.jsx(l,{variant:"outline",type:"button",children:"Cancel"})}),e.jsxs(l,{type:"submit",disabled:N,children:[e.jsx(ee,{className:"w-4 h-4 mr-2"}),N?"Updating...":"Update Part"]})]})]})})]})]})}),e.jsx(W,{isOpen:k,onClose:()=>h(!1),onSelect:F,multiple:!0,title:"Choose Images for Part",acceptedTypes:["image/*"]}),C&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",onClick:()=>g(null),children:e.jsxs("div",{className:"max-w-4xl max-h-full p-4",children:[e.jsx("img",{src:C,alt:"Preview",className:"max-w-full max-h-full object-contain",onClick:s=>s.stopPropagation()}),e.jsx(l,{variant:"secondary",size:"sm",onClick:()=>g(null),className:"absolute top-4 right-4",children:e.jsx(f,{className:"h-4 w-4"})})]})})]})}export{Be as default};
