import{x,j as e,Q as p}from"./app-J5EqS6dS.js";import{I as l}from"./input-error-SDo-ayIc.js";import{T as h}from"./text-link-BJiDbWz5.js";import{B as b}from"./smartphone-GGiwNneF.js";import{I as i}from"./input-Bo8dOn9p.js";import{L as d}from"./label-BlOrdc-X.js";import{A as f}from"./auth-layout-Do0a8FOS.js";import{U as g}from"./user-DCnDRzMf.js";import{M as j}from"./mail-CDon-vZy.js";import{L as n}from"./lock-Tx_yfI4R.js";import{L as y}from"./loader-circle-B1NtNhL1.js";import{A as v}from"./arrow-right-CCfGNWZ9.js";/* empty css            */import"./index-CJpBU2i9.js";import"./database-s9JOA0jY.js";import"./shield-D9nQfigG.js";import"./zap-BcmHRR4K.js";function S(){const{data:a,setData:t,post:m,processing:r,errors:o,reset:c}=x({name:"",email:"",password:"",password_confirmation:""}),u=s=>{s.preventDefault(),m(route("register"),{onFinish:()=>c("password","password_confirmation")})};return e.jsxs(f,{title:"Create Your Account",description:"Join thousands of professionals using our mobile parts database",children:[e.jsx(p,{title:"Register"}),e.jsxs("form",{className:"space-y-6",onSubmit:u,children:[e.jsxs("div",{className:"space-y-5",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(d,{htmlFor:"name",className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Full Name"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(g,{className:"h-5 w-5 text-gray-400"})}),e.jsx(i,{id:"name",type:"text",required:!0,autoFocus:!0,tabIndex:1,autoComplete:"name",value:a.name,onChange:s=>t("name",s.target.value),disabled:r,placeholder:"Enter your full name",className:"pl-10 h-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:focus:border-blue-400"})]}),e.jsx(l,{message:o.name,className:"mt-1"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(d,{htmlFor:"email",className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Email Address"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(j,{className:"h-5 w-5 text-gray-400"})}),e.jsx(i,{id:"email",type:"email",required:!0,tabIndex:2,autoComplete:"email",value:a.email,onChange:s=>t("email",s.target.value),disabled:r,placeholder:"Enter your email address",className:"pl-10 h-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:focus:border-blue-400"})]}),e.jsx(l,{message:o.email})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(d,{htmlFor:"password",className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Password"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(n,{className:"h-5 w-5 text-gray-400"})}),e.jsx(i,{id:"password",type:"password",required:!0,tabIndex:3,autoComplete:"new-password",value:a.password,onChange:s=>t("password",s.target.value),disabled:r,placeholder:"Create a strong password",className:"pl-10 h-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:focus:border-blue-400"})]}),e.jsx(l,{message:o.password})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(d,{htmlFor:"password_confirmation",className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Confirm Password"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(n,{className:"h-5 w-5 text-gray-400"})}),e.jsx(i,{id:"password_confirmation",type:"password",required:!0,tabIndex:4,autoComplete:"new-password",value:a.password_confirmation,onChange:s=>t("password_confirmation",s.target.value),disabled:r,placeholder:"Confirm your password",className:"pl-10 h-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:focus:border-blue-400"})]}),e.jsx(l,{message:o.password_confirmation})]}),e.jsx(b,{type:"submit",className:"w-full h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]",tabIndex:5,disabled:r,children:r?e.jsxs(e.Fragment,{children:[e.jsx(y,{className:"h-5 w-5 animate-spin mr-2"}),"Creating Account..."]}):e.jsxs(e.Fragment,{children:["Create Account",e.jsx(v,{className:"ml-2 h-5 w-5"})]})})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("div",{className:"w-full border-t border-gray-300 dark:border-gray-600"})}),e.jsx("div",{className:"relative flex justify-center text-sm",children:e.jsx("span",{className:"px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400",children:"Already have an account?"})})]}),e.jsx("div",{className:"text-center",children:e.jsx(h,{href:route("login"),tabIndex:6,className:"text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium",children:"Sign in to your account"})})]})]})}export{S as default};
