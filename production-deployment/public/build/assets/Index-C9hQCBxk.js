import{r as k,j as e,Q as C,t as j,S as v}from"./app-J5EqS6dS.js";import{B as n}from"./badge-BucYuCBs.js";import{c as N,B as c}from"./smartphone-GGiwNneF.js";import{C as m,a as p,b as u,c as x,d as S}from"./card-9XCADs-4.js";import{I as M}from"./input-Bo8dOn9p.js";import{A as F}from"./app-layout-ox1kAwY6.js";import{F as y}from"./ImpersonationBanner-CYn5eDk6.js";import{F as z}from"./filter-DKJvAZFg.js";import{S as A}from"./search-DBK6jUoc.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./users-RYmOyic9.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./eye-D-fsmYB2.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]],b=N("CalendarDays",D);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]],B=N("CirclePlus",E);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P=[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]],L=N("RotateCw",P);function I({currentPage:r,pageCount:h,onPageChange:d}){const t=()=>{let a=[1,r-1,r,r+1,h].filter(i=>i>0&&i<=h);a=[...new Set(a)].sort((i,o)=>i-o);const l=[];for(let i=0;i<a.length;i++)l.push(a[i]),i<a.length-1&&a[i+1]-a[i]>1&&l.push("...");return l};return e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(c,{variant:"outline",size:"sm",onClick:()=>d(r-1),disabled:r===1,children:"Previous"}),e.jsx("div",{className:"flex gap-1",children:t().map((a,l)=>typeof a=="number"?e.jsx(c,{variant:r===a?"default":"outline",size:"sm",onClick:()=>d(a),className:"w-8 h-8 p-0",children:a},l):e.jsx("span",{className:"flex items-center justify-center w-8 h-8 text-muted-foreground",children:"..."},l))}),e.jsx(c,{variant:"outline",size:"sm",onClick:()=>d(r+1),disabled:r===h,children:"Next"})]})}function oe({subscriptions:r={data:[]},filters:h,counts:d={total:0,active:0,cancelled:0,expired:0}}){const[t,a]=k.useState(h||{status:"",plan:"",search:"",sort_by:"created_at",sort_direction:"desc"}),l=(s,f)=>{const g={...t,[s]:f};a(g),v.get(route("admin.subscriptions.index"),g,{preserveState:!0,preserveScroll:!0})},i=()=>{const s={status:"",plan:"",search:"",sort_by:"created_at",sort_direction:"desc"};a(s),v.get(route("admin.subscriptions.index"),s,{preserveState:!0,preserveScroll:!0})},o=s=>{const f=t.sort_by===s&&t.sort_direction==="asc"?"desc":"asc";l("sort_by",s),l("sort_direction",f)},w=s=>{switch(s){case"active":return e.jsx(n,{className:"bg-green-500 hover:bg-green-600",children:"Active"});case"cancelled":return e.jsx(n,{variant:"destructive",children:"Cancelled"});case"expired":return e.jsx(n,{variant:"outline",className:"border-yellow-500 text-yellow-700",children:"Expired"});case"pending":return e.jsx(n,{variant:"secondary",children:"Pending"});default:return e.jsx(n,{variant:"outline",children:s})}},_=s=>{switch(s){case"premium":return e.jsx(n,{className:"bg-amber-500 hover:bg-amber-600",children:"Premium"});default:return e.jsx(n,{variant:"outline",children:"Free"})}};return e.jsxs(F,{children:[e.jsx(C,{title:"Subscription Management"}),e.jsxs("div",{className:"flex h-full flex-1 flex-col p-4 space-y-6",children:[e.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Subscription Management"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Manage user subscriptions, view status, and modify plans"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(j,{href:route("admin.dashboard"),children:e.jsxs(c,{variant:"outline",size:"sm",children:[e.jsx(L,{className:"mr-2 h-4 w-4"}),"Dashboard"]})}),e.jsx(j,{href:route("admin.subscriptions.create"),children:e.jsxs(c,{size:"sm",children:[e.jsx(B,{className:"mr-2 h-4 w-4"}),"Create Subscription"]})})]})]}),e.jsxs("div",{className:"grid gap-4 grid-cols-2 sm:grid-cols-4",children:[e.jsxs(m,{children:[e.jsx(p,{className:"py-3",children:e.jsxs(u,{className:"text-sm font-medium flex items-center justify-between",children:["Total Subscriptions",e.jsx(y,{className:"h-4 w-4 text-blue-600"})]})}),e.jsx(x,{className:"py-4",children:e.jsx("div",{className:"text-2xl font-bold",children:d.total})})]}),e.jsxs(m,{children:[e.jsx(p,{className:"py-3",children:e.jsxs(u,{className:"text-sm font-medium flex items-center justify-between",children:["Active",e.jsx(n,{className:"bg-green-500 hover:bg-green-600",children:"Active"})]})}),e.jsx(x,{className:"py-4",children:e.jsx("div",{className:"text-2xl font-bold",children:d.active})})]}),e.jsxs(m,{children:[e.jsx(p,{className:"py-3",children:e.jsxs(u,{className:"text-sm font-medium flex items-center justify-between",children:["Cancelled",e.jsx(n,{variant:"destructive",children:"Cancelled"})]})}),e.jsx(x,{className:"py-4",children:e.jsx("div",{className:"text-2xl font-bold",children:d.cancelled})})]}),e.jsxs(m,{children:[e.jsx(p,{className:"py-3",children:e.jsxs(u,{className:"text-sm font-medium flex items-center justify-between",children:["Expired",e.jsx(n,{variant:"outline",className:"border-yellow-500 text-yellow-700",children:"Expired"})]})}),e.jsx(x,{className:"py-4",children:e.jsx("div",{className:"text-2xl font-bold",children:d.expired})})]})]}),e.jsxs(m,{children:[e.jsxs(p,{children:[e.jsxs(u,{className:"text-base flex items-center",children:[e.jsx(z,{className:"h-4 w-4 mr-2"}),"Filter Subscriptions"]}),e.jsx(S,{children:"Filter subscription list by various criteria"})]}),e.jsx(x,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-1 block",children:"Status"}),e.jsxs("select",{value:t.status,onChange:s=>l("status",s.target.value),className:"w-full rounded-md border border-input px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",children:[e.jsx("option",{value:"",children:"All Statuses"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"cancelled",children:"Cancelled"}),e.jsx("option",{value:"expired",children:"Expired"}),e.jsx("option",{value:"pending",children:"Pending"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-1 block",children:"Plan"}),e.jsxs("select",{value:t.plan,onChange:s=>l("plan",s.target.value),className:"w-full rounded-md border border-input px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",children:[e.jsx("option",{value:"",children:"All Plans"}),e.jsx("option",{value:"free",children:"Free"}),e.jsx("option",{value:"premium",children:"Premium"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-1 block",children:"Search"}),e.jsxs("div",{className:"relative",children:[e.jsx(A,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),e.jsx(M,{type:"search",placeholder:"User name or email...",className:"pl-8",value:t.search,onChange:s=>l("search",s.target.value)})]})]}),e.jsx("div",{className:"flex items-end",children:e.jsx(c,{variant:"outline",className:"w-full",onClick:i,children:"Clear Filters"})})]})})]}),e.jsx(m,{children:e.jsxs(x,{className:"p-0",children:[e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-border",children:[e.jsx("thead",{className:"bg-muted/50",children:e.jsxs("tr",{children:[e.jsxs("th",{className:"px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider cursor-pointer",onClick:()=>o("user.name"),children:["User",t.sort_by==="user.name"&&e.jsx("span",{className:"ml-1",children:t.sort_direction==="asc"?"↑":"↓"})]}),e.jsxs("th",{className:"px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider cursor-pointer",onClick:()=>o("plan_name"),children:["Plan",t.sort_by==="plan_name"&&e.jsx("span",{className:"ml-1",children:t.sort_direction==="asc"?"↑":"↓"})]}),e.jsxs("th",{className:"px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider cursor-pointer",onClick:()=>o("status"),children:["Status",t.sort_by==="status"&&e.jsx("span",{className:"ml-1",children:t.sort_direction==="asc"?"↑":"↓"})]}),e.jsxs("th",{className:"px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider cursor-pointer",onClick:()=>o("created_at"),children:["Created",t.sort_by==="created_at"&&e.jsx("span",{className:"ml-1",children:t.sort_direction==="asc"?"↑":"↓"})]}),e.jsxs("th",{className:"px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider cursor-pointer",onClick:()=>o("current_period_end"),children:["Expires",t.sort_by==="current_period_end"&&e.jsx("span",{className:"ml-1",children:t.sort_direction==="asc"?"↑":"↓"})]}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider",children:"Actions"})]})}),e.jsxs("tbody",{className:"bg-card divide-y divide-border",children:[(r.data||[]).map(s=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"flex items-center",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-foreground",children:s.user.name}),e.jsx("div",{className:"text-sm text-muted-foreground",children:s.user.email})]})})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:_(s.plan_name)}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:w(s.status)}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-muted-foreground",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(b,{className:"h-3 w-3 mr-1"}),new Date(s.created_at).toLocaleDateString()]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-muted-foreground",children:s.status==="active"?e.jsxs("div",{className:"flex items-center",children:[e.jsx(b,{className:"h-3 w-3 mr-1"}),new Date(s.current_period_end).toLocaleDateString()]}):e.jsx("span",{className:"text-xs italic",children:"—"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx(j,{href:route("admin.subscriptions.show",s.id),children:e.jsx(c,{variant:"outline",size:"sm",children:"View"})}),e.jsx(j,{href:route("admin.subscriptions.edit",s.id),children:e.jsx(c,{variant:"outline",size:"sm",children:"Edit"})})]})})]},s.id)),(r.data||[]).length===0&&e.jsx("tr",{children:e.jsx("td",{colSpan:6,className:"px-6 py-10 text-center",children:e.jsxs("div",{className:"flex flex-col items-center justify-center text-muted-foreground",children:[e.jsx(y,{className:"h-8 w-8 mb-2"}),e.jsx("p",{children:"No subscriptions found matching the filters."}),e.jsx(c,{variant:"outline",size:"sm",onClick:i,className:"mt-2",children:"Clear Filters"})]})})})]})]})}),r.meta&&r.meta.last_page>1&&e.jsx("div",{className:"px-6 py-4",children:e.jsx(I,{currentPage:r.meta.current_page,pageCount:r.meta.last_page,onPageChange:s=>{v.get(route("admin.subscriptions.index",{...t,page:s}),{},{preserveState:!0,preserveScroll:!0})}})})]})})]})]})}export{oe as default};
