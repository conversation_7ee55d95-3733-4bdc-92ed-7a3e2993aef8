import{x as n,j as e,Q as d}from"./app-J5EqS6dS.js";import{I as p}from"./input-error-SDo-ayIc.js";import{T as c}from"./text-link-BJiDbWz5.js";import{B as x}from"./smartphone-GGiwNneF.js";import{I as u}from"./input-Bo8dOn9p.js";import{L as f}from"./label-BlOrdc-X.js";import{A as j}from"./auth-layout-Do0a8FOS.js";import{L as h}from"./loader-circle-B1NtNhL1.js";/* empty css            */import"./index-CJpBU2i9.js";import"./database-s9JOA0jY.js";import"./shield-D9nQfigG.js";import"./zap-BcmHRR4K.js";function B({status:r}){const{data:a,setData:o,post:i,processing:t,errors:m}=n({email:""}),l=s=>{s.preventDefault(),i(route("password.email"))};return e.jsxs(j,{title:"Forgot password",description:"Enter your email to receive a password reset link",children:[e.jsx(d,{title:"Forgot password"}),r&&e.jsx("div",{className:"mb-4 text-center text-sm font-medium text-green-600",children:r}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("form",{onSubmit:l,children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(f,{htmlFor:"email",children:"Email address"}),e.jsx(u,{id:"email",type:"email",name:"email",autoComplete:"off",value:a.email,autoFocus:!0,onChange:s=>o("email",s.target.value),placeholder:"<EMAIL>"}),e.jsx(p,{message:m.email})]}),e.jsx("div",{className:"my-6 flex items-center justify-start",children:e.jsxs(x,{className:"w-full",disabled:t,children:[t&&e.jsx(h,{className:"h-4 w-4 animate-spin"}),"Email password reset link"]})})]}),e.jsxs("div",{className:"space-x-1 text-center text-sm text-muted-foreground",children:[e.jsx("span",{children:"Or, return to"}),e.jsx(c,{href:route("login"),children:"log in"})]})]})]})}export{B as default};
