import{j as e}from"./app-J5EqS6dS.js";import{a as r}from"./smartphone-GGiwNneF.js";function o({className:a,...t}){return e.jsx("div",{"data-slot":"card",className:r("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function n({className:a,...t}){return e.jsx("div",{"data-slot":"card-header",className:r("flex flex-col gap-1.5 px-6",a),...t})}function c({className:a,...t}){return e.jsx("div",{"data-slot":"card-title",className:r("leading-none font-semibold",a),...t})}function i({className:a,...t}){return e.jsx("div",{"data-slot":"card-description",className:r("text-muted-foreground text-sm",a),...t})}function l({className:a,...t}){return e.jsx("div",{"data-slot":"card-content",className:r("px-6",a),...t})}function x({className:a,...t}){return e.jsx("div",{"data-slot":"card-footer",className:r("flex items-center px-6",a),...t})}export{o as C,n as a,c as b,l as c,i as d,x as e};
