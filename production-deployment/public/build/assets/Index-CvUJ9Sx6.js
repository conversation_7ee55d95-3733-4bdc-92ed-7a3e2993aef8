import{r as g,j as e,Q as y,S as b}from"./app-J5EqS6dS.js";import{C as a,a as r,b as l,c as i,d as u}from"./card-9XCADs-4.js";import{B as _}from"./smartphone-GGiwNneF.js";import{S as w,a as S,b as C,c as A,d as n}from"./select-CIhY0l9J.js";import{T,a as D,b as d,c as x}from"./tabs-DZAL-HvD.js";import{A as U,D as L}from"./app-layout-ox1kAwY6.js";import{D as k}from"./download-fvx_BKiV.js";import{U as E}from"./users-RYmOyic9.js";import{A as P,C as R}from"./ImpersonationBanner-CYn5eDk6.js";import{C as $}from"./clock-Brl7_5s7.js";import{S as p}from"./search-DBK6jUoc.js";import{C as q}from"./calendar-B-u_QN2Q.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./badge-BucYuCBs.js";import"./eye-D-fsmYB2.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";function pe({analytics:t,days:m}){const[o,f]=g.useState(m.toString()),v=s=>{f(s),b.get(route("admin.analytics.index"),{days:s})},N=s=>{window.open(`${route("admin.analytics.export")}?type=${s}&days=${o}`,"_blank")},h=s=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(s),j=s=>`${s}%`;return e.jsxs(U,{children:[e.jsx(y,{title:"Analytics Dashboard"}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:"Analytics Dashboard"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Comprehensive insights into user engagement and system performance"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(w,{value:o,onValueChange:v,children:[e.jsx(S,{className:"w-32",children:e.jsx(C,{})}),e.jsxs(A,{children:[e.jsx(n,{value:"7",children:"Last 7 days"}),e.jsx(n,{value:"30",children:"Last 30 days"}),e.jsx(n,{value:"90",children:"Last 90 days"}),e.jsx(n,{value:"365",children:"Last year"})]})]}),e.jsxs(_,{onClick:()=>N("engagement"),variant:"outline",children:[e.jsx(k,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:[e.jsxs(a,{children:[e.jsxs(r,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(l,{className:"text-sm font-medium",children:"Total Users"}),e.jsx(E,{className:"h-4 w-4 text-blue-600"})]}),e.jsxs(i,{children:[e.jsx("div",{className:"text-2xl font-bold",children:t.overview.total_users}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:[t.overview.new_users," new in last ",m," days"]})]})]}),e.jsxs(a,{children:[e.jsxs(r,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(l,{className:"text-sm font-medium",children:"Active Users"}),e.jsx(P,{className:"h-4 w-4 text-green-600"})]}),e.jsxs(i,{children:[e.jsx("div",{className:"text-2xl font-bold",children:t.overview.active_users}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:[j(t.overview.user_retention_rate)," retention rate"]})]})]}),e.jsxs(a,{children:[e.jsxs(r,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(l,{className:"text-sm font-medium",children:"Total Sessions"}),e.jsx($,{className:"h-4 w-4 text-purple-600"})]}),e.jsxs(i,{children:[e.jsx("div",{className:"text-2xl font-bold",children:t.overview.total_sessions}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:[t.overview.avg_sessions_per_user," avg per user"]})]})]}),e.jsxs(a,{children:[e.jsxs(r,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(l,{className:"text-sm font-medium",children:"Total Searches"}),e.jsx(p,{className:"h-4 w-4 text-orange-600"})]}),e.jsxs(i,{children:[e.jsx("div",{className:"text-2xl font-bold",children:t.search_analytics.total_searches}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:[j(t.search_analytics.search_success_rate)," success rate"]})]})]}),e.jsxs(a,{children:[e.jsxs(r,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(l,{className:"text-sm font-medium",children:"Revenue"}),e.jsx(L,{className:"h-4 w-4 text-green-600"})]}),e.jsxs(i,{children:[e.jsx("div",{className:"text-2xl font-bold",children:h(t.subscription_analytics.total_revenue)}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:[t.subscription_analytics.subscription_conversions," conversions"]})]})]}),e.jsxs(a,{children:[e.jsxs(r,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(l,{className:"text-sm font-medium",children:"Pending Payments"}),e.jsx(q,{className:"h-4 w-4 text-yellow-600"})]}),e.jsxs(i,{children:[e.jsx("div",{className:"text-2xl font-bold",children:t.subscription_analytics.pending_payments}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Awaiting approval"})]})]})]}),e.jsxs(T,{defaultValue:"features",className:"space-y-4",children:[e.jsxs(D,{className:"grid w-full grid-cols-4",children:[e.jsx(d,{value:"features",children:"Feature Usage"}),e.jsx(d,{value:"users",children:"User Segments"}),e.jsx(d,{value:"searches",children:"Search Analytics"}),e.jsx(d,{value:"revenue",children:"Revenue Analytics"})]}),e.jsx(x,{value:"features",className:"space-y-4",children:e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs(a,{children:[e.jsx(r,{children:e.jsxs(l,{className:"flex items-center gap-2",children:[e.jsx(R,{className:"h-5 w-5"}),"Feature Usage"]})}),e.jsx(i,{children:e.jsx("div",{className:"space-y-3",children:t.feature_usage.feature_usage.slice(0,8).map((s,c)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium",children:s.activity_type.replace("_"," ").toUpperCase()}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-sm font-bold",children:s.usage_count}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:[s.unique_users," users"]})]})]},c))})})]}),e.jsxs(a,{children:[e.jsx(r,{children:e.jsxs(l,{className:"flex items-center gap-2",children:[e.jsx(p,{className:"h-5 w-5"}),"Search Types"]})}),e.jsx(i,{children:e.jsx("div",{className:"space-y-3",children:t.feature_usage.search_types.slice(0,6).map((s,c)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium",children:s.search_type||"All"}),e.jsx("span",{className:"text-sm font-bold",children:s.usage_count})]},c))})})]})]})}),e.jsx(x,{value:"users",className:"space-y-4",children:e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs(a,{children:[e.jsx(r,{children:e.jsx(l,{children:"Subscription Segments"})}),e.jsx(i,{children:e.jsx("div",{className:"space-y-3",children:t.user_segments.subscription_segments.map((s,c)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium capitalize",children:s.subscription_plan}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-sm font-bold",children:s.user_count}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:[s.active_users," active"]})]})]},c))})})]}),e.jsxs(a,{children:[e.jsx(r,{children:e.jsx(l,{children:"Activity Segments"})}),e.jsx(i,{children:e.jsx("div",{className:"space-y-3",children:t.user_segments.activity_segments.map((s,c)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium",children:s.activity_level.replace("_"," ").toUpperCase()}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-sm font-bold",children:s.user_count}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:[Math.round(s.avg_logins)," avg logins"]})]})]},c))})})]})]})}),e.jsx(x,{value:"searches",className:"space-y-4",children:e.jsxs(a,{children:[e.jsxs(r,{children:[e.jsx(l,{children:"Popular Search Terms"}),e.jsxs(u,{children:["Most frequently searched terms in the last ",m," days"]})]}),e.jsx(i,{children:e.jsx("div",{className:"space-y-3",children:t.search_analytics.popular_searches.slice(0,10).map((s,c)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium",children:s.search_query||"Empty search"}),e.jsx("span",{className:"text-sm font-bold",children:s.search_count})]},c))})})]})}),e.jsx(x,{value:"revenue",className:"space-y-4",children:e.jsxs(a,{children:[e.jsxs(r,{children:[e.jsx(l,{children:"Revenue by Plan"}),e.jsx(u,{children:"Revenue breakdown by subscription plan"})]}),e.jsx(i,{children:e.jsx("div",{className:"space-y-3",children:t.subscription_analytics.revenue_by_plan.map((s,c)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium capitalize",children:s.subscription_plan}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-sm font-bold",children:h(s.total_revenue)}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:[s.payment_count," payments"]})]})]},c))})})]})})]})]})})]})}export{pe as default};
