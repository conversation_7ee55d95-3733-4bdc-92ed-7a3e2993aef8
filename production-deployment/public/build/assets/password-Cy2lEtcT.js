import{r as w,x as j,j as s,Q as v}from"./app-J5EqS6dS.js";import{I as p}from"./input-error-SDo-ayIc.js";import{A as _}from"./app-layout-ox1kAwY6.js";import{S as y,H as N}from"./layout-CBB3cBA6.js";import{B as C}from"./smartphone-GGiwNneF.js";import{I as i}from"./input-Bo8dOn9p.js";import{L as n}from"./label-BlOrdc-X.js";import{z as S}from"./transition-D9Ew__mD.js";/* empty css            */import"./ImpersonationBanner-CYn5eDk6.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./badge-BucYuCBs.js";import"./crown-UDSxMtlm.js";import"./shield-D9nQfigG.js";import"./user-DCnDRzMf.js";import"./search-DBK6jUoc.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./eye-D-fsmYB2.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";const b=[{title:"Password settings",href:"/settings/password"}];function rs(){const d=w.useRef(null),m=w.useRef(null),{data:o,setData:e,errors:a,put:f,reset:t,processing:x,recentlySuccessful:h}=j({current_password:"",password:"",password_confirmation:""}),g=r=>{r.preventDefault(),f(route("password.update"),{preserveScroll:!0,onSuccess:()=>t(),onError:c=>{var l,u;c.password&&(t("password","password_confirmation"),(l=d.current)==null||l.focus()),c.current_password&&(t("current_password"),(u=m.current)==null||u.focus())}})};return s.jsxs(_,{breadcrumbs:b,children:[s.jsx(v,{title:"Password settings"}),s.jsx(y,{children:s.jsxs("div",{className:"space-y-6",children:[s.jsx(N,{title:"Update password",description:"Ensure your account is using a long, random password to stay secure"}),s.jsxs("form",{onSubmit:g,className:"space-y-6",children:[s.jsxs("div",{className:"grid gap-2",children:[s.jsx(n,{htmlFor:"current_password",children:"Current password"}),s.jsx(i,{id:"current_password",ref:m,value:o.current_password,onChange:r=>e("current_password",r.target.value),type:"password",className:"mt-1 block w-full",autoComplete:"current-password",placeholder:"Current password"}),s.jsx(p,{message:a.current_password})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(n,{htmlFor:"password",children:"New password"}),s.jsx(i,{id:"password",ref:d,value:o.password,onChange:r=>e("password",r.target.value),type:"password",className:"mt-1 block w-full",autoComplete:"new-password",placeholder:"New password"}),s.jsx(p,{message:a.password})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(n,{htmlFor:"password_confirmation",children:"Confirm password"}),s.jsx(i,{id:"password_confirmation",value:o.password_confirmation,onChange:r=>e("password_confirmation",r.target.value),type:"password",className:"mt-1 block w-full",autoComplete:"new-password",placeholder:"Confirm password"}),s.jsx(p,{message:a.password_confirmation})]}),s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx(C,{disabled:x,children:"Save password"}),s.jsx(S,{show:h,enter:"transition ease-in-out",enterFrom:"opacity-0",leave:"transition ease-in-out",leaveTo:"opacity-0",children:s.jsx("p",{className:"text-sm text-neutral-600",children:"Saved"})})]})]})]})})]})}export{rs as default};
