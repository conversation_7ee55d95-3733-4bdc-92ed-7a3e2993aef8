import{j as s}from"./app-J5EqS6dS.js";import{B as a}from"./smartphone-GGiwNneF.js";import{C as h}from"./chevron-left-C6ZNA5qQ.js";import{a as j}from"./ImpersonationBanner-CYn5eDk6.js";function C({currentPage:i,lastPage:o,from:c,to:d,total:x,onPageChange:l}){const f=(()=>{const n=[],t=[];n.push(1);for(let e=Math.max(2,i-2);e<=Math.min(o-1,i+2);e++)n.push(e);o>1&&n.push(o);const p=[...new Set(n)].sort((e,u)=>e-u);let m=0;for(const e of p)e-m>1&&t.push("..."),t.push(e),m=e;return t})();return o<=1?null:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"text-sm text-muted-foreground",children:["Showing ",c," to ",d," of ",x," results"]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsxs(a,{variant:"outline",size:"sm",onClick:()=>l(i-1),disabled:i<=1,className:"flex items-center gap-1",children:[s.jsx(h,{className:"h-4 w-4"}),"Previous"]}),s.jsx("div",{className:"flex items-center gap-1",children:f.map((r,n)=>{if(r==="...")return s.jsx("span",{className:"px-2 py-1 text-muted-foreground",children:"..."},`dots-${n}`);const t=r;return s.jsx(a,{variant:i===t?"default":"outline",size:"sm",onClick:()=>l(t),className:"w-8 h-8 p-0",children:t},t)})}),s.jsxs(a,{variant:"outline",size:"sm",onClick:()=>l(i+1),disabled:i>=o,className:"flex items-center gap-1",children:["Next",s.jsx(j,{className:"h-4 w-4"})]})]})]})}export{C as P};
