import{r as o,j as e,Q as Ve,t as h,S as g}from"./app-J5EqS6dS.js";import{C as D,a as de,b as me,c as z,d as Me}from"./card-9XCADs-4.js";import{B as S}from"./badge-BucYuCBs.js";import{B as t}from"./smartphone-GGiwNneF.js";import{L}from"./label-BlOrdc-X.js";import{S as V,a as M,b as R,c as O,d as N}from"./select-CIhY0l9J.js";import{S as Re}from"./switch-yFNfZ5X-.js";import{U as Oe}from"./unified-search-interface-CjLSucUK.js";import{A as $e,G as Ue}from"./app-layout-ox1kAwY6.js";import{u as Be}from"./use-delete-confirmation-CFAJok5Z.js";import{U as Ge,P as he,X as ue,a as Ke,t as i}from"./ImpersonationBanner-CYn5eDk6.js";import{F as Xe}from"./file-text-Dx6bYLtE.js";import{D as xe}from"./download-fvx_BKiV.js";import{S as He}from"./search-DBK6jUoc.js";import{F as pe}from"./filter-DKJvAZFg.js";import{P as ge}from"./package-CoyvngX8.js";import{L as Qe}from"./list-CNjrM85i.js";import{T as We}from"./table-gSl3ppmW.js";import{E as H}from"./external-link-A4n9PP4e.js";import{S as Q}from"./users-RYmOyic9.js";import{E as W}from"./eye-D-fsmYB2.js";import{S as Y}from"./square-pen-Bepbg6wc.js";import{T as J}from"./trash-2-B3ZEh4hl.js";import{C as Ye}from"./chevron-left-C6ZNA5qQ.js";import{b as Je,A as Ze,a as qe}from"./arrow-up-DSYswbzJ.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./input-Bo8dOn9p.js";import"./category-utils-DblfPn34.js";import"./map-pin-BdPUntxP.js";import"./hard-drive-BTn_ba7c.js";import"./circle-ButWjt_D.js";import"./zap-BcmHRR4K.js";import"./building-Dgyml3QN.js";import"./tag-C9-9psxB.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./mail-CDon-vZy.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./crown-UDSxMtlm.js";function Qs({parts:l,filters:P,queryParams:y}){var ne;const{showDeleteConfirmation:fe}=Be(),[p,$]=o.useState(y.search||""),[c,Z]=o.useState(y.category_id||"all"),[d,q]=o.useState(y.manufacturer||"all"),[m,ee]=o.useState(y.status||"all"),[u,se]=o.useState(y.sort_by||"name"),[j,te]=o.useState(y.sort_order||"asc"),[ae,je]=o.useState(!1),[U,f]=o.useState(!1),[x,le]=o.useState(y.view||"table"),[w,E]=o.useState([]),[F,I]=o.useState(!1),[re,B]=o.useState(!1),[C,ve]=o.useState("skip"),[v,T]=o.useState(null),b=o.useRef(null),ie=p||c!=="all"||d!=="all"||m!=="all",Ne=async s=>{var a;try{const r=await fetch(`/admin/parts/${s.id}/toggle-status`,{method:"PATCH",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":((a=document.querySelector('meta[name="csrf-token"]'))==null?void 0:a.getAttribute("content"))||""}});if(r.ok){const n=await r.json();i.success(n.message),g.reload()}else i.error("Failed to update part status")}catch{i.error("An error occurred while updating part status")}},G=s=>{const a={page:s,...p&&{search:p},...c!=="all"&&{category_id:c},...d!=="all"&&{manufacturer:d},...m!=="all"&&{status:m},...u!=="name"&&{sort_by:u},...j!=="asc"&&{sort_order:j},...x!=="table"&&{view:x}};g.get("/admin/parts",a,{preserveState:!0,preserveScroll:!0})},be=()=>{f(!0);const s={...p&&{search:p},...c!=="all"&&{category_id:c},...d!=="all"&&{manufacturer:d},...m!=="all"&&{status:m},...u!=="name"&&{sort_by:u},...j!=="asc"&&{sort_order:j},...x!=="table"&&{view:x}};g.visit("/admin/parts",{data:s,onStart:()=>f(!0),onFinish:()=>f(!1),onError:a=>{console.error("Admin parts search error:",a),f(!1),i.error("Search failed. Please try again.")},onCancel:()=>f(!1),preserveState:!0,preserveScroll:!1})},we=(s,a,r)=>{if(!s.trim())return;f(!0),$(s);const n={search:s,...c!=="all"&&{category_id:c},...d!=="all"&&{manufacturer:d},...m!=="all"&&{status:m},...u!=="name"&&{sort_by:u},...j!=="asc"&&{sort_order:j},...x!=="table"&&{view:x}};g.visit("/admin/parts",{data:n,onStart:()=>f(!0),onFinish:()=>f(!1),onError:k=>{console.error("Admin parts search error:",k),f(!1),i.error("Search failed. Please try again.")},onCancel:()=>f(!1),preserveState:!0,preserveScroll:!1})},Se=()=>e.jsx(Oe,{searchQuery:p,setSearchQuery:$,isAuthenticated:!0,isLoading:U,setIsLoading:f,showFilters:!1,showSuggestions:!1,size:"sm",placeholder:"Search parts by name, part number, manufacturer, or category...",filters:{categories:P.categories,manufacturers:P.manufacturers},onCustomSearch:we}),ye=()=>{$(""),Z("all"),q("all"),ee("all"),se("name"),te("asc"),le("table"),g.get("/admin/parts",{},{preserveState:!0,preserveScroll:!1})},_=s=>{const a=u===s&&j==="asc"?"desc":"asc";se(s),te(a);const r={...p&&{search:p},...c!=="all"&&{category_id:c},...d!=="all"&&{manufacturer:d},...m!=="all"&&{status:m},sort_by:s,sort_order:a,...x!=="table"&&{view:x}};g.get("/admin/parts",r,{preserveState:!0,preserveScroll:!0})},A=s=>u!==s?e.jsx(Je,{className:"h-4 w-4"}):j==="asc"?e.jsx(Ze,{className:"h-4 w-4"}):e.jsx(qe,{className:"h-4 w-4"}),K=s=>{le(s);const a={...p&&{search:p},...c!=="all"&&{category_id:c},...d!=="all"&&{manufacturer:d},...m!=="all"&&{status:m},...u!=="name"&&{sort_by:u},...j!=="asc"&&{sort_order:j},...s!=="table"&&{view:s}};g.get("/admin/parts",a,{preserveState:!0,preserveScroll:!0})},X=s=>{const a=s.models_count&&s.models_count>0?`This part is compatible with ${s.models_count} model(s). This action cannot be undone.`:"This action cannot be undone.";fe({title:`Delete "${s.name}"?`,description:a,onConfirm:()=>{g.delete(`/admin/parts/${s.id}`,{onSuccess:()=>{i.success(`Part "${s.name}" has been deleted successfully.`)},onError:r=>{const n=r.message||"Failed to delete part. Please try again.";i.error(n)}})},onCancel:()=>{i.info("Delete cancelled")}})},Ce=()=>{const s=new URLSearchParams;p&&s.append("search",p),c!=="all"&&s.append("category_id",c),d!=="all"&&s.append("manufacturer",d),m!=="all"&&s.append("status",m),window.location.href=`/admin/parts/export?${s.toString()}`,i.success("Export started. Your download will begin shortly.")},_e=()=>{if(w.length===0){i.error("Please select parts to export");return}const s=new URLSearchParams;w.forEach(a=>s.append("ids[]",a.toString())),window.location.href=`/admin/parts/export?${s.toString()}`,i.success("Export started. Your download will begin shortly.")},Ae=()=>{var s;console.log("🔵 handleImport called"),console.log("Testing router object:",g),console.log("Router methods:",Object.keys(g)),console.log("fileInputRef.current:",b.current),(s=b.current)==null||s.click(),console.log("File input click triggered")},ke=s=>{var r;console.log("🟡 handleFileChange called"),console.log("Event:",s),console.log("Files:",s.target.files);const a=(r=s.target.files)==null?void 0:r[0];if(console.log("Selected file:",a),!a){console.log("No file selected, returning");return}if(!a.name.toLowerCase().endsWith(".csv")){console.log("Invalid file type:",a.name),i.error("Please select a CSV file");return}console.log("Setting selected file and showing dialog"),T(a),B(!0),console.log("Dialog should be visible now")},Te=()=>{var s;if(console.log("=== IMPORT DEBUG START ==="),console.log("selectedFile:",v),console.log("duplicateAction:",C),!v){console.log("No file selected, returning early");return}console.log("Setting importing state..."),I(!0),B(!1);try{console.log("Creating FormData...");const a=new FormData;a.append("file",v),a.append("duplicate_action",C);const r=(s=document.querySelector('meta[name="csrf-token"]'))==null?void 0:s.getAttribute("content");r&&a.append("_token",r),console.log("FormData created successfully"),console.log("File name:",v.name),console.log("File size:",v.size),console.log("File type:",v.type),console.log("About to call router.post..."),g.post("/admin/bulk-import/parts",a,{forceFormData:!0,onStart:()=>{console.log("🚀 Request started - this should appear if router.post is called")},onProgress:n=>{console.log("📊 Upload progress:",n)},onSuccess:n=>{var oe,ce;console.log("✅ Success callback called"),console.log("Page received:",n);const k=(ce=(oe=n.props)==null?void 0:oe.flash)==null?void 0:ce.import_errors;k&&k.length>0&&(console.log("Import errors found:",k),k.forEach((ze,Le)=>{setTimeout(()=>{i.error(ze,{duration:8e3})},Le*100)})),I(!1),T(null),b.current&&(b.current.value="")},onError:n=>{console.log("❌ Error callback called"),console.error("Import errors:",n),n.file?i.error(`File error: ${n.file}`):n.message?i.error(n.message):i.error("Import failed. Please check your CSV format and try again."),I(!1),T(null),b.current&&(b.current.value="")},onFinish:()=>{console.log("🏁 Request finished")}}),console.log("router.post call completed")}catch(a){console.error("❌ Exception in handleImportConfirm:",a),i.error("An unexpected error occurred. Please try again."),I(!1),T(null)}console.log("=== IMPORT DEBUG END ===")},Pe=()=>{window.location.href="/admin/parts/template/download",i.success("Template download started")},Ee=s=>{E(a=>a.includes(s)?a.filter(r=>r!==s):[...a,s])},Fe=()=>{w.length===l.data.length?E([]):E(l.data.map(s=>s.id))},Ie=({part:s})=>e.jsx(D,{className:"hover:shadow-lg transition-shadow",children:e.jsx(z,{className:"p-4",children:e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"flex items-start justify-between",children:e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-semibold text-lg text-gray-900 mb-1 line-clamp-1",children:s.name}),s.part_number&&e.jsxs("p",{className:"text-sm text-muted-foreground mb-1",children:["Part #: ",s.part_number]}),s.manufacturer&&e.jsxs("p",{className:"text-sm text-muted-foreground mb-2",children:["by ",s.manufacturer]})]})}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsx(S,{variant:s.is_active?"default":"secondary",children:s.is_active?"Active":"Inactive"}),e.jsx(S,{variant:"outline",children:s.category.name}),s.models_count!==void 0&&e.jsxs(S,{variant:"outline",children:[s.models_count," models"]})]}),s.description&&e.jsx("p",{className:"text-sm text-muted-foreground line-clamp-2",children:s.description}),e.jsxs("div",{className:"flex items-center gap-1 pt-2",children:[e.jsx(h,{href:route("parts.show",s.slug||s.id),children:e.jsx(t,{variant:"outline",size:"sm",title:"View Public Page",children:e.jsx(H,{className:"h-3 w-3"})})}),e.jsx(h,{href:`/admin/parts/${s.id}/compatibility`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Manage Compatibility",children:e.jsx(Q,{className:"h-3 w-3"})})}),e.jsx(h,{href:`/admin/parts/${s.id}`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Admin View",children:e.jsx(W,{className:"h-3 w-3"})})}),e.jsx(h,{href:`/admin/parts/${s.id}/edit`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Edit",children:e.jsx(Y,{className:"h-3 w-3"})})}),e.jsx(t,{variant:"outline",size:"sm",className:"text-destructive hover:text-destructive",onClick:()=>X(s),title:"Delete Part",children:e.jsx(J,{className:"h-3 w-3"})})]})]})})}),De=()=>e.jsx("div",{className:"border rounded-lg overflow-hidden",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-muted/50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"text-left p-3 font-medium w-12",children:e.jsx("input",{type:"checkbox",checked:w.length===l.data.length&&l.data.length>0,onChange:Fe,className:"rounded border-gray-300"})}),e.jsx("th",{className:"text-left p-3 font-medium",children:e.jsxs(t,{variant:"ghost",size:"sm",onClick:()=>_("name"),className:"flex items-center gap-1 h-auto p-0 font-medium",children:["Name ",A("name")]})}),e.jsx("th",{className:"text-left p-3 font-medium",children:"Part Number"}),e.jsx("th",{className:"text-left p-3 font-medium",children:"Manufacturer"}),e.jsx("th",{className:"text-left p-3 font-medium",children:"Category"}),e.jsx("th",{className:"text-left p-3 font-medium",children:e.jsxs(t,{variant:"ghost",size:"sm",onClick:()=>_("models_count"),className:"flex items-center gap-1 h-auto p-0 font-medium",children:["Models ",A("models_count")]})}),e.jsx("th",{className:"text-left p-3 font-medium",children:"Status"}),e.jsx("th",{className:"text-left p-3 font-medium",children:e.jsxs(t,{variant:"ghost",size:"sm",onClick:()=>_("created_at"),className:"flex items-center gap-1 h-auto p-0 font-medium",children:["Created ",A("created_at")]})}),e.jsx("th",{className:"text-right p-3 font-medium",children:"Actions"})]})}),e.jsx("tbody",{children:l.data.map(s=>e.jsxs("tr",{className:"border-t hover:bg-muted/25",children:[e.jsx("td",{className:"p-3",children:e.jsx("input",{type:"checkbox",checked:w.includes(s.id),onChange:()=>Ee(s.id),className:"rounded border-gray-300"})}),e.jsx("td",{className:"p-3",children:e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:s.name}),s.description&&e.jsx("div",{className:"text-sm text-muted-foreground line-clamp-1",children:s.description})]})}),e.jsx("td",{className:"p-3 text-sm text-muted-foreground",children:s.part_number||"-"}),e.jsx("td",{className:"p-3 text-sm text-muted-foreground",children:s.manufacturer||"-"}),e.jsx("td",{className:"p-3",children:e.jsx(S,{variant:"outline",children:s.category.name})}),e.jsx("td",{className:"p-3",children:e.jsx(S,{variant:"outline",children:s.models_count||0})}),e.jsx("td",{className:"p-3",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Re,{checked:s.is_active,onCheckedChange:()=>Ne(s)}),e.jsx("span",{className:"text-sm text-muted-foreground",children:s.is_active?"Active":"Inactive"})]})}),e.jsx("td",{className:"p-3 text-sm text-muted-foreground",children:new Date(s.created_at).toLocaleDateString()}),e.jsx("td",{className:"p-3",children:e.jsxs("div",{className:"flex items-center gap-1 justify-end",children:[e.jsx(h,{href:route("parts.show",s.slug||s.id),children:e.jsx(t,{variant:"outline",size:"sm",title:"View Public Page",children:e.jsx(H,{className:"h-3 w-3"})})}),e.jsx(h,{href:`/admin/parts/${s.id}/compatibility`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Manage Compatibility",children:e.jsx(Q,{className:"h-3 w-3"})})}),e.jsx(h,{href:`/admin/parts/${s.id}`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Admin View",children:e.jsx(W,{className:"h-3 w-3"})})}),e.jsx(h,{href:`/admin/parts/${s.id}/edit`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Edit",children:e.jsx(Y,{className:"h-3 w-3"})})}),e.jsx(t,{variant:"outline",size:"sm",className:"text-destructive hover:text-destructive",onClick:()=>X(s),title:"Delete Part",children:e.jsx(J,{className:"h-3 w-3"})})]})})]},s.id))})]})})});return e.jsxs($e,{children:[e.jsx(Ve,{title:"Parts - Admin"}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Parts"}),e.jsx("p",{className:"text-muted-foreground",children:"Manage mobile device parts"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(t,{variant:"outline",className:"rounded-full border-border bg-transparent hover:bg-accent hover:text-accent-foreground px-6 py-2 h-auto transition-colors",onClick:()=>{console.log("📄 TEMPLATE DOWNLOAD BUTTON CLICKED"),Pe()},title:"Download CSV Template",children:[e.jsx(Xe,{className:"h-4 w-4 mr-2"}),"Template"]}),e.jsxs(t,{variant:"outline",className:"rounded-full border-border bg-transparent hover:bg-accent hover:text-accent-foreground px-6 py-2 h-auto transition-colors disabled:opacity-50",onClick:()=>{console.log("📤 IMPORT BUTTON CLICKED"),Ae()},disabled:F,title:"Import Parts from CSV",children:[e.jsx(Ge,{className:"h-4 w-4 mr-2"}),"Import"]}),e.jsxs(t,{variant:"outline",className:"rounded-full border-border bg-transparent hover:bg-accent hover:text-accent-foreground px-6 py-2 h-auto transition-colors",onClick:()=>{console.log("📥 EXPORT ALL BUTTON CLICKED"),Ce()},title:"Export All Parts to CSV",children:[e.jsx(xe,{className:"h-4 w-4 mr-2"}),"Export All"]})]}),e.jsx(h,{href:"/admin/parts/create",children:e.jsxs(t,{children:[e.jsx(he,{className:"h-4 w-4 mr-2"}),"Add Part"]})})]})]}),e.jsx("input",{ref:b,type:"file",accept:".csv",onChange:ke,className:"hidden"}),e.jsxs(D,{children:[e.jsx(de,{children:e.jsxs(me,{className:"flex items-center gap-2",children:[e.jsx(He,{className:"h-5 w-5"}),"Search & Filter"]})}),e.jsx(z,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx(Se,{}),e.jsxs("div",{className:"flex gap-3",children:[e.jsxs(t,{variant:"outline",onClick:()=>je(!ae),className:"flex items-center gap-2",children:[e.jsx(pe,{className:"h-4 w-4"}),"Advanced Filters"]}),ie&&e.jsxs(t,{variant:"outline",onClick:ye,children:[e.jsx(ue,{className:"h-4 w-4 mr-2"}),"Clear All"]})]})]}),ae&&e.jsx("div",{className:"border-t pt-4 mt-4",children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-4 bg-gradient-to-r from-muted/30 to-muted/50 rounded-lg border border-border/50",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(L,{htmlFor:"category-filter",className:"text-sm font-medium text-foreground/80",children:"Category"}),e.jsxs(V,{value:c,onValueChange:Z,children:[e.jsx(M,{id:"category-filter",className:"h-9 text-sm",children:e.jsx(R,{placeholder:"All Categories"})}),e.jsxs(O,{children:[e.jsx(N,{value:"all",children:"All Categories"}),P.categories.map(s=>e.jsx(N,{value:s.id.toString(),children:s.name},s.id))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(L,{htmlFor:"manufacturer-filter",className:"text-sm font-medium text-foreground/80",children:"Manufacturer"}),e.jsxs(V,{value:d,onValueChange:q,children:[e.jsx(M,{id:"manufacturer-filter",className:"h-9 text-sm",children:e.jsx(R,{placeholder:"All Manufacturers"})}),e.jsxs(O,{children:[e.jsx(N,{value:"all",children:"All Manufacturers"}),(ne=P.manufacturers)==null?void 0:ne.filter(s=>s&&s.trim()!=="").map(s=>e.jsx(N,{value:s,children:s},s))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(L,{htmlFor:"status-filter",className:"text-sm font-medium text-foreground/80",children:"Status"}),e.jsxs(V,{value:m,onValueChange:ee,children:[e.jsx(M,{id:"status-filter",className:"h-9 text-sm",children:e.jsx(R,{placeholder:"All Status"})}),e.jsxs(O,{children:[e.jsx(N,{value:"all",children:"All Status"}),e.jsx(N,{value:"active",children:"Active"}),e.jsx(N,{value:"inactive",children:"Inactive"})]})]})]}),e.jsx("div",{className:"space-y-2 flex flex-col justify-end",children:e.jsx(t,{onClick:be,className:"h-9 text-sm bg-primary hover:bg-primary/90 transition-colors",disabled:U,children:U?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"}),"Applying..."]}):e.jsxs(e.Fragment,{children:[e.jsx(pe,{className:"h-3 w-3 mr-2"}),"Apply Filters"]})})})]})})]})})]}),w.length>0&&e.jsx(D,{className:"border-blue-200 bg-blue-50",children:e.jsx(z,{className:"py-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs("span",{className:"text-sm font-medium",children:[w.length," part",w.length!==1?"s":""," selected"]})}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(t,{variant:"outline",className:"rounded-full border-primary bg-transparent text-primary hover:bg-primary hover:text-primary-foreground px-4 py-2 h-auto transition-colors",onClick:()=>{console.log("📥 EXPORT SELECTED BUTTON CLICKED"),_e()},children:[e.jsx(xe,{className:"h-4 w-4 mr-2"}),"Export Selected"]}),e.jsxs(t,{variant:"ghost",className:"rounded-full text-muted-foreground hover:bg-accent hover:text-accent-foreground px-4 py-2 h-auto transition-colors",onClick:()=>E([]),children:[e.jsx(ue,{className:"h-4 w-4 mr-2"}),"Clear Selection"]})]})]})})}),e.jsxs(D,{children:[e.jsx(de,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(me,{className:"flex items-center gap-2",children:[e.jsx(ge,{className:"h-5 w-5"}),"All Parts"]}),e.jsxs(Me,{children:[l.total," parts total",ie&&e.jsx("span",{className:"ml-2 text-primary",children:"(filtered)"})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"View:"}),e.jsx(t,{variant:x==="list"?"default":"outline",size:"sm",onClick:()=>K("list"),title:"List View",children:e.jsx(Qe,{className:"h-4 w-4"})}),e.jsx(t,{variant:x==="grid"?"default":"outline",size:"sm",onClick:()=>K("grid"),title:"Grid View",children:e.jsx(Ue,{className:"h-4 w-4"})}),e.jsx(t,{variant:x==="table"?"default":"outline",size:"sm",onClick:()=>K("table"),title:"Table View",children:e.jsx(We,{className:"h-4 w-4"})})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Sort by:"}),e.jsxs(t,{variant:u==="name"?"default":"outline",size:"sm",onClick:()=>_("name"),className:"flex items-center gap-1",children:["Name ",A("name")]}),e.jsxs(t,{variant:u==="created_at"?"default":"outline",size:"sm",onClick:()=>_("created_at"),className:"flex items-center gap-1",children:["Date ",A("created_at")]}),e.jsxs(t,{variant:u==="models_count"?"default":"outline",size:"sm",onClick:()=>_("models_count"),className:"flex items-center gap-1",children:["Models ",A("models_count")]})]})]})]})}),e.jsxs(z,{children:[l.data.length>0?e.jsx(e.Fragment,{children:x==="table"?e.jsx(De,{}):x==="grid"?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:l.data.map(s=>e.jsx(Ie,{part:s},s.id))}):e.jsx("div",{className:"space-y-4",children:l.data.map(s=>e.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"flex items-center gap-3",children:e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:s.name}),s.part_number&&e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Part #: ",s.part_number]}),s.manufacturer&&e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Manufacturer: ",s.manufacturer]})]})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(S,{variant:s.is_active?"default":"secondary",children:s.is_active?"Active":"Inactive"}),e.jsx(S,{variant:"outline",children:s.category.name}),s.models_count!==void 0&&e.jsxs(S,{variant:"outline",children:[s.models_count," compatible models"]})]}),s.description&&e.jsx("p",{className:"text-sm text-muted-foreground line-clamp-2",children:s.description})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(h,{href:route("parts.show",s.slug||s.id),children:e.jsx(t,{variant:"outline",size:"sm",title:"View Public Page",children:e.jsx(H,{className:"h-4 w-4"})})}),e.jsx(h,{href:`/admin/parts/${s.id}/compatibility`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Manage Compatibility",children:e.jsx(Q,{className:"h-4 w-4"})})}),e.jsx(h,{href:`/admin/parts/${s.id}`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Admin View",children:e.jsx(W,{className:"h-4 w-4"})})}),e.jsx(h,{href:`/admin/parts/${s.id}/edit`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Edit",children:e.jsx(Y,{className:"h-4 w-4"})})}),e.jsx(t,{variant:"outline",size:"sm",className:"text-destructive hover:text-destructive",onClick:()=>X(s),title:"Delete Part",children:e.jsx(J,{className:"h-4 w-4"})})]})]},s.id))})}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(ge,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No parts found"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:"Get started by adding your first part."}),e.jsx(h,{href:"/admin/parts/create",children:e.jsxs(t,{children:[e.jsx(he,{className:"h-4 w-4 mr-2"}),"Add Part"]})})]}),l.last_page>1&&e.jsxs("div",{className:"flex items-center justify-between pt-4 border-t",children:[e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Showing ",l.from," to ",l.to," of ",l.total," parts"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(t,{variant:"outline",size:"sm",onClick:()=>G(l.current_page-1),disabled:l.current_page===1,children:[e.jsx(Ye,{className:"w-4 h-4 mr-1"}),"Previous"]}),Array.from({length:Math.min(5,l.last_page)},(s,a)=>{let r;return l.last_page<=5||l.current_page<=3?r=a+1:l.current_page>=l.last_page-2?r=l.last_page-4+a:r=l.current_page-2+a,e.jsx(t,{variant:r===l.current_page?"default":"outline",size:"sm",onClick:()=>G(r),children:r},r)}),e.jsxs(t,{variant:"outline",size:"sm",onClick:()=>G(l.current_page+1),disabled:l.current_page===l.last_page,children:["Next",e.jsx(Ke,{className:"w-4 h-4 ml-1"})]})]})]})]})]})]})}),re&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md mx-4",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Import Options"}),e.jsxs("p",{className:"text-sm text-muted-foreground mb-4",children:["File: ",v==null?void 0:v.name]}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{children:[e.jsx(L,{htmlFor:"duplicate-action",children:"How should duplicate parts be handled?"}),e.jsxs(V,{value:C,onValueChange:s=>ve(s),children:[e.jsx(M,{id:"duplicate-action",className:"mt-2",children:e.jsx(R,{})}),e.jsxs(O,{children:[e.jsx(N,{value:"skip",children:"Skip duplicates (recommended)"}),e.jsx(N,{value:"update",children:"Update existing parts"}),e.jsx(N,{value:"error",children:"Report as errors"})]})]}),e.jsxs("p",{className:"text-xs text-muted-foreground mt-1",children:[C==="skip"&&"Duplicate parts will be ignored and not imported.",C==="update"&&"Existing parts will be updated with new data.",C==="error"&&"Import will report duplicates as errors."]})]})}),e.jsxs("div",{className:"flex justify-end gap-2 mt-6",children:[e.jsx(t,{variant:"outline",onClick:()=>{B(!1),T(null),b.current&&(b.current.value="")},children:"Cancel"}),e.jsx(t,{onClick:()=>{console.log("🟢 Import dialog button clicked"),console.log("isImporting:",F),console.log("selectedFile:",v),console.log("showImportDialog:",re),console.log("Testing simple router.get...");try{g.get("/admin/parts",{},{onStart:()=>console.log("Test router.get started"),onSuccess:()=>console.log("Test router.get success"),onError:()=>console.log("Test router.get error"),preserveState:!0,preserveScroll:!0,only:[]})}catch(s){console.error("Router.get test failed:",s)}console.log("About to call handleImportConfirm in 1 second..."),setTimeout(()=>{console.log("Timeout reached, calling handleImportConfirm now"),Te()},1e3)},disabled:F,children:F?"Importing...":"Import Parts"})]})]})})]})}export{Qs as default};
