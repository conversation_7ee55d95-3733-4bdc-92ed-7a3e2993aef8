import{j as e,Q as x,t as c,S as o}from"./app-J5EqS6dS.js";import{C as h,a as u,b as j,d as p,c as f}from"./card-9XCADs-4.js";import{B as r}from"./smartphone-GGiwNneF.js";import{B as l}from"./badge-BucYuCBs.js";import{A as g,M as N,C as y,c as w}from"./app-layout-ox1kAwY6.js";import{A as m}from"./arrow-left-D4U9AVF9.js";import{E as i}from"./eye-off-BGSyeByl.js";import{U as b}from"./user-DCnDRzMf.js";import{C as v}from"./calendar-B-u_QN2Q.js";import{E as C}from"./eye-D-fsmYB2.js";import{I as d,T as B}from"./triangle-alert-BW76NKO9.js";/* empty css            */import"./ImpersonationBanner-CYn5eDk6.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./crown-UDSxMtlm.js";import"./shield-D9nQfigG.js";import"./search-DBK6jUoc.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./globe-zfFlVOSX.js";const k=[{title:"Notifications",href:"/notifications"},{title:"View Notification",href:"#"}],S=s=>{switch(s){case"info":return e.jsx(d,{className:"w-8 h-8 text-blue-500"});case"warning":return e.jsx(B,{className:"w-8 h-8 text-yellow-500"});case"success":return e.jsx(w,{className:"w-8 h-8 text-green-500"});case"error":return e.jsx(y,{className:"w-8 h-8 text-red-500"});case"announcement":return e.jsx(N,{className:"w-8 h-8 text-purple-500"});default:return e.jsx(d,{className:"w-8 h-8 text-gray-500"})}},A=s=>{switch(s){case"info":return"bg-blue-100 text-blue-800";case"warning":return"bg-yellow-100 text-yellow-800";case"success":return"bg-green-100 text-green-800";case"error":return"bg-red-100 text-red-800";case"announcement":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},_=s=>{switch(s){case"info":return"Information";case"warning":return"Warning";case"success":return"Success";case"error":return"Error";case"announcement":return"Announcement";default:return"Notification"}};function te({notification:s}){const t=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),n=async()=>{try{await o.post(route("notifications.mark-unread",s.id))}catch(a){console.error("Failed to mark notification as unread:",a)}};return e.jsxs(g,{breadcrumbs:k,children:[e.jsx(x,{title:`Notification: ${s.title}`}),e.jsxs("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(c,{href:route("notifications.index"),children:e.jsxs(r,{variant:"outline",size:"sm",children:[e.jsx(m,{className:"w-4 h-4 mr-2"}),"Back to Notifications"]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Notification Details"}),e.jsx("p",{className:"text-gray-600",children:"View notification information"})]})]}),s.read_at&&e.jsxs(r,{onClick:n,variant:"outline",children:[e.jsx(i,{className:"w-4 h-4 mr-2"}),"Mark as Unread"]})]}),e.jsxs(h,{className:"max-w-4xl",children:[e.jsx(u,{children:e.jsx("div",{className:"flex items-start justify-between",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[S(s.type),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(j,{className:"text-xl",children:s.title}),e.jsx(l,{className:`${A(s.type)}`,children:_(s.type)}),!s.read_at&&e.jsx(l,{variant:"secondary",children:"New"})]}),e.jsxs(p,{children:["Received on ",t(s.created_at)]})]})]})})}),e.jsxs(f,{children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Message"}),e.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:e.jsx("p",{className:"text-gray-700 whitespace-pre-wrap leading-relaxed",children:s.message})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.sentBy&&e.jsxs("div",{children:[e.jsxs("h4",{className:"text-sm font-medium text-gray-900 mb-3 flex items-center",children:[e.jsx(b,{className:"w-4 h-4 mr-2"}),"Sent By"]}),e.jsxs("div",{className:"bg-white border rounded-lg p-3",children:[e.jsx("p",{className:"font-medium text-gray-900",children:s.sentBy.name}),e.jsx("p",{className:"text-sm text-gray-600",children:s.sentBy.email})]})]}),e.jsxs("div",{children:[e.jsxs("h4",{className:"text-sm font-medium text-gray-900 mb-3 flex items-center",children:[e.jsx(v,{className:"w-4 h-4 mr-2"}),"Timing"]}),e.jsxs("div",{className:"bg-white border rounded-lg p-3 space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Sent:"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:t(s.created_at)})]}),s.read_at&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Read:"}),e.jsxs("span",{className:"text-sm font-medium text-gray-900 flex items-center",children:[e.jsx(C,{className:"w-3 h-3 mr-1"}),t(s.read_at)]})]}),!s.read_at&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Status:"}),e.jsxs("span",{className:"text-sm font-medium text-orange-600 flex items-center",children:[e.jsx(i,{className:"w-3 h-3 mr-1"}),"Unread"]})]})]})]})]}),e.jsx("div",{className:"mt-6 pt-6 border-t",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(c,{href:route("notifications.index"),children:e.jsxs(r,{variant:"outline",children:[e.jsx(m,{className:"w-4 h-4 mr-2"}),"Back to All Notifications"]})}),s.read_at&&e.jsxs(r,{onClick:n,variant:"outline",children:[e.jsx(i,{className:"w-4 h-4 mr-2"}),"Mark as Unread"]})]})})]})]})]})]})}export{te as default};
