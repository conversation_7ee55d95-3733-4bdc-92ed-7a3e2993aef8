import{r as d,j as e,J as B,U as ve,t as y,S as F,u as ye}from"./app-J5EqS6dS.js";import{u as me,i as Ne,S as M,j as A,k as I,l as _,m as S,n as we,o as P,p as Ce,q as ke,r as ee,s as O,v as E,w as R,a as Se,x as Me,y as Ae,z as Ie,E as _e,C as G,W as Pe,F as $,A as H,K as Ee,B as q,M as Re,T as $e,U as De,I as Te,L as U,H as Oe,G as qe,J as ze,N as ue,O as Le,Q as Fe,P as se,R as Be,t as te,V as Ge,Y as He,Z as Ue,_ as Ve,$ as Ze}from"./ImpersonationBanner-CYn5eDk6.js";import{c as x,a as p,u as We,S as pe,B as v}from"./smartphone-GGiwNneF.js";import{u as Ke,c as Qe,a as Je,b as Xe}from"./index-D86BnqlV.js";import{P as z}from"./index-CJpBU2i9.js";import{P as Ye}from"./index-BzZWUWqx.js";import{a as es,U as ss,S as V}from"./users-RYmOyic9.js";import{S as Z}from"./shield-D9nQfigG.js";import{L as ts}from"./lock-Tx_yfI4R.js";import{F as W}from"./file-text-Dx6bYLtE.js";import{S as D}from"./search-DBK6jUoc.js";import{M as as}from"./mail-CDon-vZy.js";import{P as rs}from"./package-CoyvngX8.js";import{D as is}from"./database-s9JOA0jY.js";import{U as ae}from"./user-DCnDRzMf.js";import{B as k}from"./badge-BucYuCBs.js";import{E as ns}from"./eye-D-fsmYB2.js";import{T as os,I as cs,M as re}from"./triangle-alert-BW76NKO9.js";import{G as ls}from"./globe-zfFlVOSX.js";import{Z as ds}from"./zap-BcmHRR4K.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hs=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M17 17H4a1 1 0 0 1-.74-1.673C4.59 13.956 6 12.499 6 8a6 6 0 0 1 .258-1.742",key:"178tsu"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M8.668 3.01A6 6 0 0 1 18 8c0 2.687.77 4.653 1.707 6.05",key:"1hqiys"}]],ie=x("BellOff",hs);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ms=[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]],us=x("ChevronsUpDown",ms);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ps=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]],xs=x("CircleCheck",ps);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fs=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]],gs=x("CircleX",fs);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bs=[["path",{d:"M12 20a8 8 0 1 0 0-16 8 8 0 0 0 0 16Z",key:"sobvz5"}],["path",{d:"M12 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z",key:"11i496"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 22v-2",key:"1osdcq"}],["path",{d:"m17 20.66-1-1.73",key:"eq3orb"}],["path",{d:"M11 10.27 7 3.34",key:"16pf9h"}],["path",{d:"m20.66 17-1.73-1",key:"sg0v6f"}],["path",{d:"m3.34 7 1.73 1",key:"1ulond"}],["path",{d:"M14 12h8",key:"4f43i9"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"m20.66 7-1.73 1",key:"1ow05n"}],["path",{d:"m3.34 17 1.73-1",key:"nuk764"}],["path",{d:"m17 3.34-1 1.73",key:"2wel8s"}],["path",{d:"m11 13.73-4 6.93",key:"794ttg"}]],js=x("Cog",bs);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vs=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],ys=x("DollarSign",vs);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ns=[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]],ws=x("FolderOpen",Ns);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cs=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]],ks=x("Grid3x3",Cs);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ss=[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]],Ms=x("Megaphone",Ss);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const As=[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]],ne=x("Moon",As);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Is=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]],_s=x("ShieldCheck",Is);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ps=[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]],oe=x("Sun",Ps);var Es="Separator",ce="horizontal",Rs=["horizontal","vertical"],xe=d.forwardRef((s,t)=>{const{decorative:n,orientation:r=ce,...i}=s,l=$s(r)?r:ce,o=n?{role:"none"}:{"aria-orientation":l==="vertical"?l:void 0,role:"separator"};return e.jsx(z.div,{"data-orientation":l,...o,...i,ref:t})});xe.displayName=Es;function $s(s){return Rs.includes(s)}var Ds=xe;function le({className:s,orientation:t="horizontal",decorative:n=!0,...r}){return e.jsx(Ds,{"data-slot":"separator-root",decorative:n,orientation:t,className:p("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",s),...r})}function Ts({isAdmin:s=!1,isAdminView:t=!1,setIsAdminView:n}){const{auth:r}=B().props,{state:i}=me(),l=Ne();return e.jsx(M,{children:e.jsx(A,{children:e.jsxs(I,{children:[e.jsx(_,{asChild:!0,children:e.jsxs(S,{size:"lg",className:"group text-sidebar-accent-foreground data-[state=open]:bg-sidebar-accent",children:[e.jsx(we,{user:r.user}),e.jsx(us,{className:"ml-auto size-4"})]})}),e.jsx(P,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",align:"end",side:l?"bottom":i==="collapsed"?"left":"bottom",children:e.jsx(Ce,{user:r.user,isAdmin:s,isAdminView:t,setIsAdminView:n})})]})})})}var L="Collapsible",[Os,St]=Qe(L),[qs,K]=Os(L),fe=d.forwardRef((s,t)=>{const{__scopeCollapsible:n,open:r,defaultOpen:i,disabled:l,onOpenChange:h,...o}=s,[u,c]=Ke({prop:r,defaultProp:i??!1,onChange:h,caller:L});return e.jsx(qs,{scope:n,disabled:l,contentId:es(),open:u,onOpenToggle:d.useCallback(()=>c(N=>!N),[c]),children:e.jsx(z.div,{"data-state":J(u),"data-disabled":l?"":void 0,...o,ref:t})})});fe.displayName=L;var ge="CollapsibleTrigger",be=d.forwardRef((s,t)=>{const{__scopeCollapsible:n,...r}=s,i=K(ge,n);return e.jsx(z.button,{type:"button","aria-controls":i.contentId,"aria-expanded":i.open||!1,"data-state":J(i.open),"data-disabled":i.disabled?"":void 0,disabled:i.disabled,...r,ref:t,onClick:Je(s.onClick,i.onOpenToggle)})});be.displayName=ge;var Q="CollapsibleContent",je=d.forwardRef((s,t)=>{const{forceMount:n,...r}=s,i=K(Q,s.__scopeCollapsible);return e.jsx(Ye,{present:n||i.open,children:({present:l})=>e.jsx(zs,{...r,ref:t,present:l})})});je.displayName=Q;var zs=d.forwardRef((s,t)=>{const{__scopeCollapsible:n,present:r,children:i,...l}=s,h=K(Q,n),[o,u]=d.useState(r),c=d.useRef(null),N=We(t,c),a=d.useRef(0),m=a.current,g=d.useRef(0),f=g.current,C=h.open||o,X=d.useRef(C),T=d.useRef(void 0);return d.useEffect(()=>{const b=requestAnimationFrame(()=>X.current=!1);return()=>cancelAnimationFrame(b)},[]),Xe(()=>{const b=c.current;if(b){T.current=T.current||{transitionDuration:b.style.transitionDuration,animationName:b.style.animationName},b.style.transitionDuration="0s",b.style.animationName="none";const Y=b.getBoundingClientRect();a.current=Y.height,g.current=Y.width,X.current||(b.style.transitionDuration=T.current.transitionDuration,b.style.animationName=T.current.animationName),u(r)}},[h.open,r]),e.jsx(z.div,{"data-state":J(h.open),"data-disabled":h.disabled?"":void 0,id:h.contentId,hidden:!C,...l,ref:N,style:{"--radix-collapsible-content-height":m?`${m}px`:void 0,"--radix-collapsible-content-width":f?`${f}px`:void 0,...s.style},children:C&&i})});function J(s){return s?"open":"closed"}var Ls=fe;function Fs({...s}){return e.jsx(Ls,{"data-slot":"collapsible",...s})}function Bs({...s}){return e.jsx(be,{"data-slot":"collapsible-trigger",...s})}function Gs({...s}){return e.jsx(je,{"data-slot":"collapsible-content",...s})}function Hs(s){const{expandedGroupId:t,expandGroup:n}=ke();return{isExpanded:t===s,toggleExpanded:()=>{n(s)}}}const j=ve.memo(function({title:t,items:n,groupId:r,icon:i,className:l}){const h=B(),{isExpanded:o,toggleExpanded:u}=Hs(r),{state:c}=me();return c==="collapsed"&&i?e.jsx(ee,{className:p("px-1 py-1",l),children:e.jsx(M,{children:e.jsx(A,{children:e.jsxs(I,{children:[e.jsx(_,{asChild:!0,children:e.jsx(S,{isActive:n.some(a=>h.url.startsWith(a.href)),tooltip:{children:t},className:p("sidebar-smooth-transition group/item relative","hover:bg-sidebar-accent/80 hover:text-sidebar-accent-foreground","hover:scale-105 active:scale-95","data-[active=true]:bg-sidebar-primary data-[active=true]:text-sidebar-primary-foreground","data-[active=true]:shadow-md data-[active=true]:scale-105","focus:ring-2 focus:ring-sidebar-ring focus:ring-offset-2 focus:ring-offset-sidebar","rounded-lg border border-transparent hover:border-sidebar-border/20","w-8 h-8 p-0 justify-center","shadow-sm hover:shadow-md"),children:e.jsx(i,{className:p("h-4 w-4 sidebar-smooth-transition","group-hover/item:scale-110")})})}),e.jsxs(P,{side:"right",align:"start",className:"w-56",sideOffset:8,children:[e.jsxs(O,{className:"flex items-center gap-2",children:[e.jsx(i,{className:"h-4 w-4"}),t]}),e.jsx(E,{}),n.map(a=>e.jsx(R,{asChild:!0,children:e.jsxs(y,{href:a.href,prefetch:!0,className:p("flex items-center gap-2 w-full",h.url.startsWith(a.href)&&"bg-accent text-accent-foreground"),children:[a.icon&&e.jsx(a.icon,{className:"h-4 w-4"}),a.title]})},a.title))]})]})})})}):e.jsx(ee,{className:p("px-2 py-1 sidebar-group-container",l),children:e.jsxs(Fs,{open:o,onOpenChange:u,children:[e.jsx(Bs,{asChild:!0,children:e.jsxs("button",{className:p("flex w-full items-center justify-between py-3 px-3 text-sm font-semibold","text-sidebar-foreground/80 hover:text-sidebar-foreground","hover:bg-sidebar-accent/60 rounded-lg sidebar-smooth-transition","group focus:outline-none focus:ring-2 focus:ring-sidebar-ring focus:ring-offset-2","focus:ring-offset-sidebar border border-transparent hover:border-sidebar-border/30","shadow-sm hover:shadow-md","relative overflow-hidden",o&&"bg-sidebar-accent/40 text-sidebar-foreground border-sidebar-border/50 shadow-md"),"aria-expanded":o,"aria-controls":`nav-group-${r}`,children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-sidebar-primary/5 to-transparent opacity-0 group-hover:opacity-100 sidebar-smooth-transition"}),e.jsxs("div",{className:"flex items-center gap-2 relative z-10",children:[i&&e.jsx(i,{className:p("h-4 w-4 sidebar-smooth-transition","group-hover:scale-105",o&&"text-sidebar-primary")}),e.jsx("span",{className:"text-left tracking-wide",children:t})]}),e.jsx(Se,{className:p("h-4 w-4 sidebar-smooth-transition relative z-10","group-hover:scale-105",o&&"rotate-90 text-sidebar-primary")})]})}),e.jsx(Gs,{id:`nav-group-${r}`,className:"overflow-hidden data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down",children:e.jsx(M,{className:"mt-2 space-y-1 px-1",children:n.map(a=>e.jsx(A,{children:e.jsx(S,{asChild:!0,isActive:h.url.startsWith(a.href),tooltip:{children:a.title},className:p("sidebar-smooth-transition group/item relative","hover:bg-sidebar-accent/80 hover:text-sidebar-accent-foreground","hover:translate-x-0.5 hover:shadow-sm","data-[active=true]:bg-sidebar-primary data-[active=true]:text-sidebar-primary-foreground","data-[active=true]:shadow-md data-[active=true]:border-sidebar-primary/20","focus:ring-2 focus:ring-sidebar-ring focus:ring-offset-2 focus:ring-offset-sidebar","rounded-lg border border-transparent hover:border-sidebar-border/20"),children:e.jsxs(y,{href:a.href,prefetch:!0,className:"flex items-center gap-3 w-full",children:[a.icon&&e.jsx(a.icon,{className:p("h-4 w-4 sidebar-smooth-transition","group-hover/item:scale-105 group-hover/item:text-sidebar-primary")}),e.jsx("span",{className:"sidebar-smooth-transition font-medium",children:a.title}),e.jsx("div",{className:p("ml-auto w-1.5 h-1.5 rounded-full sidebar-smooth-transition","bg-sidebar-primary opacity-0 scale-0",h.url.startsWith(a.href)&&"opacity-100 scale-100")})]})})},a.title))})})]})})}),w=(s,t)=>{},Us=(s,t="#")=>{try{return typeof route=="function"?route(s):(w(`Route helper not available, using fallback for: ${s}`),t)}catch{return t}},Vs=[{title:"Dashboard",href:"/dashboard",icon:U}],Zs=[{title:"Search Parts",href:"/search",icon:D},{title:"Search in Category",href:"/search/categories",icon:ks},{title:"Search in Brand",href:"/search/brands",icon:pe}],de=[{title:"Notifications",href:"/notifications",icon:q},{title:"Activity Log",href:"/activity",icon:H},{title:"Favorites",href:"/dashboard/favorites",icon:Oe},{title:"Search History",href:"/dashboard/history",icon:qe},{title:"Usage Stats",href:"/subscription/search-stats",icon:G}],Ws=[{title:"Subscription Plans",href:"/subscription/plans",icon:$},{title:"Billing Dashboard",href:"/subscription/dashboard",icon:G},{title:"Payment Requests",href:"/payment-requests",icon:W}],Ks=[{title:"Admin Dashboard",href:"/admin/dashboard",icon:Z},{title:"User Management",href:"/admin/users",icon:ss},{title:"Analytics",href:Us("admin.analytics.index","/admin/analytics"),icon:G}],Qs=[{title:"Payment Requests",href:"/admin/payment-requests",icon:$},{title:"Subscription Manager",href:"/admin/subscriptions",icon:$},{title:"Pricing Plans",href:"/admin/pricing-plans",icon:ys}],Js=[{title:"Impersonation Logs",href:"/admin/impersonation/logs",icon:H},{title:"User Activities",href:"/admin/activities",icon:W},{title:"Two-Factor Auth",href:"/admin/two-factor",icon:Ee},{title:"Rate Limiting",href:"/admin/rate-limit",icon:Z}],Xs=[{title:"Site Settings",href:"/admin/site-settings",icon:V},{title:"Search Configuration",href:"/admin/search-config",icon:D},{title:"Email Config",href:"/admin/email-config",icon:as},{title:"Notifications",href:"/admin/notifications",icon:q},{title:"Payment Gateways",href:"/admin/payment-gateways",icon:$}],Ys=[{title:"Pages",href:"/admin/pages",icon:W},{title:"Menus",href:"/admin/menus",icon:Re},{title:"Categories",href:"/admin/categories",icon:$e},{title:"Parts",href:"/admin/parts",icon:rs},{title:"Brands",href:"/admin/brands",icon:pe},{title:"Models",href:"/admin/models",icon:is},{title:"Bulk Import",href:"/admin/bulk-import",icon:De},{title:"Media Library",href:"/admin/media",icon:Te}],et=[{title:"Settings",href:"/settings/profile",icon:V}],he="admin_view_mode";function st(){var l,h;const{auth:s}=B().props,t=(()=>{try{if(!(s!=null&&s.user))return w("No authenticated user found"),!1;const o="isAdmin"in s.user&&typeof s.user.isAdmin=="boolean",u=o?!!s.user.isAdmin:["<EMAIL>","<EMAIL>","<EMAIL>"].includes(s.user.email||"");return w("Admin detection:",{userEmail:s.user.email,hasIsAdminMethod:o,adminStatus:u,userObject:s.user}),u}catch{return!1}})(),n=((l=s.user)==null?void 0:l.subscription_plan)==="premium",[r,i]=d.useState(t);return d.useEffect(()=>{try{if(t){const o=localStorage.getItem(he);if(w("Loading saved view mode:",o),o!==null){const u=o==="admin";i(u),w("Set admin view from localStorage:",u)}}else i(!1),w("Non-admin user, forcing user view")}catch{i(t)}},[t]),d.useEffect(()=>{try{if(t){const o=r?"admin":"user";localStorage.setItem(he,o),w("Saved view mode to localStorage:",o)}}catch{}},[r,t]),d.useEffect(()=>{var o;w("AppSidebar state update:",{userEmail:(o=s.user)==null?void 0:o.email})},[t,r,n,(h=s.user)==null?void 0:h.email]),e.jsxs(Me,{collapsible:"icon",variant:"inset",children:[e.jsx(Ae,{children:e.jsx(M,{children:e.jsx(A,{children:e.jsx(S,{size:"lg",asChild:!0,tooltip:{children:"FixHaat - Mobile Parts Database"},children:e.jsx(y,{href:t&&r?"/admin/dashboard":"/dashboard",prefetch:!0,children:e.jsx(Ie,{})})})})})}),e.jsxs(_e,{children:[t&&r?e.jsxs(e.Fragment,{children:[e.jsx(j,{title:"Core Administration",items:Ks,groupId:"admin-core",icon:_s}),e.jsx(j,{title:"Financial Management",items:Qs,groupId:"admin-financial",icon:Pe}),e.jsx(j,{title:"Security & Access",items:Js,groupId:"admin-security",icon:ts}),e.jsx(j,{title:"System Configuration",items:Xs,groupId:"admin-system",icon:js}),e.jsx(j,{title:"Content Management",items:Ys,groupId:"admin-content",icon:ws})]}):e.jsxs(e.Fragment,{children:[e.jsx(j,{title:"Platform",items:Vs,groupId:"user-platform",icon:U}),e.jsx(j,{title:"Search & Browse",items:Zs,groupId:"user-search",icon:D}),e.jsx(j,{title:"Activity",items:n?de:de.slice(0,2),groupId:"user-activity",icon:H}),e.jsx(j,{title:n?"Subscription":"Upgrade",items:Ws,groupId:"user-subscription",icon:$})]}),e.jsx(j,{title:"Account",items:et,groupId:"account",icon:ae})]}),e.jsx(ze,{children:s.user?e.jsx(Ts,{isAdmin:t,isAdminView:r,setIsAdminView:i}):e.jsx(M,{children:e.jsx(A,{children:e.jsxs("div",{className:"flex flex-col gap-2 p-2",children:[e.jsx(y,{href:route("login"),children:e.jsxs(S,{size:"sm",className:"w-full justify-center",children:[e.jsx(ae,{className:"h-4 w-4"}),"Log in"]})}),e.jsx(y,{href:route("register"),children:e.jsx(S,{size:"sm",className:"w-full justify-center bg-blue-600 text-white hover:bg-blue-700",children:"Sign up"})})]})})})})]})}const tt=(s,t="w-4 h-4")=>{switch(s){case"info":return e.jsx(cs,{className:`${t} text-blue-500`});case"warning":return e.jsx(os,{className:`${t} text-yellow-500`});case"success":return e.jsx(xs,{className:`${t} text-green-500`});case"error":return e.jsx(gs,{className:`${t} text-red-500`});case"announcement":return e.jsx(Ms,{className:`${t} text-purple-500`});default:return e.jsx(q,{className:`${t} text-gray-500`})}};function at({className:s=""}){const[t,n]=d.useState(0),[r,i]=d.useState([]),[l,h]=d.useState(!0);d.useEffect(()=>{o();const a=setInterval(o,3e4);return()=>clearInterval(a)},[]);const o=async()=>{try{const a=await fetch(route("notifications.unread-count"));if(!a.ok){if(a.status===401||a.status===403){n(0),i([]);return}throw new Error(`HTTP error! status: ${a.status}`)}const m=await a.json();n(m.count||0);const g=await fetch(route("notifications.recent"));if(g.ok){const f=await g.json();i(f.notifications||[])}}catch(a){console.error("Failed to fetch notification data:",a),n(0),i([])}finally{h(!1)}},u=async a=>{try{await F.post(route("notifications.mark-read",a),{},{preserveState:!0,preserveScroll:!0,onSuccess:()=>{n(m=>Math.max(0,m-1)),i(m=>m.map(g=>g.id===a?{...g,read_at:new Date().toISOString()}:g))}})}catch(m){console.error("Failed to mark notification as read:",m)}},c=a=>{const m=new Date(a),f=Math.floor((new Date().getTime()-m.getTime())/1e3);return f<60?"Just now":f<3600?`${Math.floor(f/60)}m ago`:f<86400?`${Math.floor(f/3600)}h ago`:`${Math.floor(f/86400)}d ago`},N=(a,m=60)=>a.length>m?a.substring(0,m)+"...":a;return e.jsxs(I,{children:[e.jsx(_,{asChild:!0,children:e.jsxs(v,{variant:"ghost",size:"sm",className:`relative ${s}`,children:[t>0?e.jsx(q,{className:"w-5 h-5"}):e.jsx(ie,{className:"w-5 h-5"}),t>0&&e.jsx(k,{variant:"destructive",className:"absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs",children:t>99?"99+":t})]})}),e.jsxs(P,{align:"end",className:"w-80",children:[e.jsxs(O,{className:"flex items-center justify-between",children:[e.jsx("span",{children:"Notifications"}),t>0&&e.jsxs(k,{variant:"secondary",className:"text-xs",children:[t," unread"]})]}),e.jsx(E,{}),l?e.jsx("div",{className:"p-4 text-center text-sm text-gray-500",children:"Loading notifications..."}):r.length>0?e.jsxs(e.Fragment,{children:[r.map(a=>e.jsx(R,{className:`p-3 cursor-pointer ${a.read_at?"":"bg-blue-50"}`,onClick:()=>F.get(route("notifications.show",a.id)),children:e.jsxs("div",{className:"flex items-start space-x-3 w-full",children:[tt(a.type),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center justify-between mb-1",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:a.title}),!a.read_at&&e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full ml-2 flex-shrink-0"})]}),e.jsx("p",{className:"text-xs text-gray-600 mb-1",children:N(a.message)}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs text-gray-500",children:c(a.created_at)}),!a.read_at&&e.jsx(v,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",onClick:m=>{m.stopPropagation(),u(a.id)},children:e.jsx(ns,{className:"w-3 h-3"})})]})]})]})},a.id)),e.jsx(E,{}),e.jsx(R,{asChild:!0,children:e.jsx(y,{href:route("notifications.index"),className:"w-full text-center",children:e.jsx(v,{variant:"ghost",className:"w-full",children:"View All Notifications"})})})]}):e.jsxs("div",{className:"p-4 text-center",children:[e.jsx(ie,{className:"w-8 h-8 text-gray-400 mx-auto mb-2"}),e.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"No notifications"}),e.jsx(y,{href:route("notifications.index"),children:e.jsx(v,{variant:"ghost",size:"sm",children:"View All"})})]})]})]})}function rt({breadcrumbs:s=[]}){const{appearance:t,setAppearance:n}=ye(),r=ue(),[i,l]=d.useState(!1),h=async()=>{if(!i){l(!0);try{F.post("/admin/dashboard/clear-cache",{},{onSuccess:()=>{},onError:c=>{const N=(c==null?void 0:c.message)||"Failed to clear caches. Please try again.";te.error(N)},onFinish:()=>{l(!1)}})}catch{te.error("An error occurred while clearing caches."),l(!1)}}},o=[{title:"Search Parts",href:"/search",icon:D,description:"Find mobile parts quickly",shortcut:"⌘K"},{title:"Add New Part",href:"/admin/parts/create",icon:se,description:"Create a new part entry",adminOnly:!0},{title:"Dashboard",href:r?"/admin/dashboard":"/dashboard",icon:U,description:"Go to main dashboard"},{title:"Settings",href:"/settings/profile",icon:V,description:"Manage your account"}],u=[{value:"light",label:"Light",icon:oe},{value:"dark",label:"Dark",icon:ne},{value:"system",label:"System",icon:re}];return e.jsxs("header",{className:"flex h-16 shrink-0 items-center gap-3 border-b border-sidebar-border/50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-6 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 md:px-4",children:[e.jsxs("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[e.jsx(Le,{className:"-ml-1"}),e.jsx(le,{orientation:"vertical",className:"h-6"}),e.jsx("div",{className:"flex-1 min-w-0",children:e.jsx(Fe,{breadcrumbs:s})})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(v,{variant:"ghost",size:"sm",className:"hidden md:flex items-center gap-2 h-8 px-3 text-muted-foreground hover:text-foreground transition-colors",asChild:!0,children:e.jsxs(y,{href:"/search",children:[e.jsx(D,{className:"h-4 w-4"}),e.jsx("span",{className:"text-sm",children:"Search"}),e.jsx(k,{variant:"outline",className:"ml-1 px-1.5 py-0.5 text-xs font-mono",children:"⌘K"})]})}),e.jsx(v,{variant:"ghost",size:"sm",className:"flex items-center gap-2 h-8 px-3 text-muted-foreground hover:text-foreground transition-colors hover:bg-teal-50 hover:text-teal-700 dark:hover:bg-teal-950 dark:hover:text-teal-300",asChild:!0,children:e.jsxs("a",{href:"/",target:"_blank",rel:"noopener noreferrer",children:[e.jsx(ls,{className:"h-4 w-4"}),e.jsx("span",{className:"text-sm hidden sm:inline",children:"Visit Site"})]})}),e.jsxs(I,{children:[e.jsx(_,{asChild:!0,children:e.jsxs(v,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-accent/80 transition-all duration-200",children:[e.jsx(se,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Quick Actions"})]})}),e.jsxs(P,{align:"end",className:"w-64",children:[e.jsxs(O,{className:"flex items-center gap-2",children:[e.jsx(ds,{className:"h-4 w-4"}),"Quick Actions"]}),e.jsx(E,{}),o.map(c=>c.adminOnly&&!r?null:e.jsx(R,{asChild:!0,children:e.jsxs(y,{href:c.href,className:"flex items-center gap-3 p-2",children:[e.jsx(c.icon,{className:"h-4 w-4 text-muted-foreground"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-medium",children:c.title}),e.jsx("div",{className:"text-xs text-muted-foreground",children:c.description})]}),c.shortcut&&e.jsx(k,{variant:"outline",className:"text-xs font-mono",children:c.shortcut})]})},c.href))]})]}),e.jsxs(I,{children:[e.jsx(_,{asChild:!0,children:e.jsxs(v,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-accent/80 transition-all duration-200",children:[t==="light"?e.jsx(oe,{className:"h-4 w-4"}):t==="dark"?e.jsx(ne,{className:"h-4 w-4"}):e.jsx(re,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}),e.jsxs(P,{align:"end",children:[e.jsx(O,{children:"Theme"}),e.jsx(E,{}),u.map(c=>e.jsxs(R,{onSelect:()=>{console.log("Theme selected:",c.value),n(c.value)},className:"flex items-center gap-2",children:[e.jsx(c.icon,{className:"h-4 w-4"}),c.label,t===c.value&&e.jsx(k,{variant:"outline",className:"ml-auto text-xs",children:"Active"})]},c.value))]})]}),r&&e.jsxs(v,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-accent/80 transition-all duration-200 hover:bg-red-50 dark:hover:bg-red-950",title:"Clear All Caches",onClick:h,disabled:i,children:[e.jsx("div",{className:`h-4 w-4 text-red-600 dark:text-red-400 ${i?"animate-pulse":""}`,children:e.jsxs("svg",{viewBox:"0 0 730 730",className:"w-full h-full",children:[e.jsx("defs",{children:e.jsx("style",{children:`
                                    .st0 { fill: currentColor; }
                                    .st1 { fill: none; stroke: currentColor; stroke-miterlimit: 10; stroke-width: 25px; }
                                    .st2 { fill: currentColor; }
                                `})}),e.jsxs("g",{children:[e.jsx("path",{className:"st0",d:"M358.8,358.1c4.1.6,7.1,1.9,10.7,4,1.1.6,2.2,1.2,3.3,1.9,1.2.7,2.3,1.3,3.5,2,1.2.7,2.4,1.4,3.6,2,3.6,2,7.3,4.1,10.9,6.2,1.1.6,2.2,1.2,3.3,1.9,5.9,3.4,11.8,6.7,17.7,10.1-.6,3.5-2.1,5.9-4.1,8.8-.6.9-1.3,1.8-1.9,2.8q-1,1.5-2,2.9t-2.1,3c-21.2,30.6-44.9,53.8-77.9,71.5-.7.4-1.4.7-2,1.1-4.6,2.4-9.2,4.7-14,6.9q-1.1.5-2.3,1.1c-21.7,10-44.4,16.9-67.7,21.9,3.4,3.6,6.9,6.6,10.9,9.6,1,.7,2,1.5,3,2.3q1.5,1.1,3,2.2c1,.8,1.9,1.6,2.9,2.4q3,2,6.6,1.2c1.3-.4,2.6-.9,3.9-1.3.7-.2,1.4-.5,2.1-.7,18.6-6.7,32.6-17.4,46.8-30.9,2.5-2.4,5.1-4.7,7.6-7,.8-.7,1.6-1.4,2.4-2.1q1.8-1.5,2.8-1.5c-1.8,24.5-7.3,42.1-22.3,61.5-2.1,2.7-4,5.5-5.7,8.5,1.2.3,2.4.7,3.6,1,3.9,1.3,7.2,3,10.8,5.1,7.6,4.4,13.7,6.6,22.7,4.9,23.9-6.5,44.9-23.7,63.3-39.5,1.8-1.5,3.5-3,5.3-4.5,7.7-6.6,15.3-13.3,22.7-20.1.7-.6,1.4-1.3,2.2-2,.6-.6,1.3-1.1,1.9-1.7q1.6-1.2,3.6-1.2c-1.6,17.7-6.8,37.8-15.9,53.2-1.2,2-2.3,4.1-3.4,6.2-5.9,11-12.6,21.4-19.7,31.7,10.6,2.7,21.2,4.4,32,6,.2-.7.4-1.3.7-2,1.7-4,4.1-7.4,6.4-11,23-35.9,40.5-74.9,54.8-115,.3-.9.7-1.9,1-2.8,1.7-4.7,3.3-9.4,5-14.1.6-1.7,1.2-3.4,1.8-5.1q.4-1.2.8-2.4.8-2.2,2.4-5.6c3.1-1.4,5.2-.9,8.4.2.9.3,1.7.6,2.6.9.9.3,1.8.6,2.8,1,.9.3,1.9.6,2.9,1,2,.7,4,1.4,6,2.1,3,1.1,6.1,2.1,9.2,3.2,2,.7,3.9,1.3,5.9,2,.9.3,1.8.6,2.7.9,5.7,2,11.2,4.2,16.6,6.8-.6,5.1-1.7,9.7-3.4,14.6-.2.7-.5,1.4-.8,2.2-.8,2.4-1.7,4.7-2.5,7.1-.3.8-.6,1.6-.9,2.5-9.4,26.5-20.3,52.4-32.4,77.7-.5,1-1,2-1.5,3.1-7.4,15.4-15.7,30.3-24.5,44.9q-.9,1.5-1.9,3.1-24.8,41-39.4,46c-52.9,12.4-133.6-22.6-178.7-46.1-2.2-1.1-4.3-2.2-6.5-3.3-36.7-18.7-69.2-44.4-98.3-73.4-.7-.7-1.4-1.4-2.1-2.1-4.6-4.6-8.9-9.3-13-14.2q-1.1-1.3-2.3-2.7c-8.5-10.3-11.7-18.8-10.7-32.3,2.2-9.1,6.9-15,14.7-19.9,7.4-3.6,15-3.5,23.1-4,51.8-3,107.7-17.9,147.2-53.1.5-.5,1-.9,1.6-1.4,8.1-7.2,14-15.6,20-24.5,1.8-2.7,3.6-5.3,5.5-7.9,1.7-2.4,3.3-4.8,5-7.2Z"}),e.jsx("path",{className:"st2",d:"M579.8,141.1c7.4,4.6,14.1,10.6,17,19,1.9,12.3-2.5,22.7-6.8,33.9-.7,2-1.4,3.9-2.2,5.9-1.5,4.2-3.1,8.4-4.7,12.6-2.5,6.7-4.9,13.3-7.3,20-3.8,10.4-7.5,20.8-11.3,31.1-2.6,7-5.1,14-7.6,21-1.6,4.5-3.3,9-4.9,13.5-.8,2.1-1.5,4.2-2.3,6.4-1.1,2.9-2.1,5.9-3.2,8.8-.3.8-.6,1.7-.9,2.6-2.2,6-4.9,11.6-7.8,17.2-5.6-.6-10.1-2.6-15.1-4.9-.9-.4-1.8-.8-2.7-1.2-2.8-1.3-5.6-2.6-8.4-3.9-2.7-1.3-5.5-2.5-8.2-3.8-1.8-.8-3.6-1.7-5.5-2.5-4.6-2.1-9.1-4.2-13.7-6.1-.9-.4-1.8-.8-2.7-1.2-1.7-.7-3.4-1.4-5-2.1-5.9-2.6-12.7-6.3-15.7-12.3,4.5-13.4,12.1-25.8,18.9-38.2,3.6-6.6,7.1-13.2,10.6-19.8,6.3-11.8,12.6-23.6,18.9-35.4,3.4-6.3,6.8-12.7,10.2-19.1.4-.7.8-1.4,1.1-2.2,1.8-3.5,3.7-6.9,5.5-10.4,12.1-22.9,24.6-43.3,53.9-29Z"}),e.jsx("path",{className:"st0",d:"M237,321.4c10.8,10.1,18.3,23.1,19,38.1.5,17.2-3.1,31.6-14.7,44.9-11.9,12.4-25.3,18.7-42.5,19.6-15.9.2-30.5-5.6-41.9-16.8-.7-.7-1.4-1.5-2.2-2.2-.6-.6-1.3-1.3-2-1.9-9.6-10-13.9-24.5-14.4-38.1.7-16.9,7.3-31.3,19.4-43,.5-.6,1.1-1.1,1.6-1.7,21.6-20.8,56.2-17.5,77.6,1.1Z"}),e.jsx("path",{className:"st0",d:"M552.6,349q-.8-.7-1.5-1.5-7.5-7.1-30.8-17.7c-.7-.3-1.5-.7-2.2-1-4.7-2.2-9.5-4.3-14.2-6.4-2.6-1.2-5.2-2.3-7.8-3.5-4.6-2.1-9.1-4.1-13.7-6.1-1.8-.8-3.6-1.6-5.4-2.4-13.9-6.3-27.6-12.8-43.1-12.7-.9,0-1.8,0-2.7,0-6.3,0-11.9.4-18,2-8.7,2.7-16.3,6.5-23,11.5-.1,0-.3-.1-.4-.2-2.7,2-5.1,4.1-7.5,6.5-.8.9-1.6,1.7-2.4,2.6-1.9,2-3.2,3.1-3.8,4.9-2.9,3.7-5.5,7.8-7.9,12.3-.4.7-.7,1.4-1.1,2.2-1.6,3.1-3.5,6-3.4,9.6q.9,2,3.3,3.4c1.1.5,2.2.9,3.2,1.3,1.2.5,2.5,1.1,3.7,1.6,3.6,1.5,7.1,3,10.7,4.5.8.3,1.5.7,2.3,1,8.4,3.6,16.6,7.3,24.9,11,15.8,7.2,31.6,14.3,47.4,21.2,12.7,5.5,25.3,11.1,37.9,16.8,13.2,6,26.4,11.9,39.6,17.7,1.8.8,3.7,1.6,5.5,2.4,2.6,1.2,5.2,2.3,7.9,3.5.8.4,1.6.7,2.4,1.1,4.3,1.9,8.4,3.7,13.1,4.4,4.9-12.7,9.4-25.6,9.4-39.4-.6-20.3-7.9-36.7-22.5-50.7Z"}),e.jsx("path",{className:"st0",d:"M237.4,254.8c-1.9-8.7-6.4-14.8-13.6-19.8-9.2-6.1-22.1-6.2-31.7-.9,0,0-.2,0-.3-.1-2.6,1.7-2.9,2-4.1,3.1-.3.3-.7.6-1,.9-1.3,1.4-2.4,2.7-3.3,3.9-.2.2-.3.4-.4.6-4.7,6.5-5.6,12.4-5.4,21.9.6,7.8,3.6,13.3,9.3,18.6,7.4,6.1,14.6,8.5,24.2,7.8,8.8-1.2,15-5.2,20.5-11.9,5.5-7.7,7-14.8,5.9-24.2Z"}),e.jsx("path",{className:"st0",d:"M160.9,244.1c-.9-10-6.9-18.5-14.4-24.9-14.5-10.3-33.2-10.2-47.5-1,0,0-.2,0-.3-.1l-.9.9c-2.2,1.5-4.2,3.2-6.1,5.1-7,8.3-10.1,18.1-10,28.9,1,12.1,5.8,21.1,15,29,8.7,6.8,18.8,9.6,29.8,8.8,12.5-1.8,20.8-7.9,28.5-17.7q.9-1.6,1.8-3.1c4.7-8.3,4.7-16.7,4.2-26Z"})]}),e.jsx("circle",{className:"st1",cx:"364.9",cy:"365",r:"349.5"})]})}),e.jsx("span",{className:"sr-only",children:"Clear All Caches"})]}),e.jsxs(v,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-accent/80 transition-all duration-200",title:"Help & Support",children:[e.jsx(Be,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Help"})]}),e.jsx(le,{orientation:"vertical",className:"h-6"}),e.jsx(at,{}),r&&e.jsxs(k,{variant:"outline",className:"hidden sm:flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-orange-50 to-amber-50 text-orange-700 border-orange-200 dark:from-orange-950 dark:to-amber-950 dark:text-orange-300 dark:border-orange-800",children:[e.jsx(Z,{className:"h-3 w-3"}),e.jsx("span",{className:"text-xs font-medium",children:"Admin"})]})]})]})}function it({children:s,breadcrumbs:t=[]}){const n=ue();return e.jsxs(e.Fragment,{children:[e.jsx(Ge,{}),e.jsxs(He,{variant:"sidebar",children:[e.jsx(st,{}),e.jsxs(Ue,{variant:"sidebar",className:"overflow-x-hidden",children:[e.jsx(rt,{breadcrumbs:t}),s]})]}),e.jsx(Ve,{isAdmin:n}),e.jsx(Ze,{})]})}const Mt=({children:s,breadcrumbs:t,...n})=>e.jsx(it,{breadcrumbs:t,...n,children:s});export{Mt as A,ie as B,gs as C,ys as D,ks as G,Ms as M,le as S,oe as a,ne as b,xs as c,_s as d};
