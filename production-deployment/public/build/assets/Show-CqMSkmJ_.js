import{r as h,j as e,Q as H,t as j,S as v}from"./app-J5EqS6dS.js";import{C as n,a as i,b as l,c}from"./card-9XCADs-4.js";import{B as a}from"./badge-BucYuCBs.js";import{B as t}from"./smartphone-GGiwNneF.js";import{I as M}from"./input-Bo8dOn9p.js";import{L as Q}from"./label-BlOrdc-X.js";import{D as w,a3 as S,P as q,b as y,c as _,d as C,e as b,f as P,F as D,t as d}from"./ImpersonationBanner-CYn5eDk6.js";import{A as J,D as E}from"./app-layout-ox1kAwY6.js";import{A as K}from"./arrow-left-D4U9AVF9.js";import{S as R}from"./square-pen-Bepbg6wc.js";import{B as V}from"./ban-Dctu8q_b.js";import{C as B}from"./calendar-B-u_QN2Q.js";import{C as I}from"./circle-check-big-DOFoatRy.js";import{C as W}from"./clock-Brl7_5s7.js";import{U as X}from"./user-DCnDRzMf.js";import{E as Y}from"./eye-D-fsmYB2.js";import{S as Z}from"./users-RYmOyic9.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./index-BzZWUWqx.js";import"./crown-UDSxMtlm.js";import"./shield-D9nQfigG.js";import"./search-DBK6jUoc.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";function Ae({subscription:s}){var p,u,g,f,N;const[A,m]=h.useState(!1),[L,o]=h.useState(!1),[x,k]=h.useState(1),F=r=>{switch(r){case"active":return e.jsx(a,{className:"bg-green-500 hover:bg-green-600",children:"Active"});case"cancelled":return e.jsx(a,{variant:"destructive",children:"Cancelled"});case"expired":return e.jsx(a,{variant:"outline",className:"border-yellow-500 text-yellow-700",children:"Expired"});case"pending":return e.jsx(a,{variant:"secondary",children:"Pending"});default:return e.jsx(a,{variant:"outline",children:r})}},U=r=>{switch(r){case"premium":return e.jsx(a,{className:"bg-amber-500 hover:bg-amber-600",children:"Premium"});default:return e.jsx(a,{variant:"outline",children:"Free"})}},T=r=>{switch(r){case"paddle":return e.jsx(a,{className:"bg-blue-500 hover:bg-blue-600",children:"Paddle"});case"shurjopay":return e.jsx(a,{className:"bg-green-500 hover:bg-green-600",children:"ShurjoPay"});case"coinbase_commerce":return e.jsx(a,{className:"bg-orange-500 hover:bg-orange-600",children:"Coinbase Commerce"});case"offline":return e.jsx(a,{variant:"outline",children:"Offline Payment"});default:return e.jsx(a,{variant:"outline",children:r})}},z=()=>{v.post(route("admin.subscriptions.cancel",s.id),{},{onSuccess:()=>{d.success("Subscription cancelled successfully."),m(!1)},onError:()=>{d.error("Failed to cancel subscription.")}})},G=()=>{v.post(route("admin.subscriptions.extend",s.id),{months:x},{onSuccess:()=>{d.success(`Subscription extended by ${x} month(s).`),o(!1)},onError:()=>{d.error("Failed to extend subscription.")}})},O=s.status==="active";return s.status,s.status,e.jsxs(J,{children:[e.jsx(H,{title:`Subscription: ${s.user.name}`}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(j,{href:route("admin.subscriptions.index"),children:e.jsxs(t,{variant:"outline",size:"sm",children:[e.jsx(K,{className:"h-4 w-4 mr-2"}),"Back to Subscriptions"]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:"Subscription Details"}),e.jsxs("p",{className:"text-muted-foreground mt-1",children:["Subscription ID: ",s.id," • ",s.user.name]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(j,{href:route("admin.subscriptions.edit",s.id),children:e.jsxs(t,{variant:"outline",children:[e.jsx(R,{className:"h-4 w-4 mr-2"}),"Edit"]})}),O&&e.jsxs(e.Fragment,{children:[e.jsxs(w,{open:L,onOpenChange:o,children:[e.jsx(S,{asChild:!0,children:e.jsxs(t,{variant:"outline",children:[e.jsx(q,{className:"h-4 w-4 mr-2"}),"Extend"]})}),e.jsxs(y,{children:[e.jsxs(_,{children:[e.jsx(C,{children:"Extend Subscription"}),e.jsxs(b,{children:["Extend the subscription period for ",s.user.name]})]}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{children:[e.jsx(Q,{htmlFor:"extend_months",children:"Extend by (months)"}),e.jsx(M,{id:"extend_months",type:"number",min:"1",max:"12",value:x,onChange:r=>k(parseInt(r.target.value)||1)})]})}),e.jsxs(P,{children:[e.jsx(t,{variant:"outline",onClick:()=>o(!1),children:"Cancel"}),e.jsx(t,{onClick:G,children:"Extend Subscription"})]})]})]}),e.jsxs(w,{open:A,onOpenChange:m,children:[e.jsx(S,{asChild:!0,children:e.jsxs(t,{variant:"destructive",children:[e.jsx(V,{className:"h-4 w-4 mr-2"}),"Cancel"]})}),e.jsxs(y,{children:[e.jsxs(_,{children:[e.jsx(C,{children:"Cancel Subscription"}),e.jsx(b,{children:"Are you sure you want to cancel this subscription? This action cannot be undone."})]}),e.jsxs(P,{children:[e.jsx(t,{variant:"outline",onClick:()=>m(!1),children:"Keep Subscription"}),e.jsx(t,{variant:"destructive",onClick:z,children:"Cancel Subscription"})]})]})]})]})]})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[e.jsxs(n,{children:[e.jsxs(i,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(l,{className:"text-sm font-medium",children:"Status"}),e.jsx(D,{className:"h-4 w-4 text-blue-600"})]}),e.jsx(c,{children:e.jsx("div",{className:"flex items-center gap-2",children:F(s.status)})})]}),e.jsxs(n,{children:[e.jsxs(i,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(l,{className:"text-sm font-medium",children:"Plan"}),e.jsx(E,{className:"h-4 w-4 text-green-600"})]}),e.jsx(c,{children:e.jsx("div",{className:"flex items-center gap-2",children:U(s.plan_name)})})]})]}),e.jsxs("div",{className:"grid gap-6 lg:grid-cols-3",children:[e.jsxs("div",{className:"lg:col-span-1",children:[e.jsxs(n,{children:[e.jsx(i,{children:e.jsxs(l,{className:"flex items-center gap-2",children:[e.jsx(D,{className:"h-5 w-5"}),"Subscription Information"]})}),e.jsx(c,{className:"space-y-4",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(B,{className:"h-4 w-4 text-muted-foreground"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Created"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:new Date(s.created_at).toLocaleDateString()})]})]}),s.current_period_start&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(I,{className:"h-4 w-4 text-green-600"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Period Start"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:new Date(s.current_period_start).toLocaleDateString()})]})]}),s.current_period_end&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(W,{className:"h-4 w-4 text-orange-600"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Period End"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:new Date(s.current_period_end).toLocaleDateString()})]})]})]})})]}),e.jsxs(n,{className:"mt-6",children:[e.jsx(i,{children:e.jsxs(l,{className:"flex items-center gap-2",children:[e.jsx(X,{className:"h-5 w-5"}),"User Information"]})}),e.jsxs(c,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:s.user.name}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s.user.email})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(B,{className:"h-4 w-4 text-muted-foreground"}),e.jsxs("span",{className:"text-sm",children:["Joined ",new Date(s.user.created_at).toLocaleDateString()]})]})]}),e.jsx("div",{className:"pt-2",children:e.jsx(j,{href:`/admin/users/${s.user.id}`,children:e.jsxs(t,{variant:"outline",size:"sm",className:"w-full",children:[e.jsx(Y,{className:"h-4 w-4 mr-2"}),"View User Profile"]})})})]})]})]}),e.jsxs("div",{className:"lg:col-span-2",children:[e.jsxs(n,{children:[e.jsx(i,{children:e.jsxs(l,{className:"flex items-center gap-2",children:[e.jsx(E,{className:"h-5 w-5"}),"Pricing Plan Details"]})}),e.jsxs(c,{className:"space-y-4",children:[e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Plan Name"}),e.jsx("p",{className:"text-lg font-bold",children:((p=s.pricingPlan)==null?void 0:p.display_name)||"No Plan Assigned"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Price"}),e.jsx("p",{className:"text-lg font-bold",children:((u=s.pricingPlan)==null?void 0:u.formatted_price)||"N/A"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Search Limit"}),e.jsx("p",{className:"text-lg font-bold",children:s.pricingPlan?s.pricingPlan.search_limit===-1?"Unlimited":s.pricingPlan.search_limit:"N/A"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Billing Interval"}),e.jsx("p",{className:"text-lg font-bold capitalize",children:((g=s.pricingPlan)==null?void 0:g.interval)||"N/A"})]})]}),((f=s.pricingPlan)==null?void 0:f.description)&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Description"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s.pricingPlan.description})]}),((N=s.pricingPlan)==null?void 0:N.features)&&s.pricingPlan.features.length>0&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium mb-2",children:"Features"}),e.jsx("ul",{className:"space-y-1",children:s.pricingPlan.features.map((r,$)=>e.jsxs("li",{className:"flex items-center gap-2 text-sm",children:[e.jsx(I,{className:"h-3 w-3 text-green-600"}),r]},$))})]})]})]}),e.jsxs(n,{className:"mt-6",children:[e.jsx(i,{children:e.jsxs(l,{className:"flex items-center gap-2",children:[e.jsx(Z,{className:"h-5 w-5"}),"Payment Gateway Information"]})}),e.jsxs(c,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Payment Gateway"}),e.jsx("div",{className:"flex items-center gap-2 mt-1",children:T(s.payment_gateway)})]}),s.paddle_subscription_id&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Paddle Subscription ID"}),e.jsx("p",{className:"text-sm text-muted-foreground font-mono",children:s.paddle_subscription_id})]}),s.shurjopay_subscription_id&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"ShurjoPay Subscription ID"}),e.jsx("p",{className:"text-sm text-muted-foreground font-mono",children:s.shurjopay_subscription_id})]}),s.coinbase_commerce_subscription_id&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Coinbase Commerce Subscription ID"}),e.jsx("p",{className:"text-sm text-muted-foreground font-mono",children:s.coinbase_commerce_subscription_id})]}),!s.paddle_subscription_id&&!s.shurjopay_subscription_id&&!s.coinbase_commerce_subscription_id&&e.jsx("p",{className:"text-sm text-muted-foreground",children:"No external subscription IDs available"})]})]})]})]})]})})]})}export{Ae as default};
