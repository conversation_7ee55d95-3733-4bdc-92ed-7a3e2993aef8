import{J as b,r as c,j as e,Q as n,t as a}from"./app-J5EqS6dS.js";import{S as o,B as r}from"./smartphone-GGiwNneF.js";import{C as f,a as N,b as y,d as v,c as w,e as k}from"./card-9XCADs-4.js";import{B as C}from"./badge-BucYuCBs.js";import{D as P}from"./DynamicFooter-8iBTp4-u.js";import{A as x}from"./arrow-left-D4U9AVF9.js";import{C as F}from"./crown-UDSxMtlm.js";import{Z as z}from"./zap-BcmHRR4K.js";import{C as H}from"./check-C7SdgHPn.js";/* empty css            */import"./input-Bo8dOn9p.js";import"./mail-CDon-vZy.js";function U(){const{auth:t}=b().props,[d,g]=c.useState(null),[p,j]=c.useState(!0),[m,h]=c.useState(null);c.useEffect(()=>{(async()=>{try{const i=await(await fetch("/api/pricing-plans/all")).json();i.success?g(i.data):h(i.message||"Failed to load pricing plans")}catch(l){h("Failed to load pricing plans"),console.error("Error fetching pricing plans:",l)}finally{j(!1)}})()},[]);const u=s=>{t.user?window.location.href=route("subscription.checkout",{plan:s.name}):window.location.href=route("register")};return p?e.jsxs(e.Fragment,{children:[e.jsx(n,{title:"Pricing Plans - FixHaat"}),e.jsx("nav",{className:"sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200 dark:bg-gray-900/80 dark:border-gray-800",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center h-16",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(o,{className:"h-8 w-8 text-blue-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"FixHaat"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(a,{href:route("home"),children:e.jsxs(r,{variant:"ghost",size:"sm",children:[e.jsx(x,{className:"h-4 w-4 mr-2"}),"Back to Home"]})}),t.user?e.jsx(a,{href:route("dashboard"),children:e.jsx(r,{variant:"outline",size:"sm",children:"Dashboard"})}):e.jsxs(e.Fragment,{children:[e.jsx(a,{href:route("login"),children:e.jsx(r,{variant:"ghost",size:"sm",children:"Log in"})}),e.jsx(a,{href:route("register"),children:e.jsx(r,{size:"sm",children:"Sign up"})})]})]})]})})}),e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:e.jsx("div",{className:"py-24",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsx("div",{className:"text-center",children:e.jsxs("div",{className:"animate-pulse",children:[e.jsx("div",{className:"h-8 bg-gray-300 rounded w-64 mx-auto mb-4"}),e.jsx("div",{className:"h-4 bg-gray-300 rounded w-96 mx-auto mb-8"}),e.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto",children:[1,2,3].map(s=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("div",{className:"h-6 bg-gray-300 rounded mb-4"}),e.jsx("div",{className:"h-8 bg-gray-300 rounded mb-4"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"h-4 bg-gray-300 rounded"}),e.jsx("div",{className:"h-4 bg-gray-300 rounded"}),e.jsx("div",{className:"h-4 bg-gray-300 rounded"})]})]},s))})]})})})})})]}):m?e.jsxs(e.Fragment,{children:[e.jsx(n,{title:"Pricing Plans - FixHaat"}),e.jsx("nav",{className:"sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200 dark:bg-gray-900/80 dark:border-gray-800",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center h-16",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(o,{className:"h-8 w-8 text-blue-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"FixHaat"})]}),e.jsx("div",{className:"flex items-center space-x-4",children:e.jsx(a,{href:route("home"),children:e.jsxs(r,{variant:"ghost",size:"sm",children:[e.jsx(x,{className:"h-4 w-4 mr-2"}),"Back to Home"]})})})]})})}),e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:e.jsx("div",{className:"py-24",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Pricing Plans"}),e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto",children:[e.jsx("p",{className:"text-red-600",children:m}),e.jsx(r,{className:"mt-4",onClick:()=>window.location.reload(),children:"Try Again"})]})]})})})})]}):e.jsxs(e.Fragment,{children:[e.jsx(n,{title:"Pricing Plans - FixHaat",children:e.jsx("meta",{name:"description",content:"Choose the perfect plan for your mobile parts database needs. Free searches available, premium plans for professionals."})}),e.jsx("nav",{className:"sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200 dark:bg-gray-900/80 dark:border-gray-800",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center h-16",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(o,{className:"h-8 w-8 text-blue-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"FixHaat"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(a,{href:route("home"),children:e.jsxs(r,{variant:"ghost",size:"sm",children:[e.jsx(x,{className:"h-4 w-4 mr-2"}),"Back to Home"]})}),t.user?e.jsx(a,{href:route("dashboard"),children:e.jsx(r,{variant:"outline",size:"sm",children:"Dashboard"})}):e.jsxs(e.Fragment,{children:[e.jsx(a,{href:route("login"),children:e.jsx(r,{variant:"ghost",size:"sm",children:"Log in"})}),e.jsx(a,{href:route("register"),children:e.jsx(r,{size:"sm",children:"Sign up"})})]})]})]})})}),e.jsxs("main",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[e.jsx("div",{className:"py-24",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"Choose Your Plan"}),e.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto",children:"Get access to our comprehensive mobile parts database with the plan that fits your needs"})]}),d&&d.plans.length>0?e.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto",children:d.plans.map(s=>e.jsxs(f,{className:`relative ${s.is_popular?"border-blue-500 shadow-xl scale-105":"border-gray-200 shadow-lg"} hover:shadow-xl transition-all duration-300`,children:[s.is_popular&&e.jsx("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:e.jsxs(C,{className:"bg-blue-500 text-white px-4 py-1",children:[e.jsx(F,{className:"w-4 h-4 mr-1"}),"Most Popular"]})}),e.jsxs(N,{className:"text-center pb-8",children:[e.jsxs(y,{className:"text-2xl font-bold flex items-center justify-center gap-2",children:[s.is_popular&&e.jsx(z,{className:"w-6 h-6 text-blue-500"}),s.display_name]}),e.jsxs("div",{className:"mt-4",children:[e.jsx("span",{className:"text-4xl font-bold text-gray-900 dark:text-white",children:s.formatted_price||`$${s.price}`}),!s.formatted_price&&s.price>0&&e.jsxs("span",{className:"text-gray-600 dark:text-gray-400",children:["/",s.interval]})]}),e.jsx(v,{className:"mt-2",children:s.description||(s.search_limit===-1?"Unlimited access for professionals":`Perfect for occasional searches (${s.search_limit} per day)`)})]}),e.jsx(w,{children:e.jsx("ul",{className:"space-y-3",children:s.features.map((l,i)=>e.jsxs("li",{className:"flex items-center gap-3",children:[e.jsx(H,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),e.jsx("span",{className:"text-gray-700 dark:text-gray-300",children:l})]},i))})}),e.jsx(k,{children:e.jsx(r,{className:`w-full ${s.is_popular?"bg-blue-600 hover:bg-blue-700":"bg-gray-900 hover:bg-gray-800"}`,onClick:()=>u(s),children:t.user?"Upgrade Now":"Get Started"})})]},s.id))}):e.jsx("div",{className:"text-center",children:e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"No pricing plans available at the moment."})}),e.jsxs("div",{className:"text-center mt-16",children:[e.jsxs("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:["Need help choosing? ",e.jsx(a,{href:"/contact",className:"text-blue-600 hover:underline",children:"Contact our support team"})]}),t.user&&e.jsx(a,{href:route("subscription.dashboard"),className:"text-blue-600 hover:underline",children:"Manage your subscription →"})]})]})}),e.jsx(P,{})]})]})}export{U as default};
