import{j as e,Q as h,t as n}from"./app-J5EqS6dS.js";import{C as i,a as r,b as a,c as d,d as o}from"./card-9XCADs-4.js";import{B as l}from"./badge-BucYuCBs.js";import{B as m}from"./smartphone-GGiwNneF.js";import{A as j}from"./app-layout-ox1kAwY6.js";import{A as p}from"./arrow-left-D4U9AVF9.js";import{E as u}from"./external-link-A4n9PP4e.js";import{S as f}from"./square-pen-Bepbg6wc.js";import{P as N}from"./package-CoyvngX8.js";import{U as v}from"./users-RYmOyic9.js";import{C as g}from"./calendar-B-u_QN2Q.js";/* empty css            */import"./ImpersonationBanner-CYn5eDk6.js";import"./index-D86BnqlV.js";import"./index-BzZWUWqx.js";import"./index-CJpBU2i9.js";import"./crown-UDSxMtlm.js";import"./shield-D9nQfigG.js";import"./user-DCnDRzMf.js";import"./search-DBK6jUoc.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./eye-D-fsmYB2.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";function W({category:s}){var c,x;return e.jsxs(j,{children:[e.jsx(h,{title:`${s.name} - Categories - Admin`}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(n,{href:"/admin/categories",children:e.jsxs(m,{variant:"outline",size:"sm",children:[e.jsx(p,{className:"w-4 h-4 mr-2"}),"Back to Categories"]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:s.name}),e.jsx("p",{className:"text-muted-foreground",children:"Category details and associated parts"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(n,{href:route("categories.show",s.slug||s.id),children:e.jsxs(m,{variant:"outline",children:[e.jsx(u,{className:"h-4 w-4 mr-2"}),"View Public Page"]})}),e.jsx(n,{href:`/admin/categories/${s.id}/edit`,children:e.jsxs(m,{children:[e.jsx(f,{className:"h-4 w-4 mr-2"}),"Edit Category"]})})]})]}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(a,{children:"Category Information"})}),e.jsxs(d,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Name"}),e.jsx("p",{className:"text-lg font-medium",children:s.name})]}),s.description&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Description"}),e.jsx("p",{className:"text-sm",children:s.description})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Status"}),e.jsx("div",{className:"mt-1",children:e.jsx(l,{variant:s.is_active?"default":"secondary",children:s.is_active?"Active":"Inactive"})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Sort Order"}),e.jsx("p",{className:"text-sm",children:s.sort_order})]}),s.parent&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Parent Category"}),e.jsx("p",{className:"text-sm",children:s.parent.name})]})]})]}),e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(a,{children:"Statistics"})}),e.jsxs(d,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(N,{className:"h-5 w-5 text-muted-foreground"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Parts"}),e.jsx("p",{className:"text-2xl font-bold",children:((c=s.parts)==null?void 0:c.length)||0})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(v,{className:"h-5 w-5 text-muted-foreground"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Subcategories"}),e.jsx("p",{className:"text-2xl font-bold",children:((x=s.children)==null?void 0:x.length)||0})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(g,{className:"h-5 w-5 text-muted-foreground"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Created"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:new Date(s.created_at).toLocaleDateString()})]})]})]})]})]}),s.children&&s.children.length>0&&e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(a,{children:"Subcategories"}),e.jsxs(o,{children:["Categories that belong to ",s.name]})]}),e.jsx(d,{children:e.jsx("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:s.children.map(t=>e.jsxs("div",{className:"p-4 border rounded-lg",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"font-medium",children:t.name}),e.jsx(l,{variant:t.is_active?"default":"secondary",children:t.is_active?"Active":"Inactive"})]}),t.description&&e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:t.description})]},t.id))})})]}),s.parts&&s.parts.length>0&&e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(a,{children:"Associated Parts"}),e.jsx(o,{children:"Parts that belong to this category"})]}),e.jsx(d,{children:e.jsx("div",{className:"space-y-4",children:s.parts.map(t=>e.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:t.name}),t.part_number&&e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Part #: ",t.part_number]}),t.manufacturer&&e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Manufacturer: ",t.manufacturer]})]}),e.jsx(l,{variant:t.is_active?"default":"secondary",children:t.is_active?"Active":"Inactive"})]},t.id))})})]})]})})]})}export{W as default};
