import{r as v,j as e,Q as F,t as h,S as w}from"./app-J5EqS6dS.js";import{B as r,S as _}from"./smartphone-GGiwNneF.js";import{C as p,c as j}from"./card-9XCADs-4.js";import{B as i}from"./badge-BucYuCBs.js";import{A as z,G as A}from"./app-layout-ox1kAwY6.js";import{L as D}from"./list-CNjrM85i.js";import{H as u}from"./ImpersonationBanner-CYn5eDk6.js";import{S as L}from"./search-DBK6jUoc.js";import{P}from"./package-CoyvngX8.js";import{E as S}from"./eye-D-fsmYB2.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./users-RYmOyic9.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";function ie({favorites:g}){const[d,f]=v.useState("grid"),[o,C]=v.useState("all"),N=a=>{var t;if(confirm("Are you sure you want to remove this item from your favorites?")){const s=a.favoritable_type.includes("Part")?"part":"model";(t=a.favoritable)!=null&&t.name,w.delete(route("dashboard.remove-favorite"),{data:{type:s,id:a.favoritable_id},onSuccess:()=>{w.reload()},onError:l=>{}})}},y=g.data.filter(a=>a.favoritable?o==="all"?!0:o==="parts"?a.favoritable_type.includes("Part"):o==="models"?a.favoritable_type.includes("Model"):!0:!1),M=({favorite:a})=>{var l,n,m,c;const t=a.favoritable_type.includes("Part"),s=a.favoritable;return s?e.jsx(p,{className:"hover:shadow-lg transition-shadow",children:e.jsxs(j,{className:"p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[t?e.jsx(P,{className:"w-4 h-4 text-blue-500"}):e.jsx(_,{className:"w-4 h-4 text-green-500"}),e.jsx(i,{variant:"outline",className:"text-xs",children:t?"Part":"Model"})]}),e.jsx("h3",{className:"font-semibold text-lg text-gray-900 mb-1",children:(s==null?void 0:s.name)||"Unknown Item"}),t&&s.part_number&&e.jsxs("p",{className:"text-sm text-gray-600 mb-1",children:["Part #: ",s.part_number]}),t&&s.manufacturer&&e.jsxs("p",{className:"text-sm text-gray-600 mb-2",children:["by ",s.manufacturer]}),!t&&((l=s.brand)==null?void 0:l.name)&&e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:(n=s.brand)==null?void 0:n.name})]}),e.jsx(r,{variant:"ghost",size:"sm",onClick:()=>N(a),className:"text-red-500 hover:text-red-700",children:e.jsx(u,{className:"w-4 h-4 fill-current"})})]}),e.jsxs("div",{className:"mb-3",children:[t&&((m=s.category)==null?void 0:m.name)&&e.jsx(i,{variant:"outline",className:"mb-2",children:(c=s.category)==null?void 0:c.name}),t&&s.description&&e.jsx("p",{className:"text-sm text-gray-600 line-clamp-2",children:s.description}),!t&&s.model_number&&e.jsxs("p",{className:"text-sm text-gray-600",children:["Model: ",s.model_number]}),!t&&s.release_year&&e.jsxs("p",{className:"text-sm text-gray-600",children:["Released: ",s.release_year]})]}),t&&s.models&&s.models.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"text-xs text-gray-500 mb-1",children:"Compatible with:"}),e.jsxs("div",{className:"flex flex-wrap gap-1",children:[s.models.slice(0,3).map(x=>{var b;return e.jsxs(i,{variant:"secondary",className:"text-xs",children:[((b=x.brand)==null?void 0:b.name)||"Unknown"," ",x.name]},x.id)}),s.models.length>3&&e.jsxs(i,{variant:"secondary",className:"text-xs",children:["+",s.models.length-3," more"]})]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(h,{href:t?route("parts.show",s.slug||s.id):route("models.show",s.slug||s.id),children:e.jsxs(r,{size:"sm",children:[e.jsx(S,{className:"w-4 h-4 mr-2"}),"View Details"]})}),e.jsxs("p",{className:"text-xs text-gray-500",children:["Added ",new Date(a.created_at).toLocaleDateString()]})]})]})}):null},k=({favorite:a})=>{var l,n,m,c;const t=a.favoritable_type.includes("Part"),s=a.favoritable;return s?e.jsx(p,{children:e.jsx(j,{className:"p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center",children:t?e.jsx(P,{className:"w-8 h-8 text-gray-400"}):e.jsx(_,{className:"w-8 h-8 text-gray-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h3",{className:"font-semibold text-lg text-gray-900",children:(s==null?void 0:s.name)||"Unknown Item"}),e.jsx(i,{variant:"outline",className:"text-xs",children:t?"Part":"Model"})]}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-600 mb-2",children:[t&&s.part_number&&e.jsxs("span",{children:["Part #: ",s.part_number]}),t&&s.manufacturer&&e.jsxs("span",{children:["by ",s.manufacturer]}),!t&&((l=s.brand)==null?void 0:l.name)&&e.jsx("span",{children:(n=s.brand)==null?void 0:n.name}),t&&((m=s.category)==null?void 0:m.name)&&e.jsx(i,{variant:"outline",children:(c=s.category)==null?void 0:c.name})]}),e.jsxs("p",{className:"text-xs text-gray-500",children:["Added ",new Date(a.created_at).toLocaleDateString()]})]})]})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(r,{variant:"ghost",size:"sm",onClick:()=>N(a),className:"text-red-500 hover:text-red-700",children:e.jsx(u,{className:"w-4 h-4 fill-current"})}),e.jsx(h,{href:t?route("parts.show",s.slug||s.id):route("models.show",s.slug||s.id),children:e.jsxs(r,{size:"sm",children:[e.jsx(S,{className:"w-4 h-4 mr-2"}),"View Details"]})})]})]})})}):null};return e.jsxs(z,{children:[e.jsx(F,{title:"My Favorites"}),e.jsx("div",{className:"py-12",children:e.jsxs("div",{className:"max-w-7xl mx-auto sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"My Favorites"}),e.jsxs("p",{className:"text-gray-600",children:[g.total," saved items"]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("select",{value:o,onChange:a=>C(a.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm",children:[e.jsx("option",{value:"all",children:"All Items"}),e.jsx("option",{value:"parts",children:"Parts Only"}),e.jsx("option",{value:"models",children:"Models Only"})]}),e.jsx(r,{variant:d==="grid"?"default":"outline",size:"sm",onClick:()=>f("grid"),children:e.jsx(A,{className:"w-4 h-4"})}),e.jsx(r,{variant:d==="list"?"default":"outline",size:"sm",onClick:()=>f("list"),children:e.jsx(D,{className:"w-4 h-4"})})]})]}),y.length>0?e.jsx("div",{className:d==="grid"?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:y.map(a=>d==="grid"?e.jsx(M,{favorite:a},a.id):e.jsx(k,{favorite:a},a.id))}):e.jsx(p,{children:e.jsxs(j,{className:"text-center py-12",children:[e.jsx(u,{className:"w-16 h-16 mx-auto mb-4 text-gray-300"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No favorites yet"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Start adding parts and models to your favorites while browsing"}),e.jsx(h,{href:route("search.index"),children:e.jsxs(r,{children:[e.jsx(L,{className:"w-4 h-4 mr-2"}),"Start Searching"]})})]})})]})})]})}export{ie as default};
