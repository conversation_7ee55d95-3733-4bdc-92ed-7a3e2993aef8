import{j as e,Q as B,t as O,S as p}from"./app-J5EqS6dS.js";import{C as r,c as l,a as n,b as x,d as g}from"./card-9XCADs-4.js";import{B as u,S as D}from"./smartphone-GGiwNneF.js";import{B as I}from"./badge-BucYuCBs.js";import{S as f,a as N,b as y,c as b,d as v}from"./select-CIhY0l9J.js";import{A as T,G as k}from"./app-layout-ox1kAwY6.js";import{A as d,F as E,ak as M}from"./ImpersonationBanner-CYn5eDk6.js";import{C as P}from"./calendar-B-u_QN2Q.js";import{L as _}from"./log-in-By89FsAx.js";import{S}from"./search-DBK6jUoc.js";import{M as R}from"./mail-CDon-vZy.js";import{S as U}from"./users-RYmOyic9.js";import{U as V}from"./user-DCnDRzMf.js";import{E as $}from"./eye-D-fsmYB2.js";import{P as z}from"./package-CoyvngX8.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./database-s9JOA0jY.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";const F=[{title:"Activity Log",href:"/activity"}],w=t=>{switch(t){case"login":return e.jsx(_,{className:"w-5 h-5 text-green-500"});case"logout":return e.jsx(M,{className:"w-5 h-5 text-gray-500"});case"search":return e.jsx(S,{className:"w-5 h-5 text-blue-500"});case"view_part":return e.jsx(z,{className:"w-5 h-5 text-purple-500"});case"view_category":return e.jsx(k,{className:"w-5 h-5 text-orange-500"});case"view_brand":return e.jsx(D,{className:"w-5 h-5 text-indigo-500"});case"view_model":return e.jsx($,{className:"w-5 h-5 text-teal-500"});case"subscription_change":return e.jsx(E,{className:"w-5 h-5 text-yellow-500"});case"profile_update":return e.jsx(V,{className:"w-5 h-5 text-pink-500"});case"password_change":return e.jsx(U,{className:"w-5 h-5 text-red-500"});case"email_verification":return e.jsx(R,{className:"w-5 h-5 text-cyan-500"});default:return e.jsx(d,{className:"w-5 h-5 text-gray-500"})}},H=t=>{switch(t){case"login":return"bg-green-100 text-green-800";case"logout":return"bg-gray-100 text-gray-800";case"search":return"bg-blue-100 text-blue-800";case"view_part":return"bg-purple-100 text-purple-800";case"view_category":return"bg-orange-100 text-orange-800";case"view_brand":return"bg-indigo-100 text-indigo-800";case"view_model":return"bg-teal-100 text-teal-800";case"subscription_change":return"bg-yellow-100 text-yellow-800";case"profile_update":return"bg-pink-100 text-pink-800";case"password_change":return"bg-red-100 text-red-800";case"email_verification":return"bg-cyan-100 text-cyan-800";default:return"bg-gray-100 text-gray-800"}};function be({activities:t,stats:i,activity_type_counts:m,filters:o,activity_types:h,date_ranges:C}){const j=(s,a)=>{const c=new URLSearchParams(window.location.search);a==="all"||a===""?c.delete(s):c.set(s,a),p.get(route("activity.index"),Object.fromEntries(c))},A=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),L=s=>!s||typeof s!="object"?null:Object.entries(s).map(([a,c])=>e.jsxs("span",{className:"text-xs text-gray-500",children:[a,": ",String(c)]},a));return e.jsxs(T,{breadcrumbs:F,children:[e.jsx(B,{title:"Activity Log"}),e.jsxs("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Activity Log"}),e.jsx("p",{className:"text-gray-600",children:"Track your account activity and usage history"})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsx(r,{children:e.jsx(l,{className:"p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(d,{className:"w-8 h-8 text-blue-500"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Activities"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:i.total_activities})]})]})})}),e.jsx(r,{children:e.jsx(l,{className:"p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(P,{className:"w-8 h-8 text-green-500"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Recent (7 days)"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:i.recent_activities})]})]})})}),e.jsx(r,{children:e.jsx(l,{className:"p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(_,{className:"w-8 h-8 text-purple-500"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Logins"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:i.login_count})]})]})})}),e.jsx(r,{children:e.jsx(l,{className:"p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(S,{className:"w-8 h-8 text-orange-500"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Searches"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:i.search_count})]})]})})})]}),Object.keys(m).length>0&&e.jsxs(r,{children:[e.jsxs(n,{children:[e.jsx(x,{children:"Activity Breakdown"}),e.jsx(g,{children:"Activity types for the selected time period"})]}),e.jsx(l,{children:e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:Object.entries(m).map(([s,a])=>e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"flex justify-center mb-2",children:w(s)}),e.jsx("p",{className:"text-lg font-bold text-gray-900",children:a}),e.jsx("p",{className:"text-xs text-gray-600 capitalize",children:s.replace("_"," ")})]},s))})})]}),e.jsxs(r,{children:[e.jsx(n,{children:e.jsx(x,{children:"Filters"})}),e.jsx(l,{children:e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Activity Type"}),e.jsxs(f,{value:o.activity_type,onValueChange:s=>j("activity_type",s),children:[e.jsx(N,{children:e.jsx(y,{placeholder:"Select activity type"})}),e.jsx(b,{children:Object.entries(h).map(([s,a])=>e.jsx(v,{value:s,children:a},s))})]})]}),e.jsxs("div",{className:"flex-1",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Date Range"}),e.jsxs(f,{value:o.date_range,onValueChange:s=>j("date_range",s),children:[e.jsx(N,{children:e.jsx(y,{placeholder:"Select date range"})}),e.jsx(b,{children:Object.entries(C).map(([s,a])=>e.jsx(v,{value:s,children:a},s))})]})]})]})})]}),e.jsxs(r,{children:[e.jsxs(n,{children:[e.jsx(x,{children:"Activity History"}),e.jsx(g,{children:t.meta&&t.meta.total>0?`Showing ${t.meta.from} to ${t.meta.to} of ${t.meta.total} activities`:"No activities found"})]}),e.jsx(l,{children:t.data&&t.data.length>0?e.jsx("div",{className:"space-y-4",children:t.data.map(s=>e.jsx("div",{className:"p-4 border rounded-lg transition-colors hover:bg-gray-50",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-start space-x-3 flex-1",children:[w(s.activity_type),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-900",children:s.description}),e.jsx(I,{className:`text-xs ${H(s.activity_type)}`,children:h[s.activity_type]||s.activity_type})]}),e.jsxs("div",{className:"flex items-center text-xs text-gray-500 space-x-4 mb-2",children:[e.jsx("span",{children:A(s.created_at)}),e.jsxs("span",{children:["IP: ",s.ip_address]}),s.performedBy&&e.jsxs("span",{children:["By: ",s.performedBy.name]})]}),s.metadata&&e.jsx("div",{className:"flex flex-wrap gap-2",children:L(s.metadata)})]})]}),e.jsx(O,{href:route("activity.show",s.id),children:e.jsx(u,{variant:"outline",size:"sm",children:"View Details"})})]})},s.id))}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx(d,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No activities found"}),e.jsx("p",{className:"text-gray-600",children:"No activities match your current filters."})]})})]}),t.meta&&t.meta.last_page>1&&e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"flex space-x-1",children:t.links&&t.links.map((s,a)=>e.jsx(u,{variant:s.active?"default":"outline",size:"sm",disabled:!s.url,onClick:()=>s.url&&p.get(s.url),dangerouslySetInnerHTML:{__html:s.label}},a))})})]})]})}export{be as default};
