import{r as d,j as e,Q as E,S as $}from"./app-J5EqS6dS.js";import{C as r,a as i,b as n,c as l,d as O}from"./card-9XCADs-4.js";import{B as h}from"./badge-BucYuCBs.js";import{B as U}from"./smartphone-GGiwNneF.js";import{I as b}from"./input-Bo8dOn9p.js";import{S as H,a as Q,b as V,c as P,d as w}from"./select-CIhY0l9J.js";import{A as R}from"./app-layout-ox1kAwY6.js";import{S as x}from"./shield-D9nQfigG.js";import{A as S}from"./ImpersonationBanner-CYn5eDk6.js";import{C as y}from"./calendar-B-u_QN2Q.js";import{C as _}from"./clock-Brl7_5s7.js";import{F as W}from"./filter-DKJvAZFg.js";import{S as q}from"./search-DBK6jUoc.js";import{U as z}from"./user-DCnDRzMf.js";import{C}from"./circle-check-big-DOFoatRy.js";import{T as G}from"./triangle-alert-BW76NKO9.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./index-BzZWUWqx.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./eye-D-fsmYB2.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";const J=({log:a})=>{const t=!a.ended_at;return e.jsx(h,{className:`flex items-center gap-1 ${t?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:t?e.jsxs(e.Fragment,{children:[e.jsx(S,{className:"h-3 w-3"}),"Active"]}):e.jsxs(e.Fragment,{children:[e.jsx(C,{className:"h-3 w-3"}),"Ended"]})})};function De({logs:a,stats:t,adminUsers:A,filters:c}){var u,N;const[m,T]=d.useState(c.admin_user_id||"all"),[j,k]=d.useState(c.date_from||""),[p,D]=d.useState(c.date_to||""),[f,F]=d.useState(c.active_only==="1"),I=()=>{$.get("/admin/impersonation/logs",{...c,admin_user_id:m==="all"?"":m,date_from:j,date_to:p,active_only:f?"1":""})},B=(s,g)=>{const L=new Date(s),M=(g?new Date(g):new Date).getTime()-L.getTime(),o=Math.floor(M/(1e3*60)),v=Math.floor(o/60);return v>0?`${v}h ${o%60}m`:`${o}m`};return e.jsxs(R,{children:[e.jsx(E,{title:"Impersonation Logs"}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:"Impersonation Logs"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Monitor and audit user impersonation sessions"})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(x,{className:"h-5 w-5 text-orange-600"})})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[e.jsxs(r,{children:[e.jsxs(i,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(n,{className:"text-sm font-medium",children:"Total Sessions"}),e.jsx(x,{className:"h-4 w-4 text-blue-600"})]}),e.jsx(l,{children:e.jsx("div",{className:"text-2xl font-bold text-foreground",children:(t==null?void 0:t.total_sessions)||0})})]}),e.jsxs(r,{children:[e.jsxs(i,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(n,{className:"text-sm font-medium",children:"Active Sessions"}),e.jsx(S,{className:"h-4 w-4 text-green-600"})]}),e.jsx(l,{children:e.jsx("div",{className:"text-2xl font-bold text-foreground",children:(t==null?void 0:t.active_sessions)||0})})]}),e.jsxs(r,{children:[e.jsxs(i,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(n,{className:"text-sm font-medium",children:"Today"}),e.jsx(y,{className:"h-4 w-4 text-orange-600"})]}),e.jsx(l,{children:e.jsx("div",{className:"text-2xl font-bold text-foreground",children:(t==null?void 0:t.sessions_today)||0})})]}),e.jsxs(r,{children:[e.jsxs(i,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(n,{className:"text-sm font-medium",children:"This Week"}),e.jsx(_,{className:"h-4 w-4 text-purple-600"})]}),e.jsx(l,{children:e.jsx("div",{className:"text-2xl font-bold text-foreground",children:(t==null?void 0:t.sessions_this_week)||0})})]})]}),e.jsxs(r,{children:[e.jsx(i,{children:e.jsxs(n,{className:"flex items-center gap-2",children:[e.jsx(W,{className:"h-5 w-5"}),"Filters"]})}),e.jsx(l,{children:e.jsxs("div",{className:"grid gap-4 md:grid-cols-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Admin User"}),e.jsxs(H,{value:m,onValueChange:T,children:[e.jsx(Q,{children:e.jsx(V,{placeholder:"All Admins"})}),e.jsxs(P,{children:[e.jsx(w,{value:"all",children:"All Admins"}),A.map(s=>e.jsx(w,{value:s.id.toString(),children:s.name},s.id))]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Date From"}),e.jsx(b,{type:"date",value:j,onChange:s=>k(s.target.value)})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Date To"}),e.jsx(b,{type:"date",value:p,onChange:s=>D(s.target.value)})]}),e.jsx("div",{className:"flex items-end",children:e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:f,onChange:s=>F(s.target.checked),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm font-medium",children:"Active Only"})]})}),e.jsx("div",{className:"flex items-end",children:e.jsxs(U,{onClick:I,className:"w-full",children:[e.jsx(q,{className:"h-4 w-4 mr-2"}),"Apply Filters"]})})]})})]}),e.jsxs(r,{children:[e.jsxs(i,{children:[e.jsx(n,{children:"Impersonation Sessions"}),e.jsxs(O,{children:[((u=a.meta)==null?void 0:u.total)||((N=a.data)==null?void 0:N.length)||0," sessions found"]})]}),e.jsx(l,{children:e.jsxs("div",{className:"space-y-4",children:[(a.data||[]).map(s=>e.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors",children:[e.jsx("div",{className:"flex items-center gap-4",children:e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(J,{log:s}),s.reason&&e.jsx(h,{variant:"outline",className:"text-xs",children:s.reason})]}),e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsx(z,{className:"h-4 w-4 text-blue-600"}),e.jsx("span",{className:"font-medium",children:s.admin_user.name}),e.jsx("span",{className:"text-muted-foreground",children:"impersonated"}),e.jsx("span",{className:"font-medium",children:s.target_user.name})]}),e.jsxs("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(y,{className:"h-3 w-3"}),e.jsxs("span",{children:["Started: ",new Date(s.started_at).toLocaleString()]})]}),s.ended_at&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(C,{className:"h-3 w-3"}),e.jsxs("span",{children:["Ended: ",new Date(s.ended_at).toLocaleString()]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(_,{className:"h-3 w-3"}),e.jsxs("span",{children:["Duration: ",B(s.started_at,s.ended_at)]})]}),s.ip_address&&e.jsxs("span",{children:["IP: ",s.ip_address]})]}),e.jsxs("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[e.jsxs("span",{children:["Admin: ",s.admin_user.email]}),e.jsxs("span",{children:["Target: ",s.target_user.email]})]})]})}),e.jsx("div",{className:"flex items-center gap-2",children:!s.ended_at&&e.jsxs(h,{className:"bg-red-100 text-red-800 flex items-center gap-1",children:[e.jsx(G,{className:"h-3 w-3"}),"Still Active"]})})]},s.id)),(!a.data||a.data.length===0)&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx(x,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-foreground mb-2",children:"No impersonation sessions found"}),e.jsx("p",{className:"text-muted-foreground",children:"No sessions match your current filters."})]})]})})]})]})})]})}export{De as default};
