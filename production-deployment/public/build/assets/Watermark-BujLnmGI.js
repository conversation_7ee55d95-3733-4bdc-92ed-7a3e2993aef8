import{j as l,r as s}from"./app-J5EqS6dS.js";import{a as h}from"./smartphone-GGiwNneF.js";function v(r,t=16,n=16){const e={};switch(r){case"top-left":e.top=`${n}px`,e.left=`${t}px`;break;case"top-right":e.top=`${n}px`,e.right=`${t}px`;break;case"bottom-left":e.bottom=`${n}px`,e.left=`${t}px`;break;case"bottom-right":e.bottom=`${n}px`,e.right=`${t}px`;break;case"center":e.top="50%",e.left="50%",e.transform="translate(-50%, -50%)";break}return e}function b(r,t,n){if(r==="custom"&&t&&n)return{width:`${t}px`,height:`${n}px`};const e={small:{width:"80px",height:"24px"},medium:{width:"120px",height:"36px"},large:{width:"160px",height:"48px"},xl:{width:"200px",height:"60px"},xxl:{width:"280px",height:"84px"}};return e[r]||e.medium}function T(r,t,n,e){const i=b(t,n,e);switch(r){case"single":return{containerStyles:{},itemStyles:{},count:1};case"repeat":return{containerStyles:{display:"grid",gridTemplateColumns:`repeat(auto-fit, minmax(${i.width}, 1fr))`,gridTemplateRows:`repeat(auto-fit, minmax(${i.height}, 1fr))`,gap:"15px",width:"100%",height:"100%",alignItems:"center",justifyItems:"center",justifyContent:"space-evenly",alignContent:"space-evenly",padding:"20px"},itemStyles:{width:i.width,height:i.height,maxWidth:"100%",maxHeight:"100%"},count:9};case"pattern":return{containerStyles:{display:"grid",gridTemplateColumns:"repeat(3, minmax(0, 1fr))",gridTemplateRows:"repeat(2, minmax(0, 1fr))",gap:"15px",justifyContent:"center",alignContent:"center",alignItems:"center",justifyItems:"center",width:"100%",height:"100%",padding:"20px"},itemStyles:{width:i.width,height:i.height,maxWidth:"100%",maxHeight:"100%"},count:6};default:return{containerStyles:{},itemStyles:{},count:1}}}function L(r,t,n,e=16,i=16,a,c,o="single"){const m=v(r,e,i),p=b(t,a,c);return o!=="single"?{position:"absolute",pointerEvents:"none",userSelect:"none",zIndex:10,opacity:n,top:0,left:0,right:0,bottom:0,width:"100%",height:"100%"}:{position:"absolute",pointerEvents:"none",userSelect:"none",zIndex:10,opacity:n,...m,...p}}function N({enabled:r=!0,logoUrl:t,text:n="Mobile Parts DB",position:e="bottom-right",opacity:i=.3,size:a="medium",customWidth:c=120,customHeight:o=40,offsetX:m=16,offsetY:p=16,repeat:u="single",showForUserType:S=!0,className:k}){const[x,d]=s.useState(!1),[j,g]=s.useState(!1);if(!r||!S)return null;const _=L(e,a,i,m,p,c,o,u),f=T(u,a,c,o),E=()=>{d(!0),g(!1)},I=()=>{d(!1),g(!0)};s.useEffect(()=>{t&&(d(!1),g(!1))},[t]);const $=t&&!x,C=!t||x,y=w=>l.jsxs("div",{className:h("watermark-item","transition-opacity duration-300"),style:{...f.itemStyles,...u!=="single"&&{opacity:i}},children:[$&&l.jsx("img",{src:t,alt:n,className:h("w-full h-full object-contain","transition-opacity duration-300",j?"opacity-100":"opacity-0"),onError:E,onLoad:I,draggable:!1}),C&&l.jsx("div",{className:h("flex items-center justify-center","w-full h-full","text-gray-600 font-medium","bg-white/80 rounded-sm","border border-gray-200","transition-opacity duration-300",a==="small"&&"text-xs px-2",a==="medium"&&"text-sm px-3",a==="large"&&"text-base px-4",a==="xl"&&"text-lg px-5",a==="xxl"&&"text-xl px-6",a==="custom"&&"text-sm px-2"),children:n})]},w);return l.jsx("div",{className:h("watermark-container","transition-opacity duration-300",k),style:_,children:u==="single"?y():l.jsx("div",{style:f.containerStyles,children:Array.from({length:f.count},(w,W)=>y(W))})})}function R(){const[r,t]=s.useState(null),[n,e]=s.useState(!0),[i,a]=s.useState(null);return s.useEffect(()=>{(async()=>{try{const o=await fetch("/api/watermark-config");if(!o.ok)throw new Error("Failed to fetch watermark configuration");const m=await o.json();t(m)}catch(o){a(o instanceof Error?o.message:"Unknown error"),t({enabled:!1,logo_url:"",text:"Mobile Parts DB",position:"bottom-right",opacity:.3,size:"medium",custom_width:120,custom_height:40,offset_x:16,offset_y:16,show_for_user:!1})}finally{e(!1)}})()},[]),{config:r,loading:n,error:i}}function P({className:r}){const{config:t,loading:n}=R();return n||!t?null:l.jsx(N,{enabled:t.enabled,logoUrl:t.logo_url,text:t.text,position:t.position,opacity:t.opacity,size:t.size,customWidth:t.custom_width,customHeight:t.custom_height,offsetX:t.offset_x,offsetY:t.offset_y,repeat:t.repeat,showForUserType:t.show_for_user,className:r})}export{P as A};
