import{r as d,j as e,Q as L,t as u,S as j}from"./app-J5EqS6dS.js";import{C as I,a as F,b as V,d as z,c as H}from"./card-9XCADs-4.js";import{B as p}from"./badge-BucYuCBs.js";import{B as h,f as M}from"./smartphone-GGiwNneF.js";import{I as U}from"./input-Bo8dOn9p.js";import{S as v,a as S,b as C,c as y,d as n}from"./select-CIhY0l9J.js";import{T as $,a as O,b as g,c as l,d as Q,e as t}from"./table-cw26P3lY.js";import{P as R}from"./pagination-C2CQsuj3.js";import{u as q}from"./use-delete-confirmation-CFAJok5Z.js";import{P as X,t as b}from"./ImpersonationBanner-CYn5eDk6.js";import{A as G,C as J}from"./app-layout-ox1kAwY6.js";import{S as K}from"./search-DBK6jUoc.js";import{F as W}from"./file-text-Dx6bYLtE.js";import{C as Y}from"./circle-check-big-DOFoatRy.js";import{U as Z}from"./user-DCnDRzMf.js";import{C as ee}from"./calendar-B-u_QN2Q.js";import{E as se}from"./eye-D-fsmYB2.js";import{S as ae}from"./square-pen-Bepbg6wc.js";import{T as te}from"./trash-2-B3ZEh4hl.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./chevron-left-C6ZNA5qQ.js";import"./triangle-alert-BW76NKO9.js";import"./index-BzZWUWqx.js";import"./crown-UDSxMtlm.js";import"./shield-D9nQfigG.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./zap-BcmHRR4K.js";import"./lock-Tx_yfI4R.js";import"./mail-CDon-vZy.js";import"./globe-zfFlVOSX.js";function $e({pages:a,filters:m,layouts:f}){const[i,w]=d.useState(m.search||""),[c,P]=d.useState(m.status||"all"),[o,T]=d.useState(m.layout||"all"),[re,N]=d.useState(!1),{showDeleteConfirmation:D}=q(),A=s=>{w(s.target.value)},E=s=>{P(s),x({search:i,status:s,layout:o})},_=s=>{T(s),x({search:i,status:c,layout:s})},x=s=>{N(!0),j.get("/admin/pages",{search:s.search||void 0,status:s.status&&s.status!=="all"?s.status:void 0,layout:s.layout&&s.layout!=="all"?s.layout:void 0},{preserveState:!0,replace:!0,onSuccess:()=>{N(!1)}})},k=s=>{s.preventDefault(),x({search:i,status:c,layout:o})},B=s=>{D({title:`Delete Page: ${s.title}`,description:"Are you sure you want to delete this page? This action cannot be undone.",confirmText:"Delete Page",cancelText:"Cancel",onConfirm:()=>{j.delete(`/admin/pages/${s.id}`,{onSuccess:()=>{b.success("Page deleted successfully")},onError:r=>{b.error("Failed to delete page",{description:r.message||"An error occurred while deleting the page"})}})}})};return e.jsxs(G,{children:[e.jsx(L,{title:"Page Management"}),e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Page Management"}),e.jsx(h,{asChild:!0,children:e.jsxs(u,{href:"/admin/pages/create",children:[e.jsx(X,{className:"mr-2 h-4 w-4"}),"Create Page"]})})]}),e.jsxs(I,{children:[e.jsxs(F,{children:[e.jsx(V,{children:"Pages"}),e.jsx(z,{children:"Manage website pages with rich content and SEO settings"})]}),e.jsx(H,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.jsx("form",{onSubmit:k,className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(K,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),e.jsx(U,{type:"search",placeholder:"Search pages...",className:"pl-8",value:i,onChange:A})]})}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs(v,{value:c,onValueChange:E,children:[e.jsx(S,{className:"w-[180px]",children:e.jsx(C,{placeholder:"Status"})}),e.jsxs(y,{children:[e.jsx(n,{value:"all",children:"All Status"}),e.jsx(n,{value:"published",children:"Published"}),e.jsx(n,{value:"draft",children:"Draft"})]})]}),e.jsxs(v,{value:o,onValueChange:_,children:[e.jsx(S,{className:"w-[180px]",children:e.jsx(C,{placeholder:"Layout"})}),e.jsxs(y,{children:[e.jsx(n,{value:"all",children:"All Layouts"}),Object.entries(f).map(([s,r])=>e.jsx(n,{value:s,children:r},s))]})]})]})]}),e.jsx("div",{className:"rounded-md border",children:e.jsxs($,{children:[e.jsx(O,{children:e.jsxs(g,{children:[e.jsx(l,{children:"Title"}),e.jsx(l,{children:"Status"}),e.jsx(l,{children:"Layout"}),e.jsx(l,{children:"Author"}),e.jsx(l,{children:"Published"}),e.jsx(l,{className:"text-right",children:"Actions"})]})}),e.jsx(Q,{children:a.data.length===0?e.jsx(g,{children:e.jsx(t,{colSpan:6,className:"text-center py-8 text-muted-foreground",children:"No pages found"})}):a.data.map(s=>{var r;return e.jsxs(g,{children:[e.jsx(t,{className:"font-medium",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(W,{className:"h-4 w-4 text-muted-foreground"}),e.jsxs("div",{children:[e.jsx("div",{children:s.title}),e.jsx("div",{className:"text-xs text-muted-foreground",children:s.slug})]})]})}),e.jsx(t,{children:s.is_published?e.jsxs(p,{variant:"default",className:"flex items-center gap-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100",children:[e.jsx(Y,{className:"h-3 w-3"}),"Published"]}):e.jsxs(p,{variant:"secondary",className:"flex items-center gap-1",children:[e.jsx(J,{className:"h-3 w-3"}),"Draft"]})}),e.jsx(t,{children:e.jsx(p,{variant:"outline",children:f[s.layout]||s.layout})}),e.jsx(t,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Z,{className:"h-3 w-3 text-muted-foreground"}),e.jsx("span",{children:((r=s.author)==null?void 0:r.name)||"Unknown"})]})}),e.jsx(t,{children:s.published_at?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ee,{className:"h-3 w-3 text-muted-foreground"}),e.jsx("span",{children:M(s.published_at)})]}):e.jsx("span",{className:"text-muted-foreground",children:"Not published"})}),e.jsx(t,{className:"text-right",children:e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx(h,{variant:"ghost",size:"icon",asChild:!0,children:e.jsxs(u,{href:s.url,target:"_blank",children:[e.jsx(se,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"View"})]})}),e.jsx(h,{variant:"ghost",size:"icon",asChild:!0,children:e.jsxs(u,{href:`/admin/pages/${s.id}/edit`,children:[e.jsx(ae,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Edit"})]})}),e.jsxs(h,{variant:"ghost",size:"icon",onClick:()=>B(s),children:[e.jsx(te,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Delete"})]})]})})]},s.id)})})]})}),a.last_page>1&&e.jsx(R,{currentPage:a.current_page,lastPage:a.last_page,from:a.from,to:a.to,total:a.total,onPageChange:s=>{j.get("/admin/pages",{page:s,search:i||void 0,status:c||void 0,layout:o||void 0},{preserveState:!0,replace:!0})}})]})})]})]})]})}export{$e as default};
