import{j as e,Q as j,t as a}from"./app-J5EqS6dS.js";import{S as u,B as r}from"./smartphone-GGiwNneF.js";import{C as i,c}from"./card-9XCADs-4.js";import{B as n}from"./badge-BucYuCBs.js";import{A as f}from"./Watermark-BujLnmGI.js";import{A as N}from"./arrow-left-D4U9AVF9.js";import{S as p}from"./search-DBK6jUoc.js";import{C as v}from"./circle-alert-C6UwDlxH.js";import{P as y}from"./package-CoyvngX8.js";import{C as w}from"./circle-check-big-DOFoatRy.js";import{E as k}from"./eye-D-fsmYB2.js";/* empty css            */function Q({results:s,query:t,guest_search_used:x,message:l,signup_url:d,partial_results_enabled:o=!1,max_visible_results:C=5,blur_intensity:_="medium",show_signup_cta:A=!0,is_last_search:m=!1,remaining_searches:F=0,searches_used:z=0,search_limit:B=3}){return e.jsxs(e.Fragment,{children:[e.jsx(j,{title:`Search Results for "${t}" - Mobile Parts Database`,children:e.jsx("meta",{name:"description",content:`Found ${s.total} mobile parts matching "${t}". Sign up for unlimited access to our comprehensive database.`})}),e.jsx("nav",{className:"sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200 dark:bg-gray-900/80 dark:border-gray-800",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center h-16",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(u,{className:"h-8 w-8 text-blue-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"FixHaat"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(a,{href:route("login"),children:e.jsx(r,{variant:"ghost",size:"sm",children:"Log in"})}),e.jsx(a,{href:route("register"),children:e.jsx(r,{size:"sm",children:"Sign up"})})]})]})})}),e.jsx("main",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-6 flex flex-col sm:flex-row gap-3 sm:gap-4",children:[e.jsx(a,{href:route("home"),children:e.jsxs(r,{variant:"ghost",className:"text-blue-600 hover:text-blue-700",children:[e.jsx(N,{className:"w-4 h-4 mr-2"}),"Back to Home"]})}),e.jsx(a,{href:route("home"),children:e.jsxs(r,{variant:"outline",className:"bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 hover:border-blue-300 dark:bg-blue-950/20 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-950/30",children:[e.jsx(p,{className:"w-4 h-4 mr-2"}),"Search Again"]})})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"Search Results"}),e.jsxs("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:["Found ",e.jsx("span",{className:"font-semibold",children:s.total})," results for",e.jsxs("span",{className:"font-semibold",children:[' "',t,'"']})]})]}),(m||x)&&e.jsx(i,{className:"mb-8 border-orange-200 bg-orange-50 dark:bg-orange-900/20 dark:border-orange-800",children:e.jsx(c,{className:"p-6",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(v,{className:"h-6 w-6 text-orange-600 mt-0.5"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-lg font-semibold text-orange-800 dark:text-orange-200 mb-2",children:m?"Last Free Search Used":"Free Search Used"}),e.jsx("p",{className:"text-orange-700 dark:text-orange-300 mb-2",children:l}),o&&s.blurred_count&&s.blurred_count>0&&e.jsxs("p",{className:"text-orange-600 dark:text-orange-400 text-sm mb-4",children:[e.jsx("strong",{children:"Note:"})," ",s.visible_count," results are fully visible,",s.blurred_count," additional results are partially hidden. Sign up to see all ",s.total," results clearly."]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[e.jsx(a,{href:d,children:e.jsx(r,{className:"bg-orange-600 hover:bg-orange-700",children:"Sign Up for Unlimited Access"})}),e.jsx(a,{href:route("login"),children:e.jsx(r,{variant:"outline",className:"border-orange-600 text-orange-600 hover:bg-orange-50",children:"Already have an account? Sign In"})})]})]})]})})}),e.jsx("div",{className:"space-y-6",children:s.data.length>0?e.jsx(e.Fragment,{children:s.data.map((h,b)=>{const g=o&&s.visible_count&&b===s.visible_count;return e.jsxs("div",{children:[g&&e.jsx(i,{className:"mb-6 border-blue-200 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800",children:e.jsxs(c,{className:"p-6 text-center",children:[e.jsxs("div",{className:"flex items-center justify-center mb-4",children:[e.jsx("div",{className:"h-px bg-gray-300 dark:bg-gray-600 flex-1"}),e.jsx("div",{className:"px-4",children:e.jsxs(n,{variant:"outline",className:"bg-blue-100 text-blue-800 border-blue-300",children:[s.blurred_count," More Results Available"]})}),e.jsx("div",{className:"h-px bg-gray-300 dark:bg-gray-600 flex-1"})]}),e.jsx("h3",{className:"text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2",children:"Sign up to see all results clearly"}),e.jsxs("p",{className:"text-blue-700 dark:text-blue-300 mb-4",children:["The results below are partially hidden. Create a free account to access all ",s.total," search results with full details."]}),e.jsx(a,{href:d,children:e.jsx(r,{className:"bg-blue-600 hover:bg-blue-700",children:"Sign Up Now - It's Free!"})})]})}),e.jsx(S,{part:h})]},h.id)})}):e.jsx(i,{className:"text-center py-12",children:e.jsxs(c,{children:[e.jsx(y,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"No parts found"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-6",children:"Try adjusting your search terms or browse our categories."}),e.jsx(a,{href:route("home"),children:e.jsx(r,{children:"Try Another Search"})})]})})}),s.total>s.per_page&&e.jsxs("div",{className:"mt-8 text-center",children:[e.jsxs("p",{className:"text-gray-600 dark:text-gray-300",children:["Showing ",s.data.length," of ",s.total," results"]}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-2",children:"Sign up to see all results and access advanced search features"})]}),e.jsx(i,{className:"mt-12 bg-blue-600 text-white border-0",children:e.jsxs(c,{className:"p-8 text-center",children:[e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Want to see more results?"}),e.jsx("p",{className:"text-blue-100 mb-6 text-lg",children:"Sign up now for unlimited searches and access to our complete database of mobile parts."}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[e.jsx(a,{href:d,children:e.jsx(r,{size:"lg",className:"bg-white text-blue-600 hover:bg-gray-100",children:"Start Free Trial"})}),e.jsx(a,{href:route("login"),children:e.jsx(r,{size:"lg",variant:"outline",className:"border-white text-white hover:bg-white hover:text-blue-600",children:"Sign In"})})]})]})})]})})]})}function S({part:s}){const t=s.is_blurred||!1,x=t?"blur-sm":"";return e.jsxs(i,{className:"hover:shadow-lg transition-shadow relative",children:[e.jsxs(c,{className:"p-6",children:[t&&e.jsx("div",{className:"absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white/80 dark:to-gray-800/80 z-10 rounded-lg flex items-end justify-center pb-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Sign up to see full details"}),e.jsx(a,{href:route("register"),children:e.jsx(r,{size:"sm",className:"bg-blue-600 hover:bg-blue-700 text-white",children:"View Details"})})]})}),e.jsx("div",{className:`flex flex-col lg:flex-row lg:items-start lg:space-x-6 ${x}`,children:e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-1",children:s.name}),e.jsxs("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Part #: ",s.part_number]})]}),e.jsx(n,{variant:"secondary",children:s.category.name})]}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4",children:s.description}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"text-sm font-semibold text-gray-900 dark:text-white mb-2",children:"Compatible Models:"}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[s.models.slice(0,3).map(l=>e.jsxs(n,{variant:"outline",className:"text-xs",children:[l.brand.name," ",l.name]},l.id)),s.models.length>3&&e.jsxs(n,{variant:"outline",className:"text-xs",children:["+",s.models.length-3," more"]})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400",children:e.jsxs("span",{className:"flex items-center",children:[e.jsx(w,{className:"w-4 h-4 mr-1 text-green-500"}),"Verified Compatible"]})}),e.jsx(a,{href:route("parts.show",s.slug||s.id),children:e.jsxs(r,{size:"sm",variant:"outline",className:"hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-950/20",children:[e.jsx(k,{className:"w-4 h-4 mr-2"}),"View Details"]})})]})]})})]}),e.jsx(f,{})]})}export{Q as default};
