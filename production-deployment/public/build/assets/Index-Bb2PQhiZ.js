import{r as m,j as e,Q as fe,t as h,S as f}from"./app-J5EqS6dS.js";import{C as A,a as X,b as Y,c as k,d as je}from"./card-9XCADs-4.js";import{B as v}from"./badge-BucYuCBs.js";import{B as t}from"./smartphone-GGiwNneF.js";import{I as ge}from"./input-Bo8dOn9p.js";import{L as Q}from"./label-BlOrdc-X.js";import{S as H,a as J,b as Z,c as q,d as C}from"./select-CIhY0l9J.js";import{A as ve,G as Ne}from"./app-layout-ox1kAwY6.js";import{u as Ce}from"./use-delete-confirmation-CFAJok5Z.js";import{U as we,P as ee,X as be,a as Se,t as x}from"./ImpersonationBanner-CYn5eDk6.js";import{F as _e}from"./file-text-Dx6bYLtE.js";import{D as Te}from"./download-fvx_BKiV.js";import{S as se}from"./search-DBK6jUoc.js";import{F as Ae}from"./filter-DKJvAZFg.js";import{L as ke}from"./list-CNjrM85i.js";import{T as De}from"./table-gSl3ppmW.js";import{P as te}from"./package-CoyvngX8.js";import{E as F}from"./external-link-A4n9PP4e.js";import{E as z}from"./eye-D-fsmYB2.js";import{S as P}from"./square-pen-Bepbg6wc.js";import{T as L}from"./trash-2-B3ZEh4hl.js";import{C as Ee}from"./chevron-left-C6ZNA5qQ.js";import{b as Ie,A as Fe,a as ze}from"./arrow-up-DSYswbzJ.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./users-RYmOyic9.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./mail-CDon-vZy.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";function bs({categories:r,filters:ae,queryParams:w}){const{showDeleteConfirmation:re}=Ce(),[i,O]=m.useState(w.search||""),[n,R]=m.useState(w.parent_id||"all"),[o,V]=m.useState(w.status||"all"),[c,B]=m.useState(w.sort_by||"sort_order"),[u,U]=m.useState(w.sort_order||"asc"),[y,le]=m.useState(!1),[d,$]=m.useState(w.view||"table"),[Pe,Le]=m.useState([]),[M,b]=m.useState(!1),[Oe,S]=m.useState(null),N=m.useRef(null),D=s=>{const a={page:s,...i&&{search:i},...n!=="all"&&{parent_id:n},...o!=="all"&&{status:o},...c!=="sort_order"&&{sort_by:c},...u!=="asc"&&{sort_order:u},...d!=="table"&&{view:d}};f.get("/admin/categories",a,{preserveState:!0,preserveScroll:!0})},G=()=>{const s={...i&&{search:i},...n!=="all"&&{parent_id:n},...o!=="all"&&{status:o},...c!=="sort_order"&&{sort_by:c},...u!=="asc"&&{sort_order:u},...d!=="table"&&{view:d}};f.get("/admin/categories",s,{preserveState:!0,preserveScroll:!1})},ie=()=>{O(""),R("all"),V("all"),B("sort_order"),U("asc"),$("table"),f.get("/admin/categories",{},{preserveState:!0,preserveScroll:!1})},j=s=>{const a=c===s&&u==="asc"?"desc":"asc";B(s),U(a);const l={...i&&{search:i},...n!=="all"&&{parent_id:n},...o!=="all"&&{status:o},sort_by:s,sort_order:a,...d!=="table"&&{view:d}};f.get("/admin/categories",l,{preserveState:!0,preserveScroll:!0})},g=s=>c!==s?e.jsx(Ie,{className:"h-4 w-4"}):u==="asc"?e.jsx(Fe,{className:"h-4 w-4"}):e.jsx(ze,{className:"h-4 w-4"}),E=s=>{$(s);const a={...i&&{search:i},...n!=="all"&&{parent_id:n},...o!=="all"&&{status:o},...c!=="sort_order"&&{sort_by:c},...u!=="asc"&&{sort_order:u},...s!=="table"&&{view:s}};f.get("/admin/categories",a,{preserveState:!0,preserveScroll:!0})},I=s=>{const a=s.parts_count&&s.parts_count>0?`This category has ${s.parts_count} part(s). This action cannot be undone.`:"This action cannot be undone.";re({title:`Delete "${s.name}"?`,description:a,onConfirm:()=>{f.delete(`/admin/categories/${s.id}`,{onSuccess:()=>{x.success(`Category "${s.name}" has been deleted successfully.`)},onError:l=>{const _=l.message||"Failed to delete category. It may have associated parts.";x.error(_)}})},onCancel:()=>{x.info("Delete cancelled")}})},ne=()=>{console.log("📥 EXPORT ALL BUTTON CLICKED");const s=new URLSearchParams;i&&s.append("search",i),n!=="all"&&s.append("parent_id",n),o!=="all"&&s.append("status",o),c!=="sort_order"&&s.append("sort_by",c),u!=="asc"&&s.append("sort_order",u),window.location.href=`/admin/categories/export?${s.toString()}`,x.success("Export started. Your download will begin shortly.")},oe=()=>{console.log("📄 TEMPLATE DOWNLOAD BUTTON CLICKED"),window.location.href="/admin/categories/template/download",x.success("Template download started.")},ce=()=>{var s;console.log("🔵 handleImport called"),console.log("Testing router object:",f),console.log("Router methods:",Object.keys(f)),console.log("fileInputRef.current:",N.current),(s=N.current)==null||s.click(),console.log("File input click triggered")},de=s=>{var l;console.log("🟡 handleFileChange called"),console.log("Event:",s),console.log("Files:",s.target.files);const a=(l=s.target.files)==null?void 0:l[0];if(console.log("Selected file:",a),!a){console.log("No file selected, returning");return}if(!a.name.toLowerCase().endsWith(".csv")){console.log("Invalid file type:",a.name),x.error("Please select a CSV file");return}console.log("Setting selected file and starting import"),S(a),me(a)},me=s=>{var a;if(console.log("=== CATEGORY IMPORT DEBUG START ==="),console.log("selectedFile:",s),!s){console.log("No file provided, returning early");return}console.log("Setting importing state..."),b(!0);try{console.log("Creating FormData...");const l=new FormData;l.append("file",s);const _=(a=document.querySelector('meta[name="csrf-token"]'))==null?void 0:a.getAttribute("content");_&&l.append("_token",_),console.log("FormData created successfully"),console.log("File name:",s.name),console.log("File size:",s.size),console.log("File type:",s.type),console.log("About to call router.post..."),f.post("/admin/bulk-import/categories",l,{forceFormData:!0,onStart:()=>{console.log("🚀 Request started - this should appear if router.post is called")},onProgress:p=>{console.log("📊 Upload progress:",p)},onSuccess:p=>{var K,W;console.log("✅ Success callback called"),console.log("Page received:",p);const T=(W=(K=p.props)==null?void 0:K.flash)==null?void 0:W.import_errors;T&&T.length>0&&(console.log("Import errors found:",T),T.forEach((ue,pe)=>{setTimeout(()=>{x.error(ue,{duration:8e3})},pe*100)})),b(!1),S(null),N.current&&(N.current.value="")},onError:p=>{console.log("❌ Error callback called"),console.error("Import errors:",p),p.file?x.error(`File error: ${p.file}`):p.message?x.error(p.message):x.error("Import failed. Please check your CSV format and try again."),b(!1),S(null),N.current&&(N.current.value="")},onFinish:()=>{console.log("🏁 Request finished")}}),console.log("router.post call completed")}catch(l){console.error("❌ Exception in handleImportConfirm:",l),x.error("An unexpected error occurred. Please try again."),b(!1),S(null)}console.log("=== CATEGORY IMPORT DEBUG END ===")},he=({category:s})=>e.jsx(A,{className:"hover:shadow-lg transition-shadow",children:e.jsx(k,{className:"p-4",children:e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"flex items-start justify-between",children:e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-semibold text-lg text-gray-900 mb-1 line-clamp-1",children:s.name}),s.description&&e.jsx("p",{className:"text-sm text-muted-foreground mb-2 line-clamp-2",children:s.description})]})}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsx(v,{variant:s.is_active?"default":"secondary",children:s.is_active?"Active":"Inactive"}),s.parent&&e.jsxs(v,{variant:"outline",children:["Child of ",s.parent.name]}),s.parts_count!==void 0&&e.jsxs(v,{variant:"outline",children:[s.parts_count," parts"]}),s.children&&s.children.length>0&&e.jsxs(v,{variant:"outline",children:[s.children.length," subcategories"]})]}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:["Sort Order: ",s.sort_order]}),e.jsxs("div",{className:"flex items-center gap-1 pt-2",children:[e.jsx(h,{href:route("categories.show",s.slug||s.id),children:e.jsx(t,{variant:"outline",size:"sm",title:"View Public Page",children:e.jsx(F,{className:"h-3 w-3"})})}),e.jsx(h,{href:`/admin/categories/${s.id}`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Admin View",children:e.jsx(z,{className:"h-3 w-3"})})}),e.jsx(h,{href:`/admin/categories/${s.id}/edit`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Edit",children:e.jsx(P,{className:"h-3 w-3"})})}),e.jsx(t,{variant:"outline",size:"sm",className:"text-destructive hover:text-destructive",onClick:()=>I(s),title:"Delete Category",children:e.jsx(L,{className:"h-3 w-3"})})]})]})})}),xe=()=>e.jsx("div",{className:"border rounded-lg overflow-hidden",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-muted/50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"text-left p-3 font-medium",children:e.jsxs(t,{variant:"ghost",size:"sm",onClick:()=>j("name"),className:"flex items-center gap-1 h-auto p-0 font-medium",children:["Name ",g("name")]})}),e.jsx("th",{className:"text-left p-3 font-medium",children:"Description"}),e.jsx("th",{className:"text-left p-3 font-medium",children:"Parent"}),e.jsx("th",{className:"text-left p-3 font-medium",children:e.jsxs(t,{variant:"ghost",size:"sm",onClick:()=>j("sort_order"),className:"flex items-center gap-1 h-auto p-0 font-medium",children:["Sort Order ",g("sort_order")]})}),e.jsx("th",{className:"text-left p-3 font-medium",children:e.jsxs(t,{variant:"ghost",size:"sm",onClick:()=>j("parts_count"),className:"flex items-center gap-1 h-auto p-0 font-medium",children:["Parts ",g("parts_count")]})}),e.jsx("th",{className:"text-left p-3 font-medium",children:"Status"}),e.jsx("th",{className:"text-left p-3 font-medium",children:e.jsxs(t,{variant:"ghost",size:"sm",onClick:()=>j("created_at"),className:"flex items-center gap-1 h-auto p-0 font-medium",children:["Created ",g("created_at")]})}),e.jsx("th",{className:"text-right p-3 font-medium",children:"Actions"})]})}),e.jsx("tbody",{children:r.data.map(s=>e.jsxs("tr",{className:"border-t hover:bg-muted/25",children:[e.jsx("td",{className:"p-3",children:e.jsx("div",{className:"font-medium",children:s.name})}),e.jsx("td",{className:"p-3 text-sm text-muted-foreground",children:s.description?e.jsx("span",{className:"line-clamp-1",children:s.description}):"-"}),e.jsx("td",{className:"p-3 text-sm text-muted-foreground",children:s.parent?s.parent.name:"Root"}),e.jsx("td",{className:"p-3 text-sm text-muted-foreground",children:s.sort_order}),e.jsx("td",{className:"p-3",children:e.jsx(v,{variant:"outline",children:s.parts_count||0})}),e.jsx("td",{className:"p-3",children:e.jsx(v,{variant:s.is_active?"default":"secondary",children:s.is_active?"Active":"Inactive"})}),e.jsx("td",{className:"p-3 text-sm text-muted-foreground",children:new Date(s.created_at).toLocaleDateString()}),e.jsx("td",{className:"p-3",children:e.jsxs("div",{className:"flex items-center gap-1 justify-end",children:[e.jsx(h,{href:route("categories.show",s.slug||s.id),children:e.jsx(t,{variant:"outline",size:"sm",title:"View Public Page",children:e.jsx(F,{className:"h-3 w-3"})})}),e.jsx(h,{href:`/admin/categories/${s.id}`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Admin View",children:e.jsx(z,{className:"h-3 w-3"})})}),e.jsx(h,{href:`/admin/categories/${s.id}/edit`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Edit",children:e.jsx(P,{className:"h-3 w-3"})})}),e.jsx(t,{variant:"outline",size:"sm",className:"text-destructive hover:text-destructive",onClick:()=>I(s),title:"Delete Category",children:e.jsx(L,{className:"h-3 w-3"})})]})})]},s.id))})]})})});return e.jsxs(ve,{children:[e.jsx(fe,{title:"Categories - Admin"}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Categories"}),e.jsx("p",{className:"text-muted-foreground",children:"Manage mobile part categories"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(t,{variant:"outline",className:"rounded-full border-border bg-transparent hover:bg-accent hover:text-accent-foreground px-6 py-2 h-auto transition-colors",onClick:()=>{console.log("📄 TEMPLATE DOWNLOAD BUTTON CLICKED"),oe()},title:"Download CSV Template",children:[e.jsx(_e,{className:"h-4 w-4 mr-2"}),"Template"]}),e.jsxs(t,{variant:"outline",className:"rounded-full border-border bg-transparent hover:bg-accent hover:text-accent-foreground px-6 py-2 h-auto transition-colors disabled:opacity-50",onClick:()=>{console.log("📤 IMPORT BUTTON CLICKED"),ce()},disabled:M,title:"Import Categories from CSV",children:[e.jsx(we,{className:"h-4 w-4 mr-2"}),M?"Importing...":"Import"]}),e.jsxs(t,{variant:"outline",className:"rounded-full border-border bg-transparent hover:bg-accent hover:text-accent-foreground px-6 py-2 h-auto transition-colors",onClick:()=>{console.log("📥 EXPORT ALL BUTTON CLICKED"),ne()},title:"Export All Categories to CSV",children:[e.jsx(Te,{className:"h-4 w-4 mr-2"}),"Export All"]})]}),e.jsx(h,{href:"/admin/categories/create",children:e.jsxs(t,{children:[e.jsx(ee,{className:"h-4 w-4 mr-2"}),"Add Category"]})})]})]}),e.jsx("input",{ref:N,type:"file",accept:".csv",onChange:de,className:"hidden"}),e.jsxs(A,{children:[e.jsx(X,{children:e.jsxs(Y,{className:"flex items-center gap-2",children:[e.jsx(se,{className:"h-5 w-5"}),"Search & Filter"]})}),e.jsx(k,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex gap-3",children:[e.jsxs("div",{className:"flex-1 relative",children:[e.jsx(se,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),e.jsx(ge,{placeholder:"Search categories by name, description, or parent category...",value:i,onChange:s=>O(s.target.value),onKeyDown:s=>s.key==="Enter"&&G(),className:"pl-10"})]}),e.jsx(t,{onClick:G,children:"Search"}),e.jsxs(t,{variant:"outline",onClick:()=>le(!y),children:[e.jsx(Ae,{className:"h-4 w-4 mr-2"}),"Filters"]}),(i||n!=="all"||o!=="all")&&e.jsxs(t,{variant:"outline",onClick:ie,children:[e.jsx(be,{className:"h-4 w-4 mr-2"}),"Clear"]})]}),y&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-lg bg-muted/20",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(Q,{htmlFor:"parent-filter",children:"Parent Category"}),e.jsxs(H,{value:n,onValueChange:R,children:[e.jsx(J,{children:e.jsx(Z,{placeholder:"All categories"})}),e.jsxs(q,{children:[e.jsx(C,{value:"all",children:"All categories"}),e.jsx(C,{value:"root",children:"Root categories only"}),e.jsx(C,{value:"child",children:"Child categories only"}),ae.parentCategories.map(s=>e.jsx(C,{value:s.id.toString(),children:s.name},s.id))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(Q,{htmlFor:"status-filter",children:"Status"}),e.jsxs(H,{value:o,onValueChange:V,children:[e.jsx(J,{children:e.jsx(Z,{placeholder:"All statuses"})}),e.jsxs(q,{children:[e.jsx(C,{value:"all",children:"All statuses"}),e.jsx(C,{value:"active",children:"Active only"}),e.jsx(C,{value:"inactive",children:"Inactive only"})]})]})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"View:"}),e.jsx(t,{variant:d==="list"?"default":"outline",size:"sm",onClick:()=>E("list"),title:"List View",children:e.jsx(ke,{className:"h-4 w-4"})}),e.jsx(t,{variant:d==="grid"?"default":"outline",size:"sm",onClick:()=>E("grid"),title:"Grid View",children:e.jsx(Ne,{className:"h-4 w-4"})}),e.jsx(t,{variant:d==="table"?"default":"outline",size:"sm",onClick:()=>E("table"),title:"Table View",children:e.jsx(De,{className:"h-4 w-4"})})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Sort by:"}),e.jsxs(t,{variant:c==="name"?"default":"outline",size:"sm",onClick:()=>j("name"),className:"flex items-center gap-1",children:["Name ",g("name")]}),e.jsxs(t,{variant:c==="sort_order"?"default":"outline",size:"sm",onClick:()=>j("sort_order"),className:"flex items-center gap-1",children:["Order ",g("sort_order")]}),e.jsxs(t,{variant:c==="created_at"?"default":"outline",size:"sm",onClick:()=>j("created_at"),className:"flex items-center gap-1",children:["Date ",g("created_at")]}),e.jsxs(t,{variant:c==="parts_count"?"default":"outline",size:"sm",onClick:()=>j("parts_count"),className:"flex items-center gap-1",children:["Parts ",g("parts_count")]})]})]})]})})]}),e.jsxs(A,{children:[e.jsxs(X,{children:[e.jsxs(Y,{className:"flex items-center gap-2",children:[e.jsx(te,{className:"h-5 w-5"}),"All Categories"]}),e.jsxs(je,{children:[r.total," categories total"]})]}),e.jsx(k,{children:r.data.length>0?e.jsx(e.Fragment,{children:d==="table"?e.jsx(xe,{}):d==="grid"?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:r.data.map(s=>e.jsx(he,{category:s},s.id))}):e.jsx("div",{className:"space-y-4",children:r.data.map(s=>e.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h3",{className:"font-medium",children:s.name}),e.jsx(v,{variant:s.is_active?"default":"secondary",children:s.is_active?"Active":"Inactive"}),s.parent&&e.jsxs(v,{variant:"outline",children:["Child of ",s.parent.name]})]}),s.description&&e.jsx("p",{className:"text-sm text-muted-foreground",children:s.description}),e.jsxs("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[e.jsxs("span",{children:["Sort Order: ",s.sort_order]}),s.parts_count!==void 0&&e.jsxs("span",{children:[s.parts_count," parts"]}),s.children&&s.children.length>0&&e.jsxs("span",{children:[s.children.length," subcategories"]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(h,{href:route("categories.show",s.slug||s.id),children:e.jsx(t,{variant:"outline",size:"sm",title:"View Public Page",children:e.jsx(F,{className:"h-4 w-4"})})}),e.jsx(h,{href:`/admin/categories/${s.id}`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Admin View",children:e.jsx(z,{className:"h-4 w-4"})})}),e.jsx(h,{href:`/admin/categories/${s.id}/edit`,children:e.jsx(t,{variant:"outline",size:"sm",title:"Edit",children:e.jsx(P,{className:"h-4 w-4"})})}),e.jsx(t,{variant:"outline",size:"sm",className:"text-destructive hover:text-destructive",onClick:()=>I(s),title:"Delete",children:e.jsx(L,{className:"h-4 w-4"})})]})]},s.id))})}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(te,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No categories found"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:i||n!=="all"||o!=="all"?"Try adjusting your search or filter criteria.":"Get started by creating your first category."}),!(i||n!=="all"||o!=="all")&&e.jsx(h,{href:"/admin/categories/create",children:e.jsxs(t,{children:[e.jsx(ee,{className:"h-4 w-4 mr-2"}),"Add Category"]})})]})})]}),r.last_page>1&&e.jsx(A,{children:e.jsx(k,{className:"p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"text-sm text-muted-foreground",children:["Showing ",r.from," to ",r.to," of ",r.total," categories"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(t,{variant:"outline",size:"sm",onClick:()=>D(r.current_page-1),disabled:r.current_page===1,children:[e.jsx(Ee,{className:"h-4 w-4"}),"Previous"]}),e.jsx("div",{className:"flex items-center gap-1",children:Array.from({length:Math.min(5,r.last_page)},(s,a)=>{const l=Math.max(1,Math.min(r.last_page-4,r.current_page-2))+a;return l>r.last_page?null:e.jsx(t,{variant:l===r.current_page?"default":"outline",size:"sm",onClick:()=>D(l),className:"w-8 h-8 p-0",children:l},l)})}),e.jsxs(t,{variant:"outline",size:"sm",onClick:()=>D(r.current_page+1),disabled:r.current_page===r.last_page,children:["Next",e.jsx(Se,{className:"h-4 w-4"})]})]})]})})})]})})]})}export{bs as default};
