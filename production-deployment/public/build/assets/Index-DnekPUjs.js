import{r as p,j as e,Q as G,t as B,S as h}from"./app-J5EqS6dS.js";import{C as c,a as o,b as d,c as m,d as K}from"./card-9XCADs-4.js";import{B as k}from"./badge-BucYuCBs.js";import{c as L,B as i}from"./smartphone-GGiwNneF.js";import{I as W}from"./input-Bo8dOn9p.js";import{S as w,a as C,b as _,c as U,d as a}from"./select-CIhY0l9J.js";import{A as Y}from"./app-layout-ox1kAwY6.js";import{I as Z}from"./ImpersonationSecurityCheck-Bzbl0nMh.js";import{k as ee,l as se,o as te,w as y,a as ae}from"./ImpersonationBanner-CYn5eDk6.js";import{U as D}from"./user-plus-BlEMjRvh.js";import{U as E}from"./users-RYmOyic9.js";import{C as re}from"./clock-Brl7_5s7.js";import{F as le}from"./filter-DKJvAZFg.js";import{S as ne}from"./search-DBK6jUoc.js";import{E as z}from"./eye-D-fsmYB2.js";import{E as ie}from"./ellipsis-Bwr8pvFI.js";import{C as ce}from"./circle-check-big-DOFoatRy.js";import{B as oe}from"./ban-Dctu8q_b.js";import{C as de}from"./chevron-left-C6ZNA5qQ.js";/* empty css            */import"./index-CJpBU2i9.js";import"./index-D86BnqlV.js";import"./index-Ba8m9N9L.js";import"./chevron-down-C6yPNer6.js";import"./check-C7SdgHPn.js";import"./index-BzZWUWqx.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./label-BlOrdc-X.js";import"./textarea-BDEiXlPH.js";import"./crown-UDSxMtlm.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const me=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]],F=L("UserCheck",me);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xe=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]],pe=L("UserX",xe),he=({status:t})=>{const r={active:"bg-green-100 text-green-800",pending:"bg-yellow-100 text-yellow-800",suspended:"bg-red-100 text-red-800",banned:"bg-gray-100 text-gray-800"};return e.jsx(k,{className:r[t],children:t.charAt(0).toUpperCase()+t.slice(1)})},ue=({status:t})=>{const r={approved:"bg-green-100 text-green-800",pending:"bg-yellow-100 text-yellow-800",rejected:"bg-red-100 text-red-800"};return e.jsx(k,{className:r[t],children:t.charAt(0).toUpperCase()+t.slice(1)})};function ss({users:t,stats:r,filters:x}){var I,P,M;const[b,T]=p.useState(x.search||""),[u,$]=p.useState(x.status||"all"),[j,V]=p.useState(x.approval_status||"all"),[f,H]=p.useState(x.subscription_plan||"all"),[R,g]=p.useState(!1),[N,v]=p.useState(null),S=s=>{h.get("/admin/users",{...x,page:s,search:b,status:u==="all"?"":u,approval_status:j==="all"?"":j,subscription_plan:f==="all"?"":f},{preserveState:!0,preserveScroll:!0})},A=()=>{h.get("/admin/users",{...x,search:b,status:u==="all"?"":u,approval_status:j==="all"?"":j,subscription_plan:f==="all"?"":f})},q=s=>{h.post(`/admin/users/${s}/approve`,{},{preserveScroll:!0})},Q=s=>{const l=prompt("Please provide a reason for suspension:");l&&h.post(`/admin/users/${s}/suspend`,{reason:l},{preserveScroll:!0})},X=s=>{h.post(`/admin/users/${s}/unsuspend`,{},{preserveScroll:!0})},J=s=>{v(s),g(!0)},O=(s,l)=>{N&&h.post(`/admin/impersonate/${N.id}`,{reason:s,duration:l},{onSuccess:()=>{g(!1),v(null)},onError:()=>{g(!1),v(null)}})};return e.jsxs(Y,{children:[e.jsx(G,{title:"User Management"}),e.jsx("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:"User Management"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Manage user accounts, approvals, and permissions"})]}),e.jsx(B,{href:"/admin/users/create",children:e.jsxs(i,{children:[e.jsx(D,{className:"h-4 w-4 mr-2"}),"Create User"]})})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-5",children:[e.jsxs(c,{children:[e.jsxs(o,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(d,{className:"text-sm font-medium",children:"Total Users"}),e.jsx(E,{className:"h-4 w-4 text-blue-600"})]}),e.jsx(m,{children:e.jsx("div",{className:"text-2xl font-bold text-foreground",children:r.total_users})})]}),e.jsxs(c,{children:[e.jsxs(o,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(d,{className:"text-sm font-medium",children:"Active Users"}),e.jsx(F,{className:"h-4 w-4 text-green-600"})]}),e.jsx(m,{children:e.jsx("div",{className:"text-2xl font-bold text-foreground",children:r.active_users})})]}),e.jsxs(c,{children:[e.jsxs(o,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(d,{className:"text-sm font-medium",children:"Pending Approval"}),e.jsx(re,{className:"h-4 w-4 text-yellow-600"})]}),e.jsx(m,{children:e.jsx("div",{className:"text-2xl font-bold text-foreground",children:r.pending_approval})})]}),e.jsxs(c,{children:[e.jsxs(o,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(d,{className:"text-sm font-medium",children:"Suspended"}),e.jsx(pe,{className:"h-4 w-4 text-red-600"})]}),e.jsx(m,{children:e.jsx("div",{className:"text-2xl font-bold text-foreground",children:r.suspended_users})})]}),e.jsxs(c,{children:[e.jsxs(o,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(d,{className:"text-sm font-medium",children:"Premium Users"}),e.jsx(D,{className:"h-4 w-4 text-purple-600"})]}),e.jsx(m,{children:e.jsx("div",{className:"text-2xl font-bold text-foreground",children:r.premium_users})})]})]}),e.jsxs(c,{children:[e.jsx(o,{children:e.jsxs(d,{className:"flex items-center gap-2",children:[e.jsx(le,{className:"h-5 w-5"}),"Filters"]})}),e.jsx(m,{children:e.jsxs("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-5",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(W,{placeholder:"Search users...",value:b,onChange:s=>T(s.target.value),className:"flex-1"}),e.jsx(i,{onClick:A,children:e.jsx(ne,{className:"h-4 w-4"})})]}),e.jsxs(w,{value:u,onValueChange:$,children:[e.jsx(C,{children:e.jsx(_,{placeholder:"Status"})}),e.jsxs(U,{children:[e.jsx(a,{value:"all",children:"All Statuses"}),e.jsx(a,{value:"active",children:"Active"}),e.jsx(a,{value:"pending",children:"Pending"}),e.jsx(a,{value:"suspended",children:"Suspended"}),e.jsx(a,{value:"banned",children:"Banned"})]})]}),e.jsxs(w,{value:j,onValueChange:V,children:[e.jsx(C,{children:e.jsx(_,{placeholder:"Approval Status"})}),e.jsxs(U,{children:[e.jsx(a,{value:"all",children:"All Approval Statuses"}),e.jsx(a,{value:"approved",children:"Approved"}),e.jsx(a,{value:"pending",children:"Pending"}),e.jsx(a,{value:"rejected",children:"Rejected"})]})]}),e.jsxs(w,{value:f,onValueChange:H,children:[e.jsx(C,{children:e.jsx(_,{placeholder:"Subscription Plan"})}),e.jsxs(U,{children:[e.jsx(a,{value:"all",children:"All Plans"}),e.jsx(a,{value:"free",children:"Free"}),e.jsx(a,{value:"premium",children:"Premium"})]})]}),e.jsx(i,{onClick:A,className:"w-full",children:"Apply Filters"})]})})]}),e.jsxs(c,{children:[e.jsxs(o,{children:[e.jsx(d,{children:"Users"}),e.jsxs(K,{children:[((I=t.meta)==null?void 0:I.total)||((P=t.data)==null?void 0:P.length)||0," users found"]})]}),e.jsxs(m,{children:[e.jsxs("div",{className:"overflow-x-auto",children:[e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b border-border",children:[e.jsx("th",{className:"text-left py-3 px-4 font-medium text-sm text-muted-foreground",children:"Name"}),e.jsx("th",{className:"text-left py-3 px-4 font-medium text-sm text-muted-foreground",children:"Email"}),e.jsx("th",{className:"text-left py-3 px-4 font-medium text-sm text-muted-foreground",children:"Status"}),e.jsx("th",{className:"text-left py-3 px-4 font-medium text-sm text-muted-foreground",children:"Approval"}),e.jsx("th",{className:"text-left py-3 px-4 font-medium text-sm text-muted-foreground",children:"Searches"}),e.jsx("th",{className:"text-left py-3 px-4 font-medium text-sm text-muted-foreground",children:"Logins"}),e.jsx("th",{className:"text-left py-3 px-4 font-medium text-sm text-muted-foreground",children:"Joined"}),e.jsx("th",{className:"text-right py-3 px-4 font-medium text-sm text-muted-foreground",children:"Actions"})]})}),e.jsx("tbody",{children:((M=t.data)==null?void 0:M.map(s=>e.jsxs("tr",{className:"border-b border-border hover:bg-accent/50 transition-colors",children:[e.jsx("td",{className:"py-3 px-4",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("p",{className:"text-sm font-medium text-foreground",children:s.name}),s.subscription_plan==="premium"&&e.jsx(k,{className:"bg-purple-100 text-purple-800 text-xs",children:"Premium"})]})}),e.jsx("td",{className:"py-3 px-4",children:e.jsx("p",{className:"text-sm text-muted-foreground",children:s.email})}),e.jsx("td",{className:"py-3 px-4",children:e.jsx(he,{status:s.status})}),e.jsx("td",{className:"py-3 px-4",children:e.jsx(ue,{status:s.approval_status})}),e.jsx("td",{className:"py-3 px-4",children:e.jsx("p",{className:"text-sm text-foreground",children:s.searches_count})}),e.jsx("td",{className:"py-3 px-4",children:e.jsx("p",{className:"text-sm text-foreground",children:s.login_count})}),e.jsx("td",{className:"py-3 px-4",children:e.jsx("p",{className:"text-sm text-muted-foreground",children:new Date(s.created_at).toLocaleDateString()})}),e.jsx("td",{className:"py-3 px-4",children:e.jsxs("div",{className:"flex items-center justify-end gap-1",children:[e.jsx(B,{href:`/admin/users/${s.id}`,children:e.jsx(i,{variant:"outline",size:"sm",title:"View User",children:e.jsx(z,{className:"h-3 w-3"})})}),e.jsxs(ee,{children:[e.jsx(se,{asChild:!0,children:e.jsx(i,{variant:"outline",size:"sm",title:"More Actions",children:e.jsx(ie,{className:"h-3 w-3"})})}),e.jsxs(te,{align:"end",children:[s.approval_status==="pending"&&e.jsxs(y,{onClick:()=>q(s.id),children:[e.jsx(ce,{className:"h-4 w-4 mr-2"}),"Approve User"]}),s.status==="active"&&e.jsxs(y,{onClick:()=>Q(s.id),children:[e.jsx(oe,{className:"h-4 w-4 mr-2"}),"Suspend User"]}),s.status==="suspended"&&e.jsxs(y,{onClick:()=>X(s.id),children:[e.jsx(F,{className:"h-4 w-4 mr-2"}),"Unsuspend User"]}),e.jsxs(y,{onClick:()=>J(s),children:[e.jsx(z,{className:"h-4 w-4 mr-2"}),"Login as User"]})]})]})]})})]},s.id)))||[]})]}),(!t.data||t.data.length===0)&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx(E,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-foreground mb-2",children:"No users found"}),e.jsx("p",{className:"text-muted-foreground",children:"No users match your current filters."})]})]}),t.meta&&t.meta.last_page>1&&e.jsxs("div",{className:"flex items-center justify-between pt-4 border-t",children:[e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Showing ",t.meta.from," to ",t.meta.to," of ",t.meta.total," users"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(i,{variant:"outline",size:"sm",onClick:()=>S(t.meta.current_page-1),disabled:t.meta.current_page===1,children:[e.jsx(de,{className:"w-4 h-4 mr-1"}),"Previous"]}),Array.from({length:Math.min(5,t.meta.last_page)},(s,l)=>{let n;return t.meta.last_page<=5||t.meta.current_page<=3?n=l+1:t.meta.current_page>=t.meta.last_page-2?n=t.meta.last_page-4+l:n=t.meta.current_page-2+l,e.jsx(i,{variant:n===t.meta.current_page?"default":"outline",size:"sm",onClick:()=>S(n),children:n},n)}),e.jsxs(i,{variant:"outline",size:"sm",onClick:()=>S(t.meta.current_page+1),disabled:t.meta.current_page===t.meta.last_page,children:["Next",e.jsx(ae,{className:"w-4 h-4 ml-1"})]})]})]})]})]})]})}),N&&e.jsx(Z,{user:N,isOpen:R,onClose:()=>{g(!1),v(null)},onConfirm:O})]})}export{ss as default};
