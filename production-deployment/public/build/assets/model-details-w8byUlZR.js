import{J as u,r as o,j as s,Q as b,t as a,S as N}from"./app-J5EqS6dS.js";import{S as h,B as i}from"./smartphone-GGiwNneF.js";import{C as n,c,a as m,b as x}from"./card-9XCADs-4.js";import{B as v}from"./badge-BucYuCBs.js";import{A as y}from"./app-layout-ox1kAwY6.js";import{H as w,t as S}from"./ImpersonationBanner-CYn5eDk6.js";import{H as _}from"./hash-Bk6gEERd.js";import{C as A}from"./calendar-B-u_QN2Q.js";import{S as C}from"./search-DBK6jUoc.js";import{B}from"./building-Dgyml3QN.js";import{A as F}from"./arrow-left-D4U9AVF9.js";/* empty css            */import"./index-D86BnqlV.js";import"./index-CJpBU2i9.js";import"./index-BzZWUWqx.js";import"./users-RYmOyic9.js";import"./shield-D9nQfigG.js";import"./lock-Tx_yfI4R.js";import"./file-text-Dx6bYLtE.js";import"./mail-CDon-vZy.js";import"./package-CoyvngX8.js";import"./database-s9JOA0jY.js";import"./user-DCnDRzMf.js";import"./eye-D-fsmYB2.js";import"./triangle-alert-BW76NKO9.js";import"./globe-zfFlVOSX.js";import"./zap-BcmHRR4K.js";import"./crown-UDSxMtlm.js";function ts({model:e}){const{auth:p}=u().props,[l,d]=o.useState(!1),[t,j]=o.useState(!1),f=()=>{if(!p.user){S.error("Please log in to add items to favorites",{description:"You need to be logged in to save favorites."});return}l||t||(d(!0),N.post(route("dashboard.add-favorite"),{type:"model",id:e.id},{onSuccess:()=>{j(!0),d(!1)},onError:r=>{d(!1)}}))};return s.jsxs(y,{children:[s.jsx(b,{title:`${e.brand.name} ${e.name}`}),s.jsx("div",{className:"py-12",children:s.jsxs("div",{className:"max-w-7xl mx-auto sm:px-6 lg:px-8",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-6 text-sm text-gray-600",children:[s.jsx(a,{href:route("search.index"),className:"hover:text-gray-900",children:"Search"}),s.jsx("span",{children:"/"}),s.jsx(a,{href:route("brands.show",e.brand.slug||e.brand.id),className:"hover:text-gray-900",children:e.brand.name}),s.jsx("span",{children:"/"}),s.jsx("span",{className:"text-gray-900",children:e.name})]}),s.jsxs("div",{className:"grid lg:grid-cols-3 gap-8",children:[s.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[s.jsx(n,{children:s.jsx(c,{className:"p-6",children:s.jsxs("div",{className:"flex items-start gap-4 mb-4",children:[s.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center",children:e.brand.logo_url?s.jsx("img",{src:e.brand.logo_url,alt:`${e.brand.name} logo`,className:"w-full h-full object-contain"}):s.jsx(h,{className:"w-8 h-8 text-gray-400"})}),s.jsxs("div",{className:"flex-1",children:[s.jsxs("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:[e.brand.name," ",e.name]}),s.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-600 mb-4",children:[e.model_number&&s.jsxs("span",{className:"flex items-center gap-1",children:[s.jsx(_,{className:"w-4 h-4"}),e.model_number]}),e.release_year&&s.jsxs("span",{className:"flex items-center gap-1",children:[s.jsx(A,{className:"w-4 h-4"}),e.release_year]}),s.jsx(v,{variant:e.is_active?"default":"secondary",children:e.is_active?"Active":"Inactive"})]})]})]})})}),e.specifications&&Object.keys(e.specifications).length>0&&s.jsxs(n,{children:[s.jsx(m,{children:s.jsx(x,{children:"Specifications"})}),s.jsx(c,{children:s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Object.entries(e.specifications).map(([r,g])=>s.jsxs("div",{className:"flex justify-between py-2 border-b border-gray-100 last:border-0",children:[s.jsxs("span",{className:"font-medium text-gray-600 capitalize",children:[r.replace(/_/g," "),":"]}),s.jsx("span",{className:"text-gray-900",children:g})]},r))})})]})]}),s.jsxs("div",{className:"space-y-6",children:[s.jsxs(n,{children:[s.jsx(m,{children:s.jsxs(x,{className:"flex items-center gap-2",children:[s.jsx(h,{className:"w-5 h-5"}),"Model Information"]})}),s.jsx(c,{children:s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Brand:"}),s.jsx(a,{href:route("brands.show",e.brand.slug||e.brand.id),className:"ml-2 text-blue-600 hover:text-blue-800",children:e.brand.name})]}),e.model_number&&s.jsxs("div",{children:[s.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Model Number:"}),s.jsx("span",{className:"ml-2 text-gray-900",children:e.model_number})]}),e.release_year&&s.jsxs("div",{children:[s.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Release Year:"}),s.jsx("span",{className:"ml-2 text-gray-900",children:e.release_year})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Categories:"}),s.jsx("span",{className:"ml-2 text-gray-900",children:new Set(e.parts.map(r=>r.category.name)).size})]})]})})]}),s.jsxs(n,{children:[s.jsx(m,{children:s.jsx(x,{children:"Actions"})}),s.jsx(c,{children:s.jsxs("div",{className:"space-y-3",children:[s.jsxs(i,{className:"w-full bg-green-600 hover:bg-green-700 text-white",onClick:f,disabled:t||l,children:[s.jsx(w,{className:`w-4 h-4 mr-2 ${t?"fill-current":""}`}),t?"Added to Favorites":l?"Adding...":"Add to Favorites"]}),s.jsx(a,{href:route("search.brand",e.brand.slug||e.brand.id)+`?q=${encodeURIComponent(e.name)}`,children:s.jsxs(i,{className:"w-full",children:[s.jsx(C,{className:"w-4 h-4 mr-2"}),"Search ",e.name," Parts"]})}),s.jsx(a,{href:route("brands.show",e.brand.slug||e.brand.id),children:s.jsxs(i,{className:"w-full",variant:"outline",children:[s.jsx(B,{className:"w-4 h-4 mr-2"}),"View Brand"]})}),s.jsx(a,{href:route("search.index"),children:s.jsxs(i,{className:"w-full",variant:"outline",children:[s.jsx(F,{className:"w-4 h-4 mr-2"}),"Back to Search"]})})]})})]})]})]})]})})]})}export{ts as default};
