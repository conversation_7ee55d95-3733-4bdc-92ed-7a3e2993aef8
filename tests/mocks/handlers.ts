import { http, HttpResponse } from 'msw';

// Mock API handlers for testing
export const handlers = [
  // Pricing plans API endpoints
  http.get('/api/pricing-plans', () => {
    return HttpResponse.json({
      success: true,
      data: {
        plans: [
          {
            id: 1,
            name: 'free',
            display_name: 'Free Plan',
            description: 'Perfect for occasional searches',
            price: 0,
            currency: 'USD',
            interval: 'month',
            features: ['20 searches per day', 'Basic part information', 'Email support'],
            search_limit: 20,
            is_popular: false,
            formatted_price: 'Free',
            metadata: {},
          },
          {
            id: 2,
            name: 'premium',
            display_name: 'Premium Plan',
            description: 'Unlimited access for professionals',
            price: 19,
            currency: 'USD',
            interval: 'month',
            features: ['Unlimited searches', 'Detailed specifications', 'Priority support'],
            search_limit: -1,
            is_popular: true,
            formatted_price: '$19',
            metadata: {},
          },
          {
            id: 3,
            name: 'enterprise',
            display_name: 'Enterprise Plan',
            description: 'Custom solutions for large organizations',
            price: 99,
            currency: 'USD',
            interval: 'month',
            features: ['Everything in Premium', 'Custom integrations', 'Dedicated support'],
            search_limit: -1,
            is_popular: false,
            formatted_price: '$99',
            metadata: {},
          },
        ],
        hasMorePlans: false,
        totalPlans: 3,
      },
    });
  }),

  http.get('/api/pricing-plans/all', () => {
    return HttpResponse.json({
      success: true,
      data: {
        plans: [
          {
            id: 1,
            name: 'free',
            display_name: 'Free Plan',
            description: 'Perfect for occasional searches',
            price: 0,
            currency: 'USD',
            interval: 'month',
            features: ['20 searches per day', 'Basic part information', 'Email support'],
            search_limit: 20,
            is_popular: false,
            formatted_price: 'Free',
            metadata: {},
          },
          {
            id: 2,
            name: 'premium',
            display_name: 'Premium Plan',
            description: 'Unlimited access for professionals',
            price: 19,
            currency: 'USD',
            interval: 'month',
            features: ['Unlimited searches', 'Detailed specifications', 'Priority support'],
            search_limit: -1,
            is_popular: true,
            formatted_price: '$19',
            metadata: {},
          },
          {
            id: 3,
            name: 'enterprise',
            display_name: 'Enterprise Plan',
            description: 'Custom solutions for large organizations',
            price: 99,
            currency: 'USD',
            interval: 'month',
            features: ['Everything in Premium', 'Custom integrations', 'Dedicated support'],
            search_limit: -1,
            is_popular: false,
            formatted_price: '$99',
            metadata: {},
          },
        ],
        totalPlans: 3,
      },
    });
  }),
  // Watermark configuration endpoints
  http.get('/api/watermark-config', () => {
    return HttpResponse.json({
      enabled: true,
      text: 'Mobile Parts DB',
      position: 'bottom-right',
      opacity: 0.5,
      fontSize: 14,
      color: '#000000',
      backgroundColor: 'transparent',
      padding: 10,
      margin: 20,
      rotation: 0,
      zIndex: 1000,
      fontFamily: 'Arial, sans-serif',
      fontWeight: 'normal',
      textShadow: 'none',
      borderRadius: 0,
      border: 'none',
      customCss: '',
      showOnPrint: true,
      showOnMobile: true,
      excludePages: [],
      includePages: [],
      userRoles: ['all'],
      timeBasedDisplay: false,
      displayStartTime: null,
      displayEndTime: null,
      dynamicContent: false,
      contentTemplate: '',
      multiLanguage: false,
      translations: {},
      responsive: true,
      breakpoints: {
        mobile: 768,
        tablet: 1024,
        desktop: 1200
      },
      mobileSettings: {
        fontSize: 12,
        opacity: 0.3,
        position: 'bottom-center'
      },
      tabletSettings: {
        fontSize: 13,
        opacity: 0.4,
        position: 'bottom-right'
      }
    });
  }),

  http.post('/api/watermark-config', () => {
    return HttpResponse.json({ success: true, message: 'Configuration updated successfully' });
  }),

  // Notifications endpoints
  http.get('/notifications/unread-count', () => {
    return HttpResponse.json({ count: 0 });
  }),

  http.get('/notifications/recent', () => {
    return HttpResponse.json({ notifications: [] });
  }),

  // Search suggestions endpoint
  http.get('/search/suggestions', () => {
    return HttpResponse.json([
      { id: 1, title: 'iPhone 12 Screen', category: 'Screens' },
      { id: 2, title: 'Samsung Galaxy Battery', category: 'Batteries' },
      { id: 3, title: 'Google Pixel Camera', category: 'Cameras' }
    ]);
  }),

  // API Search suggestions endpoint (used by UnifiedSearchInterface)
  http.get('/api/search/suggestions', ({ request }) => {
    const url = new URL(request.url);
    const query = url.searchParams.get('q') || '';

    // Return suggestions based on query
    if (query.toLowerCase().includes('iphone')) {
      return HttpResponse.json([
        { value: 'iPhone 14', type: 'model' },
        { value: 'iPhone Display', type: 'part' },
      ]);
    }

    return HttpResponse.json([
      { value: 'Samsung Galaxy', type: 'model' },
      { value: 'Battery', type: 'part' },
    ]);
  }),

  // Search configuration endpoints
  http.post('/admin/search-config/update', () => {
    return HttpResponse.json({ success: true, message: 'Configuration updated successfully' });
  }),

  http.post('/admin/search-config/test', () => {
    return HttpResponse.json({ success: true, message: 'Test completed successfully' });
  }),

  http.post('/admin/search-config/reset', () => {
    return HttpResponse.json({ success: true, message: 'Configuration reset to defaults' });
  }),

  // Media picker endpoints
  http.get('/api/media', () => {
    return HttpResponse.json({
      data: [
        {
          id: 1,
          name: 'test-image.jpg',
          url: '/storage/media/test-image.jpg',
          type: 'image/jpeg',
          size: 1024000,
          created_at: '2024-01-01T00:00:00Z'
        }
      ],
      meta: {
        current_page: 1,
        last_page: 1,
        per_page: 20,
        total: 1
      }
    });
  }),

  http.post('/api/media/upload', () => {
    return HttpResponse.json({
      id: 2,
      name: 'uploaded-image.jpg',
      url: '/storage/media/uploaded-image.jpg',
      type: 'image/jpeg',
      size: 2048000,
      created_at: new Date().toISOString()
    });
  }),

  // CSRF token endpoint
  http.get('/csrf-token', () => {
    return HttpResponse.json({ token: 'mock-csrf-token' });
  }),

  // Global search endpoints
  http.get('/api/global-search', ({ request }) => {
    const url = new URL(request.url);
    const query = url.searchParams.get('q');

    if (!query) {
      return HttpResponse.json([]);
    }

    return HttpResponse.json([
      {
        id: 'dashboard',
        title: 'Dashboard',
        description: 'Main dashboard overview',
        url: '/dashboard',
        category: 'Core',
        icon: 'dashboard',
        adminOnly: false
      },
      {
        id: 'users',
        title: 'User Management',
        description: 'Manage system users',
        url: '/admin/users',
        category: 'Admin',
        icon: 'users',
        adminOnly: true
      },
      {
        id: 'search-config',
        title: 'Search Configuration',
        description: 'Configure search settings',
        url: '/admin/search-config',
        category: 'Admin',
        icon: 'settings',
        adminOnly: true
      }
    ]);
  }),

  // Menu management endpoints
  http.post('/admin/menus/:menuId/order', ({ params }) => {
    const { menuId } = params;
    return HttpResponse.json({
      success: true,
      message: `Menu ${menuId} order updated successfully`
    });
  }),

  http.delete('/admin/menus/:menuId/items/:itemId', ({ params }) => {
    const { menuId, itemId } = params;
    return HttpResponse.json({
      success: true,
      message: `Menu item ${itemId} deleted successfully`
    });
  }),

  // Footer configuration endpoint
  http.get('/api/footer-config', () => {
    return HttpResponse.json({
      footer_enabled: true,
      footer_layout: 'simple',
      footer_background_color: '#1f2937',
      footer_text_color: '#ffffff',
      footer_content: 'Test footer content',
      footer_copyright: '© 2024 Test Company',
      footer_links: [
        { title: 'Privacy', url: '/privacy', target: '_self' },
        { title: 'Terms', url: '/terms', target: '_self' },
      ],
      footer_social_links: [],
      footer_show_logo: true,
      footer_logo_position: 'center',
      footer_menu_ids: [],
      footer_menus: [],
      footer_newsletter_enabled: false,
      footer_newsletter_title: 'Newsletter',
      footer_newsletter_description: 'Subscribe to our newsletter',
      footer_newsletter_placeholder: 'Your email',
    });
  }),

  // Navbar configuration endpoint
  http.get('/api/navbar-config', () => {
    return HttpResponse.json({
      navbar_enabled: true,
      navbar_menu_id: null,
      navbar_background_color: '#ffffff',
      navbar_text_color: '#1f2937',
      navbar_logo_position: 'left',
      navbar_show_search: true,
      navbar_sticky: true,
      navbar_style: 'default',
      menu_items: [],
    });
  }),

  // Notification API endpoints
  http.get('/notifications/api/unread-count', () => {
    return HttpResponse.json({ count: 2 });
  }),

  http.get('/notifications/api/recent', () => {
    return HttpResponse.json({
      notifications: [
        {
          id: 1,
          title: 'Test Notification',
          message: 'This is a test notification',
          type: 'info',
          read_at: null,
          created_at: '2024-01-01T00:00:00Z',
          sentBy: { id: 2, name: 'Admin' },
        },
        {
          id: 2,
          title: 'Read Notification',
          message: 'This notification has been read',
          type: 'success',
          read_at: '2024-01-01T01:00:00Z',
          created_at: '2024-01-01T00:30:00Z',
          sentBy: { id: 2, name: 'Admin' },
        },
      ],
    });
  }),

  http.post('/notifications/:id/mark-read', () => {
    return HttpResponse.json({ success: true, message: 'Notification marked as read.' });
  }),

  // Fallback handler for unmatched requests
  http.all('*', ({ request }) => {
    console.warn(`Unhandled ${request.method} request to ${request.url}`);
    return HttpResponse.json({ error: 'Not found' }, { status: 404 });
  })
];
