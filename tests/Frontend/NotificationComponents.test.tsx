import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { router } from '@inertiajs/react';
import NotificationBell from '@/components/user/NotificationBell';

// Mock Inertia router
vi.mock('@inertiajs/react', () => ({
    router: {
        get: vi.fn(),
        post: vi.fn(),
    },
    usePage: () => ({
        props: {
            auth: {
                user: {
                    id: 1,
                    name: 'Test User',
                    email: '<EMAIL>',
                },
            },
        },
    }),
}));

// Mock route helper
vi.mock('@/utils/route', () => ({
    route: vi.fn((name: string, params?: any) => {
        const routes: Record<string, string> = {
            'notifications.index': '/notifications',
            'notifications.show': `/notifications/${params}`,
            'notifications.mark-read': `/notifications/${params}/mark-read`,
            'notifications.unread-count': '/notifications/api/unread-count',
            'notifications.recent': '/notifications/api/recent',
        };
        return routes[name] || '/';
    }),
}));

// Mock fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('NotificationBell Component', () => {
    const mockRouter = router as any;

    beforeEach(() => {
        vi.clearAllMocks();
        
        // Default successful responses
        mockFetch.mockImplementation((url: string) => {
            if (url.includes('unread-count')) {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({ count: 2 }),
                });
            }
            if (url.includes('recent')) {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({
                        notifications: [
                            {
                                id: 1,
                                title: 'Test Notification',
                                message: 'This is a test notification',
                                type: 'info',
                                read_at: null,
                                created_at: '2024-01-01T00:00:00Z',
                                sentBy: { id: 2, name: 'Admin' },
                            },
                            {
                                id: 2,
                                title: 'Read Notification',
                                message: 'This notification has been read',
                                type: 'success',
                                read_at: '2024-01-01T01:00:00Z',
                                created_at: '2024-01-01T00:30:00Z',
                                sentBy: { id: 2, name: 'Admin' },
                            },
                        ],
                    }),
                });
            }
            return Promise.resolve({ ok: false });
        });
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    it('renders notification bell with unread count', async () => {
        render(<NotificationBell />);

        await waitFor(() => {
            expect(screen.getByText('2')).toBeInTheDocument();
        });

        expect(mockFetch).toHaveBeenCalledWith('/notifications/api/unread-count');
        expect(mockFetch).toHaveBeenCalledWith('/notifications/api/recent');
    });

    it('handles authentication errors gracefully', async () => {
        mockFetch.mockImplementation(() => 
            Promise.resolve({
                ok: false,
                status: 401,
            })
        );

        render(<NotificationBell />);

        await waitFor(() => {
            // Should not show any count when authentication fails
            expect(screen.queryByText('2')).not.toBeInTheDocument();
        });
    });

    it('handles network errors gracefully', async () => {
        mockFetch.mockImplementation(() => 
            Promise.reject(new Error('Network error'))
        );

        const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

        render(<NotificationBell />);

        await waitFor(() => {
            expect(consoleSpy).toHaveBeenCalledWith(
                'Failed to fetch notification data:',
                expect.any(Error)
            );
        });

        consoleSpy.mockRestore();
    });

    it('navigates to notification with error handling', async () => {
        render(<NotificationBell />);

        // Wait for notifications to load
        await waitFor(() => {
            expect(screen.getByText('Test Notification')).toBeInTheDocument();
        });

        // Click on a notification
        const notificationItem = screen.getByText('Test Notification').closest('[role="menuitem"]');
        expect(notificationItem).toBeInTheDocument();

        fireEvent.click(notificationItem!);

        expect(mockRouter.get).toHaveBeenCalledWith(
            '/notifications/1',
            {},
            expect.objectContaining({
                onError: expect.any(Function),
            })
        );
    });

    it('marks notification as read with error handling', async () => {
        mockRouter.post.mockImplementation((url, data, options) => {
            if (options?.onSuccess) {
                options.onSuccess();
            }
        });

        render(<NotificationBell />);

        // Wait for notifications to load
        await waitFor(() => {
            expect(screen.getByText('Test Notification')).toBeInTheDocument();
        });

        // Find and click the mark as read button (eye icon)
        const markReadButton = screen.getByRole('button', { name: /mark as read/i });
        fireEvent.click(markReadButton);

        expect(mockRouter.post).toHaveBeenCalledWith(
            '/notifications/1/mark-read',
            {},
            expect.objectContaining({
                preserveState: true,
                preserveScroll: true,
                onSuccess: expect.any(Function),
            })
        );
    });

    it('handles mark as read errors', async () => {
        const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
        
        mockRouter.post.mockImplementation(() => {
            throw new Error('Failed to mark as read');
        });

        render(<NotificationBell />);

        // Wait for notifications to load
        await waitFor(() => {
            expect(screen.getByText('Test Notification')).toBeInTheDocument();
        });

        // Find and click the mark as read button
        const markReadButton = screen.getByRole('button', { name: /mark as read/i });
        fireEvent.click(markReadButton);

        await waitFor(() => {
            expect(consoleSpy).toHaveBeenCalledWith(
                'Failed to mark notification as read:',
                expect.any(Error)
            );
        });

        consoleSpy.mockRestore();
    });

    it('displays correct notification icons based on type', async () => {
        render(<NotificationBell />);

        await waitFor(() => {
            expect(screen.getByText('Test Notification')).toBeInTheDocument();
            expect(screen.getByText('Read Notification')).toBeInTheDocument();
        });

        // Check that different notification types have different icons
        // This would depend on the specific icon implementation
        const notifications = screen.getAllByRole('menuitem');
        expect(notifications).toHaveLength(2);
    });

    it('shows loading state initially', () => {
        render(<NotificationBell />);

        expect(screen.getByText('Loading notifications...')).toBeInTheDocument();
    });

    it('shows empty state when no notifications', async () => {
        mockFetch.mockImplementation((url: string) => {
            if (url.includes('unread-count')) {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({ count: 0 }),
                });
            }
            if (url.includes('recent')) {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({ notifications: [] }),
                });
            }
            return Promise.resolve({ ok: false });
        });

        render(<NotificationBell />);

        await waitFor(() => {
            expect(screen.getByText('No notifications')).toBeInTheDocument();
        });
    });

    it('updates unread count when marking notification as read', async () => {
        let unreadCount = 2;
        
        mockRouter.post.mockImplementation((url, data, options) => {
            if (url.includes('mark-read') && options?.onSuccess) {
                unreadCount--;
                options.onSuccess();
            }
        });

        render(<NotificationBell />);

        // Wait for initial load
        await waitFor(() => {
            expect(screen.getByText('2')).toBeInTheDocument();
        });

        // Mark a notification as read
        const markReadButton = screen.getByRole('button', { name: /mark as read/i });
        fireEvent.click(markReadButton);

        // The component should update the local state
        await waitFor(() => {
            // This would depend on the component's state management
            // The test verifies that the onSuccess callback is called
            expect(mockRouter.post).toHaveBeenCalled();
        });
    });
});
